07/31/2025 11:28:11.793 INFO  [main]o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@d5b810e: startup date [Thu Jul 31 11:28:11 CST 2025]; root of context hierarchy
07/31/2025 11:28:11.801 INFO  [background-preinit]o.h.v.i.u.Version - HV000001: Hibernate Validator 5.3.5.Final
07/31/2025 11:28:11.944 INFO  [main]o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
07/31/2025 11:28:11.972 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$ddef4bd0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:32:41.151 INFO  [background-preinit]o.h.v.i.u.Version - HV000001: Hibernate Validator 5.3.5.Final
07/31/2025 11:32:41.162 INFO  [main]o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@66f4fb48: startup date [Thu Jul 31 11:32:41 CST 2025]; root of context hierarchy
07/31/2025 11:32:41.389 INFO  [main]o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
07/31/2025 11:32:41.424 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$ea087141] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:11.813 INFO  [main]o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@6392827e: startup date [Thu Jul 31 11:33:11 CST 2025]; root of context hierarchy
07/31/2025 11:33:11.815 INFO  [background-preinit]o.h.v.i.u.Version - HV000001: Hibernate Validator 5.3.5.Final
07/31/2025 11:33:11.977 INFO  [main]o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
07/31/2025 11:33:12.005 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$fa9afa51] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:14.904 INFO  [main]o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@342c38f8: startup date [Thu Jul 31 14:42:14 CST 2025]; root of context hierarchy
07/31/2025 14:42:14.910 INFO  [background-preinit]o.h.v.i.u.Version - HV000001: Hibernate Validator 5.3.5.Final
07/31/2025 14:42:15.083 INFO  [main]o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
07/31/2025 14:42:15.117 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$ea8e2dbd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:17.474 INFO  [main]o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@6392827e: startup date [Thu Jul 31 14:46:17 CST 2025]; root of context hierarchy
07/31/2025 14:46:17.480 INFO  [background-preinit]o.h.v.i.u.Version - HV000001: Hibernate Validator 5.3.5.Final
07/31/2025 14:46:17.636 INFO  [main]o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
07/31/2025 14:46:17.669 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$38297a6a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:49:04.065 INFO  [main]o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@6392827e: startup date [Thu Jul 31 14:49:04 CST 2025]; root of context hierarchy
07/31/2025 14:49:04.070 INFO  [background-preinit]o.h.v.i.u.Version - HV000001: Hibernate Validator 5.3.5.Final
07/31/2025 14:49:04.255 INFO  [main]o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
07/31/2025 14:49:04.287 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$38297a6a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:55:19.117 INFO  [main]o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@6392827e: startup date [Thu Jul 31 14:55:19 CST 2025]; root of context hierarchy
07/31/2025 14:55:19.122 INFO  [background-preinit]o.h.v.i.u.Version - HV000001: Hibernate Validator 5.3.5.Final
07/31/2025 14:55:19.298 INFO  [main]o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
07/31/2025 14:55:19.327 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$fa9afa51] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 15:03:04.639 INFO  [main]o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@6392827e: startup date [Thu Jul 31 15:03:04 CST 2025]; root of context hierarchy
07/31/2025 15:03:04.647 INFO  [background-preinit]o.h.v.i.u.Version - HV000001: Hibernate Validator 5.3.5.Final
07/31/2025 15:03:04.797 INFO  [main]o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
07/31/2025 15:03:04.827 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$ddef4bd0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 15:08:45.260 INFO  [main]o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@6392827e: startup date [Thu Jul 31 15:08:45 CST 2025]; root of context hierarchy
07/31/2025 15:08:45.268 INFO  [background-preinit]o.h.v.i.u.Version - HV000001: Hibernate Validator 5.3.5.Final
07/31/2025 15:08:45.436 INFO  [main]o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
07/31/2025 15:08:45.471 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$fa9afa51] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 15:31:15.332 INFO  [main]o.s.c.a.AnnotationConfigApplicationContext - Refreshing org.springframework.context.annotation.AnnotationConfigApplicationContext@1b2abca6: startup date [Thu Jul 31 15:31:15 CST 2025]; root of context hierarchy
07/31/2025 15:31:15.338 INFO  [background-preinit]o.h.v.i.u.Version - HV000001: Hibernate Validator 5.3.5.Final
07/31/2025 15:31:15.605 INFO  [main]o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
07/31/2025 15:31:15.648 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'configurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$15059ce5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
