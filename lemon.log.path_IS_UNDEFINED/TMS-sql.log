07/31/2025 11:28:17 INFO  [main]o.h.j.i.u.Log<PERSON>elper - HHH000204: Processing PersistenceUnitInfo [
	name: tmsPersistenceUnit
	...]
07/31/2025 11:28:17 INFO  [main]o.h.Version - HHH000412: Hibernate Core {5.0.12.Final}
07/31/2025 11:28:17 INFO  [main]o.h.c.Environment - HHH000206: hibernate.properties not found
07/31/2025 11:28:17 INFO  [main]o.h.c.Environment - HHH000021: Bytecode provider name : javassist
07/31/2025 11:28:17 INFO  [main]o.h.a.c.Version - HCANN000001: Hibernate Commons Annotations {5.0.1.Final}
07/31/2025 11:28:18 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:28:18 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 11:28:18 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 11:28:18 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:28:24 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: acmPersistenceUnit
	...]
07/31/2025 11:28:26 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:28:26 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 11:28:26 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 11:28:26 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:28:38 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: bilPersistenceUnit
	...]
07/31/2025 11:28:41 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:28:41 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:28:44 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: chkPersistenceUnit
	...]
07/31/2025 11:28:46 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:28:46 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:28:52 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cmmPersistenceUnit
	...]
07/31/2025 11:28:54 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:28:54 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:29:09 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpiPersistenceUnit
	...]
07/31/2025 11:29:11 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:29:11 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:29:12 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpmPersistenceUnit
	...]
07/31/2025 11:29:15 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:29:15 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:29:16 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 11:29:19 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:29:19 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:29:20 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: csmPersistenceUnit
	...]
07/31/2025 11:29:23 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:29:23 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:29:26 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: demoPersistenceUnit
	...]
07/31/2025 11:29:28 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:29:28 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:29:29 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: invPersistenceUnit
	...]
07/31/2025 11:29:32 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:29:32 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:29:37 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: mkmPersistenceUnit
	...]
07/31/2025 11:29:39 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:29:39 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:29:43 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: onrPersistenceUnit
	...]
07/31/2025 11:29:45 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:29:45 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:29:47 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: rptPersistenceUnit
	...]
07/31/2025 11:29:50 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:29:50 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:29:53 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: rsmPersistenceUnit
	...]
07/31/2025 11:29:55 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:29:55 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:30:03 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 11:30:05 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:30:05 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:30:05 WARN  [main]o.h.j.i.EntityManagerFactoryRegistry - HHH000436: Entity manager factory name (tamPersistenceUnit) is already registered.  If entity manager will be clustered or passivated, specify a unique value for property 'hibernate.ejb.entitymanager_factory_name'
07/31/2025 11:30:06 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tfmPersistenceUnit
	...]
07/31/2025 11:30:08 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:30:08 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:30:13 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: urmPersistenceUnit
	...]
07/31/2025 11:30:15 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:30:15 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:30:34 INFO  [main]o.h.h.i.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
07/31/2025 11:30:35 DEBUG [localhost-startStop-1]o.hibernate.SQL - select role0_.role_id as role_id1_2_, role0_.branch_name as branch_n2_2_, role0_.office_name as office_n3_2_, role0_.role as role4_2_, role0_.role_name as role_nam5_2_, role0_.status as status6_2_ from role role0_
07/31/2025 11:30:35 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 11:30:35 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [1]
07/31/2025 11:30:35 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 11:30:35 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [2]
07/31/2025 11:30:35 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 11:30:35 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [3]
07/31/2025 11:33:18 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tmsPersistenceUnit
	...]
07/31/2025 11:33:18 INFO  [main]o.h.Version - HHH000412: Hibernate Core {5.0.12.Final}
07/31/2025 11:33:18 INFO  [main]o.h.c.Environment - HHH000206: hibernate.properties not found
07/31/2025 11:33:18 INFO  [main]o.h.c.Environment - HHH000021: Bytecode provider name : javassist
07/31/2025 11:33:18 INFO  [main]o.h.a.c.Version - HCANN000001: Hibernate Commons Annotations {5.0.1.Final}
07/31/2025 11:33:18 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:33:18 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 11:33:18 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 11:33:18 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:33:25 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: acmPersistenceUnit
	...]
07/31/2025 11:33:27 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:33:27 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 11:33:27 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 11:33:27 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:33:37 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: bilPersistenceUnit
	...]
07/31/2025 11:33:39 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:33:39 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:33:42 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: chkPersistenceUnit
	...]
07/31/2025 11:33:45 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:33:45 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:33:50 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cmmPersistenceUnit
	...]
07/31/2025 11:33:52 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:33:52 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:34:09 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpiPersistenceUnit
	...]
07/31/2025 11:34:12 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:34:12 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:34:13 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpmPersistenceUnit
	...]
07/31/2025 11:34:15 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:34:15 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:34:17 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 11:34:19 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:34:19 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:34:20 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: csmPersistenceUnit
	...]
07/31/2025 11:34:23 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:34:23 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:34:26 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: demoPersistenceUnit
	...]
07/31/2025 11:34:28 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:34:28 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:34:29 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: invPersistenceUnit
	...]
07/31/2025 11:34:31 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:34:31 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:34:36 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: mkmPersistenceUnit
	...]
07/31/2025 11:34:38 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:34:38 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:34:42 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: onrPersistenceUnit
	...]
07/31/2025 11:34:44 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:34:44 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:34:47 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: rptPersistenceUnit
	...]
07/31/2025 11:34:49 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:34:49 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:34:52 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: rsmPersistenceUnit
	...]
07/31/2025 11:34:55 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:34:55 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:35:04 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 11:35:06 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:35:06 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:35:06 WARN  [main]o.h.j.i.EntityManagerFactoryRegistry - HHH000436: Entity manager factory name (tamPersistenceUnit) is already registered.  If entity manager will be clustered or passivated, specify a unique value for property 'hibernate.ejb.entitymanager_factory_name'
07/31/2025 11:35:06 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tfmPersistenceUnit
	...]
07/31/2025 11:35:08 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:35:08 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:35:12 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: urmPersistenceUnit
	...]
07/31/2025 11:35:16 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:35:16 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:35:32 INFO  [main]o.h.h.i.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
07/31/2025 11:35:33 DEBUG [localhost-startStop-1]o.hibernate.SQL - select role0_.role_id as role_id1_2_, role0_.branch_name as branch_n2_2_, role0_.office_name as office_n3_2_, role0_.role as role4_2_, role0_.role_name as role_nam5_2_, role0_.status as status6_2_ from role role0_
07/31/2025 11:35:33 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 11:35:33 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [1]
07/31/2025 11:35:34 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 11:35:34 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [2]
07/31/2025 11:35:34 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 11:35:34 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [3]
07/31/2025 14:42:21 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tmsPersistenceUnit
	...]
07/31/2025 14:42:21 INFO  [main]o.h.Version - HHH000412: Hibernate Core {5.0.12.Final}
07/31/2025 14:42:21 INFO  [main]o.h.c.Environment - HHH000206: hibernate.properties not found
07/31/2025 14:42:21 INFO  [main]o.h.c.Environment - HHH000021: Bytecode provider name : javassist
07/31/2025 14:42:21 INFO  [main]o.h.a.c.Version - HCANN000001: Hibernate Commons Annotations {5.0.1.Final}
07/31/2025 14:42:21 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:42:21 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 14:42:21 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 14:42:21 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:42:29 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: acmPersistenceUnit
	...]
07/31/2025 14:42:32 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:42:32 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 14:42:32 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 14:42:32 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:42:45 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: bilPersistenceUnit
	...]
07/31/2025 14:42:48 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:42:48 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:42:51 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: chkPersistenceUnit
	...]
07/31/2025 14:42:55 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:42:55 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:43:02 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cmmPersistenceUnit
	...]
07/31/2025 14:43:05 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:43:05 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:43:21 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpiPersistenceUnit
	...]
07/31/2025 14:43:24 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:43:24 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:43:25 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpmPersistenceUnit
	...]
07/31/2025 14:43:28 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:43:28 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:43:29 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 14:43:31 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:43:31 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:43:33 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: csmPersistenceUnit
	...]
07/31/2025 14:43:35 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:43:35 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:43:39 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: demoPersistenceUnit
	...]
07/31/2025 14:43:41 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:43:41 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:43:42 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: invPersistenceUnit
	...]
07/31/2025 14:43:45 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:43:45 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:43:51 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: mkmPersistenceUnit
	...]
07/31/2025 14:43:54 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:43:54 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:43:57 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: onrPersistenceUnit
	...]
07/31/2025 14:44:00 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:44:00 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:44:04 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: rptPersistenceUnit
	...]
07/31/2025 14:44:06 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:44:06 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:44:09 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: rsmPersistenceUnit
	...]
07/31/2025 14:44:11 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:44:11 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:44:23 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 14:44:26 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:44:26 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:44:26 WARN  [main]o.h.j.i.EntityManagerFactoryRegistry - HHH000436: Entity manager factory name (tamPersistenceUnit) is already registered.  If entity manager will be clustered or passivated, specify a unique value for property 'hibernate.ejb.entitymanager_factory_name'
07/31/2025 14:44:26 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tfmPersistenceUnit
	...]
07/31/2025 14:44:29 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:44:29 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:44:33 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: urmPersistenceUnit
	...]
07/31/2025 14:44:36 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:44:36 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:44:56 INFO  [main]o.h.h.i.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
07/31/2025 14:44:56 DEBUG [localhost-startStop-1]o.hibernate.SQL - select role0_.role_id as role_id1_2_, role0_.branch_name as branch_n2_2_, role0_.office_name as office_n3_2_, role0_.role as role4_2_, role0_.role_name as role_nam5_2_, role0_.status as status6_2_ from role role0_
07/31/2025 14:44:57 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 14:44:57 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [1]
07/31/2025 14:44:57 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 14:44:57 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [2]
07/31/2025 14:44:57 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 14:44:57 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [3]
07/31/2025 14:46:23 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tmsPersistenceUnit
	...]
07/31/2025 14:46:23 INFO  [main]o.h.Version - HHH000412: Hibernate Core {5.0.12.Final}
07/31/2025 14:46:23 INFO  [main]o.h.c.Environment - HHH000206: hibernate.properties not found
07/31/2025 14:46:23 INFO  [main]o.h.c.Environment - HHH000021: Bytecode provider name : javassist
07/31/2025 14:46:23 INFO  [main]o.h.a.c.Version - HCANN000001: Hibernate Commons Annotations {5.0.1.Final}
07/31/2025 14:46:23 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:46:24 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 14:46:24 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 14:46:24 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:46:30 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: acmPersistenceUnit
	...]
07/31/2025 14:46:33 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:46:33 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 14:46:33 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 14:46:33 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:46:44 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: bilPersistenceUnit
	...]
07/31/2025 14:46:46 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:46:46 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:46:49 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: chkPersistenceUnit
	...]
07/31/2025 14:46:52 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:46:52 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:46:59 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cmmPersistenceUnit
	...]
07/31/2025 14:47:02 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:47:02 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:47:20 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpiPersistenceUnit
	...]
07/31/2025 14:47:22 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:47:22 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:47:23 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpmPersistenceUnit
	...]
07/31/2025 14:47:25 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:47:25 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:47:27 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 14:47:30 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:47:30 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:47:31 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: csmPersistenceUnit
	...]
07/31/2025 14:47:34 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:47:34 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:47:38 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: demoPersistenceUnit
	...]
07/31/2025 14:47:41 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:47:41 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:47:42 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: invPersistenceUnit
	...]
07/31/2025 14:47:45 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:47:45 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:47:50 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: mkmPersistenceUnit
	...]
07/31/2025 14:47:53 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:47:53 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:47:57 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: onrPersistenceUnit
	...]
07/31/2025 14:47:59 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:47:59 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:49:10 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tmsPersistenceUnit
	...]
07/31/2025 14:49:10 INFO  [main]o.h.Version - HHH000412: Hibernate Core {5.0.12.Final}
07/31/2025 14:49:10 INFO  [main]o.h.c.Environment - HHH000206: hibernate.properties not found
07/31/2025 14:49:10 INFO  [main]o.h.c.Environment - HHH000021: Bytecode provider name : javassist
07/31/2025 14:49:10 INFO  [main]o.h.a.c.Version - HCANN000001: Hibernate Commons Annotations {5.0.1.Final}
07/31/2025 14:49:10 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:49:10 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 14:49:10 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 14:49:10 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:49:16 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: acmPersistenceUnit
	...]
07/31/2025 14:49:19 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:49:19 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 14:49:19 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 14:49:19 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:49:30 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: bilPersistenceUnit
	...]
07/31/2025 14:49:32 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:49:32 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:49:35 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: chkPersistenceUnit
	...]
07/31/2025 14:49:37 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:49:37 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:49:43 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cmmPersistenceUnit
	...]
07/31/2025 14:49:45 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:49:45 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:50:01 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpiPersistenceUnit
	...]
07/31/2025 14:50:04 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:50:04 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:50:05 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpmPersistenceUnit
	...]
07/31/2025 14:50:07 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:50:07 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:50:09 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 14:50:11 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:50:11 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:50:13 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: csmPersistenceUnit
	...]
07/31/2025 14:50:15 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:50:15 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:50:19 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: demoPersistenceUnit
	...]
07/31/2025 14:50:22 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:50:22 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:50:23 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: invPersistenceUnit
	...]
07/31/2025 14:50:26 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:50:26 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:50:31 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: mkmPersistenceUnit
	...]
07/31/2025 14:50:34 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:50:34 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:50:37 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: onrPersistenceUnit
	...]
07/31/2025 14:50:40 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:50:40 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:50:42 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: rptPersistenceUnit
	...]
07/31/2025 14:50:45 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:50:45 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:50:48 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: rsmPersistenceUnit
	...]
07/31/2025 14:50:51 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:50:51 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:51:03 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 14:51:05 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:51:05 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:51:05 WARN  [main]o.h.j.i.EntityManagerFactoryRegistry - HHH000436: Entity manager factory name (tamPersistenceUnit) is already registered.  If entity manager will be clustered or passivated, specify a unique value for property 'hibernate.ejb.entitymanager_factory_name'
07/31/2025 14:51:05 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tfmPersistenceUnit
	...]
07/31/2025 14:51:08 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:51:08 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:51:12 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: urmPersistenceUnit
	...]
07/31/2025 14:51:15 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:51:15 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:51:31 INFO  [main]o.h.h.i.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
07/31/2025 14:51:31 DEBUG [localhost-startStop-1]o.hibernate.SQL - select role0_.role_id as role_id1_2_, role0_.branch_name as branch_n2_2_, role0_.office_name as office_n3_2_, role0_.role as role4_2_, role0_.role_name as role_nam5_2_, role0_.status as status6_2_ from role role0_
07/31/2025 14:51:32 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 14:51:32 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [1]
07/31/2025 14:51:32 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 14:51:32 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [2]
07/31/2025 14:51:32 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 14:51:32 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [3]
07/31/2025 14:55:28 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tmsPersistenceUnit
	...]
07/31/2025 14:55:28 INFO  [main]o.h.Version - HHH000412: Hibernate Core {5.0.12.Final}
07/31/2025 14:55:28 INFO  [main]o.h.c.Environment - HHH000206: hibernate.properties not found
07/31/2025 14:55:28 INFO  [main]o.h.c.Environment - HHH000021: Bytecode provider name : javassist
07/31/2025 14:55:28 INFO  [main]o.h.a.c.Version - HCANN000001: Hibernate Commons Annotations {5.0.1.Final}
07/31/2025 14:55:28 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:55:28 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 14:55:28 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 14:55:28 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:55:35 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: acmPersistenceUnit
	...]
07/31/2025 14:55:38 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:55:38 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 14:55:38 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 14:55:38 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:55:50 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: bilPersistenceUnit
	...]
07/31/2025 14:55:54 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:55:54 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:55:57 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: chkPersistenceUnit
	...]
07/31/2025 14:55:59 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:55:59 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:56:05 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cmmPersistenceUnit
	...]
07/31/2025 14:56:08 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:56:08 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:56:26 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpiPersistenceUnit
	...]
07/31/2025 14:56:29 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:56:29 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:56:30 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpmPersistenceUnit
	...]
07/31/2025 14:56:32 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:56:32 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:56:34 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 14:56:37 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:56:37 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:56:39 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: csmPersistenceUnit
	...]
07/31/2025 14:56:41 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:56:41 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:56:45 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: demoPersistenceUnit
	...]
07/31/2025 14:56:47 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:56:47 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:56:48 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: invPersistenceUnit
	...]
07/31/2025 14:56:51 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:56:51 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:56:55 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: mkmPersistenceUnit
	...]
07/31/2025 14:56:57 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:56:57 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:57:01 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: onrPersistenceUnit
	...]
07/31/2025 14:57:04 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:57:04 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:57:08 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: rptPersistenceUnit
	...]
07/31/2025 14:57:11 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:57:11 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:57:14 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: rsmPersistenceUnit
	...]
07/31/2025 14:57:16 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:57:16 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:57:28 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 14:57:30 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:57:30 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:57:30 WARN  [main]o.h.j.i.EntityManagerFactoryRegistry - HHH000436: Entity manager factory name (tamPersistenceUnit) is already registered.  If entity manager will be clustered or passivated, specify a unique value for property 'hibernate.ejb.entitymanager_factory_name'
07/31/2025 14:57:31 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tfmPersistenceUnit
	...]
07/31/2025 14:57:33 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:57:33 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:57:37 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: urmPersistenceUnit
	...]
07/31/2025 14:57:40 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:57:40 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:57:59 INFO  [main]o.h.h.i.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
07/31/2025 14:57:59 DEBUG [localhost-startStop-1]o.hibernate.SQL - select role0_.role_id as role_id1_2_, role0_.branch_name as branch_n2_2_, role0_.office_name as office_n3_2_, role0_.role as role4_2_, role0_.role_name as role_nam5_2_, role0_.status as status6_2_ from role role0_
07/31/2025 14:58:00 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 14:58:00 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [1]
07/31/2025 14:58:00 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 14:58:00 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [2]
07/31/2025 14:58:00 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 14:58:00 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [3]
07/31/2025 15:03:11 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tmsPersistenceUnit
	...]
07/31/2025 15:03:11 INFO  [main]o.h.Version - HHH000412: Hibernate Core {5.0.12.Final}
07/31/2025 15:03:11 INFO  [main]o.h.c.Environment - HHH000206: hibernate.properties not found
07/31/2025 15:03:11 INFO  [main]o.h.c.Environment - HHH000021: Bytecode provider name : javassist
07/31/2025 15:03:11 INFO  [main]o.h.a.c.Version - HCANN000001: Hibernate Commons Annotations {5.0.1.Final}
07/31/2025 15:03:11 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:03:11 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 15:03:11 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 15:03:12 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:03:19 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: acmPersistenceUnit
	...]
07/31/2025 15:03:21 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:03:21 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 15:03:21 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 15:03:21 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:03:31 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: bilPersistenceUnit
	...]
07/31/2025 15:03:33 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:03:33 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:03:36 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: chkPersistenceUnit
	...]
07/31/2025 15:03:39 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:03:39 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:03:46 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cmmPersistenceUnit
	...]
07/31/2025 15:03:48 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:03:48 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:04:09 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpiPersistenceUnit
	...]
07/31/2025 15:04:11 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:04:11 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:04:12 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpmPersistenceUnit
	...]
07/31/2025 15:04:14 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:04:14 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:04:16 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 15:04:19 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:04:19 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:04:21 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: csmPersistenceUnit
	...]
07/31/2025 15:04:23 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:04:23 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:04:28 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: demoPersistenceUnit
	...]
07/31/2025 15:04:31 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:04:31 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:04:32 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: invPersistenceUnit
	...]
07/31/2025 15:04:35 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:04:35 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:04:40 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: mkmPersistenceUnit
	...]
07/31/2025 15:04:42 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:04:42 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:04:46 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: onrPersistenceUnit
	...]
07/31/2025 15:04:48 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:04:48 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:04:52 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: rptPersistenceUnit
	...]
07/31/2025 15:04:55 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:04:55 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:04:57 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: rsmPersistenceUnit
	...]
07/31/2025 15:05:02 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:05:02 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:05:13 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 15:05:15 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:05:15 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:05:15 WARN  [main]o.h.j.i.EntityManagerFactoryRegistry - HHH000436: Entity manager factory name (tamPersistenceUnit) is already registered.  If entity manager will be clustered or passivated, specify a unique value for property 'hibernate.ejb.entitymanager_factory_name'
07/31/2025 15:05:15 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tfmPersistenceUnit
	...]
07/31/2025 15:05:18 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:05:18 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:05:22 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: urmPersistenceUnit
	...]
07/31/2025 15:05:25 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:05:25 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:05:43 INFO  [main]o.h.h.i.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
07/31/2025 15:05:44 DEBUG [localhost-startStop-1]o.hibernate.SQL - select role0_.role_id as role_id1_2_, role0_.branch_name as branch_n2_2_, role0_.office_name as office_n3_2_, role0_.role as role4_2_, role0_.role_name as role_nam5_2_, role0_.status as status6_2_ from role role0_
07/31/2025 15:05:44 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 15:05:44 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [1]
07/31/2025 15:05:44 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 15:05:44 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [2]
07/31/2025 15:05:44 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 15:05:44 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [3]
07/31/2025 15:08:51 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tmsPersistenceUnit
	...]
07/31/2025 15:08:51 INFO  [main]o.h.Version - HHH000412: Hibernate Core {5.0.12.Final}
07/31/2025 15:08:51 INFO  [main]o.h.c.Environment - HHH000206: hibernate.properties not found
07/31/2025 15:08:51 INFO  [main]o.h.c.Environment - HHH000021: Bytecode provider name : javassist
07/31/2025 15:08:51 INFO  [main]o.h.a.c.Version - HCANN000001: Hibernate Commons Annotations {5.0.1.Final}
07/31/2025 15:08:52 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:08:52 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 15:08:52 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 15:08:52 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:08:59 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: acmPersistenceUnit
	...]
07/31/2025 15:09:02 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:09:02 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 15:09:02 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 15:09:02 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:09:13 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: bilPersistenceUnit
	...]
07/31/2025 15:09:16 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:09:16 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:09:19 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: chkPersistenceUnit
	...]
07/31/2025 15:09:22 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:09:22 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:09:29 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cmmPersistenceUnit
	...]
07/31/2025 15:09:32 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:09:32 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:09:52 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpiPersistenceUnit
	...]
07/31/2025 15:09:54 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:09:54 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:09:55 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpmPersistenceUnit
	...]
07/31/2025 15:09:58 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:09:58 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:10:00 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 15:10:03 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:10:03 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:10:05 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: csmPersistenceUnit
	...]
07/31/2025 15:10:08 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:10:08 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:10:12 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: demoPersistenceUnit
	...]
07/31/2025 15:10:15 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:10:15 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:10:16 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: invPersistenceUnit
	...]
07/31/2025 15:10:18 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:10:18 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:10:24 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: mkmPersistenceUnit
	...]
07/31/2025 15:10:27 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:10:27 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:10:30 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: onrPersistenceUnit
	...]
07/31/2025 15:10:33 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:10:33 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:10:37 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: rptPersistenceUnit
	...]
07/31/2025 15:10:41 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:10:41 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:10:44 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: rsmPersistenceUnit
	...]
07/31/2025 15:10:47 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:10:47 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:10:57 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 15:11:00 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:11:00 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:11:00 WARN  [main]o.h.j.i.EntityManagerFactoryRegistry - HHH000436: Entity manager factory name (tamPersistenceUnit) is already registered.  If entity manager will be clustered or passivated, specify a unique value for property 'hibernate.ejb.entitymanager_factory_name'
07/31/2025 15:11:00 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tfmPersistenceUnit
	...]
07/31/2025 15:11:02 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:11:02 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:11:06 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: urmPersistenceUnit
	...]
07/31/2025 15:11:09 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:11:09 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:11:25 INFO  [main]o.h.h.i.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
07/31/2025 15:11:26 DEBUG [localhost-startStop-1]o.hibernate.SQL - select role0_.role_id as role_id1_2_, role0_.branch_name as branch_n2_2_, role0_.office_name as office_n3_2_, role0_.role as role4_2_, role0_.role_name as role_nam5_2_, role0_.status as status6_2_ from role role0_
07/31/2025 15:11:26 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 15:11:26 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [1]
07/31/2025 15:11:27 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 15:11:27 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [2]
07/31/2025 15:11:27 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 15:11:27 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [3]
07/31/2025 15:11:31 INFO  [main]o.h.h.i.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
07/31/2025 15:11:32 INFO  [main]o.h.h.i.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
07/31/2025 15:12:16 DEBUG [http-nio-9020-exec-4]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:12:16 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:12:16 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:12:21 DEBUG [http-nio-9020-exec-1]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:12:21 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:12:21 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:21:26 DEBUG [http-nio-9020-exec-3]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:21:26 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:21:26 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:21:27 DEBUG [http-nio-9020-exec-10]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:21:27 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:21:27 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:21:27 DEBUG [http-nio-9020-exec-9]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:21:27 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:21:27 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:21:27 DEBUG [http-nio-9020-exec-10]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:21:27 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:21:27 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:21:29 DEBUG [http-nio-9020-exec-4]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:21:29 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:21:29 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:21:29 DEBUG [http-nio-9020-exec-2]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:21:29 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:21:29 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:21:29 DEBUG [http-nio-9020-exec-7]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:21:29 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:21:29 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:23:20 DEBUG [http-nio-9020-exec-10]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:23:20 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:23:20 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:23:20 DEBUG [http-nio-9020-exec-5]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:23:20 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:23:20 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:23:20 DEBUG [http-nio-9020-exec-6]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:23:20 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:23:20 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:23:21 DEBUG [http-nio-9020-exec-8]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:23:21 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:23:21 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:23:21 DEBUG [http-nio-9020-exec-5]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:23:21 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:23:21 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:23:21 DEBUG [http-nio-9020-exec-1]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:23:21 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:23:21 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:27:15 DEBUG [http-nio-9020-exec-5]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:27:15 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:27:15 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:27:15 DEBUG [http-nio-9020-exec-1]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:27:15 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:27:15 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:27:15 DEBUG [http-nio-9020-exec-7]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:27:15 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:27:15 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:27:17 DEBUG [http-nio-9020-exec-9]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:27:17 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:27:17 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:27:17 DEBUG [http-nio-9020-exec-1]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:27:17 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:27:17 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:27:17 DEBUG [http-nio-9020-exec-5]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:27:17 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:27:17 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:31:23 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tmsPersistenceUnit
	...]
07/31/2025 15:31:23 INFO  [main]o.h.Version - HHH000412: Hibernate Core {5.0.12.Final}
07/31/2025 15:31:23 INFO  [main]o.h.c.Environment - HHH000206: hibernate.properties not found
07/31/2025 15:31:23 INFO  [main]o.h.c.Environment - HHH000021: Bytecode provider name : javassist
07/31/2025 15:31:23 INFO  [main]o.h.a.c.Version - HCANN000001: Hibernate Commons Annotations {5.0.1.Final}
07/31/2025 15:31:24 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:31:24 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 15:31:24 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 15:31:24 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:31:31 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: acmPersistenceUnit
	...]
07/31/2025 15:31:34 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:31:34 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 15:31:34 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 15:31:34 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:31:45 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: bilPersistenceUnit
	...]
07/31/2025 15:31:48 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:31:48 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:31:51 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: chkPersistenceUnit
	...]
07/31/2025 15:31:54 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:31:54 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:32:01 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cmmPersistenceUnit
	...]
07/31/2025 15:32:04 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:32:04 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:32:22 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpiPersistenceUnit
	...]
07/31/2025 15:32:25 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:32:25 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:32:26 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpmPersistenceUnit
	...]
07/31/2025 15:32:28 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:32:28 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:32:30 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 15:32:33 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:32:33 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:32:35 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: csmPersistenceUnit
	...]
07/31/2025 15:32:39 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:32:39 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:32:42 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: demoPersistenceUnit
	...]
07/31/2025 15:32:47 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:32:47 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:32:48 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: invPersistenceUnit
	...]
07/31/2025 15:32:51 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:32:51 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:32:57 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: mkmPersistenceUnit
	...]
07/31/2025 15:33:01 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:33:01 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:33:05 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: onrPersistenceUnit
	...]
07/31/2025 15:33:08 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:33:08 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:33:11 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: rptPersistenceUnit
	...]
07/31/2025 15:33:14 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:33:14 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:33:18 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: rsmPersistenceUnit
	...]
07/31/2025 15:33:20 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:33:20 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:33:32 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 15:33:35 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:33:35 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:33:35 WARN  [main]o.h.j.i.EntityManagerFactoryRegistry - HHH000436: Entity manager factory name (tamPersistenceUnit) is already registered.  If entity manager will be clustered or passivated, specify a unique value for property 'hibernate.ejb.entitymanager_factory_name'
07/31/2025 15:33:35 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tfmPersistenceUnit
	...]
07/31/2025 15:33:38 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:33:38 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:33:42 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: urmPersistenceUnit
	...]
07/31/2025 15:33:44 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 15:33:44 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 15:34:01 INFO  [main]o.h.h.i.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
07/31/2025 15:34:02 DEBUG [localhost-startStop-1]o.hibernate.SQL - select role0_.role_id as role_id1_2_, role0_.branch_name as branch_n2_2_, role0_.office_name as office_n3_2_, role0_.role as role4_2_, role0_.role_name as role_nam5_2_, role0_.status as status6_2_ from role role0_
07/31/2025 15:34:02 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 15:34:02 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [1]
07/31/2025 15:34:02 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 15:34:02 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [2]
07/31/2025 15:34:02 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 15:34:02 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [3]
07/31/2025 15:34:08 INFO  [main]o.h.h.i.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
07/31/2025 15:34:10 INFO  [main]o.h.h.i.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
07/31/2025 15:45:01 DEBUG [http-nio-9020-exec-1]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:45:01 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:45:01 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:45:02 DEBUG [http-nio-9020-exec-2]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:45:02 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:45:02 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:45:02 DEBUG [http-nio-9020-exec-9]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:45:02 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:45:02 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:45:27 DEBUG [http-nio-9020-exec-10]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:45:27 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:45:27 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:45:27 DEBUG [http-nio-9020-exec-4]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:45:27 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:45:27 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:45:27 DEBUG [http-nio-9020-exec-6]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:45:27 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:45:27 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:45:30 DEBUG [http-nio-9020-exec-2]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:45:30 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:45:30 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:45:30 DEBUG [http-nio-9020-exec-3]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:45:30 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:45:30 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:45:30 DEBUG [http-nio-9020-exec-1]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:45:30 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:45:30 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:45:44 DEBUG [http-nio-9020-exec-4]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:45:44 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:45:44 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:45:44 DEBUG [http-nio-9020-exec-6]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:45:44 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:45:44 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:45:44 DEBUG [http-nio-9020-exec-8]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:45:44 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:45:44 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:45:49 DEBUG [http-nio-9020-exec-1]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:45:49 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:45:49 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:45:49 DEBUG [http-nio-9020-exec-1]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:45:49 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:45:49 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 15:45:49 DEBUG [http-nio-9020-exec-5]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:45:49 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:45:49 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:45:49 DEBUG [http-nio-9020-exec-5]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:45:49 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:45:49 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 15:45:49 DEBUG [http-nio-9020-exec-9]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:45:49 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:45:49 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:45:49 DEBUG [http-nio-9020-exec-9]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:45:49 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:45:49 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 15:45:51 DEBUG [http-nio-9020-exec-6]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:45:51 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:45:51 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:45:51 DEBUG [http-nio-9020-exec-8]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:45:51 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:45:51 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:45:51 DEBUG [http-nio-9020-exec-7]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:45:51 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:45:51 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:49:04 DEBUG [http-nio-9020-exec-4]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:49:04 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:49:04 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:49:04 DEBUG [http-nio-9020-exec-9]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:49:04 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:49:04 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:49:04 DEBUG [http-nio-9020-exec-8]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:49:04 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:49:04 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:49:13 DEBUG [http-nio-9020-exec-10]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:49:13 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:49:13 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:49:13 DEBUG [http-nio-9020-exec-2]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:49:13 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:49:13 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:49:13 DEBUG [http-nio-9020-exec-9]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:49:13 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:49:13 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:49:15 DEBUG [http-nio-9020-exec-3]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:49:15 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:49:15 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:49:15 DEBUG [http-nio-9020-exec-2]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:49:15 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:49:15 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:49:15 DEBUG [http-nio-9020-exec-6]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:49:15 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:49:15 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:49:21 DEBUG [http-nio-9020-exec-10]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:49:21 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:49:21 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:49:21 DEBUG [http-nio-9020-exec-10]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:49:21 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:49:21 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 15:49:21 DEBUG [http-nio-9020-exec-1]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:49:21 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:49:21 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:49:21 DEBUG [http-nio-9020-exec-1]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:49:21 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:49:21 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 15:49:21 DEBUG [http-nio-9020-exec-2]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:49:21 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:49:21 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:49:21 DEBUG [http-nio-9020-exec-2]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:49:21 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:49:21 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 15:49:22 DEBUG [http-nio-9020-exec-4]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:49:22 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:49:22 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:49:22 DEBUG [http-nio-9020-exec-9]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:49:22 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:49:22 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:49:22 DEBUG [http-nio-9020-exec-10]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:49:22 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:49:22 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:56:58 DEBUG [http-nio-9020-exec-8]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:56:58 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:56:58 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:56:58 DEBUG [http-nio-9020-exec-4]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:56:58 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:56:58 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:56:58 DEBUG [http-nio-9020-exec-8]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:56:58 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:56:58 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:59:16 DEBUG [http-nio-9020-exec-8]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:59:16 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:59:16 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:59:16 DEBUG [http-nio-9020-exec-9]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:59:16 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:59:16 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 15:59:16 DEBUG [http-nio-9020-exec-6]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 15:59:16 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 15:59:16 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:01:23 DEBUG [http-nio-9020-exec-3]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:01:23 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:01:23 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:01:23 DEBUG [http-nio-9020-exec-3]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:01:23 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:01:23 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 16:01:23 DEBUG [http-nio-9020-exec-6]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:01:23 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:01:23 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:01:23 DEBUG [http-nio-9020-exec-6]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:01:23 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:01:23 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 16:01:23 DEBUG [http-nio-9020-exec-8]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:01:23 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:01:23 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:01:24 DEBUG [http-nio-9020-exec-8]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:01:24 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:01:24 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 16:01:25 DEBUG [http-nio-9020-exec-5]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:01:25 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:01:25 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:01:25 DEBUG [http-nio-9020-exec-5]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:01:25 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:01:25 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 16:01:25 DEBUG [http-nio-9020-exec-10]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:01:25 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:01:25 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:01:25 DEBUG [http-nio-9020-exec-10]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:01:25 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:01:25 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 16:01:25 DEBUG [http-nio-9020-exec-7]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:01:25 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:01:25 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:01:25 DEBUG [http-nio-9020-exec-7]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:01:25 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:01:25 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 16:01:26 DEBUG [http-nio-9020-exec-3]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:01:26 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:01:26 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:01:26 DEBUG [http-nio-9020-exec-3]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:01:26 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:01:26 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 16:01:26 DEBUG [http-nio-9020-exec-4]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:01:26 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:01:26 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:01:26 DEBUG [http-nio-9020-exec-4]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:01:26 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:01:26 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 16:01:26 DEBUG [http-nio-9020-exec-8]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:01:26 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:01:26 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:01:26 DEBUG [http-nio-9020-exec-8]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:01:26 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:01:26 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 16:02:38 DEBUG [http-nio-9020-exec-5]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:02:38 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:02:38 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:02:38 DEBUG [http-nio-9020-exec-2]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:02:38 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:02:38 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:02:38 DEBUG [http-nio-9020-exec-7]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:02:38 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:02:38 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:36:55 DEBUG [http-nio-9020-exec-7]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:36:55 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:36:55 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:36:55 DEBUG [http-nio-9020-exec-10]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:36:55 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:36:55 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:36:55 DEBUG [http-nio-9020-exec-5]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:36:55 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:36:55 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:38:39 DEBUG [http-nio-9020-exec-2]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:38:39 DEBUG [http-nio-9020-exec-8]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:38:39 DEBUG [http-nio-9020-exec-3]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:39 DEBUG [http-nio-9020-exec-5]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 16:38:39 DEBUG [http-nio-9020-exec-7]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 16:38:39 DEBUG [http-nio-9020-exec-9]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 16:38:39 DEBUG [http-nio-9020-exec-10]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 16:38:39 DEBUG [http-nio-9020-exec-6]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 16:38:39 DEBUG [http-nio-9020-exec-4]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:39 DEBUG [http-nio-9020-exec-2]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 16:38:39 DEBUG [http-nio-9020-exec-3]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:38:39 DEBUG [http-nio-9020-exec-3]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:39 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 16:38:40 DEBUG [http-nio-9020-exec-9]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:40 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:40 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:38:40 DEBUG [http-nio-9020-exec-9]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:40 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:40 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 16:38:40 DEBUG [http-nio-9020-exec-5]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:40 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:40 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:38:40 DEBUG [http-nio-9020-exec-5]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:40 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:40 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 16:38:40 DEBUG [http-nio-9020-exec-1]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:40 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:40 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:38:40 DEBUG [http-nio-9020-exec-1]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:40 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:40 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/error]
07/31/2025 16:38:41 DEBUG [http-nio-9020-exec-6]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:41 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:41 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:38:41 DEBUG [http-nio-9020-exec-2]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:41 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:41 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:38:41 DEBUG [http-nio-9020-exec-10]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:41 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:41 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:38:41 DEBUG [http-nio-9020-exec-9]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:41 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:41 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:38:41 DEBUG [http-nio-9020-exec-5]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:41 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:41 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:38:42 DEBUG [http-nio-9020-exec-4]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:42 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:42 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:38:43 DEBUG [http-nio-9020-exec-1]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:43 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:43 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:38:43 DEBUG [http-nio-9020-exec-7]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:43 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:43 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:38:43 DEBUG [http-nio-9020-exec-10]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:38:43 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:38:43 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:39:05 DEBUG [http-nio-9020-exec-8]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:39:05 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:39:05 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:39:05 DEBUG [http-nio-9020-exec-9]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:39:05 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:39:05 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:39:05 DEBUG [http-nio-9020-exec-1]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:39:05 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:39:05 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:39:06 DEBUG [http-nio-9020-exec-6]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:39:06 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:39:06 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:39:07 DEBUG [http-nio-9020-exec-8]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:39:07 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:39:07 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:39:07 DEBUG [http-nio-9020-exec-9]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:39:07 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:39:07 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:41:44 DEBUG [http-nio-9020-exec-4]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:41:44 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:41:44 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:41:44 DEBUG [http-nio-9020-exec-5]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:41:44 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:41:44 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:41:44 DEBUG [http-nio-9020-exec-2]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:41:44 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:41:44 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:41:45 DEBUG [http-nio-9020-exec-7]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:41:45 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:41:45 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:41:45 DEBUG [http-nio-9020-exec-3]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:41:45 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:41:45 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:41:45 DEBUG [http-nio-9020-exec-5]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:41:45 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:41:45 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:45:47 DEBUG [http-nio-9020-exec-10]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:45:47 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:45:47 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:45:47 DEBUG [http-nio-9020-exec-1]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:45:47 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:45:47 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:45:47 DEBUG [http-nio-9020-exec-9]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:45:47 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:45:47 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:45:54 DEBUG [http-nio-9020-exec-2]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:45:54 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:45:54 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:45:54 DEBUG [http-nio-9020-exec-7]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:45:54 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:45:54 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:45:54 DEBUG [http-nio-9020-exec-5]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:45:54 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:45:54 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:47:46 DEBUG [http-nio-9020-exec-3]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:47:46 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:47:46 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:47:46 DEBUG [http-nio-9020-exec-1]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:47:46 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:47:46 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:47:47 DEBUG [http-nio-9020-exec-10]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:47:47 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:47:47 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:51:40 DEBUG [http-nio-9020-exec-5]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:51:40 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:51:40 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:51:40 DEBUG [http-nio-9020-exec-2]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:51:40 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:51:40 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:51:40 DEBUG [http-nio-9020-exec-9]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:51:40 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:51:40 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:53:40 DEBUG [http-nio-9020-exec-8]o.hibernate.SQL - select user0_.user_id as user_id1_6_, user0_.active as active2_6_, user0_.bra_id as bra_id3_6_, user0_.bra_nm as bra_nm4_6_, user0_.email as email5_6_, user0_.mbl_no as mbl_no6_6_, user0_.name as name7_6_, user0_.office_id as office_i8_6_, user0_.office_nm as office_n9_6_, user0_.password as passwor10_6_ from user user0_ where user0_.name=?
07/31/2025 16:53:40 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [sadsad]
07/31/2025 16:53:40 DEBUG [http-nio-9020-exec-10]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:53:40 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:53:40 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:53:40 DEBUG [http-nio-9020-exec-9]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:53:40 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:53:40 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:53:40 DEBUG [http-nio-9020-exec-7]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:53:40 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:53:40 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:57:56 DEBUG [http-nio-9020-exec-1]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:57:56 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:57:56 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:57:56 DEBUG [http-nio-9020-exec-10]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:57:56 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:57:56 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:57:57 DEBUG [http-nio-9020-exec-2]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:57:57 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:57:57 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:58:04 DEBUG [http-nio-9020-exec-4]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:58:04 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:58:04 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:58:04 DEBUG [http-nio-9020-exec-10]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:58:04 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:58:04 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:58:04 DEBUG [http-nio-9020-exec-8]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:58:04 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:58:04 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:58:06 DEBUG [http-nio-9020-exec-3]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:58:06 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:58:06 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:58:06 DEBUG [http-nio-9020-exec-10]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:58:06 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:58:06 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:58:06 DEBUG [http-nio-9020-exec-7]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:58:06 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:58:06 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:58:08 DEBUG [http-nio-9020-exec-6]o.hibernate.SQL - select user0_.user_id as user_id1_6_, user0_.active as active2_6_, user0_.bra_id as bra_id3_6_, user0_.bra_nm as bra_nm4_6_, user0_.email as email5_6_, user0_.mbl_no as mbl_no6_6_, user0_.name as name7_6_, user0_.office_id as office_i8_6_, user0_.office_nm as office_n9_6_, user0_.password as passwor10_6_ from user user0_ where user0_.name=?
07/31/2025 16:58:08 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [dfsf]
07/31/2025 16:58:08 DEBUG [http-nio-9020-exec-8]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:58:08 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:58:08 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:58:08 DEBUG [http-nio-9020-exec-3]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:58:08 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:58:08 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:58:09 DEBUG [http-nio-9020-exec-5]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:58:09 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:58:09 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:59:30 DEBUG [http-nio-9020-exec-1]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:59:30 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:59:30 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:59:30 DEBUG [http-nio-9020-exec-9]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:59:30 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:59:30 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:59:30 DEBUG [http-nio-9020-exec-7]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:59:30 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:59:30 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:59:38 DEBUG [http-nio-9020-exec-10]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:59:38 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:59:38 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:59:38 DEBUG [http-nio-9020-exec-3]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:59:38 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:59:38 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:59:38 DEBUG [http-nio-9020-exec-8]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:59:38 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:59:38 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:59:55 DEBUG [http-nio-9020-exec-4]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:59:55 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:59:55 TRACE [http-nio-9020-exec-4]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:59:55 DEBUG [http-nio-9020-exec-5]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:59:55 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:59:55 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 16:59:55 DEBUG [http-nio-9020-exec-3]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 16:59:55 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 16:59:55 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 17:00:01 DEBUG [http-nio-9020-exec-5]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 17:00:01 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 17:00:01 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 17:00:01 DEBUG [http-nio-9020-exec-8]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 17:00:01 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 17:00:01 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 17:00:01 DEBUG [http-nio-9020-exec-2]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 17:00:01 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 17:00:01 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 17:00:05 DEBUG [http-nio-9020-exec-7]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 17:00:05 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 17:00:05 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 17:00:05 DEBUG [http-nio-9020-exec-9]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 17:00:05 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 17:00:05 TRACE [http-nio-9020-exec-9]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 17:00:05 DEBUG [http-nio-9020-exec-3]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 17:00:05 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 17:00:05 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 17:00:10 DEBUG [http-nio-9020-exec-6]o.hibernate.SQL - select user0_.user_id as user_id1_6_, user0_.active as active2_6_, user0_.bra_id as bra_id3_6_, user0_.bra_nm as bra_nm4_6_, user0_.email as email5_6_, user0_.mbl_no as mbl_no6_6_, user0_.name as name7_6_, user0_.office_id as office_i8_6_, user0_.office_nm as office_n9_6_, user0_.password as passwor10_6_ from user user0_ where user0_.name=?
07/31/2025 17:00:10 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [fsdfds]
07/31/2025 17:00:10 DEBUG [http-nio-9020-exec-8]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 17:00:10 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 17:00:10 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 17:00:10 DEBUG [http-nio-9020-exec-3]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 17:00:10 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 17:00:10 TRACE [http-nio-9020-exec-3]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 17:00:11 DEBUG [http-nio-9020-exec-7]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 17:00:11 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 17:00:11 TRACE [http-nio-9020-exec-7]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 17:04:09 DEBUG [http-nio-9020-exec-10]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 17:04:09 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 17:04:09 TRACE [http-nio-9020-exec-10]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 17:04:09 DEBUG [http-nio-9020-exec-6]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 17:04:09 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 17:04:09 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 17:04:09 DEBUG [http-nio-9020-exec-8]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 17:04:09 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 17:04:09 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 17:04:10 DEBUG [http-nio-9020-exec-6]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 17:04:10 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 17:04:10 TRACE [http-nio-9020-exec-6]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 17:04:10 DEBUG [http-nio-9020-exec-2]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 17:04:10 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 17:04:10 TRACE [http-nio-9020-exec-2]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 17:04:11 DEBUG [http-nio-9020-exec-1]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 17:04:11 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 17:04:11 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 17:05:59 DEBUG [http-nio-9020-exec-1]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 17:05:59 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 17:05:59 TRACE [http-nio-9020-exec-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 17:05:59 DEBUG [http-nio-9020-exec-8]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 17:05:59 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 17:05:59 TRACE [http-nio-9020-exec-8]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
07/31/2025 17:05:59 DEBUG [http-nio-9020-exec-5]o.hibernate.SQL - select * from tms_opr_mapper where opr_url = ? and method = ? limit 0,1 
07/31/2025 17:05:59 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [2] as [VARCHAR] - [GET]
07/31/2025 17:05:59 TRACE [http-nio-9020-exec-5]o.h.t.d.s.BasicBinder - binding parameter [1] as [VARCHAR] - [/login]
