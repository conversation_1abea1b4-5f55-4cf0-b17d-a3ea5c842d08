07/31/2025 11:28:12.532 INFO  [main]c.h.t.Application - The following profiles are active: dev
07/31/2025 11:28:12.549 INFO  [main]o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@40021799: startup date [Thu Jul 31 11:28:12 CST 2025]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@d5b810e
07/31/2025 11:28:13.650 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:28:13.688 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:28:13.694 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:28:13.703 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:28:13.740 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:28:13.745 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:28:13.750 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:28:13.757 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:28:13.763 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:28:13.769 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:28:13.780 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:28:13.788 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:28:13.797 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:28:13.805 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:28:13.828 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:28:13.830 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:28:13.835 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.DatatablesTfmRateRuleRepository.
07/31/2025 11:28:13.840 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:28:13.857 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:28:13.894 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'bilTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=bilConfig; factoryMethodName=demoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/bilConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=bilMyBatisConfig; factoryMethodName=cpoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/BilMyBatisConfig.class]]
07/31/2025 11:28:13.899 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'invTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=invConfig; factoryMethodName=demoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/InvConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=invMyBatisConfig; factoryMethodName=invTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/InvMyBatisConfig.class]]
07/31/2025 11:28:13.904 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICardBinDao' and 'com.hisun.tms.cpt.dao.cpi.ICardBinDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:28:13.905 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICardProtDao' and 'com.hisun.tms.cpt.dao.cpi.ICardProtDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:28:13.905 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpiOrgnBusiDao' and 'com.hisun.tms.cpt.dao.cpi.ICpiOrgnBusiDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:28:13.905 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpiOrgnInfoDao' and 'com.hisun.tms.cpt.dao.cpi.ICpiOrgnInfoDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:28:13.905 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpiOrgnRouteDao' and 'com.hisun.tms.cpt.dao.cpi.ICpiOrgnRouteDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:28:13.905 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'IFundOrderDao' and 'com.hisun.tms.cpt.dao.cpi.IFundOrderDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:28:13.905 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'IRefundOrderDao' and 'com.hisun.tms.cpt.dao.cpi.IRefundOrderDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:28:13.905 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ISettleDetailDao' and 'com.hisun.tms.cpt.dao.cpi.ISettleDetailDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:28:13.905 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpoOrgnBusiDao' and 'com.hisun.tms.cpt.dao.cpo.ICpoOrgnBusiDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:28:13.905 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpoOrgnInfoDao' and 'com.hisun.tms.cpt.dao.cpo.ICpoOrgnInfoDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:28:13.905 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpoOrgnRouteDao' and 'com.hisun.tms.cpt.dao.cpo.ICpoOrgnRouteDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:28:13.905 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'IWithdrawOrderDao' and 'com.hisun.tms.cpt.dao.cpo.IWithdrawOrderDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:28:13.906 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'tamTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tamConfig; factoryMethodName=tamTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/TamConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tamMyBatisConfig; factoryMethodName=tamTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/TamMyBatisConfig.class]]
07/31/2025 11:28:13.907 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'tfmTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tfmConfig; factoryMethodName=demoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/TfmConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tfmMyBatisConfig; factoryMethodName=tfmTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/TfmMyBatisConfig.class]]
07/31/2025 11:28:14.357 INFO  [main]o.s.i.c.IntegrationRegistrar - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
07/31/2025 11:28:14.431 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:28:14.646 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.demo.repository.DatatablesExampleRepository.
07/31/2025 11:28:14.647 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.MercItfRepository.
07/31/2025 11:28:14.647 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.OfficeInfoRepository.
07/31/2025 11:28:14.647 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.DatatablesRoleRepository.
07/31/2025 11:28:14.647 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmAcBalRepository.
07/31/2025 11:28:14.648 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesRuleListRepository.
07/31/2025 11:28:14.648 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.WhiteListRepository.
07/31/2025 11:28:14.648 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.chk.repository.ChkErrorRepository.
07/31/2025 11:28:14.648 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DataTablesMercExtInfoRepository.
07/31/2025 11:28:14.648 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.TfmFeeOrderRepository.
07/31/2025 11:28:14.648 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csh.repository.CshOrderRepository.
07/31/2025 11:28:14.648 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.DatatablesProInfoRepository.
07/31/2025 11:28:14.648 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesPhoneSegementInfoRepository.
07/31/2025 11:28:14.648 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.demo.repository.ExampleRepository.
07/31/2025 11:28:14.648 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rpt.repository.RptRsRepository.
07/31/2025 11:28:14.648 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmAdjustInfRepository.
07/31/2025 11:28:14.648 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.ConstantParamsRepository.
07/31/2025 11:28:14.649 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.onr.repository.DatatablesOnrRfdOrderRepository.
07/31/2025 11:28:14.649 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.MercRegisterSeqRepository.
07/31/2025 11:28:14.649 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.UserRepository.
07/31/2025 11:28:14.649 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.RoleRepository.
07/31/2025 11:28:14.649 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesCampaignInfoRepository.
07/31/2025 11:28:14.649 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesWhiteListRepository.
07/31/2025 11:28:14.650 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.CheckRuleRepository.
07/31/2025 11:28:14.650 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.onr.repository.OnrRfdOrderRepository.
07/31/2025 11:28:14.650 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UrmCprExtInfRespository.
07/31/2025 11:28:14.650 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UserBaseInfoRepository.
07/31/2025 11:28:14.650 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DatatablesAcmItmPropertyRepository.
07/31/2025 11:28:14.650 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.bil.repository.DataTablesMerchantOrderInfoRepository.
07/31/2025 11:28:14.650 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DatatablesUserHistoryRepository.
07/31/2025 11:28:14.650 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.DataTableMercRegisterRepository.
07/31/2025 11:28:14.651 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.mkm.repository.BatchFileRecRepository.
07/31/2025 11:28:14.651 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.RiskRuleRepository.
07/31/2025 11:28:14.651 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.SafeInfoRespository.
07/31/2025 11:28:14.651 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesSmsTemplateInfoRepository.
07/31/2025 11:28:14.651 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csm.repository.SettleBaseDORespository.
07/31/2025 11:28:14.651 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.DatatablesTfmFeeOrderRepository.
07/31/2025 11:28:14.651 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DatatablesMercSafelInfoRepository.
07/31/2025 11:28:14.651 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.onr.repository.DatatablesOnrOrderRepository.
07/31/2025 11:28:14.652 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.chk.repository.DatatablesChkErrorRepository.
07/31/2025 11:28:14.652 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UserInfoRepository.
07/31/2025 11:28:14.652 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesBannerInfoRepository.
07/31/2025 11:28:14.652 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesConstantParamsRepository.
07/31/2025 11:28:14.652 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.TmsUserOprLogRepository.
07/31/2025 11:28:14.652 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cpm.repository.DatatablesCpmOrderRepository.
07/31/2025 11:28:14.652 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.NoticeInfoRepository.
07/31/2025 11:28:14.652 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.onr.repository.OnrOrderRepository.
07/31/2025 11:28:14.653 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.mkm.repository.MkmAtvUserRespository.
07/31/2025 11:28:14.653 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesNoticeInfoRepository.
07/31/2025 11:28:14.653 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.SmsTemplateInfoRepository.
07/31/2025 11:28:14.653 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesCheckRuleRepository.
07/31/2025 11:28:14.653 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.TmsOprMapperRespository.
07/31/2025 11:28:14.653 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.PhoneSegementInfoRepository.
07/31/2025 11:28:14.653 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.CampaignInfoRepository.
07/31/2025 11:28:14.653 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesBlackListRepository.
07/31/2025 11:28:14.654 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DataTablesAcmAcDetailRepository.
07/31/2025 11:28:14.654 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.MercExtInfoRepository.
07/31/2025 11:28:14.654 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.DatatablesTfmRateRuleRepository.
07/31/2025 11:28:14.654 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.BannerInfoRepository.
07/31/2025 11:28:14.654 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.DatatablesOrderRepository.
07/31/2025 11:28:14.654 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmItmInfRepository.
07/31/2025 11:28:14.654 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.UserRoleRespository.
07/31/2025 11:28:14.654 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.bil.repository.DataTablesOrderInfoRepository.
07/31/2025 11:28:14.655 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesAreaInfoRepository.
07/31/2025 11:28:14.655 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.MercInfoRepository.
07/31/2025 11:28:14.655 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.chk.repository.ChkControlRepository.
07/31/2025 11:28:14.655 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.MercSafelInfoRepository.
07/31/2025 11:28:14.655 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmAdjustDetailRepository.
07/31/2025 11:28:14.656 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cpt.repository.CpiSubMercCastRepository.
07/31/2025 11:28:14.656 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.MessageTemplateInfoRepository.
07/31/2025 11:28:14.656 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DatatablesMercInfoRepository.
07/31/2025 11:28:14.656 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DatatablesAcmItmInfRepository.
07/31/2025 11:28:14.656 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesRiskRuleRepository.
07/31/2025 11:28:14.656 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesHighRiskRepository.
07/31/2025 11:28:14.656 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmAcInfRepository.
07/31/2025 11:28:14.656 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.DateRateRepository.
07/31/2025 11:28:14.656 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.EmailTemplateInfoRepository.
07/31/2025 11:28:14.657 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csh.repository.PaytypeRepository.
07/31/2025 11:28:14.657 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesOfficeInfoRepository.
07/31/2025 11:28:14.657 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UrmKybSeqRepository.
07/31/2025 11:28:14.657 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csh.repository.DatatablesPayTypeRepository.
07/31/2025 11:28:14.657 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesRuleParamRepository.
07/31/2025 11:28:14.657 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.RsmUserStatusRepository.
07/31/2025 11:28:14.657 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UrmKybCertInfRepository.
07/31/2025 11:28:14.657 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.SafeLoginRespository.
07/31/2025 11:28:14.658 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmItmPropertyRepository.
07/31/2025 11:28:14.658 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.BlackListRepository.
07/31/2025 11:28:14.658 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DatatablesMercItfInfoRepository.
07/31/2025 11:28:14.658 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cpm.repository.CpmOrderRepository.
07/31/2025 11:28:14.658 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesEmailTemplateInfoRepository.
07/31/2025 11:28:14.658 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesBranchInfoRepository.
07/31/2025 11:28:14.658 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.AreaInfoRepository.
07/31/2025 11:28:14.658 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.ProInfoRepository.
07/31/2025 11:28:14.659 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.mkm.repository.SeaccyDetailRepository.
07/31/2025 11:28:14.659 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.HighRiskRepository.
07/31/2025 11:28:14.659 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DataTablesAdjustInfRepository.
07/31/2025 11:28:14.659 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rpt.repository.DataTablesRptRsRepository.
07/31/2025 11:28:14.659 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.RuleParamRepository.
07/31/2025 11:28:14.659 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.OrderRepository.
07/31/2025 11:28:14.659 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.BranchInfoRepository.
07/31/2025 11:28:14.659 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.CprResInfRespository.
07/31/2025 11:28:14.659 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.InvUserInfoRepository.
07/31/2025 11:28:14.660 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DataTablesAdjustDetailRepository.
07/31/2025 11:28:14.660 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.DatatablesUserRepository.
07/31/2025 11:28:14.660 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rpt.repository.DataTablesRptRepository.
07/31/2025 11:28:14.660 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.MerchantRateRuleRespository.
07/31/2025 11:28:14.660 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.chk.repository.DatatablesChkControlRepository.
07/31/2025 11:28:14.660 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csm.repository.SettleCardRespository.
07/31/2025 11:28:14.660 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.ResourceRepository.
07/31/2025 11:28:14.660 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesMessageTemplateInfoRepository.
07/31/2025 11:28:14.660 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rpt.repository.RptRepository.
07/31/2025 11:28:14.854 INFO  [main]o.s.c.c.s.GenericScope - BeanFactory id=d6db338d-1ef3-3f6f-ab3e-2040c76f77b0
07/31/2025 11:28:14.878 INFO  [main]o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
07/31/2025 11:28:14.885 INFO  [main]o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created.
07/31/2025 11:28:14.896 INFO  [main]o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
07/31/2025 11:28:14.963 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.tam.client.ExchangeOrderClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.963 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.tam.client.TransferOrderClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.964 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.BankClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.965 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.CardClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.965 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.CheckClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.965 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.CregisClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.966 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.EbankpayClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.966 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.FastpayClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.967 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.PosClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.967 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.RefundClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.967 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.RemittanceClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.968 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.RouteClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.969 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.csh.client.CshAuditTransferOrderClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.969 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.csh.client.CshOrderClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.970 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.csh.client.CshRefundClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.972 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskCheckClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.972 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskCheckUserStatusClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.972 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskListMngClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.973 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskParamMngClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.973 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskRuleMngClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.974 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.CmmServerClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.975 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.ConstantParamClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.975 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.GrantClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.975 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.QRCodeServerClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.976 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.SmsServerClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.976 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpo.client.CopAgcyBizClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.977 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpo.client.RouteClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.977 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpo.client.WithdrawClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.978 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.urm.client.UserAuthenticationClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.979 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.urm.client.UserBasicInfClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.979 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.urm.client.UserBasicInfClient1' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.979 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.urm.client.UserPasswordClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.980 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.acm.client.AccountingTreatmentClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.981 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.acm.client.AccountManagementClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.982 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.mkm.client.MarketActivityClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.982 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.mkm.client.MarketingToolsMngClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.983 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OrderCancleClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.983 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OrderRefundClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.984 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.ConfigureClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.984 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OrderQueryClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.984 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OnrCheckedHandleClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.985 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OrderRegistratClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.985 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.NotifySendClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:14.986 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.chk.client.ChkClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:15.021 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$88410701] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:15.119 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$c1d548d3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:15.258 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration' of type [org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration$$EnhancerBySpringCGLIB$$5a83110d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:15.267 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'objectPostProcessor' of type [org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:15.270 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@3c5e4aac' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:15.282 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'webMvcConfig' of type [com.hisun.tms.common.config.WebMvcConfig$$EnhancerBySpringCGLIB$$e741e1ad] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:15.303 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#2e1ad7de' of type [org.springframework.beans.factory.config.PropertiesFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:15.303 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#2e1ad7de' of type [java.util.Properties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:15.308 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#3b8b5b40' of type [org.springframework.data.repository.core.support.PropertiesBasedNamedQueries] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:15.311 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#582c1f8d' of type [org.springframework.data.repository.query.ExtensionAwareEvaluationContextProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:15.343 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSourceConfig' of type [com.hisun.tms.common.config.DataSourceConfig$$EnhancerBySpringCGLIB$$7b6154c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:15.523 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tmsDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:15.545 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.JpaProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:15.546 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tmsConfig' of type [com.hisun.tms.common.config.TmsConfig$$EnhancerBySpringCGLIB$$1c02ce27] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:15.567 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration' of type [org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$$EnhancerBySpringCGLIB$$564943dd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:15.576 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties' of type [org.springframework.boot.autoconfigure.transaction.TransactionProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:15.583 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'platformTransactionManagerCustomizers' of type [org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:15.589 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration$$EnhancerBySpringCGLIB$$2b5bacf3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:17.850 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jpaVendorAdapter' of type [org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:17.855 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'entityManagerFactoryBuilder' of type [org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:17.873 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'tmsPersistenceUnit'
07/31/2025 11:28:17.881 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tmsPersistenceUnit
	...]
07/31/2025 11:28:17.923 INFO  [main]o.h.Version - HHH000412: Hibernate Core {5.0.12.Final}
07/31/2025 11:28:17.925 INFO  [main]o.h.c.Environment - HHH000206: hibernate.properties not found
07/31/2025 11:28:17.925 INFO  [main]o.h.c.Environment - HHH000021: Bytecode provider name : javassist
07/31/2025 11:28:17.956 INFO  [main]o.h.a.c.Version - HCANN000001: Hibernate Commons Annotations {5.0.1.Final}
07/31/2025 11:28:18.120 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:28:18.275 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 11:28:18.276 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 11:28:18.455 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:28:24.154 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'tmsPersistenceUnit'
07/31/2025 11:28:24.156 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tmsEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:24.158 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tmsEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:24.169 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#61becbcf' of type [com.sun.proxy.$Proxy163] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:24.285 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'acmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:24.291 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'acmConfig' of type [com.hisun.tms.common.config.AcmConfig$$EnhancerBySpringCGLIB$$70ddf5f8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:24.300 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'acmPersistenceUnit'
07/31/2025 11:28:24.300 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: acmPersistenceUnit
	...]
07/31/2025 11:28:26.616 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:28:26.634 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 11:28:26.634 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 11:28:26.653 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#7f6137fb' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:26.654 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#7f6137fb' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:26.678 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:28:38.335 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'acmPersistenceUnit'
07/31/2025 11:28:38.335 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'acmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:38.336 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'acmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:38.441 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'bilDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:38.446 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'bilConfig' of type [com.hisun.tms.common.config.bilConfig$$EnhancerBySpringCGLIB$$987cc852] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:38.455 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'bilPersistenceUnit'
07/31/2025 11:28:38.455 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: bilPersistenceUnit
	...]
07/31/2025 11:28:41.347 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:28:41.361 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:28:43.959 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'bilPersistenceUnit'
07/31/2025 11:28:43.959 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'bilEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:43.960 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'bilEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:44.068 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'chkDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:44.073 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'chkConfig' of type [com.hisun.tms.common.config.ChkConfig$$EnhancerBySpringCGLIB$$38dfaa33] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:44.081 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'chkPersistenceUnit'
07/31/2025 11:28:44.082 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: chkPersistenceUnit
	...]
07/31/2025 11:28:46.311 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:28:46.341 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:28:51.906 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'chkPersistenceUnit'
07/31/2025 11:28:51.906 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'chkEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:51.908 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'chkEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:52.012 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cmmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:52.017 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cmmConfig' of type [com.hisun.tms.common.config.CmmConfig$$EnhancerBySpringCGLIB$$764593b0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:52.027 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'cmmPersistenceUnit'
07/31/2025 11:28:52.028 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cmmPersistenceUnit
	...]
07/31/2025 11:28:54.271 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:28:54.295 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#7f6137fb' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:54.295 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#7f6137fb' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:28:54.314 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:29:09.643 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'cmmPersistenceUnit'
07/31/2025 11:29:09.644 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cmmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:09.644 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cmmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:09.748 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpiDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:09.753 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpiConfig' of type [com.hisun.tms.common.config.CpiConfig$$EnhancerBySpringCGLIB$$6001cb69] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:09.763 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'cpiPersistenceUnit'
07/31/2025 11:29:09.763 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpiPersistenceUnit
	...]
07/31/2025 11:29:11.915 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:29:11.924 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:29:12.737 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'cpiPersistenceUnit'
07/31/2025 11:29:12.738 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpiEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:12.738 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpiEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:12.842 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:12.847 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpmConfig' of type [com.hisun.tms.common.config.CpmConfig$$EnhancerBySpringCGLIB$$a129e4ed] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:12.855 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'cpmPersistenceUnit'
07/31/2025 11:29:12.855 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpmPersistenceUnit
	...]
07/31/2025 11:29:15.026 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:29:15.033 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#7f6137fb' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:15.033 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#7f6137fb' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:15.036 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:29:16.748 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'cpmPersistenceUnit'
07/31/2025 11:29:16.748 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:16.749 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:16.851 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cshDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:16.857 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cshConfig' of type [com.hisun.tms.common.config.CshConfig$$EnhancerBySpringCGLIB$$3a9c1645] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:16.864 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'tamPersistenceUnit'
07/31/2025 11:29:16.864 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 11:29:19.194 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:29:19.201 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#7f6137fb' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:19.201 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#7f6137fb' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:19.205 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:29:20.681 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'tamPersistenceUnit'
07/31/2025 11:29:20.681 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cshEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:20.682 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cshEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:20.783 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'csmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:20.788 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'csmConfig' of type [com.hisun.tms.common.config.CsmConfig$$EnhancerBySpringCGLIB$$cc0e362a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:20.796 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'csmPersistenceUnit'
07/31/2025 11:29:20.796 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: csmPersistenceUnit
	...]
07/31/2025 11:29:23.641 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:29:23.655 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:29:26.656 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'csmPersistenceUnit'
07/31/2025 11:29:26.657 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'csmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:26.657 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'csmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:26.760 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'demoDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:26.766 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'demoConfig' of type [com.hisun.tms.common.config.DemoConfig$$EnhancerBySpringCGLIB$$96295fca] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:26.774 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'demoPersistenceUnit'
07/31/2025 11:29:26.774 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: demoPersistenceUnit
	...]
07/31/2025 11:29:28.920 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:29:28.927 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#7f6137fb' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:28.927 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#7f6137fb' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:28.929 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:29:29.744 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'demoPersistenceUnit'
07/31/2025 11:29:29.744 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'demoEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:29.745 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'demoEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:29.846 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'invDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:29.851 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'invConfig' of type [com.hisun.tms.common.config.InvConfig$$EnhancerBySpringCGLIB$$65223f9e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:29.860 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'invPersistenceUnit'
07/31/2025 11:29:29.860 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: invPersistenceUnit
	...]
07/31/2025 11:29:32.136 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:29:32.146 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#7f6137fb' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:32.146 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#7f6137fb' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:32.153 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:29:37.259 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'invPersistenceUnit'
07/31/2025 11:29:37.259 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'invEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:37.260 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'invEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:37.363 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mkmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:37.368 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mkmConfig' of type [com.hisun.tms.common.config.mkmConfig$$EnhancerBySpringCGLIB$$bb69c19c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:37.378 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'mkmPersistenceUnit'
07/31/2025 11:29:37.378 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: mkmPersistenceUnit
	...]
07/31/2025 11:29:39.637 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:29:39.651 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:29:42.889 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'mkmPersistenceUnit'
07/31/2025 11:29:42.889 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mkmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:42.890 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mkmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:42.997 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'onrDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:43.002 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'onrConfig' of type [com.hisun.tms.common.config.OnrConfig$$EnhancerBySpringCGLIB$$8745d2e0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:43.010 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'onrPersistenceUnit'
07/31/2025 11:29:43.010 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: onrPersistenceUnit
	...]
07/31/2025 11:29:45.251 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:29:45.258 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#7f6137fb' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:45.259 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#7f6137fb' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:45.263 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:29:47.772 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'onrPersistenceUnit'
07/31/2025 11:29:47.772 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'onrEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:47.772 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'onrEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:47.874 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rptDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:47.879 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rptConfig' of type [com.hisun.tms.common.config.rptConfig$$EnhancerBySpringCGLIB$$87ab85a3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:47.886 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'rptPersistenceUnit'
07/31/2025 11:29:47.886 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: rptPersistenceUnit
	...]
07/31/2025 11:29:50.238 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:29:50.251 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:29:52.992 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'rptPersistenceUnit'
07/31/2025 11:29:52.992 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rptEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:52.993 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rptEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:53.111 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rsmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:53.116 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rsmConfig' of type [com.hisun.tms.common.config.rsmConfig$$EnhancerBySpringCGLIB$$8089aa39] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:29:53.126 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'rsmPersistenceUnit'
07/31/2025 11:29:53.126 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: rsmPersistenceUnit
	...]
07/31/2025 11:29:55.319 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:29:55.343 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:30:03.612 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'rsmPersistenceUnit'
07/31/2025 11:30:03.613 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rsmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:03.614 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rsmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:03.713 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tamDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:03.718 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tamConfig' of type [com.hisun.tms.common.config.TamConfig$$EnhancerBySpringCGLIB$$8eb562ed] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:03.726 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'tamPersistenceUnit'
07/31/2025 11:30:03.726 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 11:30:03.816 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 11:30:04.040 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 11:30:04.248 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 11:30:04.470 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 11:30:04.721 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 11:30:04.917 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 11:30:05.118 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 11:30:05.332 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 11:30:05.547 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 11:30:05.759 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 11:30:05.954 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:30:05.959 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:30:05.990 WARN  [main]o.h.j.i.EntityManagerFactoryRegistry - HHH000436: Entity manager factory name (tamPersistenceUnit) is already registered.  If entity manager will be clustered or passivated, specify a unique value for property 'hibernate.ejb.entitymanager_factory_name'
07/31/2025 11:30:05.990 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'tamPersistenceUnit'
07/31/2025 11:30:05.990 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tamEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:05.991 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tamEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:06.095 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tfmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:06.100 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tfmConfig' of type [com.hisun.tms.common.config.TfmConfig$$EnhancerBySpringCGLIB$$2b873fa8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:06.108 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'tfmPersistenceUnit'
07/31/2025 11:30:06.108 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tfmPersistenceUnit
	...]
07/31/2025 11:30:08.860 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:30:08.869 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#7f6137fb' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:08.869 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#7f6137fb' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:08.874 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:30:12.973 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'tfmPersistenceUnit'
07/31/2025 11:30:12.973 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tfmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:12.974 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tfmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:13.075 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'urmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:13.080 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'urmConfig' of type [com.hisun.tms.common.config.urmConfig$$EnhancerBySpringCGLIB$$f938badd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:13.092 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'urmPersistenceUnit'
07/31/2025 11:30:13.092 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: urmPersistenceUnit
	...]
07/31/2025 11:30:15.909 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:30:15.927 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#7f6137fb' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:15.928 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#7f6137fb' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:15.946 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:30:33.959 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'urmPersistenceUnit'
07/31/2025 11:30:33.959 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'urmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:33.960 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'urmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:34.221 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jpaMappingContext' of type [org.springframework.data.jpa.repository.config.JpaMetamodelMappingContextFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:34.222 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jpaMappingContext' of type [org.springframework.data.jpa.mapping.JpaMetamodelMappingContext] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:34.328 INFO  [main]o.h.h.i.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
07/31/2025 11:30:34.372 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRepository' of type [org.springframework.data.jpa.datatables.repository.DataTablesRepositoryFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:34.374 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRepository' of type [com.sun.proxy.$Proxy176] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:34.387 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'getPermissionEvaluator' of type [com.hisun.tms.common.security.TmsPermissionEvaluator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:34.387 INFO  [main]c.h.t.c.c.WebMvcConfig - DefaultMethodSecurityExpressionHandler has benn set custom TMSPermissionEvaluator.
07/31/2025 11:30:34.388 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'getExpressionHandler' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:34.388 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration' of type [org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration$$EnhancerBySpringCGLIB$$7f57b3bf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:34.395 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:34.401 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.netflix.metrics.MetricsInterceptorConfiguration$MetricsRestTemplateConfiguration' of type [org.springframework.cloud.netflix.metrics.MetricsInterceptorConfiguration$MetricsRestTemplateConfiguration$$EnhancerBySpringCGLIB$$f401ef14] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:34.425 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationGlobalProperties' of type [org.springframework.beans.factory.config.PropertiesFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:34.426 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationGlobalProperties' of type [java.util.Properties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:34.444 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$ddef4bd0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:30:34.702 INFO  [main]o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 9020 (http)
07/31/2025 11:30:34.708 INFO  [main]o.a.c.c.StandardService - Starting service [Tomcat]
07/31/2025 11:30:34.709 INFO  [main]o.a.c.c.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.15
07/31/2025 11:30:34.755 INFO  [localhost-startStop-1]o.a.c.c.C.[.[.[/] - Initializing Spring embedded WebApplicationContext
07/31/2025 11:30:34.756 INFO  [localhost-startStop-1]o.s.w.c.ContextLoader - Root WebApplicationContext: initialization completed in 142207 ms
07/31/2025 11:30:35.071 DEBUG [localhost-startStop-1]o.hibernate.SQL - select role0_.role_id as role_id1_2_, role0_.branch_name as branch_n2_2_, role0_.office_name as office_n3_2_, role0_.role as role4_2_, role0_.role_name as role_nam5_2_, role0_.status as status6_2_ from role role0_
07/31/2025 11:30:35.259 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 11:30:35.261 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [1]
07/31/2025 11:30:35.390 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 11:30:35.390 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [2]
07/31/2025 11:30:35.505 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 11:30:35.505 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [3]
07/31/2025 11:30:35.598 DEBUG [localhost-startStop-1]c.h.t.c.s.TmsFilterSecurityInterceptor - Validated configuration attributes
07/31/2025 11:30:35.812 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'metricsFilter' to: [/*]
07/31/2025 11:30:35.812 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
07/31/2025 11:30:35.812 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
07/31/2025 11:30:35.812 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
07/31/2025 11:30:35.812 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
07/31/2025 11:30:35.813 INFO  [localhost-startStop-1]o.s.b.w.s.DelegatingFilterProxyRegistrationBean - Mapping filter: 'springSecurityFilterChain' to: [/*]
07/31/2025 11:30:35.813 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'webRequestLoggingFilter' to: [/*]
07/31/2025 11:30:35.813 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'tmsFilterSecurityInterceptor' to: [/*]
07/31/2025 11:30:35.813 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'applicationContextIdFilter' to: [/*]
07/31/2025 11:30:35.813 INFO  [localhost-startStop-1]o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
07/31/2025 11:30:35.963 WARN  [main]o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'accountAddressController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'accountAddressService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'IAccountAddressDao' defined in file [E:\Cele4TeR\Project\mcht-be\tms\build\classes\java\main\com\hisun\tms\acm\dao\IAccountAddressDao.class]: Cannot resolve reference to bean 'acmSqlSessionTemplate' while setting bean property 'sqlSessionTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'acmSqlSessionTemplate' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Unsatisfied dependency expressed through method 'cpoSqlSessionTemplate' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acmSqlSessionFactory' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'cpoSqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
07/31/2025 11:30:35.964 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'urmPersistenceUnit'
07/31/2025 11:30:35.965 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'tfmPersistenceUnit'
07/31/2025 11:30:35.966 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'tamPersistenceUnit'
07/31/2025 11:30:35.966 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'rsmPersistenceUnit'
07/31/2025 11:30:35.967 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'rptPersistenceUnit'
07/31/2025 11:30:35.968 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'onrPersistenceUnit'
07/31/2025 11:30:35.968 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'mkmPersistenceUnit'
07/31/2025 11:30:35.969 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'invPersistenceUnit'
07/31/2025 11:30:35.969 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'demoPersistenceUnit'
07/31/2025 11:30:35.970 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'csmPersistenceUnit'
07/31/2025 11:30:36.032 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'tamPersistenceUnit'
07/31/2025 11:30:36.033 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'cpmPersistenceUnit'
07/31/2025 11:30:36.034 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'cpiPersistenceUnit'
07/31/2025 11:30:36.034 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'cmmPersistenceUnit'
07/31/2025 11:30:36.035 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'chkPersistenceUnit'
07/31/2025 11:30:36.036 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'bilPersistenceUnit'
07/31/2025 11:30:36.037 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'acmPersistenceUnit'
07/31/2025 11:30:36.038 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'tmsPersistenceUnit'
07/31/2025 11:30:36.039 INFO  [main]o.a.c.c.StandardService - Stopping service [Tomcat]
07/31/2025 11:30:36.051 INFO  [main]o.s.b.a.l.AutoConfigurationReportLoggingInitializer - 

Error starting ApplicationContext. To display the auto-configuration report re-run your application with 'debug' enabled.
07/31/2025 11:30:36.059 ERROR [main]o.s.b.SpringApplication - Application startup failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'accountAddressController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'accountAddressService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'IAccountAddressDao' defined in file [E:\Cele4TeR\Project\mcht-be\tms\build\classes\java\main\com\hisun\tms\acm\dao\IAccountAddressDao.class]: Cannot resolve reference to bean 'acmSqlSessionTemplate' while setting bean property 'sqlSessionTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'acmSqlSessionTemplate' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Unsatisfied dependency expressed through method 'cpoSqlSessionTemplate' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acmSqlSessionFactory' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'cpoSqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessPropertyValues(CommonAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1264)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:553)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:761)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:867)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:543)
	at org.springframework.boot.context.embedded.EmbeddedWebApplicationContext.refresh(EmbeddedWebApplicationContext.java:122)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:693)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:360)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1118)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1107)
	at com.hisun.tms.Application.main(Application.java:35)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'accountAddressService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'IAccountAddressDao' defined in file [E:\Cele4TeR\Project\mcht-be\tms\build\classes\java\main\com\hisun\tms\acm\dao\IAccountAddressDao.class]: Cannot resolve reference to bean 'acmSqlSessionTemplate' while setting bean property 'sqlSessionTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'acmSqlSessionTemplate' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Unsatisfied dependency expressed through method 'cpoSqlSessionTemplate' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acmSqlSessionFactory' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'cpoSqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessPropertyValues(CommonAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1264)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:553)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:522)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:496)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:627)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:169)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:88)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessPropertyValues(CommonAnnotationBeanPostProcessor.java:318)
	... 17 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'IAccountAddressDao' defined in file [E:\Cele4TeR\Project\mcht-be\tms\build\classes\java\main\com\hisun\tms\acm\dao\IAccountAddressDao.class]: Cannot resolve reference to bean 'acmSqlSessionTemplate' while setting bean property 'sqlSessionTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'acmSqlSessionTemplate' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Unsatisfied dependency expressed through method 'cpoSqlSessionTemplate' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acmSqlSessionFactory' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'cpoSqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:359)
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveValueIfNecessary(BeanDefinitionValueResolver.java:108)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyPropertyValues(AbstractAutowireCapableBeanFactory.java:1531)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1276)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:553)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1138)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1066)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:518)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:496)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:627)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:169)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:88)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessPropertyValues(CommonAnnotationBeanPostProcessor.java:318)
	... 30 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'acmSqlSessionTemplate' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Unsatisfied dependency expressed through method 'cpoSqlSessionTemplate' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acmSqlSessionFactory' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'cpoSqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:749)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:467)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1173)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1067)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:513)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:351)
	... 48 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acmSqlSessionFactory' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'cpoSqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1173)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1067)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:513)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1138)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1066)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:835)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:741)
	... 58 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'cpoSqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:189)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:588)
	... 71 common frames omitted
Caused by: org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.mybatis.spring.SqlSessionFactoryBean.buildSqlSessionFactory(SqlSessionFactoryBean.java:500)
	at org.mybatis.spring.SqlSessionFactoryBean.afterPropertiesSet(SqlSessionFactoryBean.java:380)
	at org.mybatis.spring.SqlSessionFactoryBean.getObject(SqlSessionFactoryBean.java:547)
	at com.hisun.tms.common.mybatiesConfig.AcmMyBatisConfig.cpoSqlSessionFactory(AcmMyBatisConfig.java:39)
	at com.hisun.tms.common.mybatiesConfig.AcmMyBatisConfig$$EnhancerBySpringCGLIB$$bfacc2c1.CGLIB$cpoSqlSessionFactory$0(<generated>)
	at com.hisun.tms.common.mybatiesConfig.AcmMyBatisConfig$$EnhancerBySpringCGLIB$$bfacc2c1$$FastClassBySpringCGLIB$$34e5bbf3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:228)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:358)
	at com.hisun.tms.common.mybatiesConfig.AcmMyBatisConfig$$EnhancerBySpringCGLIB$$bfacc2c1.cpoSqlSessionFactory(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:162)
	... 72 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.apache.ibatis.builder.xml.XMLConfigBuilder.parseConfiguration(XMLConfigBuilder.java:120)
	at org.apache.ibatis.builder.xml.XMLConfigBuilder.parse(XMLConfigBuilder.java:98)
	at org.mybatis.spring.SqlSessionFactoryBean.buildSqlSessionFactory(SqlSessionFactoryBean.java:494)
	... 85 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.apache.ibatis.builder.xml.XMLConfigBuilder.typeAliasesElement(XMLConfigBuilder.java:170)
	at org.apache.ibatis.builder.xml.XMLConfigBuilder.parseConfiguration(XMLConfigBuilder.java:108)
	... 87 common frames omitted
Caused by: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:200)
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:89)
	at org.apache.ibatis.io.Resources.classForName(Resources.java:261)
	at org.apache.ibatis.builder.xml.XMLConfigBuilder.typeAliasesElement(XMLConfigBuilder.java:163)
	... 88 common frames omitted
07/31/2025 11:32:42.132 INFO  [main]c.h.t.Application - The following profiles are active: dev
07/31/2025 11:32:42.153 INFO  [main]o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@4139efa4: startup date [Thu Jul 31 11:32:42 CST 2025]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@66f4fb48
07/31/2025 11:32:43.574 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:32:43.626 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:32:43.634 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:32:43.646 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:32:43.692 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:32:43.697 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:32:43.704 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:32:43.712 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:32:43.720 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:32:43.727 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:32:43.740 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:32:43.750 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:32:43.760 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:32:43.770 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:32:43.800 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:32:43.802 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:32:43.807 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.DatatablesTfmRateRuleRepository.
07/31/2025 11:32:43.815 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:32:43.839 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:32:43.886 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'bilTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=bilConfig; factoryMethodName=demoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/bilConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=bilMyBatisConfig; factoryMethodName=cpoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/BilMyBatisConfig.class]]
07/31/2025 11:32:43.891 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'invTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=invConfig; factoryMethodName=demoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/InvConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=invMyBatisConfig; factoryMethodName=invTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/InvMyBatisConfig.class]]
07/31/2025 11:32:43.897 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICardBinDao' and 'com.hisun.tms.cpt.dao.cpi.ICardBinDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:32:43.897 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICardProtDao' and 'com.hisun.tms.cpt.dao.cpi.ICardProtDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:32:43.897 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpiOrgnBusiDao' and 'com.hisun.tms.cpt.dao.cpi.ICpiOrgnBusiDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:32:43.897 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpiOrgnInfoDao' and 'com.hisun.tms.cpt.dao.cpi.ICpiOrgnInfoDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:32:43.897 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpiOrgnRouteDao' and 'com.hisun.tms.cpt.dao.cpi.ICpiOrgnRouteDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:32:43.897 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'IFundOrderDao' and 'com.hisun.tms.cpt.dao.cpi.IFundOrderDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:32:43.897 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'IRefundOrderDao' and 'com.hisun.tms.cpt.dao.cpi.IRefundOrderDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:32:43.897 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ISettleDetailDao' and 'com.hisun.tms.cpt.dao.cpi.ISettleDetailDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:32:43.897 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpoOrgnBusiDao' and 'com.hisun.tms.cpt.dao.cpo.ICpoOrgnBusiDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:32:43.897 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpoOrgnInfoDao' and 'com.hisun.tms.cpt.dao.cpo.ICpoOrgnInfoDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:32:43.897 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpoOrgnRouteDao' and 'com.hisun.tms.cpt.dao.cpo.ICpoOrgnRouteDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:32:43.897 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'IWithdrawOrderDao' and 'com.hisun.tms.cpt.dao.cpo.IWithdrawOrderDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:32:43.898 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'tamTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tamConfig; factoryMethodName=tamTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/TamConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tamMyBatisConfig; factoryMethodName=tamTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/TamMyBatisConfig.class]]
07/31/2025 11:32:43.899 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'tfmTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tfmConfig; factoryMethodName=demoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/TfmConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tfmMyBatisConfig; factoryMethodName=tfmTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/TfmMyBatisConfig.class]]
07/31/2025 11:32:44.522 INFO  [main]o.s.i.c.IntegrationRegistrar - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
07/31/2025 11:32:44.612 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:33:12.564 INFO  [main]c.h.t.Application - The following profiles are active: dev
07/31/2025 11:33:12.582 INFO  [main]o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@6443b128: startup date [Thu Jul 31 11:33:12 CST 2025]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6392827e
07/31/2025 11:33:13.671 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:33:13.709 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:33:13.715 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:33:13.724 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:33:13.761 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:33:13.765 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:33:13.771 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:33:13.778 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:33:13.783 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:33:13.789 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:33:13.800 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:33:13.807 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:33:13.815 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:33:13.824 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:33:13.847 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:33:13.849 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:33:13.853 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.DatatablesTfmRateRuleRepository.
07/31/2025 11:33:13.858 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:33:13.875 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:33:13.911 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'bilTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=bilConfig; factoryMethodName=demoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/bilConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=bilMyBatisConfig; factoryMethodName=cpoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/BilMyBatisConfig.class]]
07/31/2025 11:33:13.916 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'invTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=invConfig; factoryMethodName=demoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/InvConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=invMyBatisConfig; factoryMethodName=invTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/InvMyBatisConfig.class]]
07/31/2025 11:33:13.921 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICardBinDao' and 'com.hisun.tms.cpt.dao.cpi.ICardBinDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:33:13.921 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICardProtDao' and 'com.hisun.tms.cpt.dao.cpi.ICardProtDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:33:13.921 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpiOrgnBusiDao' and 'com.hisun.tms.cpt.dao.cpi.ICpiOrgnBusiDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:33:13.921 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpiOrgnInfoDao' and 'com.hisun.tms.cpt.dao.cpi.ICpiOrgnInfoDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:33:13.921 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpiOrgnRouteDao' and 'com.hisun.tms.cpt.dao.cpi.ICpiOrgnRouteDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:33:13.921 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'IFundOrderDao' and 'com.hisun.tms.cpt.dao.cpi.IFundOrderDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:33:13.921 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'IRefundOrderDao' and 'com.hisun.tms.cpt.dao.cpi.IRefundOrderDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:33:13.921 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ISettleDetailDao' and 'com.hisun.tms.cpt.dao.cpi.ISettleDetailDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:33:13.921 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpoOrgnBusiDao' and 'com.hisun.tms.cpt.dao.cpo.ICpoOrgnBusiDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:33:13.921 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpoOrgnInfoDao' and 'com.hisun.tms.cpt.dao.cpo.ICpoOrgnInfoDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:33:13.921 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpoOrgnRouteDao' and 'com.hisun.tms.cpt.dao.cpo.ICpoOrgnRouteDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:33:13.921 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'IWithdrawOrderDao' and 'com.hisun.tms.cpt.dao.cpo.IWithdrawOrderDao' mapperInterface. Bean already defined with the same name!
07/31/2025 11:33:13.922 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'tamTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tamConfig; factoryMethodName=tamTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/TamConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tamMyBatisConfig; factoryMethodName=tamTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/TamMyBatisConfig.class]]
07/31/2025 11:33:13.923 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'tfmTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tfmConfig; factoryMethodName=demoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/TfmConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tfmMyBatisConfig; factoryMethodName=tfmTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/TfmMyBatisConfig.class]]
07/31/2025 11:33:14.378 INFO  [main]o.s.i.c.IntegrationRegistrar - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
07/31/2025 11:33:14.439 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 11:33:14.668 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.demo.repository.DatatablesExampleRepository.
07/31/2025 11:33:14.669 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.MercItfRepository.
07/31/2025 11:33:14.669 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.OfficeInfoRepository.
07/31/2025 11:33:14.669 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.DatatablesRoleRepository.
07/31/2025 11:33:14.669 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmAcBalRepository.
07/31/2025 11:33:14.670 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesRuleListRepository.
07/31/2025 11:33:14.670 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.WhiteListRepository.
07/31/2025 11:33:14.670 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.chk.repository.ChkErrorRepository.
07/31/2025 11:33:14.670 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DataTablesMercExtInfoRepository.
07/31/2025 11:33:14.670 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.TfmFeeOrderRepository.
07/31/2025 11:33:14.670 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csh.repository.CshOrderRepository.
07/31/2025 11:33:14.670 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.DatatablesProInfoRepository.
07/31/2025 11:33:14.670 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesPhoneSegementInfoRepository.
07/31/2025 11:33:14.671 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.demo.repository.ExampleRepository.
07/31/2025 11:33:14.671 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rpt.repository.RptRsRepository.
07/31/2025 11:33:14.671 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmAdjustInfRepository.
07/31/2025 11:33:14.671 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.ConstantParamsRepository.
07/31/2025 11:33:14.671 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.onr.repository.DatatablesOnrRfdOrderRepository.
07/31/2025 11:33:14.671 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.MercRegisterSeqRepository.
07/31/2025 11:33:14.671 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.UserRepository.
07/31/2025 11:33:14.671 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.RoleRepository.
07/31/2025 11:33:14.672 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesCampaignInfoRepository.
07/31/2025 11:33:14.672 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesWhiteListRepository.
07/31/2025 11:33:14.672 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.CheckRuleRepository.
07/31/2025 11:33:14.672 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.onr.repository.OnrRfdOrderRepository.
07/31/2025 11:33:14.672 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UrmCprExtInfRespository.
07/31/2025 11:33:14.672 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UserBaseInfoRepository.
07/31/2025 11:33:14.672 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DatatablesAcmItmPropertyRepository.
07/31/2025 11:33:14.672 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.bil.repository.DataTablesMerchantOrderInfoRepository.
07/31/2025 11:33:14.673 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DatatablesUserHistoryRepository.
07/31/2025 11:33:14.673 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.DataTableMercRegisterRepository.
07/31/2025 11:33:14.673 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.mkm.repository.BatchFileRecRepository.
07/31/2025 11:33:14.673 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.RiskRuleRepository.
07/31/2025 11:33:14.674 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.SafeInfoRespository.
07/31/2025 11:33:14.674 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesSmsTemplateInfoRepository.
07/31/2025 11:33:14.674 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csm.repository.SettleBaseDORespository.
07/31/2025 11:33:14.674 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.DatatablesTfmFeeOrderRepository.
07/31/2025 11:33:14.674 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DatatablesMercSafelInfoRepository.
07/31/2025 11:33:14.674 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.onr.repository.DatatablesOnrOrderRepository.
07/31/2025 11:33:14.674 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.chk.repository.DatatablesChkErrorRepository.
07/31/2025 11:33:14.674 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UserInfoRepository.
07/31/2025 11:33:14.674 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesBannerInfoRepository.
07/31/2025 11:33:14.674 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesConstantParamsRepository.
07/31/2025 11:33:14.674 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.TmsUserOprLogRepository.
07/31/2025 11:33:14.674 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cpm.repository.DatatablesCpmOrderRepository.
07/31/2025 11:33:14.674 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.NoticeInfoRepository.
07/31/2025 11:33:14.675 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.onr.repository.OnrOrderRepository.
07/31/2025 11:33:14.675 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.mkm.repository.MkmAtvUserRespository.
07/31/2025 11:33:14.675 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesNoticeInfoRepository.
07/31/2025 11:33:14.675 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.SmsTemplateInfoRepository.
07/31/2025 11:33:14.675 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesCheckRuleRepository.
07/31/2025 11:33:14.675 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.TmsOprMapperRespository.
07/31/2025 11:33:14.675 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.PhoneSegementInfoRepository.
07/31/2025 11:33:14.675 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.CampaignInfoRepository.
07/31/2025 11:33:14.676 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesBlackListRepository.
07/31/2025 11:33:14.676 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DataTablesAcmAcDetailRepository.
07/31/2025 11:33:14.676 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.MercExtInfoRepository.
07/31/2025 11:33:14.676 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.DatatablesTfmRateRuleRepository.
07/31/2025 11:33:14.676 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.BannerInfoRepository.
07/31/2025 11:33:14.676 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.DatatablesOrderRepository.
07/31/2025 11:33:14.676 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmItmInfRepository.
07/31/2025 11:33:14.676 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.UserRoleRespository.
07/31/2025 11:33:14.676 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.bil.repository.DataTablesOrderInfoRepository.
07/31/2025 11:33:14.677 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesAreaInfoRepository.
07/31/2025 11:33:14.677 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.MercInfoRepository.
07/31/2025 11:33:14.677 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.chk.repository.ChkControlRepository.
07/31/2025 11:33:14.677 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.MercSafelInfoRepository.
07/31/2025 11:33:14.677 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmAdjustDetailRepository.
07/31/2025 11:33:14.677 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cpt.repository.CpiSubMercCastRepository.
07/31/2025 11:33:14.677 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.MessageTemplateInfoRepository.
07/31/2025 11:33:14.677 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DatatablesMercInfoRepository.
07/31/2025 11:33:14.677 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DatatablesAcmItmInfRepository.
07/31/2025 11:33:14.677 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesRiskRuleRepository.
07/31/2025 11:33:14.678 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesHighRiskRepository.
07/31/2025 11:33:14.678 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmAcInfRepository.
07/31/2025 11:33:14.678 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.DateRateRepository.
07/31/2025 11:33:14.678 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.EmailTemplateInfoRepository.
07/31/2025 11:33:14.678 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csh.repository.PaytypeRepository.
07/31/2025 11:33:14.678 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesOfficeInfoRepository.
07/31/2025 11:33:14.678 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UrmKybSeqRepository.
07/31/2025 11:33:14.678 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csh.repository.DatatablesPayTypeRepository.
07/31/2025 11:33:14.678 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesRuleParamRepository.
07/31/2025 11:33:14.679 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.RsmUserStatusRepository.
07/31/2025 11:33:14.679 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UrmKybCertInfRepository.
07/31/2025 11:33:14.679 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.SafeLoginRespository.
07/31/2025 11:33:14.679 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmItmPropertyRepository.
07/31/2025 11:33:14.679 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.BlackListRepository.
07/31/2025 11:33:14.679 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DatatablesMercItfInfoRepository.
07/31/2025 11:33:14.679 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cpm.repository.CpmOrderRepository.
07/31/2025 11:33:14.679 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesEmailTemplateInfoRepository.
07/31/2025 11:33:14.680 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesBranchInfoRepository.
07/31/2025 11:33:14.680 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.AreaInfoRepository.
07/31/2025 11:33:14.680 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.ProInfoRepository.
07/31/2025 11:33:14.680 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.mkm.repository.SeaccyDetailRepository.
07/31/2025 11:33:14.680 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.HighRiskRepository.
07/31/2025 11:33:14.680 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DataTablesAdjustInfRepository.
07/31/2025 11:33:14.680 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rpt.repository.DataTablesRptRsRepository.
07/31/2025 11:33:14.680 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.RuleParamRepository.
07/31/2025 11:33:14.680 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.OrderRepository.
07/31/2025 11:33:14.681 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.BranchInfoRepository.
07/31/2025 11:33:14.681 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.CprResInfRespository.
07/31/2025 11:33:14.681 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.InvUserInfoRepository.
07/31/2025 11:33:14.681 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DataTablesAdjustDetailRepository.
07/31/2025 11:33:14.681 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.DatatablesUserRepository.
07/31/2025 11:33:14.681 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rpt.repository.DataTablesRptRepository.
07/31/2025 11:33:14.681 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.MerchantRateRuleRespository.
07/31/2025 11:33:14.681 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.chk.repository.DatatablesChkControlRepository.
07/31/2025 11:33:14.681 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csm.repository.SettleCardRespository.
07/31/2025 11:33:14.682 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.ResourceRepository.
07/31/2025 11:33:14.682 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesMessageTemplateInfoRepository.
07/31/2025 11:33:14.682 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rpt.repository.RptRepository.
07/31/2025 11:33:14.878 INFO  [main]o.s.c.c.s.GenericScope - BeanFactory id=d6db338d-1ef3-3f6f-ab3e-2040c76f77b0
07/31/2025 11:33:14.901 INFO  [main]o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
07/31/2025 11:33:14.907 INFO  [main]o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created.
07/31/2025 11:33:14.916 INFO  [main]o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
07/31/2025 11:33:14.980 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.tam.client.ExchangeOrderClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.981 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.tam.client.TransferOrderClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.982 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.BankClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.983 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.CardClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.983 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.CheckClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.984 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.CregisClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.984 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.EbankpayClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.985 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.FastpayClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.986 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.PosClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.986 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.RefundClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.987 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.RemittanceClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.987 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.RouteClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.988 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.csh.client.CshAuditTransferOrderClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.989 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.csh.client.CshOrderClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.989 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.csh.client.CshRefundClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.991 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskCheckClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.992 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskCheckUserStatusClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.992 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskListMngClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.993 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskParamMngClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.993 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskRuleMngClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.994 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.CmmServerClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.995 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.ConstantParamClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.995 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.GrantClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.996 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.QRCodeServerClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.996 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.SmsServerClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.997 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpo.client.CopAgcyBizClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.998 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpo.client.RouteClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.998 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpo.client.WithdrawClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:14.999 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.urm.client.UserAuthenticationClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.000 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.urm.client.UserBasicInfClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.000 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.urm.client.UserBasicInfClient1' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.001 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.urm.client.UserPasswordClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.002 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.acm.client.AccountingTreatmentClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.002 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.acm.client.AccountManagementClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.004 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.mkm.client.MarketActivityClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.004 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.mkm.client.MarketingToolsMngClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.005 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OrderCancleClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.006 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OrderRefundClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.006 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.ConfigureClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.007 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OrderQueryClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.007 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OnrCheckedHandleClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.008 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OrderRegistratClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.008 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.NotifySendClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.009 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.chk.client.ChkClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.046 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$a4ecb582] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.154 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$de80f754] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.299 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration' of type [org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration$$EnhancerBySpringCGLIB$$772ebf8e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.310 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'objectPostProcessor' of type [org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.314 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@4067634b' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.330 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'webMvcConfig' of type [com.hisun.tms.common.config.WebMvcConfig$$EnhancerBySpringCGLIB$$3ed902e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.355 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#71f056a' of type [org.springframework.beans.factory.config.PropertiesFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.356 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#71f056a' of type [java.util.Properties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.362 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#5521407f' of type [org.springframework.data.repository.core.support.PropertiesBasedNamedQueries] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.366 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#1de6dc80' of type [org.springframework.data.repository.query.ExtensionAwareEvaluationContextProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.397 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSourceConfig' of type [com.hisun.tms.common.config.DataSourceConfig$$EnhancerBySpringCGLIB$$2461c3cd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.563 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tmsDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.587 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.JpaProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.588 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tmsConfig' of type [com.hisun.tms.common.config.TmsConfig$$EnhancerBySpringCGLIB$$38ae7ca8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.612 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration' of type [org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$$EnhancerBySpringCGLIB$$72f4f25e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.623 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties' of type [org.springframework.boot.autoconfigure.transaction.TransactionProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.631 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'platformTransactionManagerCustomizers' of type [org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:15.637 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration$$EnhancerBySpringCGLIB$$48075b74] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:18.242 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jpaVendorAdapter' of type [org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:18.248 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'entityManagerFactoryBuilder' of type [org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:18.270 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'tmsPersistenceUnit'
07/31/2025 11:33:18.280 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tmsPersistenceUnit
	...]
07/31/2025 11:33:18.333 INFO  [main]o.h.Version - HHH000412: Hibernate Core {5.0.12.Final}
07/31/2025 11:33:18.335 INFO  [main]o.h.c.Environment - HHH000206: hibernate.properties not found
07/31/2025 11:33:18.336 INFO  [main]o.h.c.Environment - HHH000021: Bytecode provider name : javassist
07/31/2025 11:33:18.371 INFO  [main]o.h.a.c.Version - HCANN000001: Hibernate Commons Annotations {5.0.1.Final}
07/31/2025 11:33:18.549 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:33:18.726 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 11:33:18.726 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 11:33:18.931 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:33:24.932 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'tmsPersistenceUnit'
07/31/2025 11:33:24.934 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tmsEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:24.936 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tmsEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:24.945 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#134955bb' of type [com.sun.proxy.$Proxy163] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:25.055 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'acmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:25.060 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'acmConfig' of type [com.hisun.tms.common.config.AcmConfig$$EnhancerBySpringCGLIB$$8d89a479] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:25.070 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'acmPersistenceUnit'
07/31/2025 11:33:25.070 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: acmPersistenceUnit
	...]
07/31/2025 11:33:27.247 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:33:27.266 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 11:33:27.266 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 11:33:27.284 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#34538ffe' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:27.285 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#34538ffe' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:27.307 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:33:37.440 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'acmPersistenceUnit'
07/31/2025 11:33:37.440 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'acmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:37.441 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'acmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:37.544 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'bilDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:37.549 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'bilConfig' of type [com.hisun.tms.common.config.bilConfig$$EnhancerBySpringCGLIB$$b52876d3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:37.558 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'bilPersistenceUnit'
07/31/2025 11:33:37.558 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: bilPersistenceUnit
	...]
07/31/2025 11:33:39.787 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:33:39.800 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:33:42.703 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'bilPersistenceUnit'
07/31/2025 11:33:42.703 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'bilEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:42.704 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'bilEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:42.808 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'chkDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:42.813 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'chkConfig' of type [com.hisun.tms.common.config.ChkConfig$$EnhancerBySpringCGLIB$$558b58b4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:42.822 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'chkPersistenceUnit'
07/31/2025 11:33:42.822 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: chkPersistenceUnit
	...]
07/31/2025 11:33:45.380 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:33:45.409 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:33:50.488 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'chkPersistenceUnit'
07/31/2025 11:33:50.488 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'chkEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:50.489 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'chkEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:50.591 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cmmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:50.597 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cmmConfig' of type [com.hisun.tms.common.config.CmmConfig$$EnhancerBySpringCGLIB$$92f14231] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:50.607 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'cmmPersistenceUnit'
07/31/2025 11:33:50.607 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cmmPersistenceUnit
	...]
07/31/2025 11:33:52.800 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:33:52.823 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#34538ffe' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:52.823 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#34538ffe' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:33:52.843 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:34:09.373 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'cmmPersistenceUnit'
07/31/2025 11:34:09.373 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cmmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:09.373 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cmmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:09.476 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpiDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:09.481 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpiConfig' of type [com.hisun.tms.common.config.CpiConfig$$EnhancerBySpringCGLIB$$7cad79ea] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:09.490 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'cpiPersistenceUnit'
07/31/2025 11:34:09.490 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpiPersistenceUnit
	...]
07/31/2025 11:34:12.139 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:34:12.147 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:34:12.931 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'cpiPersistenceUnit'
07/31/2025 11:34:12.931 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpiEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:12.931 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpiEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:13.034 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:13.039 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpmConfig' of type [com.hisun.tms.common.config.CpmConfig$$EnhancerBySpringCGLIB$$bdd5936e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:13.047 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'cpmPersistenceUnit'
07/31/2025 11:34:13.047 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpmPersistenceUnit
	...]
07/31/2025 11:34:15.228 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:34:15.234 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#34538ffe' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:15.234 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#34538ffe' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:15.237 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:34:17.003 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'cpmPersistenceUnit'
07/31/2025 11:34:17.003 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:17.004 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:17.105 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cshDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:17.110 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cshConfig' of type [com.hisun.tms.common.config.CshConfig$$EnhancerBySpringCGLIB$$5747c4c6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:17.118 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'tamPersistenceUnit'
07/31/2025 11:34:17.118 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 11:34:19.410 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:34:19.417 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#34538ffe' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:19.418 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#34538ffe' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:19.421 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:34:20.846 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'tamPersistenceUnit'
07/31/2025 11:34:20.846 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cshEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:20.847 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cshEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:20.951 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'csmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:20.956 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'csmConfig' of type [com.hisun.tms.common.config.CsmConfig$$EnhancerBySpringCGLIB$$e8b9e4ab] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:20.963 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'csmPersistenceUnit'
07/31/2025 11:34:20.964 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: csmPersistenceUnit
	...]
07/31/2025 11:34:23.180 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:34:23.195 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:34:26.203 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'csmPersistenceUnit'
07/31/2025 11:34:26.203 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'csmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:26.204 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'csmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:26.300 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'demoDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:26.305 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'demoConfig' of type [com.hisun.tms.common.config.DemoConfig$$EnhancerBySpringCGLIB$$b2d50e4b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:26.313 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'demoPersistenceUnit'
07/31/2025 11:34:26.313 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: demoPersistenceUnit
	...]
07/31/2025 11:34:28.552 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:34:28.559 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#34538ffe' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:28.559 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#34538ffe' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:28.562 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:34:29.487 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'demoPersistenceUnit'
07/31/2025 11:34:29.487 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'demoEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:29.488 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'demoEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:29.589 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'invDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:29.593 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'invConfig' of type [com.hisun.tms.common.config.InvConfig$$EnhancerBySpringCGLIB$$81cdee1f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:29.602 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'invPersistenceUnit'
07/31/2025 11:34:29.602 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: invPersistenceUnit
	...]
07/31/2025 11:34:31.752 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:34:31.763 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#34538ffe' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:31.763 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#34538ffe' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:31.769 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:34:36.207 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'invPersistenceUnit'
07/31/2025 11:34:36.207 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'invEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:36.208 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'invEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:36.308 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mkmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:36.313 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mkmConfig' of type [com.hisun.tms.common.config.mkmConfig$$EnhancerBySpringCGLIB$$d815701d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:36.322 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'mkmPersistenceUnit'
07/31/2025 11:34:36.322 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: mkmPersistenceUnit
	...]
07/31/2025 11:34:38.610 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:34:38.624 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:34:42.063 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'mkmPersistenceUnit'
07/31/2025 11:34:42.063 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mkmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:42.064 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mkmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:42.166 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'onrDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:42.170 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'onrConfig' of type [com.hisun.tms.common.config.OnrConfig$$EnhancerBySpringCGLIB$$a3f18161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:42.178 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'onrPersistenceUnit'
07/31/2025 11:34:42.178 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: onrPersistenceUnit
	...]
07/31/2025 11:34:44.428 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:34:44.435 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#34538ffe' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:44.435 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#34538ffe' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:44.438 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:34:47.117 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'onrPersistenceUnit'
07/31/2025 11:34:47.118 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'onrEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:47.118 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'onrEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:47.218 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rptDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:47.223 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rptConfig' of type [com.hisun.tms.common.config.rptConfig$$EnhancerBySpringCGLIB$$a4573424] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:47.230 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'rptPersistenceUnit'
07/31/2025 11:34:47.230 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: rptPersistenceUnit
	...]
07/31/2025 11:34:49.429 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:34:49.440 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:34:52.609 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'rptPersistenceUnit'
07/31/2025 11:34:52.609 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rptEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:52.610 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rptEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:52.710 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rsmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:52.715 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rsmConfig' of type [com.hisun.tms.common.config.rsmConfig$$EnhancerBySpringCGLIB$$9d3558ba] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:34:52.724 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'rsmPersistenceUnit'
07/31/2025 11:34:52.724 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: rsmPersistenceUnit
	...]
07/31/2025 11:34:55.490 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:34:55.513 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:35:03.950 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'rsmPersistenceUnit'
07/31/2025 11:35:03.950 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rsmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:03.951 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rsmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:04.050 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tamDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:04.055 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tamConfig' of type [com.hisun.tms.common.config.TamConfig$$EnhancerBySpringCGLIB$$ab61116e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:04.063 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'tamPersistenceUnit'
07/31/2025 11:35:04.063 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 11:35:04.151 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 11:35:04.385 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 11:35:04.586 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 11:35:04.794 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 11:35:04.990 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 11:35:05.194 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 11:35:05.391 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 11:35:05.603 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 11:35:05.827 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 11:35:06.017 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 11:35:06.236 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:35:06.242 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:35:06.275 WARN  [main]o.h.j.i.EntityManagerFactoryRegistry - HHH000436: Entity manager factory name (tamPersistenceUnit) is already registered.  If entity manager will be clustered or passivated, specify a unique value for property 'hibernate.ejb.entitymanager_factory_name'
07/31/2025 11:35:06.275 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'tamPersistenceUnit'
07/31/2025 11:35:06.275 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tamEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:06.276 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tamEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:06.377 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tfmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:06.381 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tfmConfig' of type [com.hisun.tms.common.config.TfmConfig$$EnhancerBySpringCGLIB$$4832ee29] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:06.389 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'tfmPersistenceUnit'
07/31/2025 11:35:06.389 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tfmPersistenceUnit
	...]
07/31/2025 11:35:08.621 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:35:08.629 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#34538ffe' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:08.629 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#34538ffe' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:08.634 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:35:12.860 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'tfmPersistenceUnit'
07/31/2025 11:35:12.860 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tfmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:12.861 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tfmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:12.962 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'urmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:12.968 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'urmConfig' of type [com.hisun.tms.common.config.urmConfig$$EnhancerBySpringCGLIB$$15e4695e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:12.979 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'urmPersistenceUnit'
07/31/2025 11:35:12.979 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: urmPersistenceUnit
	...]
07/31/2025 11:35:16.828 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 11:35:16.845 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#34538ffe' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:16.846 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#34538ffe' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:16.862 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 11:35:32.504 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'urmPersistenceUnit'
07/31/2025 11:35:32.504 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'urmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:32.505 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'urmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:32.775 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jpaMappingContext' of type [org.springframework.data.jpa.repository.config.JpaMetamodelMappingContextFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:32.777 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jpaMappingContext' of type [org.springframework.data.jpa.mapping.JpaMetamodelMappingContext] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:32.879 INFO  [main]o.h.h.i.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
07/31/2025 11:35:32.924 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRepository' of type [org.springframework.data.jpa.datatables.repository.DataTablesRepositoryFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:32.926 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRepository' of type [com.sun.proxy.$Proxy176] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:32.938 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'getPermissionEvaluator' of type [com.hisun.tms.common.security.TmsPermissionEvaluator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:32.938 INFO  [main]c.h.t.c.c.WebMvcConfig - DefaultMethodSecurityExpressionHandler has benn set custom TMSPermissionEvaluator.
07/31/2025 11:35:32.938 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'getExpressionHandler' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:32.942 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration' of type [org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration$$EnhancerBySpringCGLIB$$9c036240] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:32.948 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:32.955 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.netflix.metrics.MetricsInterceptorConfiguration$MetricsRestTemplateConfiguration' of type [org.springframework.cloud.netflix.metrics.MetricsInterceptorConfiguration$MetricsRestTemplateConfiguration$$EnhancerBySpringCGLIB$$10ad9d95] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:32.979 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationGlobalProperties' of type [org.springframework.beans.factory.config.PropertiesFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:32.980 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationGlobalProperties' of type [java.util.Properties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:32.998 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$fa9afa51] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 11:35:33.251 INFO  [main]o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 9020 (http)
07/31/2025 11:35:33.259 INFO  [main]o.a.c.c.StandardService - Starting service [Tomcat]
07/31/2025 11:35:33.259 INFO  [main]o.a.c.c.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.15
07/31/2025 11:35:33.306 INFO  [localhost-startStop-1]o.a.c.c.C.[.[.[/] - Initializing Spring embedded WebApplicationContext
07/31/2025 11:35:33.306 INFO  [localhost-startStop-1]o.s.w.c.ContextLoader - Root WebApplicationContext: initialization completed in 140724 ms
07/31/2025 11:35:33.637 DEBUG [localhost-startStop-1]o.hibernate.SQL - select role0_.role_id as role_id1_2_, role0_.branch_name as branch_n2_2_, role0_.office_name as office_n3_2_, role0_.role as role4_2_, role0_.role_name as role_nam5_2_, role0_.status as status6_2_ from role role0_
07/31/2025 11:35:33.898 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 11:35:33.921 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [1]
07/31/2025 11:35:34.077 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 11:35:34.077 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [2]
07/31/2025 11:35:34.237 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 11:35:34.237 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [3]
07/31/2025 11:35:34.366 DEBUG [localhost-startStop-1]c.h.t.c.s.TmsFilterSecurityInterceptor - Validated configuration attributes
07/31/2025 11:35:34.543 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'metricsFilter' to: [/*]
07/31/2025 11:35:34.543 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
07/31/2025 11:35:34.543 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
07/31/2025 11:35:34.543 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
07/31/2025 11:35:34.543 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
07/31/2025 11:35:34.544 INFO  [localhost-startStop-1]o.s.b.w.s.DelegatingFilterProxyRegistrationBean - Mapping filter: 'springSecurityFilterChain' to: [/*]
07/31/2025 11:35:34.544 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'webRequestLoggingFilter' to: [/*]
07/31/2025 11:35:34.544 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'tmsFilterSecurityInterceptor' to: [/*]
07/31/2025 11:35:34.544 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'applicationContextIdFilter' to: [/*]
07/31/2025 11:35:34.544 INFO  [localhost-startStop-1]o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
07/31/2025 11:35:34.698 WARN  [main]o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'accountAddressController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'accountAddressService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'IAccountAddressDao' defined in file [E:\Cele4TeR\Project\mcht-be\tms\build\classes\java\main\com\hisun\tms\acm\dao\IAccountAddressDao.class]: Cannot resolve reference to bean 'acmSqlSessionTemplate' while setting bean property 'sqlSessionTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'acmSqlSessionTemplate' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Unsatisfied dependency expressed through method 'cpoSqlSessionTemplate' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acmSqlSessionFactory' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'cpoSqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
07/31/2025 11:35:34.699 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'urmPersistenceUnit'
07/31/2025 11:35:34.701 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'tfmPersistenceUnit'
07/31/2025 11:35:34.702 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'tamPersistenceUnit'
07/31/2025 11:35:34.702 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'rsmPersistenceUnit'
07/31/2025 11:35:34.703 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'rptPersistenceUnit'
07/31/2025 11:35:34.703 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'onrPersistenceUnit'
07/31/2025 11:35:34.704 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'mkmPersistenceUnit'
07/31/2025 11:35:34.705 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'invPersistenceUnit'
07/31/2025 11:35:34.705 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'demoPersistenceUnit'
07/31/2025 11:35:34.706 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'csmPersistenceUnit'
07/31/2025 11:35:34.706 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'tamPersistenceUnit'
07/31/2025 11:35:34.707 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'cpmPersistenceUnit'
07/31/2025 11:35:34.707 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'cpiPersistenceUnit'
07/31/2025 11:35:34.708 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'cmmPersistenceUnit'
07/31/2025 11:35:34.728 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'chkPersistenceUnit'
07/31/2025 11:35:34.728 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'bilPersistenceUnit'
07/31/2025 11:35:34.730 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'acmPersistenceUnit'
07/31/2025 11:35:34.731 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'tmsPersistenceUnit'
07/31/2025 11:35:35.109 INFO  [main]o.a.c.c.StandardService - Stopping service [Tomcat]
07/31/2025 11:35:35.122 INFO  [main]o.s.b.a.l.AutoConfigurationReportLoggingInitializer - 

Error starting ApplicationContext. To display the auto-configuration report re-run your application with 'debug' enabled.
07/31/2025 11:35:35.129 ERROR [main]o.s.b.SpringApplication - Application startup failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'accountAddressController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'accountAddressService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'IAccountAddressDao' defined in file [E:\Cele4TeR\Project\mcht-be\tms\build\classes\java\main\com\hisun\tms\acm\dao\IAccountAddressDao.class]: Cannot resolve reference to bean 'acmSqlSessionTemplate' while setting bean property 'sqlSessionTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'acmSqlSessionTemplate' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Unsatisfied dependency expressed through method 'cpoSqlSessionTemplate' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acmSqlSessionFactory' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'cpoSqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessPropertyValues(CommonAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1264)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:553)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:761)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:867)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:543)
	at org.springframework.boot.context.embedded.EmbeddedWebApplicationContext.refresh(EmbeddedWebApplicationContext.java:122)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:693)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:360)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1118)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1107)
	at com.hisun.tms.Application.main(Application.java:35)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'accountAddressService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'IAccountAddressDao' defined in file [E:\Cele4TeR\Project\mcht-be\tms\build\classes\java\main\com\hisun\tms\acm\dao\IAccountAddressDao.class]: Cannot resolve reference to bean 'acmSqlSessionTemplate' while setting bean property 'sqlSessionTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'acmSqlSessionTemplate' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Unsatisfied dependency expressed through method 'cpoSqlSessionTemplate' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acmSqlSessionFactory' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'cpoSqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessPropertyValues(CommonAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1264)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:553)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:522)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:496)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:627)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:169)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:88)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessPropertyValues(CommonAnnotationBeanPostProcessor.java:318)
	... 17 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'IAccountAddressDao' defined in file [E:\Cele4TeR\Project\mcht-be\tms\build\classes\java\main\com\hisun\tms\acm\dao\IAccountAddressDao.class]: Cannot resolve reference to bean 'acmSqlSessionTemplate' while setting bean property 'sqlSessionTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'acmSqlSessionTemplate' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Unsatisfied dependency expressed through method 'cpoSqlSessionTemplate' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acmSqlSessionFactory' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'cpoSqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:359)
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveValueIfNecessary(BeanDefinitionValueResolver.java:108)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyPropertyValues(AbstractAutowireCapableBeanFactory.java:1531)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1276)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:553)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1138)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1066)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:518)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:496)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:627)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:169)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:88)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessPropertyValues(CommonAnnotationBeanPostProcessor.java:318)
	... 30 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'acmSqlSessionTemplate' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Unsatisfied dependency expressed through method 'cpoSqlSessionTemplate' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acmSqlSessionFactory' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'cpoSqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:749)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:467)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1173)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1067)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:513)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:351)
	... 48 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acmSqlSessionFactory' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'cpoSqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1173)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1067)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:513)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1138)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1066)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:835)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:741)
	... 58 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'cpoSqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:189)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:588)
	... 71 common frames omitted
Caused by: org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.mybatis.spring.SqlSessionFactoryBean.buildSqlSessionFactory(SqlSessionFactoryBean.java:500)
	at org.mybatis.spring.SqlSessionFactoryBean.afterPropertiesSet(SqlSessionFactoryBean.java:380)
	at org.mybatis.spring.SqlSessionFactoryBean.getObject(SqlSessionFactoryBean.java:547)
	at com.hisun.tms.common.mybatiesConfig.AcmMyBatisConfig.cpoSqlSessionFactory(AcmMyBatisConfig.java:39)
	at com.hisun.tms.common.mybatiesConfig.AcmMyBatisConfig$$EnhancerBySpringCGLIB$$dc587142.CGLIB$cpoSqlSessionFactory$0(<generated>)
	at com.hisun.tms.common.mybatiesConfig.AcmMyBatisConfig$$EnhancerBySpringCGLIB$$dc587142$$FastClassBySpringCGLIB$$186e2c2a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:228)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:358)
	at com.hisun.tms.common.mybatiesConfig.AcmMyBatisConfig$$EnhancerBySpringCGLIB$$dc587142.cpoSqlSessionFactory(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:162)
	... 72 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.apache.ibatis.builder.xml.XMLConfigBuilder.parseConfiguration(XMLConfigBuilder.java:120)
	at org.apache.ibatis.builder.xml.XMLConfigBuilder.parse(XMLConfigBuilder.java:98)
	at org.mybatis.spring.SqlSessionFactoryBean.buildSqlSessionFactory(SqlSessionFactoryBean.java:494)
	... 85 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.apache.ibatis.builder.xml.XMLConfigBuilder.typeAliasesElement(XMLConfigBuilder.java:170)
	at org.apache.ibatis.builder.xml.XMLConfigBuilder.parseConfiguration(XMLConfigBuilder.java:108)
	... 87 common frames omitted
Caused by: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:200)
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:89)
	at org.apache.ibatis.io.Resources.classForName(Resources.java:261)
	at org.apache.ibatis.builder.xml.XMLConfigBuilder.typeAliasesElement(XMLConfigBuilder.java:163)
	... 88 common frames omitted
07/31/2025 14:42:15.743 INFO  [main]c.h.t.Application - The following profiles are active: dev
07/31/2025 14:42:15.760 INFO  [main]o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@61d01788: startup date [Thu Jul 31 14:42:15 CST 2025]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@342c38f8
07/31/2025 14:42:16.830 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:42:16.866 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:42:16.873 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:42:16.882 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:42:16.922 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:42:16.927 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:42:16.933 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:42:16.941 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:42:16.948 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:42:16.954 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:42:16.966 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:42:16.974 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:42:16.983 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:42:16.992 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:42:17.016 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:42:17.018 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:42:17.022 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.DatatablesTfmRateRuleRepository.
07/31/2025 14:42:17.027 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:42:17.046 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:42:17.084 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'bilTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=bilConfig; factoryMethodName=demoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/bilConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=bilMyBatisConfig; factoryMethodName=cpoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/BilMyBatisConfig.class]]
07/31/2025 14:42:17.090 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'invTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=invConfig; factoryMethodName=demoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/InvConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=invMyBatisConfig; factoryMethodName=invTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/InvMyBatisConfig.class]]
07/31/2025 14:42:17.096 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICardBinDao' and 'com.hisun.tms.cpt.dao.cpi.ICardBinDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:42:17.096 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICardProtDao' and 'com.hisun.tms.cpt.dao.cpi.ICardProtDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:42:17.096 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpiOrgnBusiDao' and 'com.hisun.tms.cpt.dao.cpi.ICpiOrgnBusiDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:42:17.096 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpiOrgnInfoDao' and 'com.hisun.tms.cpt.dao.cpi.ICpiOrgnInfoDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:42:17.096 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpiOrgnRouteDao' and 'com.hisun.tms.cpt.dao.cpi.ICpiOrgnRouteDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:42:17.096 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'IFundOrderDao' and 'com.hisun.tms.cpt.dao.cpi.IFundOrderDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:42:17.096 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'IRefundOrderDao' and 'com.hisun.tms.cpt.dao.cpi.IRefundOrderDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:42:17.096 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ISettleDetailDao' and 'com.hisun.tms.cpt.dao.cpi.ISettleDetailDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:42:17.096 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpoOrgnBusiDao' and 'com.hisun.tms.cpt.dao.cpo.ICpoOrgnBusiDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:42:17.096 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpoOrgnInfoDao' and 'com.hisun.tms.cpt.dao.cpo.ICpoOrgnInfoDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:42:17.096 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpoOrgnRouteDao' and 'com.hisun.tms.cpt.dao.cpo.ICpoOrgnRouteDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:42:17.096 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'IWithdrawOrderDao' and 'com.hisun.tms.cpt.dao.cpo.IWithdrawOrderDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:42:17.098 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'tamTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tamConfig; factoryMethodName=tamTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/TamConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tamMyBatisConfig; factoryMethodName=tamTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/TamMyBatisConfig.class]]
07/31/2025 14:42:17.099 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'tfmTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tfmConfig; factoryMethodName=demoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/TfmConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tfmMyBatisConfig; factoryMethodName=tfmTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/TfmMyBatisConfig.class]]
07/31/2025 14:42:17.574 INFO  [main]o.s.i.c.IntegrationRegistrar - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
07/31/2025 14:42:17.642 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:42:17.858 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.demo.repository.DatatablesExampleRepository.
07/31/2025 14:42:17.859 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.MercItfRepository.
07/31/2025 14:42:17.859 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.OfficeInfoRepository.
07/31/2025 14:42:17.859 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.DatatablesRoleRepository.
07/31/2025 14:42:17.859 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmAcBalRepository.
07/31/2025 14:42:17.859 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesRuleListRepository.
07/31/2025 14:42:17.860 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.WhiteListRepository.
07/31/2025 14:42:17.860 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.chk.repository.ChkErrorRepository.
07/31/2025 14:42:17.860 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DataTablesMercExtInfoRepository.
07/31/2025 14:42:17.860 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.TfmFeeOrderRepository.
07/31/2025 14:42:17.860 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csh.repository.CshOrderRepository.
07/31/2025 14:42:17.860 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.DatatablesProInfoRepository.
07/31/2025 14:42:17.860 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesPhoneSegementInfoRepository.
07/31/2025 14:42:17.860 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.demo.repository.ExampleRepository.
07/31/2025 14:42:17.861 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rpt.repository.RptRsRepository.
07/31/2025 14:42:17.861 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmAdjustInfRepository.
07/31/2025 14:42:17.861 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.ConstantParamsRepository.
07/31/2025 14:42:17.861 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.onr.repository.DatatablesOnrRfdOrderRepository.
07/31/2025 14:42:17.861 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.MercRegisterSeqRepository.
07/31/2025 14:42:17.861 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.UserRepository.
07/31/2025 14:42:17.861 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.RoleRepository.
07/31/2025 14:42:17.862 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesCampaignInfoRepository.
07/31/2025 14:42:17.862 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesWhiteListRepository.
07/31/2025 14:42:17.862 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.CheckRuleRepository.
07/31/2025 14:42:17.862 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.onr.repository.OnrRfdOrderRepository.
07/31/2025 14:42:17.862 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UrmCprExtInfRespository.
07/31/2025 14:42:17.863 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UserBaseInfoRepository.
07/31/2025 14:42:17.863 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DatatablesAcmItmPropertyRepository.
07/31/2025 14:42:17.863 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.bil.repository.DataTablesMerchantOrderInfoRepository.
07/31/2025 14:42:17.863 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DatatablesUserHistoryRepository.
07/31/2025 14:42:17.863 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.DataTableMercRegisterRepository.
07/31/2025 14:42:17.863 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.mkm.repository.BatchFileRecRepository.
07/31/2025 14:42:17.863 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.RiskRuleRepository.
07/31/2025 14:42:17.863 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.SafeInfoRespository.
07/31/2025 14:42:17.864 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesSmsTemplateInfoRepository.
07/31/2025 14:42:17.864 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csm.repository.SettleBaseDORespository.
07/31/2025 14:42:17.864 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.DatatablesTfmFeeOrderRepository.
07/31/2025 14:42:17.864 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DatatablesMercSafelInfoRepository.
07/31/2025 14:42:17.864 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.onr.repository.DatatablesOnrOrderRepository.
07/31/2025 14:42:17.864 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.chk.repository.DatatablesChkErrorRepository.
07/31/2025 14:42:17.865 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UserInfoRepository.
07/31/2025 14:42:17.865 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesBannerInfoRepository.
07/31/2025 14:42:17.865 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesConstantParamsRepository.
07/31/2025 14:42:17.865 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.TmsUserOprLogRepository.
07/31/2025 14:42:17.865 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cpm.repository.DatatablesCpmOrderRepository.
07/31/2025 14:42:17.865 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.NoticeInfoRepository.
07/31/2025 14:42:17.865 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.onr.repository.OnrOrderRepository.
07/31/2025 14:42:17.865 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.mkm.repository.MkmAtvUserRespository.
07/31/2025 14:42:17.866 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesNoticeInfoRepository.
07/31/2025 14:42:17.866 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.SmsTemplateInfoRepository.
07/31/2025 14:42:17.866 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesCheckRuleRepository.
07/31/2025 14:42:17.866 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.TmsOprMapperRespository.
07/31/2025 14:42:17.866 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.PhoneSegementInfoRepository.
07/31/2025 14:42:17.866 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.CampaignInfoRepository.
07/31/2025 14:42:17.866 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesBlackListRepository.
07/31/2025 14:42:17.866 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DataTablesAcmAcDetailRepository.
07/31/2025 14:42:17.867 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.MercExtInfoRepository.
07/31/2025 14:42:17.867 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.DatatablesTfmRateRuleRepository.
07/31/2025 14:42:17.867 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.BannerInfoRepository.
07/31/2025 14:42:17.867 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.DatatablesOrderRepository.
07/31/2025 14:42:17.867 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmItmInfRepository.
07/31/2025 14:42:17.867 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.UserRoleRespository.
07/31/2025 14:42:17.867 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.bil.repository.DataTablesOrderInfoRepository.
07/31/2025 14:42:17.867 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesAreaInfoRepository.
07/31/2025 14:42:17.868 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.MercInfoRepository.
07/31/2025 14:42:17.868 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.chk.repository.ChkControlRepository.
07/31/2025 14:42:17.868 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.MercSafelInfoRepository.
07/31/2025 14:42:17.868 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmAdjustDetailRepository.
07/31/2025 14:42:17.868 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cpt.repository.CpiSubMercCastRepository.
07/31/2025 14:42:17.868 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.MessageTemplateInfoRepository.
07/31/2025 14:42:17.868 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DatatablesMercInfoRepository.
07/31/2025 14:42:17.868 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DatatablesAcmItmInfRepository.
07/31/2025 14:42:17.869 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesRiskRuleRepository.
07/31/2025 14:42:17.869 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesHighRiskRepository.
07/31/2025 14:42:17.869 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmAcInfRepository.
07/31/2025 14:42:17.869 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.DateRateRepository.
07/31/2025 14:42:17.869 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.EmailTemplateInfoRepository.
07/31/2025 14:42:17.869 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csh.repository.PaytypeRepository.
07/31/2025 14:42:17.869 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesOfficeInfoRepository.
07/31/2025 14:42:17.869 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UrmKybSeqRepository.
07/31/2025 14:42:17.869 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csh.repository.DatatablesPayTypeRepository.
07/31/2025 14:42:17.870 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesRuleParamRepository.
07/31/2025 14:42:17.870 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.RsmUserStatusRepository.
07/31/2025 14:42:17.870 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UrmKybCertInfRepository.
07/31/2025 14:42:17.870 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.SafeLoginRespository.
07/31/2025 14:42:17.870 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmItmPropertyRepository.
07/31/2025 14:42:17.870 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.BlackListRepository.
07/31/2025 14:42:17.871 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DatatablesMercItfInfoRepository.
07/31/2025 14:42:17.871 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cpm.repository.CpmOrderRepository.
07/31/2025 14:42:17.871 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesEmailTemplateInfoRepository.
07/31/2025 14:42:17.871 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesBranchInfoRepository.
07/31/2025 14:42:17.871 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.AreaInfoRepository.
07/31/2025 14:42:17.871 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.ProInfoRepository.
07/31/2025 14:42:17.871 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.mkm.repository.SeaccyDetailRepository.
07/31/2025 14:42:17.871 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.HighRiskRepository.
07/31/2025 14:42:17.871 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DataTablesAdjustInfRepository.
07/31/2025 14:42:17.872 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rpt.repository.DataTablesRptRsRepository.
07/31/2025 14:42:17.872 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.RuleParamRepository.
07/31/2025 14:42:17.872 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.OrderRepository.
07/31/2025 14:42:17.872 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.BranchInfoRepository.
07/31/2025 14:42:17.872 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.CprResInfRespository.
07/31/2025 14:42:17.872 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.InvUserInfoRepository.
07/31/2025 14:42:17.872 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DataTablesAdjustDetailRepository.
07/31/2025 14:42:17.872 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.DatatablesUserRepository.
07/31/2025 14:42:17.872 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rpt.repository.DataTablesRptRepository.
07/31/2025 14:42:17.873 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.MerchantRateRuleRespository.
07/31/2025 14:42:17.873 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.chk.repository.DatatablesChkControlRepository.
07/31/2025 14:42:17.873 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csm.repository.SettleCardRespository.
07/31/2025 14:42:17.873 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.ResourceRepository.
07/31/2025 14:42:17.873 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesMessageTemplateInfoRepository.
07/31/2025 14:42:17.873 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rpt.repository.RptRepository.
07/31/2025 14:42:18.065 INFO  [main]o.s.c.c.s.GenericScope - BeanFactory id=d6db338d-1ef3-3f6f-ab3e-2040c76f77b0
07/31/2025 14:42:18.089 INFO  [main]o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
07/31/2025 14:42:18.095 INFO  [main]o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created.
07/31/2025 14:42:18.106 INFO  [main]o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
07/31/2025 14:42:18.176 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.tam.client.ExchangeOrderClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.176 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.tam.client.TransferOrderClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.178 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.BankClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.179 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.CardClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.179 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.CheckClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.180 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.CregisClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.180 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.EbankpayClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.181 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.FastpayClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.182 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.PosClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.182 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.RefundClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.183 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.RemittanceClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.183 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.RouteClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.184 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.csh.client.CshAuditTransferOrderClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.185 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.csh.client.CshOrderClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.185 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.csh.client.CshRefundClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.187 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskCheckClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.188 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskCheckUserStatusClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.188 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskListMngClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.189 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskParamMngClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.189 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskRuleMngClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.190 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.CmmServerClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.191 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.ConstantParamClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.191 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.GrantClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.192 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.QRCodeServerClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.192 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.SmsServerClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.193 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpo.client.CopAgcyBizClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.194 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpo.client.RouteClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.194 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpo.client.WithdrawClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.195 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.urm.client.UserAuthenticationClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.196 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.urm.client.UserBasicInfClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.196 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.urm.client.UserBasicInfClient1' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.197 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.urm.client.UserPasswordClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.198 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.acm.client.AccountingTreatmentClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.199 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.acm.client.AccountManagementClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.200 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.mkm.client.MarketActivityClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.200 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.mkm.client.MarketingToolsMngClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.201 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OrderCancleClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.202 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OrderRefundClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.202 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.ConfigureClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.203 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OrderQueryClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.203 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OnrCheckedHandleClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.204 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OrderRegistratClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.204 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.NotifySendClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.205 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.chk.client.ChkClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.241 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$94dfe8ee] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.347 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$ce742ac0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.499 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration' of type [org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration$$EnhancerBySpringCGLIB$$6721f2fa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.507 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'objectPostProcessor' of type [org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.510 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@21bf308' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.523 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'webMvcConfig' of type [com.hisun.tms.common.config.WebMvcConfig$$EnhancerBySpringCGLIB$$f3e0c39a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.542 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#418d1c03' of type [org.springframework.beans.factory.config.PropertiesFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.543 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#418d1c03' of type [java.util.Properties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.548 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#7c56c911' of type [org.springframework.data.repository.core.support.PropertiesBasedNamedQueries] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.552 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#191774d6' of type [org.springframework.data.repository.query.ExtensionAwareEvaluationContextProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.585 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSourceConfig' of type [com.hisun.tms.common.config.DataSourceConfig$$EnhancerBySpringCGLIB$$1454f739] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.762 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tmsDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.788 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.JpaProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.789 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tmsConfig' of type [com.hisun.tms.common.config.TmsConfig$$EnhancerBySpringCGLIB$$28a1b014] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.808 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration' of type [org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$$EnhancerBySpringCGLIB$$62e825ca] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.816 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties' of type [org.springframework.boot.autoconfigure.transaction.TransactionProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.822 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'platformTransactionManagerCustomizers' of type [org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:18.828 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration$$EnhancerBySpringCGLIB$$37fa8ee0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:21.182 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jpaVendorAdapter' of type [org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:21.187 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'entityManagerFactoryBuilder' of type [org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:21.204 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'tmsPersistenceUnit'
07/31/2025 14:42:21.212 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tmsPersistenceUnit
	...]
07/31/2025 14:42:21.253 INFO  [main]o.h.Version - HHH000412: Hibernate Core {5.0.12.Final}
07/31/2025 14:42:21.254 INFO  [main]o.h.c.Environment - HHH000206: hibernate.properties not found
07/31/2025 14:42:21.254 INFO  [main]o.h.c.Environment - HHH000021: Bytecode provider name : javassist
07/31/2025 14:42:21.283 INFO  [main]o.h.a.c.Version - HCANN000001: Hibernate Commons Annotations {5.0.1.Final}
07/31/2025 14:42:21.446 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:42:21.622 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 14:42:21.622 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 14:42:21.821 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:42:29.049 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'tmsPersistenceUnit'
07/31/2025 14:42:29.050 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tmsEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:29.052 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tmsEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:29.062 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#7d70ba18' of type [com.sun.proxy.$Proxy163] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:29.174 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'acmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:29.179 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'acmConfig' of type [com.hisun.tms.common.config.AcmConfig$$EnhancerBySpringCGLIB$$7d7cd7e5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:29.189 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'acmPersistenceUnit'
07/31/2025 14:42:29.189 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: acmPersistenceUnit
	...]
07/31/2025 14:42:32.075 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:42:32.093 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 14:42:32.093 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 14:42:32.111 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#64864875' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:32.112 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#64864875' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:32.136 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:42:45.271 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'acmPersistenceUnit'
07/31/2025 14:42:45.271 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'acmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:45.272 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'acmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:45.377 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'bilDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:45.382 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'bilConfig' of type [com.hisun.tms.common.config.bilConfig$$EnhancerBySpringCGLIB$$a51baa3f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:45.391 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'bilPersistenceUnit'
07/31/2025 14:42:45.391 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: bilPersistenceUnit
	...]
07/31/2025 14:42:48.325 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:42:48.338 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:42:51.738 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'bilPersistenceUnit'
07/31/2025 14:42:51.738 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'bilEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:51.738 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'bilEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:51.839 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'chkDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:51.844 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'chkConfig' of type [com.hisun.tms.common.config.ChkConfig$$EnhancerBySpringCGLIB$$457e8c20] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:42:51.853 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'chkPersistenceUnit'
07/31/2025 14:42:51.853 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: chkPersistenceUnit
	...]
07/31/2025 14:42:55.407 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:42:55.434 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:43:02.048 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'chkPersistenceUnit'
07/31/2025 14:43:02.048 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'chkEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:02.049 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'chkEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:02.149 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cmmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:02.154 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cmmConfig' of type [com.hisun.tms.common.config.CmmConfig$$EnhancerBySpringCGLIB$$82e4759d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:02.164 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'cmmPersistenceUnit'
07/31/2025 14:43:02.164 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cmmPersistenceUnit
	...]
07/31/2025 14:43:05.006 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:43:05.028 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#64864875' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:05.028 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#64864875' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:05.049 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:43:21.792 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'cmmPersistenceUnit'
07/31/2025 14:43:21.792 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cmmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:21.794 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cmmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:21.894 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpiDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:21.899 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpiConfig' of type [com.hisun.tms.common.config.CpiConfig$$EnhancerBySpringCGLIB$$6ca0ad56] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:21.909 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'cpiPersistenceUnit'
07/31/2025 14:43:21.909 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpiPersistenceUnit
	...]
07/31/2025 14:43:24.736 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:43:24.744 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:43:25.708 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'cpiPersistenceUnit'
07/31/2025 14:43:25.708 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpiEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:25.708 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpiEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:25.807 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:25.813 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpmConfig' of type [com.hisun.tms.common.config.CpmConfig$$EnhancerBySpringCGLIB$$adc8c6da] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:25.820 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'cpmPersistenceUnit'
07/31/2025 14:43:25.820 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpmPersistenceUnit
	...]
07/31/2025 14:43:28.039 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:43:28.045 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#64864875' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:28.045 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#64864875' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:28.048 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:43:29.523 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'cpmPersistenceUnit'
07/31/2025 14:43:29.524 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:29.525 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:29.627 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cshDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:29.632 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cshConfig' of type [com.hisun.tms.common.config.CshConfig$$EnhancerBySpringCGLIB$$473af832] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:29.639 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'tamPersistenceUnit'
07/31/2025 14:43:29.639 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 14:43:31.809 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:43:31.816 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#64864875' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:31.816 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#64864875' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:31.819 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:43:33.081 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'tamPersistenceUnit'
07/31/2025 14:43:33.081 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cshEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:33.082 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cshEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:33.181 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'csmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:33.186 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'csmConfig' of type [com.hisun.tms.common.config.CsmConfig$$EnhancerBySpringCGLIB$$d8ad1817] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:33.193 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'csmPersistenceUnit'
07/31/2025 14:43:33.194 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: csmPersistenceUnit
	...]
07/31/2025 14:43:35.740 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:43:35.754 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:43:39.170 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'csmPersistenceUnit'
07/31/2025 14:43:39.170 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'csmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:39.171 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'csmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:39.263 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'demoDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:39.268 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'demoConfig' of type [com.hisun.tms.common.config.DemoConfig$$EnhancerBySpringCGLIB$$a2c841b7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:39.276 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'demoPersistenceUnit'
07/31/2025 14:43:39.276 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: demoPersistenceUnit
	...]
07/31/2025 14:43:41.543 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:43:41.551 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#64864875' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:41.552 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#64864875' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:41.554 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:43:42.559 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'demoPersistenceUnit'
07/31/2025 14:43:42.559 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'demoEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:42.560 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'demoEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:42.677 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'invDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:42.683 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'invConfig' of type [com.hisun.tms.common.config.InvConfig$$EnhancerBySpringCGLIB$$71c1218b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:42.693 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'invPersistenceUnit'
07/31/2025 14:43:42.693 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: invPersistenceUnit
	...]
07/31/2025 14:43:45.668 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:43:45.679 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#64864875' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:45.679 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#64864875' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:45.688 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:43:51.712 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'invPersistenceUnit'
07/31/2025 14:43:51.712 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'invEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:51.713 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'invEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:51.836 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mkmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:51.843 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mkmConfig' of type [com.hisun.tms.common.config.mkmConfig$$EnhancerBySpringCGLIB$$c808a389] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:51.854 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'mkmPersistenceUnit'
07/31/2025 14:43:51.854 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: mkmPersistenceUnit
	...]
07/31/2025 14:43:54.345 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:43:54.361 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:43:57.720 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'mkmPersistenceUnit'
07/31/2025 14:43:57.720 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mkmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:57.721 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mkmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:57.847 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'onrDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:57.853 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'onrConfig' of type [com.hisun.tms.common.config.OnrConfig$$EnhancerBySpringCGLIB$$93e4b4cd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:43:57.863 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'onrPersistenceUnit'
07/31/2025 14:43:57.863 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: onrPersistenceUnit
	...]
07/31/2025 14:44:00.253 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:44:00.261 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#64864875' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:00.262 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#64864875' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:00.265 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:44:04.021 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'onrPersistenceUnit'
07/31/2025 14:44:04.021 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'onrEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:04.022 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'onrEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:04.145 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rptDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:04.151 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rptConfig' of type [com.hisun.tms.common.config.rptConfig$$EnhancerBySpringCGLIB$$944a6790] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:04.161 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'rptPersistenceUnit'
07/31/2025 14:44:04.161 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: rptPersistenceUnit
	...]
07/31/2025 14:44:06.635 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:44:06.647 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:44:09.438 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'rptPersistenceUnit'
07/31/2025 14:44:09.439 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rptEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:09.439 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rptEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:09.563 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rsmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:09.569 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rsmConfig' of type [com.hisun.tms.common.config.rsmConfig$$EnhancerBySpringCGLIB$$8d288c26] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:09.580 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'rsmPersistenceUnit'
07/31/2025 14:44:09.580 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: rsmPersistenceUnit
	...]
07/31/2025 14:44:11.790 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:44:11.817 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:44:23.320 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'rsmPersistenceUnit'
07/31/2025 14:44:23.320 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rsmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:23.321 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rsmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:23.422 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tamDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:23.427 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tamConfig' of type [com.hisun.tms.common.config.TamConfig$$EnhancerBySpringCGLIB$$9b5444da] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:23.434 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'tamPersistenceUnit'
07/31/2025 14:44:23.435 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 14:44:23.523 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 14:44:23.817 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 14:44:24.107 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 14:44:24.397 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 14:44:24.677 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 14:44:25.018 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 14:44:25.278 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 14:44:25.497 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 14:44:25.778 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 14:44:26.038 WARN  [main]o.a.t.j.p.PooledConnection - Not loading a JDBC driver as driverClassName property is null.
07/31/2025 14:44:26.297 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:44:26.303 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:44:26.339 WARN  [main]o.h.j.i.EntityManagerFactoryRegistry - HHH000436: Entity manager factory name (tamPersistenceUnit) is already registered.  If entity manager will be clustered or passivated, specify a unique value for property 'hibernate.ejb.entitymanager_factory_name'
07/31/2025 14:44:26.339 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'tamPersistenceUnit'
07/31/2025 14:44:26.339 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tamEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:26.340 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tamEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:26.441 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tfmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:26.445 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tfmConfig' of type [com.hisun.tms.common.config.TfmConfig$$EnhancerBySpringCGLIB$$38262195] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:26.453 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'tfmPersistenceUnit'
07/31/2025 14:44:26.453 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tfmPersistenceUnit
	...]
07/31/2025 14:44:29.327 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:44:29.335 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#64864875' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:29.335 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#64864875' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:29.339 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:44:33.437 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'tfmPersistenceUnit'
07/31/2025 14:44:33.437 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tfmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:33.437 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tfmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:33.536 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'urmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:33.541 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'urmConfig' of type [com.hisun.tms.common.config.urmConfig$$EnhancerBySpringCGLIB$$5d79cca] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:33.552 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'urmPersistenceUnit'
07/31/2025 14:44:33.552 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: urmPersistenceUnit
	...]
07/31/2025 14:44:36.286 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:44:36.304 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#64864875' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:36.304 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#64864875' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:36.320 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:44:55.693 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'urmPersistenceUnit'
07/31/2025 14:44:55.693 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'urmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:55.694 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'urmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:55.964 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jpaMappingContext' of type [org.springframework.data.jpa.repository.config.JpaMetamodelMappingContextFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:55.965 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jpaMappingContext' of type [org.springframework.data.jpa.mapping.JpaMetamodelMappingContext] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:56.073 INFO  [main]o.h.h.i.QueryTranslatorFactoryInitiator - HHH000397: Using ASTQueryTranslatorFactory
07/31/2025 14:44:56.121 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRepository' of type [org.springframework.data.jpa.datatables.repository.DataTablesRepositoryFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:56.123 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRepository' of type [com.sun.proxy.$Proxy176] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:56.137 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'getPermissionEvaluator' of type [com.hisun.tms.common.security.TmsPermissionEvaluator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:56.137 INFO  [main]c.h.t.c.c.WebMvcConfig - DefaultMethodSecurityExpressionHandler has benn set custom TMSPermissionEvaluator.
07/31/2025 14:44:56.138 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'getExpressionHandler' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:56.141 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration' of type [org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration$$EnhancerBySpringCGLIB$$8bf695ac] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:56.149 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:56.156 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.netflix.metrics.MetricsInterceptorConfiguration$MetricsRestTemplateConfiguration' of type [org.springframework.cloud.netflix.metrics.MetricsInterceptorConfiguration$MetricsRestTemplateConfiguration$$EnhancerBySpringCGLIB$$a0d101] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:56.182 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationGlobalProperties' of type [org.springframework.beans.factory.config.PropertiesFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:56.183 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'integrationGlobalProperties' of type [java.util.Properties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:56.199 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$ea8e2dbd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:44:56.467 INFO  [main]o.s.b.c.e.t.TomcatEmbeddedServletContainer - Tomcat initialized with port(s): 9020 (http)
07/31/2025 14:44:56.473 INFO  [main]o.a.c.c.StandardService - Starting service [Tomcat]
07/31/2025 14:44:56.474 INFO  [main]o.a.c.c.StandardEngine - Starting Servlet Engine: Apache Tomcat/8.5.15
07/31/2025 14:44:56.523 INFO  [localhost-startStop-1]o.a.c.c.C.[.[.[/] - Initializing Spring embedded WebApplicationContext
07/31/2025 14:44:56.523 INFO  [localhost-startStop-1]o.s.w.c.ContextLoader - Root WebApplicationContext: initialization completed in 160763 ms
07/31/2025 14:44:56.859 DEBUG [localhost-startStop-1]o.hibernate.SQL - select role0_.role_id as role_id1_2_, role0_.branch_name as branch_n2_2_, role0_.office_name as office_n3_2_, role0_.role as role4_2_, role0_.role_name as role_nam5_2_, role0_.status as status6_2_ from role role0_
07/31/2025 14:44:57.073 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 14:44:57.076 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [1]
07/31/2025 14:44:57.215 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 14:44:57.216 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [2]
07/31/2025 14:44:57.344 DEBUG [localhost-startStop-1]o.hibernate.SQL - select resources0_.role_id as role_id1_3_0_, resources0_.resource_id as resource2_3_0_, resource1_.resource_id as resource1_1_1_, resource1_.resource_name as resource2_1_1_, resource1_.parent_id as parent_i3_1_1_, resource1_.resource as resource4_1_1_ from role_resource resources0_ inner join resource resource1_ on resources0_.resource_id=resource1_.resource_id where resources0_.role_id=?
07/31/2025 14:44:57.344 TRACE [localhost-startStop-1]o.h.t.d.s.BasicBinder - binding parameter [1] as [INTEGER] - [3]
07/31/2025 14:44:57.447 DEBUG [localhost-startStop-1]c.h.t.c.s.TmsFilterSecurityInterceptor - Validated configuration attributes
07/31/2025 14:44:57.621 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'metricsFilter' to: [/*]
07/31/2025 14:44:57.621 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'characterEncodingFilter' to: [/*]
07/31/2025 14:44:57.621 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
07/31/2025 14:44:57.621 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'httpPutFormContentFilter' to: [/*]
07/31/2025 14:44:57.621 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'requestContextFilter' to: [/*]
07/31/2025 14:44:57.622 INFO  [localhost-startStop-1]o.s.b.w.s.DelegatingFilterProxyRegistrationBean - Mapping filter: 'springSecurityFilterChain' to: [/*]
07/31/2025 14:44:57.622 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'webRequestLoggingFilter' to: [/*]
07/31/2025 14:44:57.622 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'tmsFilterSecurityInterceptor' to: [/*]
07/31/2025 14:44:57.622 INFO  [localhost-startStop-1]o.s.b.w.s.FilterRegistrationBean - Mapping filter: 'applicationContextIdFilter' to: [/*]
07/31/2025 14:44:57.622 INFO  [localhost-startStop-1]o.s.b.w.s.ServletRegistrationBean - Mapping servlet: 'dispatcherServlet' to [/]
07/31/2025 14:44:57.775 WARN  [main]o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'accountAddressController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'accountAddressService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'IAccountAddressDao' defined in file [E:\Cele4TeR\Project\mcht-be\tms\build\classes\java\main\com\hisun\tms\acm\dao\IAccountAddressDao.class]: Cannot resolve reference to bean 'acmSqlSessionTemplate' while setting bean property 'sqlSessionTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'acmSqlSessionTemplate' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Unsatisfied dependency expressed through method 'cpoSqlSessionTemplate' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acmSqlSessionFactory' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'cpoSqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
07/31/2025 14:44:57.775 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'urmPersistenceUnit'
07/31/2025 14:44:57.777 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'tfmPersistenceUnit'
07/31/2025 14:44:57.778 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'tamPersistenceUnit'
07/31/2025 14:44:57.779 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'rsmPersistenceUnit'
07/31/2025 14:44:57.809 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'rptPersistenceUnit'
07/31/2025 14:44:57.809 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'onrPersistenceUnit'
07/31/2025 14:44:57.810 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'mkmPersistenceUnit'
07/31/2025 14:44:57.810 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'invPersistenceUnit'
07/31/2025 14:44:57.811 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'demoPersistenceUnit'
07/31/2025 14:44:57.812 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'csmPersistenceUnit'
07/31/2025 14:44:57.812 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'tamPersistenceUnit'
07/31/2025 14:44:57.813 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'cpmPersistenceUnit'
07/31/2025 14:44:57.813 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'cpiPersistenceUnit'
07/31/2025 14:44:57.814 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'cmmPersistenceUnit'
07/31/2025 14:44:57.815 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'chkPersistenceUnit'
07/31/2025 14:44:57.815 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'bilPersistenceUnit'
07/31/2025 14:44:57.816 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'acmPersistenceUnit'
07/31/2025 14:44:57.817 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'tmsPersistenceUnit'
07/31/2025 14:44:58.104 INFO  [main]o.a.c.c.StandardService - Stopping service [Tomcat]
07/31/2025 14:44:58.117 INFO  [main]o.s.b.a.l.AutoConfigurationReportLoggingInitializer - 

Error starting ApplicationContext. To display the auto-configuration report re-run your application with 'debug' enabled.
07/31/2025 14:44:58.257 ERROR [main]o.s.b.SpringApplication - Application startup failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'accountAddressController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'accountAddressService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'IAccountAddressDao' defined in file [E:\Cele4TeR\Project\mcht-be\tms\build\classes\java\main\com\hisun\tms\acm\dao\IAccountAddressDao.class]: Cannot resolve reference to bean 'acmSqlSessionTemplate' while setting bean property 'sqlSessionTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'acmSqlSessionTemplate' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Unsatisfied dependency expressed through method 'cpoSqlSessionTemplate' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acmSqlSessionFactory' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'cpoSqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessPropertyValues(CommonAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1264)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:553)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:761)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:867)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:543)
	at org.springframework.boot.context.embedded.EmbeddedWebApplicationContext.refresh(EmbeddedWebApplicationContext.java:122)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:693)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:360)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1118)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1107)
	at com.hisun.tms.Application.main(Application.java:35)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'accountAddressService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'IAccountAddressDao' defined in file [E:\Cele4TeR\Project\mcht-be\tms\build\classes\java\main\com\hisun\tms\acm\dao\IAccountAddressDao.class]: Cannot resolve reference to bean 'acmSqlSessionTemplate' while setting bean property 'sqlSessionTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'acmSqlSessionTemplate' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Unsatisfied dependency expressed through method 'cpoSqlSessionTemplate' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acmSqlSessionFactory' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'cpoSqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessPropertyValues(CommonAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1264)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:553)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:522)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:496)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:627)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:169)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:88)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessPropertyValues(CommonAnnotationBeanPostProcessor.java:318)
	... 17 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'IAccountAddressDao' defined in file [E:\Cele4TeR\Project\mcht-be\tms\build\classes\java\main\com\hisun\tms\acm\dao\IAccountAddressDao.class]: Cannot resolve reference to bean 'acmSqlSessionTemplate' while setting bean property 'sqlSessionTemplate'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'acmSqlSessionTemplate' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Unsatisfied dependency expressed through method 'cpoSqlSessionTemplate' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acmSqlSessionFactory' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'cpoSqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:359)
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveValueIfNecessary(BeanDefinitionValueResolver.java:108)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyPropertyValues(AbstractAutowireCapableBeanFactory.java:1531)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1276)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:553)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1138)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1066)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:518)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:496)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:627)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:169)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:88)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessPropertyValues(CommonAnnotationBeanPostProcessor.java:318)
	... 30 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'acmSqlSessionTemplate' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Unsatisfied dependency expressed through method 'cpoSqlSessionTemplate' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acmSqlSessionFactory' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'cpoSqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:749)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:467)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1173)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1067)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:513)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:351)
	... 48 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acmSqlSessionFactory' defined in class path resource [com/hisun/tms/common/mybatiesConfig/AcmMyBatisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'cpoSqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1173)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1067)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:513)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:483)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1138)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1066)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:835)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:741)
	... 58 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'cpoSqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:189)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:588)
	... 71 common frames omitted
Caused by: org.springframework.core.NestedIOException: Failed to parse config resource: class path resource [mybatis/mybatis-query-config.xml]; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.mybatis.spring.SqlSessionFactoryBean.buildSqlSessionFactory(SqlSessionFactoryBean.java:500)
	at org.mybatis.spring.SqlSessionFactoryBean.afterPropertiesSet(SqlSessionFactoryBean.java:380)
	at org.mybatis.spring.SqlSessionFactoryBean.getObject(SqlSessionFactoryBean.java:547)
	at com.hisun.tms.common.mybatiesConfig.AcmMyBatisConfig.cpoSqlSessionFactory(AcmMyBatisConfig.java:39)
	at com.hisun.tms.common.mybatiesConfig.AcmMyBatisConfig$$EnhancerBySpringCGLIB$$cc4ba4ae.CGLIB$cpoSqlSessionFactory$0(<generated>)
	at com.hisun.tms.common.mybatiesConfig.AcmMyBatisConfig$$EnhancerBySpringCGLIB$$cc4ba4ae$$FastClassBySpringCGLIB$$b13b2a55.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:228)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:358)
	at com.hisun.tms.common.mybatiesConfig.AcmMyBatisConfig$$EnhancerBySpringCGLIB$$cc4ba4ae.cpoSqlSessionFactory(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:162)
	... 72 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error parsing SQL Mapper Configuration. Cause: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.apache.ibatis.builder.xml.XMLConfigBuilder.parseConfiguration(XMLConfigBuilder.java:120)
	at org.apache.ibatis.builder.xml.XMLConfigBuilder.parse(XMLConfigBuilder.java:98)
	at org.mybatis.spring.SqlSessionFactoryBean.buildSqlSessionFactory(SqlSessionFactoryBean.java:494)
	... 85 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error registering typeAlias for 'DmTransferCallbackDO'. Cause: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.apache.ibatis.builder.xml.XMLConfigBuilder.typeAliasesElement(XMLConfigBuilder.java:170)
	at org.apache.ibatis.builder.xml.XMLConfigBuilder.parseConfiguration(XMLConfigBuilder.java:108)
	... 87 common frames omitted
Caused by: java.lang.ClassNotFoundException: Cannot find class: com.hisun.tms.cpt.entity.DmTransferCallbackDO
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:200)
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:89)
	at org.apache.ibatis.io.Resources.classForName(Resources.java:261)
	at org.apache.ibatis.builder.xml.XMLConfigBuilder.typeAliasesElement(XMLConfigBuilder.java:163)
	... 88 common frames omitted
07/31/2025 14:46:18.230 INFO  [main]c.h.t.Application - The following profiles are active: dev
07/31/2025 14:46:18.246 INFO  [main]o.s.b.c.e.AnnotationConfigEmbeddedWebApplicationContext - Refreshing org.springframework.boot.context.embedded.AnnotationConfigEmbeddedWebApplicationContext@4a1c0752: startup date [Thu Jul 31 14:46:18 CST 2025]; parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@6392827e
07/31/2025 14:46:19.341 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:46:19.379 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:46:19.385 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:46:19.394 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:46:19.432 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:46:19.437 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:46:19.443 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:46:19.451 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:46:19.457 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:46:19.463 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:46:19.474 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:46:19.482 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:46:19.491 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:46:19.500 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:46:19.524 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:46:19.525 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:46:19.529 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.DatatablesTfmRateRuleRepository.
07/31/2025 14:46:19.535 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:46:19.553 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:46:19.591 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'bilTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=bilConfig; factoryMethodName=demoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/bilConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=bilMyBatisConfig; factoryMethodName=cpoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/BilMyBatisConfig.class]]
07/31/2025 14:46:19.596 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'invTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=invConfig; factoryMethodName=demoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/InvConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=invMyBatisConfig; factoryMethodName=invTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/InvMyBatisConfig.class]]
07/31/2025 14:46:19.601 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICardBinDao' and 'com.hisun.tms.cpt.dao.cpi.ICardBinDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:46:19.601 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICardProtDao' and 'com.hisun.tms.cpt.dao.cpi.ICardProtDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:46:19.601 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpiOrgnBusiDao' and 'com.hisun.tms.cpt.dao.cpi.ICpiOrgnBusiDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:46:19.601 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpiOrgnInfoDao' and 'com.hisun.tms.cpt.dao.cpi.ICpiOrgnInfoDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:46:19.601 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpiOrgnRouteDao' and 'com.hisun.tms.cpt.dao.cpi.ICpiOrgnRouteDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:46:19.601 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'IFundOrderDao' and 'com.hisun.tms.cpt.dao.cpi.IFundOrderDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:46:19.601 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'IRefundOrderDao' and 'com.hisun.tms.cpt.dao.cpi.IRefundOrderDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:46:19.601 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ISettleDetailDao' and 'com.hisun.tms.cpt.dao.cpi.ISettleDetailDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:46:19.601 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpoOrgnBusiDao' and 'com.hisun.tms.cpt.dao.cpo.ICpoOrgnBusiDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:46:19.601 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpoOrgnInfoDao' and 'com.hisun.tms.cpt.dao.cpo.ICpoOrgnInfoDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:46:19.601 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'ICpoOrgnRouteDao' and 'com.hisun.tms.cpt.dao.cpo.ICpoOrgnRouteDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:46:19.601 WARN  [main]o.m.s.m.ClassPathMapperScanner - Skipping MapperFactoryBean with name 'IWithdrawOrderDao' and 'com.hisun.tms.cpt.dao.cpo.IWithdrawOrderDao' mapperInterface. Bean already defined with the same name!
07/31/2025 14:46:19.602 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'tamTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tamConfig; factoryMethodName=tamTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/TamConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tamMyBatisConfig; factoryMethodName=tamTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/TamMyBatisConfig.class]]
07/31/2025 14:46:19.603 INFO  [main]o.s.b.f.s.DefaultListableBeanFactory - Overriding bean definition for bean 'tfmTransactionManager' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tfmConfig; factoryMethodName=demoTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/config/TfmConfig.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=tfmMyBatisConfig; factoryMethodName=tfmTransactionManager; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/hisun/tms/common/mybatiesConfig/TfmMyBatisConfig.class]]
07/31/2025 14:46:20.061 INFO  [main]o.s.i.c.IntegrationRegistrar - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
07/31/2025 14:46:20.118 INFO  [main]o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07/31/2025 14:46:20.346 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.demo.repository.DatatablesExampleRepository.
07/31/2025 14:46:20.346 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.MercItfRepository.
07/31/2025 14:46:20.346 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.OfficeInfoRepository.
07/31/2025 14:46:20.346 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.DatatablesRoleRepository.
07/31/2025 14:46:20.346 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmAcBalRepository.
07/31/2025 14:46:20.347 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesRuleListRepository.
07/31/2025 14:46:20.347 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.WhiteListRepository.
07/31/2025 14:46:20.347 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.chk.repository.ChkErrorRepository.
07/31/2025 14:46:20.347 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DataTablesMercExtInfoRepository.
07/31/2025 14:46:20.347 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.TfmFeeOrderRepository.
07/31/2025 14:46:20.347 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csh.repository.CshOrderRepository.
07/31/2025 14:46:20.347 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.DatatablesProInfoRepository.
07/31/2025 14:46:20.348 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesPhoneSegementInfoRepository.
07/31/2025 14:46:20.348 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.demo.repository.ExampleRepository.
07/31/2025 14:46:20.348 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rpt.repository.RptRsRepository.
07/31/2025 14:46:20.348 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmAdjustInfRepository.
07/31/2025 14:46:20.348 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.ConstantParamsRepository.
07/31/2025 14:46:20.348 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.onr.repository.DatatablesOnrRfdOrderRepository.
07/31/2025 14:46:20.348 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.MercRegisterSeqRepository.
07/31/2025 14:46:20.349 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.UserRepository.
07/31/2025 14:46:20.349 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.RoleRepository.
07/31/2025 14:46:20.349 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesCampaignInfoRepository.
07/31/2025 14:46:20.349 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesWhiteListRepository.
07/31/2025 14:46:20.349 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.CheckRuleRepository.
07/31/2025 14:46:20.349 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.onr.repository.OnrRfdOrderRepository.
07/31/2025 14:46:20.350 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UrmCprExtInfRespository.
07/31/2025 14:46:20.350 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UserBaseInfoRepository.
07/31/2025 14:46:20.350 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DatatablesAcmItmPropertyRepository.
07/31/2025 14:46:20.350 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.bil.repository.DataTablesMerchantOrderInfoRepository.
07/31/2025 14:46:20.350 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DatatablesUserHistoryRepository.
07/31/2025 14:46:20.350 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.DataTableMercRegisterRepository.
07/31/2025 14:46:20.350 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.mkm.repository.BatchFileRecRepository.
07/31/2025 14:46:20.350 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.RiskRuleRepository.
07/31/2025 14:46:20.351 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.SafeInfoRespository.
07/31/2025 14:46:20.351 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesSmsTemplateInfoRepository.
07/31/2025 14:46:20.351 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csm.repository.SettleBaseDORespository.
07/31/2025 14:46:20.351 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.DatatablesTfmFeeOrderRepository.
07/31/2025 14:46:20.351 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DatatablesMercSafelInfoRepository.
07/31/2025 14:46:20.351 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.onr.repository.DatatablesOnrOrderRepository.
07/31/2025 14:46:20.351 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.chk.repository.DatatablesChkErrorRepository.
07/31/2025 14:46:20.351 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UserInfoRepository.
07/31/2025 14:46:20.352 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesBannerInfoRepository.
07/31/2025 14:46:20.352 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesConstantParamsRepository.
07/31/2025 14:46:20.352 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.TmsUserOprLogRepository.
07/31/2025 14:46:20.352 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cpm.repository.DatatablesCpmOrderRepository.
07/31/2025 14:46:20.352 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.NoticeInfoRepository.
07/31/2025 14:46:20.353 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.onr.repository.OnrOrderRepository.
07/31/2025 14:46:20.353 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.mkm.repository.MkmAtvUserRespository.
07/31/2025 14:46:20.353 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesNoticeInfoRepository.
07/31/2025 14:46:20.353 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.SmsTemplateInfoRepository.
07/31/2025 14:46:20.353 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesCheckRuleRepository.
07/31/2025 14:46:20.353 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.TmsOprMapperRespository.
07/31/2025 14:46:20.353 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.PhoneSegementInfoRepository.
07/31/2025 14:46:20.353 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.CampaignInfoRepository.
07/31/2025 14:46:20.354 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesBlackListRepository.
07/31/2025 14:46:20.354 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DataTablesAcmAcDetailRepository.
07/31/2025 14:46:20.354 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.MercExtInfoRepository.
07/31/2025 14:46:20.354 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.DatatablesTfmRateRuleRepository.
07/31/2025 14:46:20.354 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.BannerInfoRepository.
07/31/2025 14:46:20.354 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.DatatablesOrderRepository.
07/31/2025 14:46:20.354 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmItmInfRepository.
07/31/2025 14:46:20.354 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.UserRoleRespository.
07/31/2025 14:46:20.354 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.bil.repository.DataTablesOrderInfoRepository.
07/31/2025 14:46:20.355 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesAreaInfoRepository.
07/31/2025 14:46:20.355 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.MercInfoRepository.
07/31/2025 14:46:20.355 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.chk.repository.ChkControlRepository.
07/31/2025 14:46:20.355 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.MercSafelInfoRepository.
07/31/2025 14:46:20.355 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmAdjustDetailRepository.
07/31/2025 14:46:20.355 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cpt.repository.CpiSubMercCastRepository.
07/31/2025 14:46:20.355 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.MessageTemplateInfoRepository.
07/31/2025 14:46:20.355 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DatatablesMercInfoRepository.
07/31/2025 14:46:20.355 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DatatablesAcmItmInfRepository.
07/31/2025 14:46:20.356 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesRiskRuleRepository.
07/31/2025 14:46:20.356 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesHighRiskRepository.
07/31/2025 14:46:20.356 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmAcInfRepository.
07/31/2025 14:46:20.356 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.DateRateRepository.
07/31/2025 14:46:20.356 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.EmailTemplateInfoRepository.
07/31/2025 14:46:20.356 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csh.repository.PaytypeRepository.
07/31/2025 14:46:20.356 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesOfficeInfoRepository.
07/31/2025 14:46:20.356 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UrmKybSeqRepository.
07/31/2025 14:46:20.357 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csh.repository.DatatablesPayTypeRepository.
07/31/2025 14:46:20.357 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.DataTablesRuleParamRepository.
07/31/2025 14:46:20.357 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.RsmUserStatusRepository.
07/31/2025 14:46:20.357 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.UrmKybCertInfRepository.
07/31/2025 14:46:20.357 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.SafeLoginRespository.
07/31/2025 14:46:20.357 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.AcmItmPropertyRepository.
07/31/2025 14:46:20.357 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.BlackListRepository.
07/31/2025 14:46:20.357 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.DatatablesMercItfInfoRepository.
07/31/2025 14:46:20.358 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cpm.repository.CpmOrderRepository.
07/31/2025 14:46:20.358 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesEmailTemplateInfoRepository.
07/31/2025 14:46:20.358 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesBranchInfoRepository.
07/31/2025 14:46:20.358 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.AreaInfoRepository.
07/31/2025 14:46:20.358 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.ProInfoRepository.
07/31/2025 14:46:20.358 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.mkm.repository.SeaccyDetailRepository.
07/31/2025 14:46:20.358 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.HighRiskRepository.
07/31/2025 14:46:20.358 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DataTablesAdjustInfRepository.
07/31/2025 14:46:20.358 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rpt.repository.DataTablesRptRsRepository.
07/31/2025 14:46:20.359 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rsm.repository.RuleParamRepository.
07/31/2025 14:46:20.359 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.OrderRepository.
07/31/2025 14:46:20.359 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.BranchInfoRepository.
07/31/2025 14:46:20.359 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.urm.repository.CprResInfRespository.
07/31/2025 14:46:20.359 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.inv.repository.InvUserInfoRepository.
07/31/2025 14:46:20.359 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.acm.repository.DataTablesAdjustDetailRepository.
07/31/2025 14:46:20.359 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.DatatablesUserRepository.
07/31/2025 14:46:20.359 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rpt.repository.DataTablesRptRepository.
07/31/2025 14:46:20.359 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.tfm.repository.MerchantRateRuleRespository.
07/31/2025 14:46:20.360 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.chk.repository.DatatablesChkControlRepository.
07/31/2025 14:46:20.360 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.csm.repository.SettleCardRespository.
07/31/2025 14:46:20.360 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.sys.repository.ResourceRepository.
07/31/2025 14:46:20.360 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.cmm.repository.DatatablesMessageTemplateInfoRepository.
07/31/2025 14:46:20.360 INFO  [main]o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.hisun.tms.rpt.repository.RptRepository.
07/31/2025 14:46:20.553 INFO  [main]o.s.c.c.s.GenericScope - BeanFactory id=d6db338d-1ef3-3f6f-ab3e-2040c76f77b0
07/31/2025 14:46:20.578 INFO  [main]o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
07/31/2025 14:46:20.584 INFO  [main]o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor - No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created.
07/31/2025 14:46:20.595 INFO  [main]o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
07/31/2025 14:46:20.664 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.tam.client.ExchangeOrderClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.664 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.tam.client.TransferOrderClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.665 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.BankClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.666 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.CardClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.666 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.CheckClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.667 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.CregisClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.667 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.EbankpayClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.668 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.FastpayClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.668 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.PosClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.668 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.RefundClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.669 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.RemittanceClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.669 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpi.client.RouteClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.670 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.csh.client.CshAuditTransferOrderClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.671 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.csh.client.CshOrderClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.671 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.csh.client.CshRefundClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.673 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskCheckClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.674 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskCheckUserStatusClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.674 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskListMngClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.674 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskParamMngClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.675 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.rsm.client.RiskRuleMngClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.676 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.CmmServerClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.676 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.ConstantParamClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.677 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.GrantClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.677 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.QRCodeServerClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.677 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cmm.client.SmsServerClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.678 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpo.client.CopAgcyBizClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.678 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpo.client.RouteClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.679 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.cpo.client.WithdrawClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.680 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.urm.client.UserAuthenticationClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.680 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.urm.client.UserBasicInfClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.681 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.urm.client.UserBasicInfClient1' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.681 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.urm.client.UserPasswordClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.682 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.acm.client.AccountingTreatmentClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.682 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.acm.client.AccountManagementClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.683 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.mkm.client.MarketActivityClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.684 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.mkm.client.MarketingToolsMngClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.685 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OrderCancleClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.685 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OrderRefundClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.685 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.ConfigureClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.686 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OrderQueryClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.686 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OnrCheckedHandleClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.686 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.OrderRegistratClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.686 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.onr.client.NotifySendClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.688 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.hisun.lemon.chk.client.ChkClient' of type [org.springframework.cloud.netflix.feign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.722 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration' of type [org.springframework.amqp.rabbit.annotation.RabbitBootstrapConfiguration$$EnhancerBySpringCGLIB$$e27b359b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.832 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$1c0f776d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.988 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration' of type [org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration$$EnhancerBySpringCGLIB$$b4bd3fa7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:20.999 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'objectPostProcessor' of type [org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:21.002 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@20afd96f' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:21.016 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'webMvcConfig' of type [com.hisun.tms.common.config.WebMvcConfig$$EnhancerBySpringCGLIB$$417c1047] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:21.042 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#5151accb' of type [org.springframework.beans.factory.config.PropertiesFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:21.043 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#5151accb' of type [java.util.Properties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:21.049 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#64f6dd19' of type [org.springframework.data.repository.core.support.PropertiesBasedNamedQueries] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:21.053 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#7305cfb1' of type [org.springframework.data.repository.query.ExtensionAwareEvaluationContextProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:21.091 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSourceConfig' of type [com.hisun.tms.common.config.DataSourceConfig$$EnhancerBySpringCGLIB$$61f043e6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:21.256 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tmsDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:21.282 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.JpaProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:21.284 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tmsConfig' of type [com.hisun.tms.common.config.TmsConfig$$EnhancerBySpringCGLIB$$763cfcc1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:21.308 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration' of type [org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$$EnhancerBySpringCGLIB$$b0837277] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:21.316 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties' of type [org.springframework.boot.autoconfigure.transaction.TransactionProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:21.323 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'platformTransactionManagerCustomizers' of type [org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:21.328 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration$$EnhancerBySpringCGLIB$$8595db8d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:23.635 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jpaVendorAdapter' of type [org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:23.640 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'entityManagerFactoryBuilder' of type [org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:23.657 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'tmsPersistenceUnit'
07/31/2025 14:46:23.665 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tmsPersistenceUnit
	...]
07/31/2025 14:46:23.707 INFO  [main]o.h.Version - HHH000412: Hibernate Core {5.0.12.Final}
07/31/2025 14:46:23.708 INFO  [main]o.h.c.Environment - HHH000206: hibernate.properties not found
07/31/2025 14:46:23.709 INFO  [main]o.h.c.Environment - HHH000021: Bytecode provider name : javassist
07/31/2025 14:46:23.741 INFO  [main]o.h.a.c.Version - HCANN000001: Hibernate Commons Annotations {5.0.1.Final}
07/31/2025 14:46:23.915 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:46:24.075 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 14:46:24.076 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.sys.model.UserRoleDoPK
07/31/2025 14:46:24.257 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:46:30.338 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'tmsPersistenceUnit'
07/31/2025 14:46:30.340 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tmsEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:30.342 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'tmsEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:30.351 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#3883b5e9' of type [com.sun.proxy.$Proxy163] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:30.465 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'acmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:30.471 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'acmConfig' of type [com.hisun.tms.common.config.AcmConfig$$EnhancerBySpringCGLIB$$cb182492] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:30.481 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'acmPersistenceUnit'
07/31/2025 14:46:30.481 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: acmPersistenceUnit
	...]
07/31/2025 14:46:33.190 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:46:33.208 WARN  [main]o.h.m.RootClass - HHH000038: Composite-id class does not override equals(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 14:46:33.208 WARN  [main]o.h.m.RootClass - HHH000039: Composite-id class does not override hashCode(): com.hisun.tms.acm.model.PrimaryKeyAcmAcBal
07/31/2025 14:46:33.227 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#2bab1821' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:33.228 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#2bab1821' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:33.254 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:46:44.165 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'acmPersistenceUnit'
07/31/2025 14:46:44.165 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'acmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:44.166 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'acmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:44.274 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'bilDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:44.279 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'bilConfig' of type [com.hisun.tms.common.config.bilConfig$$EnhancerBySpringCGLIB$$f2b6f6ec] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:44.287 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'bilPersistenceUnit'
07/31/2025 14:46:44.288 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: bilPersistenceUnit
	...]
07/31/2025 14:46:46.528 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:46:46.542 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:46:49.283 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'bilPersistenceUnit'
07/31/2025 14:46:49.283 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'bilEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:49.284 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'bilEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:49.386 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'chkDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:49.392 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'chkConfig' of type [com.hisun.tms.common.config.ChkConfig$$EnhancerBySpringCGLIB$$9319d8cd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:49.400 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'chkPersistenceUnit'
07/31/2025 14:46:49.400 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: chkPersistenceUnit
	...]
07/31/2025 14:46:52.191 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:46:52.219 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:46:59.324 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'chkPersistenceUnit'
07/31/2025 14:46:59.324 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'chkEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:59.325 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'chkEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:59.429 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cmmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:59.435 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cmmConfig' of type [com.hisun.tms.common.config.CmmConfig$$EnhancerBySpringCGLIB$$d07fc24a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:46:59.446 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'cmmPersistenceUnit'
07/31/2025 14:46:59.446 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cmmPersistenceUnit
	...]
07/31/2025 14:47:02.333 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:47:02.358 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#2bab1821' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:02.358 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#2bab1821' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:02.380 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:47:19.993 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'cmmPersistenceUnit'
07/31/2025 14:47:19.993 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cmmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:19.993 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cmmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:20.096 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpiDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:20.101 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpiConfig' of type [com.hisun.tms.common.config.CpiConfig$$EnhancerBySpringCGLIB$$ba3bfa03] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:20.111 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'cpiPersistenceUnit'
07/31/2025 14:47:20.111 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpiPersistenceUnit
	...]
07/31/2025 14:47:22.292 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:47:22.298 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:47:23.042 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'cpiPersistenceUnit'
07/31/2025 14:47:23.042 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpiEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:23.043 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpiEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:23.146 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:23.152 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpmConfig' of type [com.hisun.tms.common.config.CpmConfig$$EnhancerBySpringCGLIB$$fb641387] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:23.159 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'cpmPersistenceUnit'
07/31/2025 14:47:23.159 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: cpmPersistenceUnit
	...]
07/31/2025 14:47:25.581 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:47:25.588 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#2bab1821' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:25.588 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#2bab1821' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:25.591 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:47:27.506 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'cpmPersistenceUnit'
07/31/2025 14:47:27.506 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:27.507 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cpmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:27.627 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cshDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:27.633 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cshConfig' of type [com.hisun.tms.common.config.CshConfig$$EnhancerBySpringCGLIB$$94d644df] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:27.642 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'tamPersistenceUnit'
07/31/2025 14:47:27.642 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: tamPersistenceUnit
	...]
07/31/2025 14:47:30.237 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:47:30.244 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#2bab1821' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:30.245 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#2bab1821' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:30.248 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:47:31.537 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'tamPersistenceUnit'
07/31/2025 14:47:31.537 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cshEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:31.538 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cshEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:31.640 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'csmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:31.646 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'csmConfig' of type [com.hisun.tms.common.config.CsmConfig$$EnhancerBySpringCGLIB$$264864c4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:31.653 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'csmPersistenceUnit'
07/31/2025 14:47:31.653 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: csmPersistenceUnit
	...]
07/31/2025 14:47:34.882 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:47:34.894 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:47:38.313 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'csmPersistenceUnit'
07/31/2025 14:47:38.313 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'csmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:38.314 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'csmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:38.408 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'demoDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:38.412 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'demoConfig' of type [com.hisun.tms.common.config.DemoConfig$$EnhancerBySpringCGLIB$$f0638e64] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:38.419 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'demoPersistenceUnit'
07/31/2025 14:47:38.419 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: demoPersistenceUnit
	...]
07/31/2025 14:47:41.092 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:47:41.099 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#2bab1821' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:41.099 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#2bab1821' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:41.101 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:47:42.234 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'demoPersistenceUnit'
07/31/2025 14:47:42.234 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'demoEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:42.235 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'demoEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:42.338 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'invDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:42.343 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'invConfig' of type [com.hisun.tms.common.config.InvConfig$$EnhancerBySpringCGLIB$$bf5c6e38] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:42.350 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'invPersistenceUnit'
07/31/2025 14:47:42.350 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: invPersistenceUnit
	...]
07/31/2025 14:47:45.094 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:47:45.104 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#2bab1821' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:45.104 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#2bab1821' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:45.112 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:47:49.936 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'invPersistenceUnit'
07/31/2025 14:47:49.936 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'invEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:49.938 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'invEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:50.042 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mkmDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:50.047 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mkmConfig' of type [com.hisun.tms.common.config.mkmConfig$$EnhancerBySpringCGLIB$$15a3f036] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:50.055 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'mkmPersistenceUnit'
07/31/2025 14:47:50.055 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: mkmPersistenceUnit
	...]
07/31/2025 14:47:53.329 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:47:53.345 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
07/31/2025 14:47:56.984 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'mkmPersistenceUnit'
07/31/2025 14:47:56.984 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mkmEntityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:56.985 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mkmEntityManagerFactory' of type [com.sun.proxy.$Proxy161] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:57.089 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'onrDataSource' of type [org.apache.tomcat.jdbc.pool.DataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:57.094 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'onrConfig' of type [com.hisun.tms.common.config.OnrConfig$$EnhancerBySpringCGLIB$$e180017a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:57.101 INFO  [main]o.s.o.j.LocalContainerEntityManagerFactoryBean - Building JPA container EntityManagerFactory for persistence unit 'onrPersistenceUnit'
07/31/2025 14:47:57.101 INFO  [main]o.h.j.i.u.LogHelper - HHH000204: Processing PersistenceUnitInfo [
	name: onrPersistenceUnit
	...]
07/31/2025 14:47:59.931 INFO  [main]o.h.d.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
07/31/2025 14:47:59.939 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#2bab1821' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:59.939 INFO  [main]o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean '(inner bean)#2bab1821' of type [org.springframework.beans.factory.config.ObjectFactoryCreatingFactoryBean$TargetBeanObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
07/31/2025 14:47:59.943 INFO  [main]o.h.t.h.SchemaUpdate - HHH000228: Running hbm2ddl schema update
