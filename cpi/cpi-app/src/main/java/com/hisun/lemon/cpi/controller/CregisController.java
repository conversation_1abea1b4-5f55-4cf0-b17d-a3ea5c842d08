package com.hisun.lemon.cpi.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.cpi.cregis.CregisApi;
import com.hisun.lemon.cpi.cregis.rsp.PageVaultAccountAddressBalanceObject;
import com.hisun.lemon.cpi.cregis.rsp.VaultAccountAddressBalanceObject;
import com.hisun.lemon.cpi.dto.*;
import com.hisun.lemon.cpi.service.ICregisService;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Cregis控制层
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/14 11:38
 */
@RestController
@RequestMapping("/cpi")
@Api(tags = "CregisController", description = "Cregis控制层")
public class CregisController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(CregisController.class);

    @Resource
    private CregisApi cregisApi;

    @Resource
    private ICregisService cregisService;

    @Value("${cregis.accountDetailUrl}")
    private String accountDetailUrl;


    @ApiOperation(value = "查询金库账户对应的地址余额信息", notes = "查询金库账户对应的地址余额信息")
    @ApiResponse(code = 200, message = "查询金库账户对应的地址余额信息")
    @PostMapping("/address/list/balance")
    public CregisAccDetailRspDTO getAccountDetail(@Validated @RequestBody CregisReqDTO req) {
        log.info("查询金库账户对应的地址余额信息");

        Map<String, Object> pathParams = new HashMap<>();
        pathParams.put("vaultCode", req.getVaultCode());
        pathParams.put("groupCode", req.getGroupCode());
        pathParams.put("accountId", req.getAccountId());
        String ccy = req.getCcy();

        CregisRsp<PageVaultAccountAddressBalanceObject> response = cregisApi.doSend(
                accountDetailUrl,
                Method.POST,
                req.getCregisAccDetailReqDTO(),
                pathParams,
                new TypeReference<CregisRsp<PageVaultAccountAddressBalanceObject>>() {
                }
        );

        // 转换返回类型
        CregisAccDetailRspDTO result = new CregisAccDetailRspDTO();
        if ("500".equals(response.getCode())) {
            throw new LemonException(response.getCode(), response.getMessage());
        }
        if (response.getData() != null && response.getData().getRecords() != null
                && !response.getData().getRecords().isEmpty()) {
            Map<String, VaultAccountAddressBalanceObject> map = response.getData().getRecords().stream().collect(Collectors.toMap(item -> item.getCoinId(), item -> item));
            VaultAccountAddressBalanceObject balanceObject = map.get(ccy);
            log.info("查询金库账户对应的地址余额信息:{}", balanceObject);
            if (balanceObject != null) {
                // 精度转换
                BeanUtils.copyProperties(result, balanceObject);
                result.setBalance(cregisService.decimalChange(ccy, balanceObject.getBalance()));
            }
        }
        return result;
    }

    @ApiOperation(value = "准备付款数据", notes = "准备付款数据")
    @ApiResponse(code = 200, message = "准备付款数据")
    @PostMapping("/address/fund-flow/prepare/payment")
    public CregisRsp<TransferSubmitRspDTO> prePayment(
            @RequestBody PaymentPrepareReqDTO req
    ) {
        log.info("准备付款数据:/address/fund-flow/prepare/payment");
        CregisRsp<TransferSubmitRspDTO>  cregisRsp= cregisService.prePayment( req);
        return cregisRsp;
    }

    @ApiOperation(value = "付款数据签名", notes = "付款数据签名")
    @ApiResponse(code = 200, message = "付款数据签名")
    @PostMapping("/address/fund-flow/signTypedData")
    public CregisRsp<SignRspDTO> signData(@RequestBody SignReqDTO signReqDTO) {
        log.info("付款数据签名:/address/fund-flow/signTypedData");
        CregisRsp<SignRspDTO> response =cregisService.signData(signReqDTO);

        return response;
    }

    @ApiOperation(value = "提交转账", notes = "提交转账")
    @ApiResponse(code = 200, message = "提交转账")
    @PostMapping("/address/fund-flow/transfer")
    public CregisRsp<NoBody> submitTransfer(@RequestBody TransferSubmitReqDTO request) {
        CregisRsp<NoBody> response = cregisService.submitTransfer(request);

        return response;
    }

    @ApiOperation(value = "转账签名提交全流程", notes = "转账签名提交全流程")
    @ApiResponse(code = 200, message = "转账签名提交全流程")
    @PostMapping("/address/fund-flow/transfer/all")
    public CregisRsp<NoBody> transferAll(@RequestBody PaymentReqDTO req) {
        log.info("转账签名提交全流程:/address/fund-flow/transfer/all");
        CregisRsp<NoBody> response = cregisService.transferAll(req);
        return response;
    }

    @ApiOperation(value = "处理Cregis回调数据", notes = "处理Cregis回调数据")
    @ApiResponse(code = 200, message = "处理Cregis回调数据成功")
    @PostMapping("/cregis/callback")
    public GenericRspDTO<NoBody> cregisCallback(@RequestBody CregisCallbackBaseReqDTO req) {
        log.info("处理Cregis回调数据...");
        if(req.getCallbackType().equals("TRANSFER")) {
            CregisCallbackDTO reqDTO = JSONUtil.toBean(req.getCallbackData(), CregisCallbackDTO.class);
            if(StrUtil.length(reqDTO.getTxBase().getCreateTime()) == 19){
                reqDTO.getTxBase().setCreateTime(reqDTO.getTxBase().getCreateTime()+".000");
            }
            cregisService.handleCallback(reqDTO);
        }
        return GenericRspDTO.newSuccessInstance();
    }
}
