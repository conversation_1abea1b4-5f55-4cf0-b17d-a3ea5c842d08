package com.hisun.lemon.cpi.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hisun.lemon.acm.client.AccountManagementClient;
import com.hisun.lemon.acm.client.AccountingTreatmentClient;
import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.acm.constants.CapTypEnum;
import com.hisun.lemon.acm.dto.*;
import com.hisun.lemon.bil.client.UserBillQueryClient;
import com.hisun.lemon.bil.constants.BilConstants;
import com.hisun.lemon.bil.dto.AddOrderReqDTO;
import com.hisun.lemon.bil.dto.UpdateOrderReqDTO;
import com.hisun.lemon.bil.dto.UserBilOrderRspDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.common.CpiConstants;
import com.hisun.lemon.cpi.common.CpiMsgCd;
import com.hisun.lemon.cpi.cregis.CregisApi;
import com.hisun.lemon.cpi.cregis.rsp.VaultAccountAddressBalanceObject;
import com.hisun.lemon.cpi.dao.IDmAccountInfoDao;
import com.hisun.lemon.cpi.dto.*;
import com.hisun.lemon.cpi.entity.DmAccountInfoDO;
import com.hisun.lemon.cpi.service.ICregisService;
import com.hisun.lemon.csh.client.CshAuditTransferOrderClient;
import com.hisun.lemon.csh.client.CshOrderClient;
import com.hisun.lemon.csh.dto.HandleFailTranDTO;
import com.hisun.lemon.csh.dto.HandleFinanceDTO;
import com.hisun.lemon.csh.dto.order.ExchangeCoinDTO;
import com.hisun.lemon.csh.dto.order.OrderDTO;
import com.hisun.lemon.csh.enums.BussinessType;
import com.hisun.lemon.csh.enums.TradeType;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;
import com.hisun.lemon.tam.client.ExchangeOrderClient;
import com.hisun.lemon.tam.constants.TamConstants;
import com.hisun.lemon.tam.dto.ExchangeOrderDetailRspDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static com.hisun.lemon.cpi.bnkapi.common.JsonUtil.objectMapper;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/17 17:29
 */
@Service
public class CregisServiceImpl extends BaseService implements ICregisService {

    private static final Logger logger = LoggerFactory.getLogger(CregisServiceImpl.class);

    @Resource
    private CregisApi cregisApi;

    @Value("${cregis.perPayment}")
    private String prePaymentUrl;

    @Value("${cregis.submitTransfer}")
    private String submitTransferUrl;

    @Value("${cregis.signData}")
    private String signDataUrl;

    @Resource
    private AccountManagementClient accountManagementClient;

    @Resource
    private UserBillQueryClient userBillQueryClient;

    @Resource
    private AccountingTreatmentClient accountingTreatmentClient;

    @Resource
    private ExchangeOrderClient exchangeOrderClient;

    @Resource
    private IDmAccountInfoDao dmAccountInfoDao;

    @Resource
    private CshOrderClient cshOrderClient;

    @Resource
    private CshAuditTransferOrderClient cshAuditTransferOrderClient;

    @Override
    public CregisRsp<TransferSubmitRspDTO> prePayment(PaymentPrepareReqDTO req) {
        CregisRsp<TransferSubmitRspDTO> response = cregisApi.doSend(
                prePaymentUrl,
                Method.POST,
                req,
                null,
                new TypeReference<CregisRsp<TransferSubmitRspDTO>>() {
                }
        );
        if (!"200".equals(response.getCode())) {
            logger.error("CregisServiceImpl.prePayment error:{}", response.getMessage());
            throw new LemonException(CpiMsgCd.CREGIS_HTTP_ERROR.getMsgCd());
        }
        return response;
    }

    @Override
    public CregisRsp<SignRspDTO> signData(SignReqDTO req) {

        // 数据签名，发送请求，获取响应
        CregisRsp<SignRspDTO> response = cregisApi.doSend(
                signDataUrl,
                Method.POST,
                req,
                null,
                new TypeReference<CregisRsp<SignRspDTO>>() {
                }
        );
        return response;
    }

    @Override
    public CregisRsp<NoBody> submitTransfer(TransferSubmitReqDTO request) {
        // 参数校验
        if (JudgeUtils.isNull(request)) {
            logger.info("参数不能为空");
            throw new LemonException(CpiMsgCd.CREGIS_HTTP_ERROR.getMsgCd());
        }

        CregisRsp<NoBody> response = cregisApi.doSend(
                submitTransferUrl,
                Method.POST,
                request,
                null,
                new TypeReference<CregisRsp<NoBody>>() {
                }
        );

        // 如果失败，则抛异常
        if (!"200".equals(response.getCode())) {
            logger.error("CregisServiceImpl.signData error:{}", response.getMessage());
            throw new LemonException(CpiMsgCd.CREGIS_HTTP_ERROR.getMsgCd());
        }
        return response;
    }

    @Override
    public CregisRsp<NoBody> transferAll(PaymentReqDTO req) {
        // 准备付款数据
        logger.info("准备付款数据");
        PaymentPrepareReqDTO paymentPrepareReqDTO = new PaymentPrepareReqDTO();
        BeanUtils.copyProperties(paymentPrepareReqDTO, req);
        CregisRsp<TransferSubmitRspDTO> prepareRsp = this.prePayment(paymentPrepareReqDTO);

        // 获取签名信息
        TransferSubmitRspDTO prepareData = prepareRsp.getData();
        if (prepareData == null) {
            throw new LemonException(CpiMsgCd.CREGIS_HTTP_ERROR.getMsgCd());
        }
        // 获取地址和签名信息
        String address = prepareData.getSigners().get(0);
        String signContent = prepareData.getSignContent();

        // 确定签名者
        if (JudgeUtils.isEmpty(address)) {
            throw new LemonException(CpiMsgCd.CREGIS_HTTP_ERROR.getMsgCd());
        }
        if (JudgeUtils.isEmpty(signContent)) {
            throw new LemonException(CpiMsgCd.CREGIS_HTTP_ERROR.getMsgCd());
        }

        // 执行签名
        logger.info("执行签名");

        JSONObject entries = JSONUtil.parseObj(signContent);
        SignReqDTO signReqDTO = new SignReqDTO(address, entries);

        CregisRsp<SignRspDTO> signRsp = this.signData(signReqDTO);
        String signature = signRsp.getData().getSignature();

        // 构建转账提交请求
        TransferSubmitReqDTO submitRequest = buildSubmitRequest(req, prepareData, signature);

        // 提交转账
        logger.info("提交转账");
        return this.submitTransfer(submitRequest);
    }

    /**
     * 处理Cregis回调数据
     *
     * @param req
     */
    @Transactional
    @Override
    public void handleCallback(CregisCallbackDTO req) {

        //判断账户类型
        DmAccountInfoDO dmAccountInfoDO = dmAccountInfoDao.queryByAccId(String.valueOf(req.getAccountId()));
        if (JudgeUtils.isNull(dmAccountInfoDO)) {
            throw new LemonException(CpiMsgCd.DM_ACCOUNT_NOT_EXIST.getMsgCd());
        }
        // 验证账户类型
        String accountType = dmAccountInfoDO.getAccountType();
        if (JudgeUtils.isBlank(accountType) ||
                (!JudgeUtils.equals(CpiConstants.ACC_TYP_PS, accountType) &&
                        !JudgeUtils.equals(CpiConstants.ACC_TYP_MA, accountType))) {
            throw new LemonException(CpiMsgCd.DM_ACCOUNT_NOT_EXIST.getMsgCd());
        }
        req.setAccountType(accountType);
        if (JudgeUtils.equals(CpiConstants.ACC_TYP_MA, accountType)) {
            //todo 平台账户类型
            logger.info("平台账户类型");
            return;
        }
        //商户账户类型
        // 1. 添加回调数据（独立事务）
        AddCallbackReqDTO addReq = addCallbackData(req);
        logger.info("添加回调数据成功，addReq: {}", addReq);

        // 2. 根据地址address 获取账户地址信息(账号编码、用户id、用途类型...)
        GenericRspDTO<DmAccountAddressRspDTO> genericDmAddrRspDTO = accountManagementClient.getByAddress(req.getAddress());
        if (JudgeUtils.isNotSuccess(genericDmAddrRspDTO.getMsgCd()) || JudgeUtils.isNull(genericDmAddrRspDTO.getBody())) {
            throw new LemonException(genericDmAddrRspDTO.getMsgCd());
        }
        DmAccountAddressRspDTO addressRspDTO = genericDmAddrRspDTO.getBody();
        logger.info("根据地址获取账户地址信息成功，address: {}, addressRspDTO: {}", req.getAddress(), addressRspDTO);

        // 3. 获取账户信息(余额等等)
        GenericRspDTO<DmAccountDetailRspDTO> genericRspDTO = accountManagementClient.queryDmAccountDetail(addressRspDTO.getAcmAcNo());
        if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd()) || JudgeUtils.isNull(genericRspDTO.getBody())) {
            throw new LemonException(genericRspDTO.getMsgCd());
        }
        DmAccountDetailRspDTO accountDetail = genericRspDTO.getBody();
        logger.info("查询该账户信息成功，address: {}, accountDetail: {}", req.getAddress(), accountDetail);

//        DmAccountDetailRspDTO accountDetail = new DmAccountDetailRspDTO();

        // 4. 处理订单(更新或插入)
        String orderId = req.getOrderId();
        logger.info("订单编号：{}", orderId);
        // 判断 orderId是否存在订单表中
        GenericRspDTO<UserBilOrderRspDTO> bilInfoByOrderNo = userBillQueryClient.getBilInfoByOrderNo(orderId);
        if (JudgeUtils.isNotSuccess(bilInfoByOrderNo.getMsgCd())) {
            throw new LemonException(bilInfoByOrderNo.getMsgCd());
        }
        UserBilOrderRspDTO bilOrder = bilInfoByOrderNo.getBody();
        String billOrderNo;
        //交易状态
        String status = req.getTxBase().getStatus();
        //更新交易哈希
        GenericDTO<UpdateOrderReqDTO> updateOrderReqDTO = new GenericDTO<>();
        UpdateOrderReqDTO reqDTO = new UpdateOrderReqDTO();
        reqDTO.setOrderNo(orderId);
        reqDTO.setTxHash(req.getTxBase().getTxId());
        updateOrderReqDTO.setBody(reqDTO);
        userBillQueryClient.updateOrder(updateOrderReqDTO);
        // 兑换订单处理
        ExchangeOrderDetailRspDTO exchangeOrder = exchangeOrderClient.queryOrderDetail(orderId).getBody();
        if (JudgeUtils.isNotNull(exchangeOrder)) {
            logger.info("回调进入处理兑换订单流程");
            doExchangeOrder(exchangeOrder, status);
            return;
        }
        if (JudgeUtils.isNotNull(bilOrder.getOrderNo()) && JudgeUtils.notEquals(bilOrder.getTxType(), "DH")) {
            // 订单存在，获取该订单信息,如: 交易类型
            String busType = bilOrder.getBusType();
            if (JudgeUtils.equals(CpiConstants.CREGIS_SUCCESS, status)) {
                //交易成功
                if (JudgeUtils.equals(BussinessType.DM_TRANSFER.getValue(), busType)) {
                    // 数币站内转账
                    // 财务处理
                    GenericDTO<HandleFinanceDTO> handleFinanceDTOGenericDTO = new GenericDTO<>();
                    HandleFinanceDTO handleFinanceDTO = new HandleFinanceDTO();
                    // csh_order表中订单编号
                    handleFinanceDTO.setOrderNo(orderId);
                    //交易哈希
                    handleFinanceDTO.setTxHash(req.getTxBase().getTxId());
                    // 业务类型
                    handleFinanceDTO.setBusType(BussinessType.DM_TRANSFER.getValue());
                    handleFinanceDTOGenericDTO.setBody(handleFinanceDTO);
                    GenericRspDTO<NoBody> dmRspDTO = cshOrderClient.handleFinance(handleFinanceDTOGenericDTO);
                    if (JudgeUtils.isNotSuccess(dmRspDTO.getMsgCd())) {
                        logger.info("处理订单失败,handleFinanceDTO: {}", handleFinanceDTO);
                        LemonException.throwBusinessException(dmRspDTO.getMsgCd());
                    }
                }
                if (JudgeUtils.equals(BussinessType.DM_TRANSFER02.getValue(), busType)) {
                    // 数币链上转账
                    GenericDTO<HandleFinanceDTO> handleFinanceDTOGenericDTO = new GenericDTO<>();
                    HandleFinanceDTO handleFinanceDTO = new HandleFinanceDTO();
                    // csh_order表中订单编号
                    handleFinanceDTO.setOrderNo(orderId);
                    //交易哈希
                    handleFinanceDTO.setTxHash(req.getTxBase().getTxId());
                    // 业务类型
                    handleFinanceDTO.setBusType(BussinessType.DM_TRANSFER02.getValue());
                    handleFinanceDTOGenericDTO.setBody(handleFinanceDTO);
                    GenericRspDTO<NoBody> dmRspDTO = cshAuditTransferOrderClient.handleCregisOk(handleFinanceDTOGenericDTO);
                    if (JudgeUtils.isNotSuccess(dmRspDTO.getMsgCd())) {
                        LemonException.throwBusinessException(dmRspDTO.getMsgCd());
                    }
                }
                if (JudgeUtils.equals(BussinessType.DM_WITHDRAW.getValue(), busType)) {
                    // 数币提现
                    GenericDTO<HandleFinanceDTO> handleFinanceDTOGenericDTO = new GenericDTO<>();
                    HandleFinanceDTO handleFinanceDTO = new HandleFinanceDTO();
                    // csh_order表中订单编号
                    handleFinanceDTO.setOrderNo(orderId);
                    //交易哈希
                    handleFinanceDTO.setTxHash(req.getTxBase().getTxId());
                    // 业务类型
                    handleFinanceDTO.setBusType(BussinessType.DM_WITHDRAW.getValue());
                    handleFinanceDTOGenericDTO.setBody(handleFinanceDTO);
                    GenericRspDTO<NoBody> dmRspDTO = cshAuditTransferOrderClient.handleWithdrawOk(handleFinanceDTOGenericDTO);
                    if (JudgeUtils.isNotSuccess(dmRspDTO.getMsgCd())) {
                        LemonException.throwBusinessException(dmRspDTO.getMsgCd());
                    }
                }
            } else {
                //交易失败

                if (JudgeUtils.equals(BussinessType.DM_TRANSFER.getValue(), busType) ||
                        JudgeUtils.equals(BussinessType.DM_TRANSFER02.getValue(), busType)) {
                    // 数币转账失败
                    //失败,账务冲正,更新订单状态
                    GenericDTO<HandleFailTranDTO> handleFinanceDTOGenericDTO = new GenericDTO<>();
                    HandleFailTranDTO handleFailTranDTO = new HandleFailTranDTO();
                    // csh_order表中订单编号
                    handleFailTranDTO.setOrderNo(orderId);
                    //交易哈希
                    handleFailTranDTO.setTxHash(req.getTxBase().getTxId());
                    // 业务类型
                    handleFailTranDTO.setBusType(busType);
                    handleFinanceDTOGenericDTO.setBody(handleFailTranDTO);
                    GenericRspDTO<NoBody> dmRspDTO = cshOrderClient.handleFailTransfer(handleFinanceDTOGenericDTO);
                    if (JudgeUtils.isNotSuccess(dmRspDTO.getMsgCd())) {
                        LemonException.throwBusinessException(dmRspDTO.getMsgCd());
                    }
                }
                if (JudgeUtils.equals(BussinessType.DM_WITHDRAW.getValue(), busType)) {
                    // 数币提现
                    String fkAcNo = addressRspDTO.getAcmAcNo();
                    GenericDTO<HandleFailTranDTO> handleFinanceDTOGenericDTO = new GenericDTO<>();
                    HandleFailTranDTO handleFinanceDTO = new HandleFailTranDTO();
                    // csh_order表中订单编号
                    handleFinanceDTO.setOrderNo(orderId);
                    //交易哈希
                    handleFinanceDTO.setTxHash(req.getTxBase().getTxId());
                    //付款方账户号码
                    handleFinanceDTO.setFkAcNo(fkAcNo);
                    // 业务类型
                    handleFinanceDTO.setBusType(BussinessType.DM_WITHDRAW.getValue());
                    handleFinanceDTOGenericDTO.setBody(handleFinanceDTO);
                    GenericRspDTO<NoBody> dmRspDTO = cshOrderClient.handleFailWithdraw(handleFinanceDTOGenericDTO);
                    if (JudgeUtils.isNotSuccess(dmRspDTO.getMsgCd())) {
                        LemonException.throwBusinessException(dmRspDTO.getMsgCd());
                    }
                }
            }

//            // 更新订单状态
//            bilOrder.setOrderStatus(req.getTxBase().getStatus());
//            // 更新订单表现金账户余额
//            bilOrder.setCashBalAmt(BigDecimal.valueOf(accountDetail.getBalance()));
//            bilOrder.setActAmt(BigDecimal.valueOf(accountDetail.getBalance()));
//            bilOrder.setModifyTime(LocalDateTime.now());
//            UpdateOrderReqDTO reqDTO = new UpdateOrderReqDTO();
//            BeanUtils.copyProperties(reqDTO, bilOrder);
//            GenericDTO<UpdateOrderReqDTO> updateOrderReqDTO = new GenericDTO<>();
//            updateOrderReqDTO.setBody(reqDTO);
//            userBillQueryClient.updateOrder(updateOrderReqDTO);
//
//            logger.info("更新订单表成功，orderId: {}, billOrderDO: {}", orderId, bilOrder);

        } else {
            // 如果bil订单不为空，且资金流向为IN（入账），则进行收款或充值处理
            if ("IN".equals(req.getDirection())) {
                billOrderNo = rechargeAndpaymentProcess(req, addReq, addressRspDTO, accountDetail);
                logger.info("充值和收款逻辑处理成功:{}", billOrderNo);
            }
        }

    }

    /**
     * 精度转换
     * @param ccy 币种
     * @param balanceObject  余额
     * @return 转换后的余额
     */
    @Override
    public BigDecimal decimalChange(String ccy, Integer balanceObject) {
        if (balanceObject == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal balance = BigDecimal.valueOf(balanceObject);

        if (ccy == null) {
            return balance;
        }

        // 根据不同币种进行精度转换
        switch (ccy.toUpperCase()) {
            case "USDT":
                // USDT除以1000000 (10^6)
                return balance.divide(BigDecimal.valueOf(1000000), 6, BigDecimal.ROUND_HALF_UP);
            case "USDC":
                // USDC除以1000000 (10^6)
                return balance.divide(BigDecimal.valueOf(10000), 6, BigDecimal.ROUND_HALF_UP);
            case "TRX":
                // USDC除以1000000 (10^6)
                return balance.divide(BigDecimal.valueOf(10000), 6, BigDecimal.ROUND_HALF_UP);
            default:
                // 其他币种默认不转换或根据需要添加
                return balance;
        }
    }



    /**
     * 精度转换
     * @param ccy 币种
     * @param balanceObject  余额
     * @return 转换后的余额
     */
    public BigDecimal decimalChange(String ccy, BigDecimal balanceObject) {
        if (balanceObject == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal balance = balanceObject;

        if (ccy == null) {
            return balance;
        }

        // 根据不同币种进行精度转换
        switch (ccy.toUpperCase()) {
            case "USDT":
                // USDT除以1000000 (10^6)
                return balance.divide(BigDecimal.valueOf(1000000), 6, BigDecimal.ROUND_HALF_UP);
            case "USDC":
                // USDC除以1000000 (10^6)
                return balance.divide(BigDecimal.valueOf(10000), 6, BigDecimal.ROUND_HALF_UP);
            case "TRX":
                // USDC除以1000000 (10^6)
                return balance.divide(BigDecimal.valueOf(10000), 6, BigDecimal.ROUND_HALF_UP);
            default:
                // 其他币种默认不转换或根据需要添加
                return balance;
        }
    }



    /**
     * 处理Cregis回调数据 -  新
     *
     * @param req
     */
    @Transactional
    public void handleCallback_New(CregisCallbackDTO req) {

        //判断账户类型
        DmAccountInfoDO dmAccountInfoDO = dmAccountInfoDao.queryByAccId(String.valueOf(req.getAccountId()));
        if (JudgeUtils.isNull(dmAccountInfoDO)) {
            throw new LemonException(CpiMsgCd.DM_ACCOUNT_NOT_EXIST.getMsgCd());
        }
        // 验证账户类型
        String accountType = dmAccountInfoDO.getAccountType();
        if (JudgeUtils.isBlank(accountType) ||
                (!JudgeUtils.equals(CpiConstants.ACC_TYP_PS, accountType) &&
                        !JudgeUtils.equals(CpiConstants.ACC_TYP_MA, accountType))) {
            throw new LemonException(CpiMsgCd.DM_ACCOUNT_NOT_EXIST.getMsgCd());
        }
        req.setAccountType(accountType);
        if (JudgeUtils.equals(CpiConstants.ACC_TYP_MA, accountType)) {
            // 平台账户类型
            logger.info("平台账户类型");
            return;
        }
        //商户账户类型
        // 1. 添加回调数据（独立事务）
        AddCallbackReqDTO addReq = addCallbackData(req);
        logger.info("添加回调数据成功，addReq: {}", addReq);


        // 2. 根据订单号判断当前订单是否存在
        String orderId = req.getOrderId();
        logger.info("订单编号：{}", orderId);
        // 判断 orderId是否存在订单表中
        GenericRspDTO<UserBilOrderRspDTO> bilInfoByOrderNo = userBillQueryClient.getBilInfoByOrderNo(orderId);
        if (JudgeUtils.isNotSuccess(bilInfoByOrderNo.getMsgCd())) {
            throw new LemonException(bilInfoByOrderNo.getMsgCd());
        }
        UserBilOrderRspDTO bilOrder = bilInfoByOrderNo.getBody();
        //交易状态
        String status = req.getTxBase().getStatus();
        // 兑换订单处理
        ExchangeOrderDetailRspDTO exchangeOrder = exchangeOrderClient.queryOrderDetail(orderId).getBody();
        if (JudgeUtils.isNotNull(exchangeOrder)) {
            logger.info("回调进入处理兑换订单流程");
            doExchangeOrder(exchangeOrder,status);
            return;
        }
        //更新交易哈希
        GenericDTO<UpdateOrderReqDTO> updateOrderReqDTO = new GenericDTO<>();
        UpdateOrderReqDTO reqDTO = new UpdateOrderReqDTO();
        reqDTO.setOrderNo(orderId);
        reqDTO.setTxHash(req.getTxBase().getTxId());
        updateOrderReqDTO.setBody(reqDTO);
        userBillQueryClient.updateOrder(updateOrderReqDTO);
        if(JudgeUtils.isNotNull(bilOrder)) {
            if (JudgeUtils.equals(status, "SUCCESS")) {
                //  数币提现成功

                //  数币站内转账成功

                //  数币链上转账成功

            } else if (JudgeUtils.equals(status, "FAIL")) {
                //  数币提现失败

                //  数币站内转账失败

                //  数币链上转账失败
            } else {
                logger.info("订单状态未知，订单编号: {}", orderId);
            }
        } else {
            // 订单不存在,根据地址获取用户账户地址信息,判断交易是充值还是收款

            // 1. 根据地址address 获取用户账户地址信息(账号编码、用户id、用途类型...)
            GenericRspDTO<DmAccountAddressRspDTO> genericDmAddrRspDTO = accountManagementClient.getByAddress(req.getAddress());
            if (JudgeUtils.isNotSuccess(genericDmAddrRspDTO.getMsgCd()) || JudgeUtils.isNull(genericDmAddrRspDTO.getBody())) {
                throw new LemonException(genericDmAddrRspDTO.getMsgCd());
            }
            // 用户账户地址信息
            DmAccountAddressRspDTO addressRspDTO = genericDmAddrRspDTO.getBody();
            logger.info("根据地址获取账户地址信息成功，address: {}, addressRspDTO: {}", req.getAddress(), addressRspDTO);

            // 2. 根据地址cpAddress 获取对手方账户地址信息(账号编码、用户id、用途类型...)
            GenericRspDTO<DmAccountAddressRspDTO> cpAddressRspDTO = accountManagementClient.getByAddress(req.getCpAddress());
            if (JudgeUtils.isNotSuccess(cpAddressRspDTO.getMsgCd())) {
                throw new LemonException(genericDmAddrRspDTO.getMsgCd());
            }
            // 如果对手方地址不为空同时对手方地址对应的用户id一致，则为充值
            DmAccountAddressRspDTO cpAddressRspDTOBody = cpAddressRspDTO.getBody();
            if (JudgeUtils.isNotNull(cpAddressRspDTOBody) && addressRspDTO.getUserId().equals(cpAddressRspDTOBody.getUserId() )) {
                // 充值逻辑
                if (JudgeUtils.equals(status, "SUCCESS")) {
                    //  充值成功

                } else if (JudgeUtils.equals(status, "FAIL")) {
                    //  充值失败

                } else {
                    logger.info("订单状态未知，订单编号: {}", orderId);
                }
            } else {
                if (JudgeUtils.equals(status, "SUCCESS")) {
                    //  收款成功

                } else if (JudgeUtils.equals(status, "FAIL")) {
                    //  收款失败

                } else {
                    logger.info("订单状态未知，订单编号: {}", orderId);
                }
            }

            // 订单不存在，生成账单并更新回调表
//            billOrderNo = createOrderAndUpdateCallback(req, addReq, addressRspDTO, accountDetail);

//            logger.info("生成账单并更新回调表成功:{}", billOrderNo);
        }


//
//
//        if (JudgeUtils.isNotNull(bilOrder.getOrderNo()) && JudgeUtils.notEquals(bilOrder.getTxType(), "DH")) {
//            // 订单存在，获取该订单信息,如: 交易类型
//            String busType = bilOrder.getBusType();
//            if (JudgeUtils.equals("SUCCESS", status)) {
//                //交易成功
//                if (JudgeUtils.equals(BussinessType.DM_TRANSFER.getValue(), busType)) {
//                    // 数币站内转账
//                    // 财务处理
//                    GenericDTO<HandleFinanceDTO> handleFinanceDTOGenericDTO = new GenericDTO<>();
//                    HandleFinanceDTO handleFinanceDTO = new HandleFinanceDTO();
//                    // csh_order表中订单编号
//                    handleFinanceDTO.setOrderNo(orderId);
//                    // 业务类型
//                    handleFinanceDTO.setBusType(BussinessType.DM_TRANSFER.getValue());
//                    handleFinanceDTOGenericDTO.setBody(handleFinanceDTO);
//                    GenericRspDTO<NoBody> dmRspDTO = cshOrderClient.handleFinance(handleFinanceDTOGenericDTO);
//                    if (JudgeUtils.isNotSuccess(dmRspDTO.getMsgCd())) {
//                        logger.info("处理订单失败,handleFinanceDTO: {}", handleFinanceDTO);
//                        LemonException.throwBusinessException(dmRspDTO.getMsgCd());
//                    }
//                }
//                if (JudgeUtils.equals(BussinessType.DM_TRANSFER02.getValue(), busType)) {
//                    // 数币链上转账
//                    GenericDTO<HandleFinanceDTO> handleFinanceDTOGenericDTO = new GenericDTO<>();
//                    HandleFinanceDTO handleFinanceDTO = new HandleFinanceDTO();
//                    // csh_order表中订单编号
//                    handleFinanceDTO.setOrderNo(orderId);
//                    // 业务类型
//                    handleFinanceDTO.setBusType(BussinessType.DM_TRANSFER02.getValue());
//                    handleFinanceDTOGenericDTO.setBody(handleFinanceDTO);
//                    GenericRspDTO<NoBody> dmRspDTO = cshAuditTransferOrderClient.handleCregisOk(handleFinanceDTOGenericDTO);
//                    if (JudgeUtils.isNotSuccess(dmRspDTO.getMsgCd())) {
//                        LemonException.throwBusinessException(dmRspDTO.getMsgCd());
//                    }
//                }
//            } else {
//                //交易失败
//
//                if (JudgeUtils.equals(BussinessType.DM_TRANSFER.getValue(), busType) ||
//                        JudgeUtils.equals(BussinessType.DM_TRANSFER02.getValue(), busType)) {
//                    // 数币转账失败
//                    //失败,账务冲正,更新订单状态
//                    GenericDTO<HandleFailTranDTO> handleFinanceDTOGenericDTO = new GenericDTO<>();
//                    HandleFailTranDTO handleFailTranDTO = new HandleFailTranDTO();
//                    // csh_order表中订单编号
//                    handleFailTranDTO.setOrderNo(orderId);
//                    // 业务类型
//                    handleFailTranDTO.setBusType(busType);
//                    handleFinanceDTOGenericDTO.setBody(handleFailTranDTO);
//                    GenericRspDTO<NoBody> dmRspDTO = cshOrderClient.handleFailTransfer(handleFinanceDTOGenericDTO);
//                    if (JudgeUtils.isNotSuccess(dmRspDTO.getMsgCd())) {
//                        LemonException.throwBusinessException(dmRspDTO.getMsgCd());
//                    }
//                }
//            }

    }

    /**
     * 更新账户余额表
     * @param addressRspDTO
     * @param accountDetail
     */
    private void updateAcBal(DmAccountAddressRspDTO addressRspDTO,DmAccountDetailRspDTO accountDetail,CregisCallbackDTO req) {
        //查询出该账户余额信息
        com.hisun.lemon.acm.dto.UserAccountDTO userAccountDTO = new com.hisun.lemon.acm.dto.UserAccountDTO();
        userAccountDTO.setUserId(addressRspDTO.getUserId());
        userAccountDTO.setAcNo(addressRspDTO.getAcmAcNo());
        userAccountDTO.setCcy(accountDetail.getCoinId());
        userAccountDTO.setCapTyp(CapTypEnum.CAP_TYP_CASH.getCapTyp());
        GenericRspDTO<List<QueryAcBalRspDTO>> queryAcBal = accountManagementClient.queryAcBal(userAccountDTO);
        QueryAcBalRspDTO acBal = queryAcBal.getBody().get(0);

        logger.info("更新账户余额信息");
        UpdateAccBalReqDTO updateAccBalReq = new UpdateAccBalReqDTO();
        BeanUtils.copyProperties(updateAccBalReq, acBal);
        //updateAccBalReq.setAcCurBal(JudgeUtils.isNull(accountDetail.getBalance()) ? BigDecimal.valueOf(0, 2) : BigDecimal.valueOf(accountDetail.getBalance()));
        if(JudgeUtils.equals(req.getDirection(),"OUT")) {
            updateAccBalReq.setAcCurBal(acBal.getAcCurBal().subtract(req.getAmount()));
        } else {
            updateAccBalReq.setAcCurBal(acBal.getAcCurBal().add(req.getAmount()));
        }

        updateAccBalReq.setModifyTime(LocalDateTime.now());
        accountManagementClient.updateBalance(updateAccBalReq);
    }

    /**
     * 创建订单并更新回调表数据
     * @param req
     * @param addReq
     * @param addressRspDTO
     * @param accountDetail
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String rechargeAndpaymentProcess(CregisCallbackDTO req, AddCallbackReqDTO addReq,
                                               DmAccountAddressRspDTO addressRspDTO, DmAccountDetailRspDTO accountDetail) {
        //交易类型、业务类型
        String busType = "";
        String txType = "";
        //判断是充值还是收款
        String fkAddress = req.getCpAddress();
        String skAddress = req.getAddress();
        GenericRspDTO<DmAccountAddressRspDTO> fkGenericRspDTO = accountManagementClient.getByAddress(fkAddress);
        if (JudgeUtils.isNotSuccess(fkGenericRspDTO.getMsgCd())) {
            LemonException.throwBusinessException(fkGenericRspDTO.getMsgCd());
        }
        if (JudgeUtils.isNull(fkGenericRspDTO.getBody())) {
            txType = BilConstants.TX_TYPE_DM_RECEIVE;
            busType = BussinessType.DM_RECEIVE.getValue();
        } else {
            GenericRspDTO<DmAccountAddressRspDTO> skGenericRspDTO = accountManagementClient.getByAddress(skAddress);
            if (JudgeUtils.isNotSuccess(skGenericRspDTO.getMsgCd()) || JudgeUtils.isNull(skGenericRspDTO.getBody())) {
                LemonException.throwBusinessException(skGenericRspDTO.getMsgCd());
            }
            DmAccountAddressRspDTO fkAccRsp = fkGenericRspDTO.getBody();
            DmAccountAddressRspDTO skAccRsp = skGenericRspDTO.getBody();
            String fkUserId = fkAccRsp.getUserId();
            String skUserId = skAccRsp.getUserId();
            if (JudgeUtils.equals(fkUserId, skUserId)) {
                txType = BilConstants.TX_TYPE_DM_RECHARGE;
                busType = BussinessType.DM_RECHARGE.getValue();
            } else {
                txType = BilConstants.TX_TYPE_DM_RECEIVE;
                busType = BussinessType.DM_RECEIVE.getValue();
            }
        }

        //订单状态转换
        String orderStatus = "";
        if (JudgeUtils.equals(CpiConstants.CREGIS_SUCCESS, req.getTxBase().getStatus())) {
            orderStatus = BilConstants.ORD_STS_S;
        } else {
            orderStatus = BilConstants.ORD_STS_F;
        }

        // 生成账单编号
        String time = DateTimeUtils.getCurrentDateStr();
        String billOrderNo = txType + time
                + IdGenUtils.generateId(BilConstants.ORD_DM_PRE + time, 12);
        logger.info("生成账单编号：{}", billOrderNo);

        //更新回调表数据
        UpdateCallbackReqDTO updateReq = new UpdateCallbackReqDTO();
        BeanUtils.copyProperties(updateReq, addReq);
        updateReq.setBilOrderNo(billOrderNo);
        updateReq.setId(addReq.getId());
        accountManagementClient.updateCallback(updateReq);
        logger.info("更新回调表数据成功，updateReq: {}", updateReq);

        //新增订单表数据
        AddOrderReqDTO addOrderReqDTO = new AddOrderReqDTO();
        addOrderReqDTO.setOrderNo(billOrderNo);
        addOrderReqDTO.setTxType(txType);
        addOrderReqDTO.setTxTm(DateUtil.parseLocalDateTime(req.getTxBase().getCreateTime(), "yyyy-MM-dd HH:mm:ss.SSS"));
        addOrderReqDTO.setBusType(busType);
        addOrderReqDTO.setOrderAmt(req.getAmount());
        addOrderReqDTO.setOrderStatus(orderStatus);
        addOrderReqDTO.setUserId(addressRspDTO.getUserId());
        addOrderReqDTO.setCashBalAmt(BigDecimal.valueOf(accountDetail.getBalance()));
        addOrderReqDTO.setFee(req.getTxBase().getFee());
        addOrderReqDTO.setRemark(req.getTxBase().getMemo());
        addOrderReqDTO.setCcy(accountDetail.getCoinId());
        addOrderReqDTO.setAcNo(accountDetail.getAcNo());
        addOrderReqDTO.setCreateTime(LocalDateTime.now());
        addOrderReqDTO.setModifyTime(LocalDateTime.now());

        GenericDTO<AddOrderReqDTO> addOrderReqGenericDTO = new GenericDTO<>();
        addOrderReqGenericDTO.setBody(addOrderReqDTO);
        userBillQueryClient.addOrder(addOrderReqGenericDTO);

        // 账务处理
        if(BilConstants.ORD_STS_S.equals(orderStatus)) {
            com.hisun.lemon.acm.dto.AddOrderReqDTO ReqDto = new com.hisun.lemon.acm.dto.AddOrderReqDTO();
            BeanUtils.copyProperties(ReqDto, addOrderReqDTO);
            String reqData = ObjectMapperHelper.writeValueAsString(objectMapper, ReqDto, true);
            logger.info("账务处理开始,请求参数 " + reqData);
            GenericRspDTO<NoBody> rspDTO = accountManagementClient.rechargeAndReceiptAct(ReqDto);
            logger.info("账务处理结束,返回结果 " + rspDTO);
            if(JudgeUtils.isNotSuccess(rspDTO.getMsgCd())){
                logger.info("账务处理失败................");
                LemonException.throwBusinessException(rspDTO.getMsgCd());
            }
        }
        return billOrderNo;
    }

    private void doExchangeOrder(ExchangeOrderDetailRspDTO exchangeOrder, String status) {
        if(JudgeUtils.notEquals(status, CpiConstants.CREGIS_SUCCESS)) {
            logger.info("回调状态不成功，对订单进行失败处理:{}",exchangeOrder.getOrderNo());
            exchangeOrderClient.updateOrderSts(exchangeOrder.getOrderNo(), TamConstants.FAILED);
            ExchangeCoinDTO failDTO = new ExchangeCoinDTO();
            failDTO.setOrderNo(exchangeOrder.getOrderNo());
            failDTO.setTxType(TradeType.EXCHANGE.getType());
            failDTO.setBusType(BussinessType.EXCHANGE_COIN.getValue());
            failDTO.setHoldNo(exchangeOrder.getHoldNo());
            failDTO.setOrderSts(TamConstants.REJECTED);
            GenericDTO<ExchangeCoinDTO> failOrder = GenericDTO.newInstance(failDTO);
            cshOrderClient.completeExchangeOrder(failOrder);
            logger.info("失败订单处理完成:{}",exchangeOrder.getOrderNo());
            return;
        }

        if (JudgeUtils.equals(exchangeOrder.getDirection(), TamConstants.DM_TO_FM)) {
            //数转法
            logger.info("数转法");
            logger.info("更新数币账户余额-acNo:{}",exchangeOrder.getFromAcNo());
            accountManagementClient.updateBalByCregis(exchangeOrder.getFromAcNo());
            //平台内转给用户（账务处理）
            //借:其他应付款-暂收-收银台 100 贷：其他应付款-支付账户-现金账户 100
            logger.info("账务处理-order:{}",exchangeOrder.getOrderNo());
            ExchangeCoinDTO exchangeCoinDTO = new ExchangeCoinDTO();
            exchangeCoinDTO.setAcNo(exchangeOrder.getFromAcNo());
            exchangeCoinDTO.setOrderNo(exchangeOrder.getOrderNo());
            exchangeCoinDTO.setPayeeId(exchangeOrder.getToAcNo());
            exchangeCoinDTO.setOrderAmt(exchangeOrder.getToAmountUsd());
            exchangeCoinDTO.setCcy(exchangeOrder.getToCoin());
            exchangeCoinDTO.setTxType(TradeType.EXCHANGE.getType());
            exchangeCoinDTO.setBusType(BussinessType.EXCHANGE_COIN.getValue());
            exchangeCoinDTO.setHoldNo(exchangeOrder.getHoldNo());
            exchangeCoinDTO.setOrderSts(exchangeOrder.getStatus());
            GenericDTO<ExchangeCoinDTO> completeReq = GenericDTO.newInstance(exchangeCoinDTO);
            if (JudgeUtils.isSuccess(cshOrderClient.completeExchangeOrder(completeReq).getMsgCd())) {
                logger.info("兑换订单处理完成:{}", exchangeOrder.getOrderNo());
            } else {
                logger.info("兑换订单处理失败:{}", exchangeOrder.getOrderNo());
            }
        } else if (JudgeUtils.equals(exchangeOrder.getDirection(), TamConstants.FM_TO_DM)) {
            //法转数
            logger.info("法转数");
            logger.info("更新数币账户余额-acNo:{}",exchangeOrder.getToAcNo());
            accountManagementClient.updateBalByCregis(exchangeOrder.getFromAcNo());
            //账务处理 生成科目
            //借：其他应付款-支付账户-现金账户 102 贷：其他应付款-暂收-收银台 100 贷：手续费收入-支付账户-转账 2
            logger.info("账务处理-order:{}",exchangeOrder.getOrderNo());
            ExchangeCoinDTO exchangeCoinDTO = new ExchangeCoinDTO();
            exchangeCoinDTO.setAcNo(exchangeOrder.getFromAcNo());
            exchangeCoinDTO.setOrderNo(exchangeOrder.getOrderNo());
            exchangeCoinDTO.setPayerId(exchangeOrder.getFromAcNo());
            exchangeCoinDTO.setOrderAmt(exchangeOrder.getFromAmount());
            exchangeCoinDTO.setFee(exchangeOrder.getFromAmountUsd());
            exchangeCoinDTO.setCcy(exchangeOrder.getFromCoin());
            exchangeCoinDTO.setTxType(TradeType.EXCHANGE.getType());
            exchangeCoinDTO.setBusType(BussinessType.EXCHANGE_COIN.getValue());
            exchangeCoinDTO.setHoldNo(exchangeOrder.getHoldNo());
            exchangeCoinDTO.setOrderSts(exchangeOrder.getStatus());
            GenericDTO<ExchangeCoinDTO> completeReq = GenericDTO.newInstance(exchangeCoinDTO);
            if (JudgeUtils.isSuccess(cshOrderClient.completeExchangeOrder(completeReq).getMsgCd())) {
                logger.info("兑换订单处理完成:{}", exchangeOrder.getOrderNo());
            } else {
                logger.info("兑换订单处理失败:{}", exchangeOrder.getOrderNo());
            }
        }
        exchangeOrderClient.updateOrderSts(exchangeOrder.getOrderNo(), TamConstants.SUCCESS);
    }

    /**
     * 添加回调数据
     *
     * @param req
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public AddCallbackReqDTO addCallbackData(CregisCallbackDTO req) {
        // 添加回调数据addReq
        AddCallbackReqDTO addReq = new AddCallbackReqDTO();
        // 往回调表中插入数据
        addReq.setTxBaseNetwork(req.getTxBase().getNetwork());
        addReq.setTxBaseBlockId(req.getTxBase().getBlockId());
        addReq.setTxBaseTxId(req.getTxBase().getTxId());
        addReq.setTxBaseEcode(req.getTxBase().getEcode());
        addReq.setTxBaseGroupId(req.getTxBase().getGroupId());
        addReq.setTxBaseFee(req.getTxBase().getFee());
        addReq.setTxBaseStatus(req.getTxBase().getStatus());
        // 使用指定的日期时间格式解析
        addReq.setTxBaseCreateTime(DateUtil.parseLocalDateTime(req.getTxBase().getCreateTime(), "yyyy-MM-dd HH:mm:ss.SSS"));
        addReq.setTxBaseBlockHash(req.getTxBase().getBlockHash());
        addReq.setTxBaseProgramId(req.getTxBase().getProgramId());
        addReq.setTxBaseComputeUnitsConsumed(req.getTxBase().getComputeUnitsConsumed());
        addReq.setTxBaseMemo(req.getTxBase().getMemo());
        addReq.setVaultCode(req.getVaultCode());
        addReq.setAccountId(req.getAccountId());
        addReq.setAccountType(req.getAccountType());
        addReq.setOrderId(req.getOrderId());
        addReq.setCoinId(req.getCoinId());
        addReq.setNetwork(req.getNetwork());
        addReq.setAddress(req.getAddress());
        addReq.setCpAddress(req.getCpAddress());
        addReq.setAmount(decimalChange(req.getCoinId(),req.getAmount()));
        addReq.setDirection(req.getDirection());
        GenericRspDTO<Long> id = accountManagementClient.addCallback(addReq);
        addReq.setId(id.getBody());
        logger.info("添加回调数据成功，addReq.id: {}", id);
        return addReq;
    }


    /**
     * 构建转账提交请求
     *
     * @param req
     * @param prepareData
     * @param signature
     * @return
     */
    private TransferSubmitReqDTO buildSubmitRequest(
            PaymentReqDTO req, TransferSubmitRspDTO prepareData,
            String signature) {

        TransferSubmitReqDTO request = new TransferSubmitReqDTO();
        request.setTaskId(prepareData.getTaskId());
        request.setVaultCode(prepareData.getVaultCode());
        request.setGroupCode(prepareData.getGroupCode());
        request.setAccountId(prepareData.getAccountId());
        request.setSubmitter(prepareData.getSubmitter());
        request.setCmdType(prepareData.getCmdType());
        request.setCmdBasic(prepareData.getCmdBasic());
        request.setCmdForm(prepareData.getCmdForm());
        request.setState(prepareData.getState());
        request.setBusinessType(prepareData.getBusinessType());
        request.setSignContent(prepareData.getSignContent());
        request.setThreshold(prepareData.getThreshold());
        request.setSigners(prepareData.getSigners());
        request.setSignersLevel(prepareData.getSignersLevel());
        request.setSignatures(Collections.singletonList(signature));

        // 设置其他必要字段
        request.setCoinId(req.getCoinId());
        request.setOrderId(req.getOrderId());
        request.setIncludes(req.getIncludes());

        return request;
    }
}
