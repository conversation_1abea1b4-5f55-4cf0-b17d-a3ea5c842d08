eureka :
  client :
    healthcheck :
      enabled : true
zuul :
  #close the global hytrix retry
  retryable : false
  host :
    maxTotalConnections : 1000
    maxPerRouteConnections : 30
  ignoredServices: '*'
  #ignoredHeaders : 
  #ignored-patterns :
  sensitiveHeaders : <PERSON><PERSON>,<PERSON>-<PERSON><PERSON>,Authorization,x-lemon-secure,X-Forwarded-For
  stripPrefix : false
  routes:
    #服务名：建议 实例名+"-"+功能名
    urm-queryuser :
      path : /urm/users/{userId}
      serviceId : urm
      authenticated : false
      signatured : false

    urm-openUser :
      path : /urm/users
      serviceId : urm
      authenticated : false

    urm-updPayPwd :
      path : /urm/paypwd/update
      serviceId : urm
      authenticated : false

    urm-updatepaypwd-sea :
      path : /urm/paypwd/sea/update
      serviceId : urm
      authenticated : false

    urm-resetPaypwd-sea :
      path : /urm/paypwd/sea/reset
      serviceId : urm
      authenticated : false

    urm-resetPayPwd :
      path : /urm/paypwd/reset
      serviceId : urm
      authenticated : false

    urm-queryUserInf :
      path : /urm/users/loginId/{loginId}
      serviceId : urm
      authenticated : false
      signatured : false

    urm-queryUserBal :
      path : /urm/users/bal/{loginId}
      serviceId : urm
      authenticated : false
      signatured : false

    urm-querySafeQuesNo :
      path : /urm/users/ques/{loginId}
      serviceId : urm
      authenticated : false
      signatured : false

    pwm-hallQuery :
      #api gateway请求路径
      path : /pwm/recharge/hall/info
      #服务ID
      serviceId : pwm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      signatured : true
      
    pwm-hallOrderQuery :
      #api gateway请求路径
      path : /pwm/recharge/order/info
      #服务ID
      serviceId : pwm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      signatured : true

    pwm-hallPay :
      #api gateway请求路径
      path : /pwm/recharge/hall
      #服务ID
      serviceId : pwm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      signatured : true
      
    pwm-createHCouponOrderOut :
      #api gateway请求路径
      path : /pwm/recharge/order/sea/out
      #服务ID
      serviceId : pwm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      signatured : true

    pwm-hallCancel :
      #api gateway请求路径
      path : /pwm/recharge/hall/revocation
      #服务ID
      serviceId : pwm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      signatured : true
    
    pwm-hallChkNotify :
      #api gateway请求路径
      path : /pwm/recharge/chk/{type}/{date}/{filename}
      #服务ID
      serviceId : pwm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      signatured : true

    pwm-hallWithdraw :
      #api gateway请求路径
      path : /pwm/withdraw/hall
      #服务ID
      serviceId : pwm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      signatured : true
     
    tam-createOutUserOrder :
      #api gateway请求路径
      path : /tam/transfer/order/user/out
      #服务ID
      serviceId : tam
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      signatured : true
      
    tam-outUserOrderEncrypt :
      #api gateway请求路径
      path : /tam/transfer/order/user/encrypt/out
      #服务ID
      serviceId : tam
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      signatured : true

    mkm-seaccyGitf :
      #海币转赠接口
      #api gateway请求路径
      path : /innerMkmTool/seaccyGitf
      #服务ID
      serviceId : mkm
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true

    mkm-queryUserMkmTool :
      #api gateway请求路径
      path : /innerMkmTool/queryUserSeaccy
      #服务ID
      serviceId : mkm
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true

    #海币交易列表查询
    mkm-seaccyTraceDetail :
      #api gateway请求路径
      path : /innerMkmTool/seaccyTraceDetail
      #服务ID
      serviceId : mkm
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true

    #海币发放
    mkm-get :
      #api gateway请求路径
      path : /innerMkmTool/get
      #服务ID
      serviceId : mkm
      #是否需要认证，默认为true, 如果不需要认证，请主动设置为false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true

     #服务名：线上收单模块-支付下单
    onr-usrThird-order-orderPay :
      #api gateway请求路径
      path : /onr/usrThird/order/orderPay
      #服务ID
      serviceId : onr
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      signatured : true

     #服务名：线上收单模块-支付下单
    bil-inside-order-query :
      #api gateway请求路径
      path : /bil/inside/bill/all
      #服务ID
      serviceId : bil
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      signatured : true

     #服务名：缴费模块-缴费结果异步通知
    cpm-order-notify :
      #api gateway请求路径
      path : /cpm/order/notify
      #服务ID
      serviceId : cpm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      signatured : false
      
      #服务名：线上收单模块-退款
    onr-merc-mercrefund-order :
      #api gateway请求路径
      path : /onr/merc/mercrefund/order
      #服务ID
      serviceId : onr
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      signatured : true
      
      #服务名：线上收单模块-撤单
    onr-merc-merccancle-order :
      #api gateway请求路径
      path : /onr/merc/merccancle/order
      #服务ID
      serviceId : onr
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      signatured : true
      
      #服务名：线上收单模块-查询
    onr-merc-query-order :
      #api gateway请求路径
      path : /onr/merc/query/order
      #服务ID
      serviceId : onr
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      signatured : true

      #服务名：商户营业厅结算申请
    csm-merc-settleaApply :
      #api gateway请求路径
      path : /csm/settle/hall/apply
      #服务ID
      serviceId : csm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true

       #服务名：商户营业厅商户结算信息查询
    csm-merc-information :
      #api gateway请求路径
      path : /csm/settle/information
      #服务ID
      serviceId : csm
      #是否需要认证，默认为true；如果不需要认证，请主动设置false
      authenticated : false
      #是否需要签名，默认为true, 如果不需要签名，请主动设置为false
      signatured : true