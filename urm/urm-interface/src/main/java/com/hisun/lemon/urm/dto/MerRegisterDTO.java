package com.hisun.lemon.urm.dto;

import com.hisun.lemon.urm.constants.URMMessageCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @function MerRegisterDTO
 * @description 商户注册传输对象
 * @date 7/27/2017 Thu
 * @time 10:42 AM
 */
@ApiModel("商户注册")
public class MerRegisterDTO {
    //商户扩展信息
    /**
     *  mercName 商户名称
     */
    @ApiModelProperty(name = "mercName", value = "商户名称", required = true)
    @Length(max = 100)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String mercName;
    /**
     *  mercShortName 商户简称
     */
    @ApiModelProperty(name = "mercShortName", value = "商户简称", required = true)
    @Length(max = 50)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String mercShortName;
    /**
     *  cprRegNmCn 注册名称(中文)
     */
    @ApiModelProperty(name = "cprRegNmCn", value = "注册名称")
    @Length(max = 60)
    private String cprRegNmCn;
    /**
     *  cprOperNmCn 经营名称（中文）
     */
    @ApiModelProperty(name = "cprOperNmCn", value = "经营名称")
    @Length(max = 60)
    private String cprOperNmCn;
    /**
     *  prinNm 负责人名称
     */
    @ApiModelProperty(name = "prinNm", value = "负责人名称")
    @Length(max = 64)
    private String prinNm;

    /**
     *  crpNm 法人名称
     */
    @ApiModelProperty(name = "crpNm", value = "法人名称")
    @Length(max = 64)
    private String crpNm;

    /**
     *  cprIdTyp 法人证件类型
     */
    @ApiModelProperty(name = "crpIdTyp", value = "法人证件类型")
    @Length(max = 2)
    private String crpIdTyp;

    /**
     *  crpIdNo 法人证件号
     */
    @ApiModelProperty(name = "crpIdNo", value = "法人证件号")
    @Length(max = 64)
    private String crpIdNo;

    /**
     * opnBusDt 开业日期
     */
    @ApiModelProperty(name = "opnBusDt", value = "开业日期")
    private LocalDate opnBusDt;

    /**
     * refereeMblNo 推荐人手机号
     */
    @ApiModelProperty(name = "refereeMblNo", value = "推荐人手机号")
    @Length(max = 20)
    private String refereeMblNo;

    /**
     * busLicExpDt (经营年限)营业执照失效日期
     */
    @ApiModelProperty(name = "busLicExpDt", value = "(经营年限)营业执照失效日期")
    private LocalDate busLicExpDt;

    /**
     *  comercReg 工商注册号
     */
    @ApiModelProperty(name = "comercReg", value = "工商注册号")
    @Length(max = 32)
    private String comercReg;
    /**
     *  socialCrdCd 社会信用代码
     */
    @ApiModelProperty(name = "socialCrdCd", value = "社会信用代码")
    @Length(max = 32)
    private String socialCrdCd;
    /**
     *  orgCd 组织机构代码
     */
    @ApiModelProperty(name = "orgCd", value = "组织机构代码")
    @Length(max = 32)
    private String orgCd;
    /**
     *  busiLisc 营业执照
     */
    @ApiModelProperty(name = "busiLisc", value = "营业执照")
    @Length(max = 32)
    private String busiLisc;
    /**
     *  taxCertId 税务证明
     */
    @ApiModelProperty(name = "taxCertId", value = "税务证明")
    @Length(max = 128)
    private String taxCertId;
    /**
     *  webNm 网站名称
     */
    @ApiModelProperty(name = "webNm", value = "网站名称")
    @Length(max = 64)
    private String webNm;
    /**
     *  webUrl 网站地址
     */
    @ApiModelProperty(name = "webUrl", value = "网站地址")
    @Length(max = 128)
    private String webUrl;
    /**
     *  merRegAddr 公司注册地址
     */
    @ApiModelProperty(name = "merRegAddr", value = "公司注册地址")
    @Length(max = 128)
    private String merRegAddr;
    /**
     *  merAddrLongitude 公司地址的所在经度
     */
    @ApiModelProperty(name = "merAddrLongitude", value = "公司地址所在经度")
    private BigDecimal merAddrLongitude;
    /**
     *  merAddrLatitude 公司地址的所在纬度
     */
    @ApiModelProperty(name = "merAddrLatitude", value = "公司地址所在纬度")
    private BigDecimal merAddrLatitude;
    /**
     *  mgtScp 经营范围
     */
    @ApiModelProperty(name = "mgtScp", value = "经营范围")
    @Length(max = 1024)
    private String mgtScp;
    /**
     *  needInvFlg 是否开具发票 Y：需要；N：不需要；
     */
    @ApiModelProperty(name = "needInvFlg", value = "是否开具发票")
    @Length(max = 2)
    private String needInvFlg;
    /**
     *  invMod 开具发票方式 0 - 按季度开；1 - 按月开；2 –按年开；
     */
    @ApiModelProperty(name = "invMod", value = "开具发票方式")
    @Length(max = 2)
    private String invMod;
    /**
     *  invTit 发票抬头
     */
    @ApiModelProperty(name = "invTit", value = "发票抬头")
    @Length(max = 64)
    private String invTit;
    /**
     *  invMailAddr 发票邮寄地址
     */
    @ApiModelProperty(name = "invMailAddr", value = "发票邮寄地址")
    @Length(max = 128)
    private String invMailAddr;
    /**
     *  invMailZip 发票邮寄邮编
     */
    @ApiModelProperty(name = "invMailZip", value = "发票邮寄邮编")
    @Length(max = 12)
    private String invMailZip;
    /**
     *  mercTrdCls 商户行业类别
     */
    @ApiModelProperty(name = "mercTrdCls", value = "商户行业类别")
    @Length(max = 20)
    private String mercTrdCls;
    /**
     *  mercTrdDesc 商户行业描述
     */
    @ApiModelProperty(name = "mercTrdDesc", value = "商户商业描述")
    private String mercTrdDesc;
    /**
     *  cprTyp 商户类别 01-国有，02-私有，03-外资，04-合资 08-个人，10-公司，11-个人独资
     */
    @ApiModelProperty(name = "cprTyp", value = "商户类别")
    private String cprTyp;

    /**
     * merLvl 商户级别
     */
    @ApiModelProperty(name = "merLvl", value = "商户级别")
    @Length(max = 10)
    private String merLvl;

    /**
     *  csTelNo 商户客服电话
     */
    @ApiModelProperty(name = "csTelNo", value = "商户客服电话")
    @Length(max = 20)
    private String csTelNo;
    /**
     *  mercHotLin 商户热线
     */
    @ApiModelProperty(name = "mercHotLin", value = "商户热线")
    @Length(max = 20)
    private String mercHotLin;
    /**
     *  cusMgr 客户经理编号
     */
    @ApiModelProperty(name = "cusMgr", value = "客户经理编号")
    @Length(max = 24)
    private String cusMgr;
    /**
     *  cusMgrNm 客户经理名称
     */
    @ApiModelProperty(name = "cusMgrNm", value = "客户经理名称")
    @Length(max = 64)
    private String cusMgrNm;
    /**
     *  rcvMagAmt 应收商户保证金
     */
    @ApiModelProperty(name = "rcvMagAmt", value = "应收商户保证金")
    private BigDecimal rcvMagAmt;

    /**
     * mblNo 管理员手机号
     */
    @ApiModelProperty(name = "mblNo", value = "管理员手机号")
    @Length(max = 20)
    private String mblNo;

    /**
     * displayNm 管理员姓名
     */
    @ApiModelProperty(name = "displayNm", value = "管理员姓名")
    @Length(max = 60)
    private String displayNm;

    /**
     * email 管理员邮箱
     */
    @ApiModelProperty(name = "email", value = "管理员邮箱")
    @Length(max = 128)
    private String email;

    /**
     * loginId 管理员账号
     */
    @ApiModelProperty(name = "loginId", value = "管理员账号")
    @Length(max = 20)
    private String loginId;

    /**
     * belongMerc 所属上级商户
     */
    private String belongMerc;

    public String getMercName() {
        return mercName;
    }

    public void setMercName(String mercName) {
        this.mercName = mercName;
    }

    public String getMercShortName() {
        return mercShortName;
    }

    public void setMercShortName(String mercShortName) {
        this.mercShortName = mercShortName;
    }

    public String getCprRegNmCn() {
        return cprRegNmCn;
    }

    public void setCprRegNmCn(String cprRegNmCn) {
        this.cprRegNmCn = cprRegNmCn;
    }

    public String getCprOperNmCn() {
        return cprOperNmCn;
    }

    public void setCprOperNmCn(String cprOperNmCn) {
        this.cprOperNmCn = cprOperNmCn;
    }

    public String getPrinNm() {
        return prinNm;
    }

    public void setPrinNm(String prinNm) {
        this.prinNm = prinNm;
    }

    public String getCrpNm() {
        return crpNm;
    }

    public void setCrpNm(String crpNm) {
        this.crpNm = crpNm;
    }

    public String getCrpIdTyp() {
        return crpIdTyp;
    }

    public LocalDate getBusLicExpDt() {
        return busLicExpDt;
    }

    public void setBusLicExpDt(LocalDate busLicExpDt) {
        this.busLicExpDt = busLicExpDt;
    }

    public void setCrpIdTyp(String crpIdTyp) {
        this.crpIdTyp = crpIdTyp;
    }

    public String getCrpIdNo() {
        return crpIdNo;
    }

    public void setCrpIdNo(String crpIdNo) {
        this.crpIdNo = crpIdNo;
    }

    public LocalDate getOpnBusDt() {
        return opnBusDt;
    }

    public void setOpnBusDt(LocalDate opnBusDt) {
        this.opnBusDt = opnBusDt;
    }

    public String getRefereeMblNo() {
        return refereeMblNo;
    }

    public void setRefereeMblNo(String refereeMblNo) {
        this.refereeMblNo = refereeMblNo;
    }

    public String getComercReg() {
        return comercReg;
    }

    public void setComercReg(String comercReg) {
        this.comercReg = comercReg;
    }

    public String getSocialCrdCd() {
        return socialCrdCd;
    }

    public void setSocialCrdCd(String socialCrdCd) {
        this.socialCrdCd = socialCrdCd;
    }

    public String getOrgCd() {
        return orgCd;
    }

    public void setOrgCd(String orgCd) {
        this.orgCd = orgCd;
    }

    public String getBusiLisc() {
        return busiLisc;
    }

    public void setBusiLisc(String busiLisc) {
        this.busiLisc = busiLisc;
    }

    public String getTaxCertId() {
        return taxCertId;
    }

    public void setTaxCertId(String taxCertId) {
        this.taxCertId = taxCertId;
    }

    public String getWebNm() {
        return webNm;
    }

    public void setWebNm(String webNm) {
        this.webNm = webNm;
    }

    public String getWebUrl() {
        return webUrl;
    }

    public void setWebUrl(String webUrl) {
        this.webUrl = webUrl;
    }

    public String getMerRegAddr() {
        return merRegAddr;
    }

    public void setMerRegAddr(String merRegAddr) {
        this.merRegAddr = merRegAddr;
    }

    public BigDecimal getMerAddrLongitude() {
        return merAddrLongitude;
    }

    public void setMerAddrLongitude(BigDecimal merAddrLongitude) {
        this.merAddrLongitude = merAddrLongitude;
    }

    public BigDecimal getMerAddrLatitude() {
        return merAddrLatitude;
    }

    public void setMerAddrLatitude(BigDecimal merAddrLatitude) {
        this.merAddrLatitude = merAddrLatitude;
    }

    public String getMgtScp() {
        return mgtScp;
    }

    public void setMgtScp(String mgtScp) {
        this.mgtScp = mgtScp;
    }

    public String getNeedInvFlg() {
        return needInvFlg;
    }

    public void setNeedInvFlg(String needInvFlg) {
        this.needInvFlg = needInvFlg;
    }

    public String getInvMod() {
        return invMod;
    }

    public void setInvMod(String invMod) {
        this.invMod = invMod;
    }

    public String getInvTit() {
        return invTit;
    }

    public void setInvTit(String invTit) {
        this.invTit = invTit;
    }

    public String getInvMailAddr() {
        return invMailAddr;
    }

    public void setInvMailAddr(String invMailAddr) {
        this.invMailAddr = invMailAddr;
    }

    public String getInvMailZip() {
        return invMailZip;
    }

    public void setInvMailZip(String invMailZip) {
        this.invMailZip = invMailZip;
    }

    public String getMercTrdCls() {
        return mercTrdCls;
    }

    public void setMercTrdCls(String mercTrdCls) {
        this.mercTrdCls = mercTrdCls;
    }

    public String getMercTrdDesc() {
        return mercTrdDesc;
    }

    public void setMercTrdDesc(String mercTrdDesc) {
        this.mercTrdDesc = mercTrdDesc;
    }

    public String getCprTyp() {
        return cprTyp;
    }

    public void setCprTyp(String cprTyp) {
        this.cprTyp = cprTyp;
    }

    public String getCsTelNo() {
        return csTelNo;
    }

    public void setCsTelNo(String csTelNo) {
        this.csTelNo = csTelNo;
    }

    public String getMercHotLin() {
        return mercHotLin;
    }

    public void setMercHotLin(String mercHotLin) {
        this.mercHotLin = mercHotLin;
    }

    public String getCusMgr() {
        return cusMgr;
    }

    public void setCusMgr(String cusMgr) {
        this.cusMgr = cusMgr;
    }

    public String getCusMgrNm() {
        return cusMgrNm;
    }

    public void setCusMgrNm(String cusMgrNm) {
        this.cusMgrNm = cusMgrNm;
    }

    public BigDecimal getRcvMagAmt() {
        return rcvMagAmt;
    }

    public void setRcvMagAmt(BigDecimal rcvMagAmt) {
        this.rcvMagAmt = rcvMagAmt;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getDisplayNm() {
        return displayNm;
    }

    public void setDisplayNm(String displayNm) {
        this.displayNm = displayNm;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getMerLvl() {
        return merLvl;
    }

    public void setMerLvl(String merLvl) {
        this.merLvl = merLvl;
    }

    public String getBelongMerc() {
        return belongMerc;
    }

    public void setBelongMerc(String belongMerc) {
        this.belongMerc = belongMerc;
    }
}
