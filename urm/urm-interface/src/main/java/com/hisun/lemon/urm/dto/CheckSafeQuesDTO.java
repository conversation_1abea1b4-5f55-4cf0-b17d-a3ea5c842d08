package com.hisun.lemon.urm.dto;

import com.hisun.lemon.urm.constants.URMMessageCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @function CheckSafeQuesDTO
 * @description 密保校验传输对象
 * @date 8/15/2017 Tue
 * @time 2:30 PM
 */
@ApiModel("密保校验")
public class CheckSafeQuesDTO {
    /**
     * 登录ID
     */
    @ApiModelProperty(name = "loginId", value = "登录ID", required = true)
    @Length(max = 20)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String loginId;

    /**
     * 密保问题
     */
    @ApiModelProperty(name = "safeQues", value = "密保问题", required = true)
    @Length(max = 256)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String safeQues;

    /**
     * 密保答案
     */
    @ApiModelProperty(name = "safeAns", value = "密保答案", required = true)
    @Length(max = 256)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String safeAns;

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getSafeQues() {
        return safeQues;
    }

    public void setSafeQues(String safeQues) {
        this.safeQues = safeQues;
    }

    public String getSafeAns() {
        return safeAns;
    }

    public void setSafeAns(String safeAns) {
        this.safeAns = safeAns;
    }
}
