package com.hisun.lemon.urm.dto;

import com.hisun.lemon.urm.constants.URMMessageCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;


/**
 *
 */
@ApiModel("手势密码设置")
public class UserHandLoginSetDTO {

    /**
     * 登录id
     */
    @ApiModelProperty(name = "loginId", value = "登录id", required = true)
    private String loginId;
    /**
     * 手势密码
     */
    @ApiModelProperty(name = "handPwd", value = "手势密码", required = true)
    private String handPwd;

    /**
     * 手势密码
     */
    @ApiModelProperty(name = "handFlg", value = "手势密码标志：1开启.2关闭.3设置", required = true)
    @NotBlank(message = URMMessageCode.HAND_FLG_ISNULL)
    private String handFlg;

    public String getHandFlg() {
        return handFlg;
    }

    public void setHandFlg(String handFlg) {
        this.handFlg = handFlg;
    }

    public String getHandPwd() {
        return handPwd;
    }

    public void setHandPwd(String handPwd) {
        this.handPwd = handPwd;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }
}
