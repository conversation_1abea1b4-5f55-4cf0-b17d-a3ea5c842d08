package com.hisun.lemon.urm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 邮箱验证DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/9 09:28
 */
@ApiModel("邮箱验证DTO")
public class EmailAuthDto {
    /**
     * 邮箱
     */
    @ApiModelProperty(name = "email", value = "邮箱")
    private String email;
    /**
     * 验证码
     */
    @ApiModelProperty(name = "code", value = "验证码")
    private String code;
    /**
     * 类型标识
     * @return
     */
    @ApiModelProperty(name = "flag", value = "类型标识，1:注册 2:登录/重置密码", required = true)
    private String flag;



    public String getFlag() {
        return flag;
    }
    public void setFlag(String flag) {
        this.flag = flag;
    }
    public String getEmail() {
        return email;
    }
    public void setEmail(String email) {
        this.email = email;
    }
    public String getCode() {
        return code;
    }
    public void setCode(String code) {
        this.code = code;
    }

}
