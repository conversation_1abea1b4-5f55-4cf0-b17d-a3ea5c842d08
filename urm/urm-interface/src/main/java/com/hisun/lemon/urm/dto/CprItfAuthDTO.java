package com.hisun.lemon.urm.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.constants.URMMessageCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @function CprItfAuthDTO
 * @description 商户接口权限查询DTO
 * @date 8/11/2017 Fri
 * @time 6:03 PM
 */
@ApiModel("商户接口交易权限")
@ClientValidated
public class CprItfAuthDTO {
    /**
     *  userId 内部用户号
     */
    @ApiModelProperty(name = "userId", value = "用户ID", required = true)
    @Length(max = 16)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String userId;
    /**
     *  itfNm 接口名称
     */
    @ApiModelProperty(name = "itfNm", value = "接口名称")
    @Length(max = 32)
    private String itfNm;
    /**
     *  version 接口版本
     */
    @ApiModelProperty(name = "version", value = "版本号", required = true)
    @Length(max = 10)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String version;
    /**
     *  verifyType 接口签名类型 MD5
     */
    @ApiModelProperty(name = "verifyType", value = "接口签名类型，默认为MD5")
    private String verifyType;
    /**
     *  secretKey 接口密钥
     */
    @ApiModelProperty(name = "secretKey", value = "接口密钥")
    @Length(max = 128)
    private String secretKey;
    /**
     *  sts 接口状态  1:生效 0:失效
     */
    @ApiModelProperty(name = "sts", value = "接口状态 1:生效 0:失效")
    private String sts;
    /**
     *  macItem 请求签名字段串
     */
    @ApiModelProperty(name = "macItem", value = "请求签名的字段串")
    @Length(max = 1024)
    private String macItem;
    /**
     *  rspMacItem 返回签名字段串
     */
    @ApiModelProperty(name = "rspMacItem", value = "返回签名字段串")
    @Length(max = 1024)
    private String rspMacItem;
    /**
     *  lastUpdOpr 最后更新柜员
     */
    @ApiModelProperty(name = "lastUpdOpr", value = "最后更新柜员")
    private String lastUpdOpr;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getItfNm() {
        return itfNm;
    }

    public void setItfNm(String itfNm) {
        this.itfNm = itfNm;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getVerifyType() {
        return verifyType;
    }

    public void setVerifyType(String verifyType) {
        this.verifyType = verifyType;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getSts() {
        return sts;
    }

    public void setSts(String sts) {
        this.sts = sts;
    }

    public String getMacItem() {
        return macItem;
    }

    public void setMacItem(String macItem) {
        this.macItem = macItem;
    }

    public String getRspMacItem() {
        return rspMacItem;
    }

    public void setRspMacItem(String rspMacItem) {
        this.rspMacItem = rspMacItem;
    }

    public String getLastUpdOpr() {
        return lastUpdOpr;
    }

    public void setLastUpdOpr(String lastUpdOpr) {
        this.lastUpdOpr = lastUpdOpr;
    }
}
