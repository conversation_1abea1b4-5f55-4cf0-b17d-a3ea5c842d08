package com.hisun.lemon.urm.dto;

import com.hisun.lemon.urm.constants.URMMessageCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @function CheckPayPwdDTO
 * @description 用户支付密码管理
 * @date 7/27/2017 Thu
 * @time 5:18 PM
 */
@ApiModel("支付密码校验")
public class ReSetCheckPayPwdDTO {
    /**
     * 用户号
     */
    @ApiModelProperty(name = "loginId", value = "用户ID")
    private String loginId;

    /**
     * 支付密码
     */
    @ApiModelProperty(name = "payPwd", value = "支付密码", required = true)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String payPwd;



    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String userId) {
        this.loginId = userId;
    }

    public String getPayPwd() {
        return payPwd;
    }

    public void setPayPwd(String payPwd) {
        this.payPwd = payPwd;
    }

}
