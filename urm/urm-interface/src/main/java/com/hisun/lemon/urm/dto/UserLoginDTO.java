package com.hisun.lemon.urm.dto;

import com.hisun.lemon.urm.constants.URMMessageCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @function UserLoginDTO
 * @description 用户登录传输对象
 * @date 7/25/2017 Tue
 * @time 2:29 PM
 */
@ApiModel("用户登录信息")
public class UserLoginDTO {
    /**
     * 用户登录ID
     */
    @ApiModelProperty(name = "loginId", value = "登录ID", required = true)
    @Length(max = 20)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String loginId;

    /**
     * 登录密码随机数
     */
    @ApiModelProperty(name = "loginPwdRandom", value = "登录密码随机数")
    private String loginPwdRandom;

    /**
     * 用户登录密码
     */
    @ApiModelProperty(name = "loginPwd", value = "登录密码", required = true)
    //@NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String loginPwd;

    /**
     * 登录类型 1:密码控件,2:手势密码
     */
    @ApiModelProperty(name = "loginType", value = "登录类型 1:密码控件,2:手势密码")
    private String loginType;

    /**
     * 手势密码
     */
    @ApiModelProperty(name = "handPwd", value = "手势密码", required = true)
    private String handPwd;

    public String getLoginType() {
        return loginType;
    }

    public void setLoginType(String loginType) {
        this.loginType = loginType;
    }

    public String getHandPwd() {
        return handPwd;
    }

    public void setHandPwd(String handPwd) {
        this.handPwd = handPwd;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getLoginPwd() {
        return loginPwd;
    }

    public void setLoginPwd(String loginPwd) {
        this.loginPwd = loginPwd;
    }

    public String getLoginPwdRandom() {
        return loginPwdRandom;
    }

    public void setLoginPwdRandom(String loginPwdRandom) {
        this.loginPwdRandom = loginPwdRandom;
    }
}
