package com.hisun.lemon.urm.client;

import com.hisun.lemon.framework.annotation.LemonBody;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.urm.dto.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @function UserBasicInfClient
 * @description 用户基本信息处理接口
 * @date 7/25/2017 Tue
 * @time 9:38 AM
 */
@FeignClient("URM")
public interface UserBasicInfClient {
    /**
     * 用户开户
     * @param userRegisterDTO
     * @return
     */
    @PostMapping("/urm/users")
    public GenericRspDTO<NoBody> openUser(@RequestBody GenericDTO<UserRegisterDTO> userRegisterDTO);

    /**
     * 商户开户
     * @param merRegisterDTO
     * @return
     */
    @PostMapping("/urm/merusers")
    public GenericRspDTO<String> openMerUser(@RequestBody GenericDTO<MerRegisterDTO> merRegisterDTO);

    /**
     * IGW用户批量开户
     * @param userRegisterDTO
     * @return
     */
    @PostMapping("/urm/users/batchopen")
    public GenericRspDTO<NoBody> batchOpenIGWUser(@RequestBody GenericDTO<UserRegisterDTO> userRegisterDTO);

    /**
     * 用户销户
     */
    @DeleteMapping("/urm/users/{userId}")
    public GenericRspDTO<NoBody> cancelUser(@PathVariable("userId") String userId);

    /**
     * 用户基本信息查询
     * @param userId
     * @return
     */
    @GetMapping("/urm/users/{userId}")
    public GenericRspDTO<UserBasicInfDTO> queryUser(@PathVariable("userId") String userId);

    /**
     * 通过登录Id查询用户信息
     * @param loginId
     * @return
     */
    @GetMapping("/urm/users/loginId/{loginId}")
    public GenericRspDTO<UserBasicInfDTO> queryUserByLoginId(@PathVariable("loginId") String loginId);

    /**
     * 升级实名
     * @param realNameDTO
     * @return
     */
    @PutMapping("/urm/users/realname")
    public GenericRspDTO<NoBody> upgradeRealName(@RequestBody GenericDTO<UserRealNameDTO> realNameDTO);

    /**
     * 查询商户交易权限
     * @param cprItfAuthDTO
     * @return
     */
    @GetMapping("/urm/users/tradingprivilege")
    public GenericRspDTO<CprItfAuthDTO> queryTradingPrivilege(@RequestBody GenericDTO<CprItfAuthDTO> cprItfAuthDTO);

    /**
     * 查询所有商户的ID
     * @param genericDTO
     * @return
     */
    @GetMapping("/urm/users/crpusers")
    public GenericRspDTO<List<String>> queryCrpUser(@RequestBody GenericDTO<NoBody> genericDTO);

    /**
     * 添加商户操作员
     * @param genericDTO
     * @return
     */
    @PostMapping(value = "/urm/merc/opr")
    GenericRspDTO<NoBody> addMercOpr(@Validated @RequestBody GenericDTO<MercOprDTO> genericDTO);

    /**
     * 修改操作员信息
     * @param genericDTO
     * @return
     */
    @PutMapping(value = "/urm/merc/opr")
    GenericRspDTO<NoBody> modifyMercOpr(@Validated @RequestBody GenericDTO<MercOprModifyDTO> genericDTO);

    /**
     * 删除操作员
     * @param genericDTO
     * @return
     */
    @DeleteMapping(value = "/urm/merc/opr")
    GenericRspDTO<NoBody> deleteMercOpr(@Validated @RequestBody GenericDTO<MercOprAuthDTO> genericDTO);

    /**
     * 查询商户操作员列表
     * @param genericDTO
     * @return
     */
    @PostMapping(value = "/urm/merc/opr/list")
    GenericRspDTO<MercOprInfoDTO> queryMercOpr(@Validated @RequestBody GenericDTO<MercOprInfoListDTO> genericDTO);

    /**
     * 给app调用补全信息并不升级的接口
     * @param genericDTO
     * @return
     */
    @PutMapping(value = "/urm/users/information")
    GenericRspDTO<NoBody> upgradeInformation(@Validated @RequestBody GenericDTO<UserInformationDTO> genericDTO);

    /**
     * 查询商户操作员权限
     * @param genericDTO
     * @param loginId
     * @return
     */
    @GetMapping(value = "/urm/users/authority/{loginId}")
    GenericRspDTO<String> mercOprAuthority(@LemonBody GenericDTO genericDTO,@PathVariable("loginId") String loginId);

    /**
     * 修改商户操作员权限
     * @param genericDTO
     * @return
     */
    @PutMapping(value = "/urm/users/authority")
    GenericRspDTO<NoBody> modifyMercOprAuthority(@Validated @RequestBody GenericDTO<MercOprAuthDTO> genericDTO);

    @GetMapping(value = "/urm/users/ques/{loginId}")
    GenericRspDTO<String> querySafeQuesNo(@PathVariable("loginId") String loginId, @LemonBody GenericDTO genericDTO);

    @PostMapping(value = "/urm/batch/open")
    GenericRspDTO<NoBody> batchOpenUser(@Validated @RequestBody GenericDTO<BatchOpenUserDTO> genericDTO);

    //查询商户管理员登录名
    @GetMapping(value = "/urm/merchant/{userId}")
    GenericRspDTO<String> getMerchantAdminLoginId(@PathVariable("userId") String userId);

    @GetMapping(value = "/urm/merchant/affiliate/{userId}")
    GenericRspDTO<List<String>> getAffiliatedList(@LemonBody GenericDTO genericDTO, @PathVariable("userId") String userId);

    @PostMapping(value = "/urm/merchant/info")
    GenericRspDTO<List<UrmMercInfDTO>> getMercListInfo(@Validated @RequestBody GenericDTO<UrmMercInfDTO> genericDTO);
}
