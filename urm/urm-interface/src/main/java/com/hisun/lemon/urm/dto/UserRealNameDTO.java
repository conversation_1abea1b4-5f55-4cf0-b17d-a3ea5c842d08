package com.hisun.lemon.urm.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.constants.URMMessageCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @function UserRealNameDTO
 * @description 用户实名处理DTO
 * @date 8/11/2017 Fri
 * @time 10:00 AM
 */
@Api("用户实名")
@ClientValidated
public class UserRealNameDTO {
    /**
     *  userId 内部用户号
     */
    @ApiModelProperty(name = "userId", value = "用户ID，userId或loginId必传其一")
    @Length(max = 16)
    private String userId;

    /**
     * loginId 登录ID，可以传手机号
     */
    @ApiModelProperty(name = "loginId", value = "用户手机号，userId或loginId必传其一，可以传手机号")
    @Length(max = 20)
    private String loginId;

    /**
     * usrNation 用户所属国家
     */
    @ApiModelProperty(name = "usrCountry", value = "用户所属国家")
    @Length(max = 10)
    private String usrCountry;

    /**
     *  idType 证件类型
     */
    @ApiModelProperty(name = "idType", value = "证件类型", required = true)
    @Length(max = 2)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String idType;

    /**
     *  idNo 证件号码
     */
    @ApiModelProperty(name = "idNo", value = "证件号码", required = true)
    @Length(max = 64)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String idNo;

    /**
     *  usrNm 用户姓名
     */
    @ApiModelProperty(name = "usrNm", value = "用户姓名", required = true)
    @Length(max = 64)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String usrNm;

    /**
     *  usrGender 用户性别 M：男 F：女
     */
    @ApiModelProperty(name = "usrGender", value = "用户性别")
    @Length(max = 1)
    private String usrGender;

    /**
     *  usrBirthDt 出生日期
     */
    @ApiModelProperty(name = "usrBirthDr", value = "出生日期")
    @Length(max = 8)
    private String usrBirthDt;

    /**
     *  issuAuth 签发机关
     */
    @ApiModelProperty(name = "issuAuth", value = "签发机关")
    @Length(max = 128)
    private String issuAuth;

    /**
     *  idEffDt 证件有效期起始
     */
    @ApiModelProperty(name = "idEffDt", value = "证件有效期起始")
    @Length(max = 8)
    private String idEffDt;

    /**
     *  idExpDt 证件有效期截止
     */
    @ApiModelProperty(name = "idExpDt", value = "证件有效期结束")
    @Length(max = 8)
    private String idExpDt;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getUsrNm() {
        return usrNm;
    }

    public void setUsrNm(String usrNm) {
        this.usrNm = usrNm;
    }

    public String getUsrGender() {
        return usrGender;
    }

    public void setUsrGender(String usrGender) {
        this.usrGender = usrGender;
    }

    public String getUsrBirthDt() {
        return usrBirthDt;
    }

    public void setUsrBirthDt(String usrBirthDt) {
        this.usrBirthDt = usrBirthDt;
    }

    public String getIssuAuth() {
        return issuAuth;
    }

    public void setIssuAuth(String issuAuth) {
        this.issuAuth = issuAuth;
    }

    public String getIdEffDt() {
        return idEffDt;
    }

    public void setIdEffDt(String idEffDt) {
        this.idEffDt = idEffDt;
    }

    public String getIdExpDt() {
        return idExpDt;
    }

    public void setIdExpDt(String idExpDt) {
        this.idExpDt = idExpDt;
    }

    public String getUsrCountry() {
        return usrCountry;
    }

    public void setUsrCountry(String usrCountry) {
        this.usrCountry = usrCountry;
    }
}
