package com.hisun.lemon.urm.dto;

import com.hisun.lemon.urm.constants.URMMessageCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @function ChangPwdDTO
 * @description 密码变更对象
 * @date 7/27/2017 Thu
 * @time 5:38 PM
 */
@ApiModel("密码变更对象")
public class ChangPwdDTO {

    /**
     * 用户登录ID
     */
    @ApiModelProperty(name = "loginId", value = "用户登录ID", required = true)
    @Length(max = 20)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String loginId;

    /**
     * 用户原密码
     */
    @ApiModelProperty(name = "oldPwd", value = "用户原密码")
    private String oldPwd;

    /**
     * 用户原密码随机数
     */
    @ApiModelProperty(name = "oldPwdRandom", value = "用户原密码随机数")
    private String oldPwdRandom;

    /**
     * 用户新密码
     */
    @ApiModelProperty(name = "newPwd", value = "用户新密码", required = true)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String newPwd;

    /**
     * 用户新密码随机数
     */
    @ApiModelProperty(name = "newPwdRandom", value = "用户新密码随机数")
    private String newPwdRandom;

    /**
     * 密保问题
     */
    @ApiModelProperty(name = "safeQues", value = "密保问题")
    private String safeQues;

    /**
     * 密保答案
     */
    @ApiModelProperty(name = "safeAns", value = "密保答案")
    private String safeAns;

    /**
     * 银行卡号
     */
    @ApiModelProperty(name = "crdNo", value = "银行卡号")
    private String crdNo;

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getOldPwd() {
        return oldPwd;
    }

    public void setOldPwd(String oldPwd) {
        this.oldPwd = oldPwd;
    }

    public String getNewPwd() {
        return newPwd;
    }

    public void setNewPwd(String newPwd) {
        this.newPwd = newPwd;
    }

    public String getSafeQues() {
        return safeQues;
    }

    public void setSafeQues(String safeQues) {
        this.safeQues = safeQues;
    }

    public String getSafeAns() {
        return safeAns;
    }

    public void setSafeAns(String safeAns) {
        this.safeAns = safeAns;
    }

    public String getOldPwdRandom() {
        return oldPwdRandom;
    }

    public void setOldPwdRandom(String oldPwdRandom) {
        this.oldPwdRandom = oldPwdRandom;
    }

    public String getNewPwdRandom() {
        return newPwdRandom;
    }

    public void setNewPwdRandom(String newPwdRandom) {
        this.newPwdRandom = newPwdRandom;
    }

    public String getCrdNo() {
        return crdNo;
    }

    public void setCrdNo(String crdNo) {
        this.crdNo = crdNo;
    }
}
