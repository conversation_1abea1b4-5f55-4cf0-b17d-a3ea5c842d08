package com.hisun.lemon.urm.dto;

import io.swagger.annotations.ApiModel;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/10/16
 */
@ApiModel("商户操作员列表返回对象")
public class MercOprInfoDTO {

    private List<MercOprInfoListDTO> mercOprInfoListDTOList;

    private int totNum;

    public List<MercOprInfoListDTO> getMercOprInfoListDTOList() {
        return mercOprInfoListDTOList;
    }

    public void setMercOprInfoListDTOList(List<MercOprInfoListDTO> mercOprInfoListDTOList) {
        this.mercOprInfoListDTOList = mercOprInfoListDTOList;
    }

    public int getTotNum() {
        return totNum;
    }

    public void setTotNum(int totNum) {
        this.totNum = totNum;
    }

    @Override
    public String toString() {
        return "MercOprInfoDTO{" +
                "mercOprInfoListDTOList=" + mercOprInfoListDTOList +
                ", totNum=" + totNum +
                '}';
    }
}
