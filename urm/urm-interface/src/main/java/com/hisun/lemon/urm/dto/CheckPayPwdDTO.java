package com.hisun.lemon.urm.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.constants.URMMessageCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @function CheckPayPwdDTO
 * @description 用户支付密码管理
 * @date 7/27/2017 Thu
 * @time 5:18 PM
 */
@ClientValidated
@ApiModel(value = "CheckPayPwdDTO", description = "支付密码校验")
public class CheckPayPwdDTO {
//    /**
//     * 用户号
//     */
//    @ApiModelProperty(name = "userId", value = "用户ID")
//    @Length(max = 16)
//    private String userId;

    /**
     * 支付密码
     */
    @ApiModelProperty(name = "payPwd", value = "支付密码", required = true)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String payPwd;

    /**
     * 支付密码随机数
     */
    @ApiModelProperty(name = "payPwdRandom", value = "支付密码随机数")
    private String payPwdRandom;

//    public String getUserId() {
//        return userId;
//    }
//
//    public void setUserId(String userId) {
//        this.userId = userId;
//    }

    public String getPayPwd() {
        return payPwd;
    }

    public void setPayPwd(String payPwd) {
        this.payPwd = payPwd;
    }

    public String getPayPwdRandom() {
        return payPwdRandom;
    }

    public void setPayPwdRandom(String payPwdRandom) {
        this.payPwdRandom = payPwdRandom;
    }
}
