package com.hisun.lemon.urm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

@ApiModel("下属商户列表基本信息")
public class UrmMercInfReqDTO {

    @ApiModelProperty(name = "userId", value = "用户ID")
    @Length(max = 16)
    private String userId;

    @ApiModelProperty(name = "mercName", value = "商户名称")
    @Length(max = 100)
    private String mercName;

    private int pageNum;

    private int pageSize;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMercName() {
        return mercName;
    }

    public void setMercName(String mercName) {
        this.mercName = mercName;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
}
