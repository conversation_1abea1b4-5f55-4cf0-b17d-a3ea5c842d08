package com.hisun.lemon.urm.dto;

import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;

/**
 * <AUTHOR>
 * @function CrmRspDTO
 * @description CRM服务密码鉴权应答接口
 * @date 9/15/2017 Fri
 * @time 10:43 PM
 */
@ApiModel("CRM服务密码鉴权应答接口")
@ClientValidated
public class CrmRspDTO<T> {
    private String status;   //1成功，其他失败，如失败无data属性
    private String desc;//结果描述"succ"
    private T data;

    public static <T> CrmRspDTO<T> newInstance(T data) {
        CrmRspDTO<T> crmRspDTO = new CrmRspDTO<>();
        crmRspDTO.setData(data);
        return crmRspDTO;
    }

    public static CrmRspDTO<NoBody> newInstance() {
        return newInstance((NoBody) null);
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
