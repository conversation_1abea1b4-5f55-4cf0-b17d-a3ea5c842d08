package com.hisun.lemon.urm.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.constants.URMMessageCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @create 2017/10/18
 */
@Api("补全信息")
@ClientValidated
public class UserInformationDTO {
    /**
     * usrNation 用户所属国家
     */
    @ApiModelProperty(name = "usrCountry", value = "用户所属国家")
    private String usrCountry;

    /**
     *  idType 证件类型
     */
    @ApiModelProperty(name = "idType", value = "证件类型", required = true)
    @Length(max = 2)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String idType;

    /**
     *  idNo 证件号码
     */
    @ApiModelProperty(name = "idNo", value = "证件号码", required = true)
    @Length(max = 64)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String idNo;

    /**
     *  usrNm 用户姓名
     */
    @ApiModelProperty(name = "usrNm", value = "用户姓名", required = true)
    @Length(max = 64)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String usrNm;

    public String getUsrCountry() {
        return usrCountry;
    }

    public void setUsrCountry(String usrCountry) {
        this.usrCountry = usrCountry;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getUsrNm() {
        return usrNm;
    }

    public void setUsrNm(String usrNm) {
        this.usrNm = usrNm;
    }

    @Override
    public String toString() {
        return "UserInformationDTO{" +
                "usrCountry='" + usrCountry + '\'' +
                ", idType='" + idType + '\'' +
                ", idNo='" + idNo + '\'' +
                ", usrNm='" + usrNm + '\'' +
                '}';
    }
}
