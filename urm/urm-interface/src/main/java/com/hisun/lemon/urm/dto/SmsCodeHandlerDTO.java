package com.hisun.lemon.urm.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.constants.URMMessageCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @function SmsCodeHandlerDTO
 * @description 短信验证码相关处理
 * @date 8/10/2017 Thu
 * @time 9:50 AM
 */
@ApiModel("短信验证码")
@ClientValidated
public class SmsCodeHandlerDTO {
    /**
     * mblNo 手机号
     */
    @ApiModelProperty(name = "mblNo", value = "手机号", required = true)
    @Length(max = 20)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String mblNO;

    /**
     * smsTyp 短信类型
     */
    @ApiModelProperty(name = "smsTyp", value = "短信类型", required = true)
    @Length(max = 3)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String smsTyp;

    /**
     * chkNo 验证码
     */
    @ApiModelProperty(name = "chkNo", value = "验证码")
    @Length(max = 8)
    private String chkNo;

    /**
     * smsToken 短信Token
     */
    @ApiModelProperty(name = "smsToken", value = "短信Token")
    @Length(max = 32)
    private String smsToken;

    public String getMblNO() {
        return mblNO;
    }

    public void setMblNO(String mblNO) {
        this.mblNO = mblNO;
    }

    public String getSmsTyp() {
        return smsTyp;
    }

    public void setSmsTyp(String smsTyp) {
        this.smsTyp = smsTyp;
    }

    public String getChkNo() {
        return chkNo;
    }

    public void setChkNo(String chkNo) {
        this.chkNo = chkNo;
    }

    public String getSmsToken() {
        return smsToken;
    }

    public void setSmsToken(String smsToken) {
        this.smsToken = smsToken;
    }
}
