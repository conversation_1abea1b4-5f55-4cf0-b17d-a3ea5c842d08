package com.hisun.lemon.urm.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.constants.URMMessageCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @create 2017/10/12
 */
@ApiModel("商户操作员修改对象")
@ClientValidated
public class MercOprModifyDTO {

    @ApiModelProperty(name = "loginId", value = "用户登录ID", required = true)
    @Length(max = 20)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String loginId;

    @ApiModelProperty(name = "mblNo", value = "手机号", required = true)
    @Length(max = 20)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String mblNO;

    @ApiModelProperty(name = "displayNm", value = "操作员姓名")
    @Length(max = 60)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String displayNm;

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getMblNO() {
        return mblNO;
    }

    public void setMblNO(String mblNO) {
        this.mblNO = mblNO;
    }

    public String getDisplayNm() {
        return displayNm;
    }

    public void setDisplayNm(String displayNm) {
        this.displayNm = displayNm;
    }

    @Override
    public String toString() {
        return "MercOprDTO{" +
                "loginId='" + loginId + '\'' +
                ", mblNO='" + mblNO + '\'' +
                ", displayNm='" + displayNm + '\'' +
                '}';
    }
}
