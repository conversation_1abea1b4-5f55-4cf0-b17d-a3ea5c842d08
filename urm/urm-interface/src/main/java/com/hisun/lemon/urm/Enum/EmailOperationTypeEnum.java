package com.hisun.lemon.urm.Enum;

/**
 *
 * <AUTHOR>
 * @date 2019/4/28
 */
public enum EmailOperationTypeEnum {
    /** 邮箱操作类型 */
    REGISTER("1", "注册"),
    RESET("2", "重置密码");
    /** value */
    private final String value;

    /** desc */
    private final String desc;

    /**
     * 构造函数
     * @param value 值
     * @param desc 描述
     */
    private EmailOperationTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String value) {
        for(EmailOperationTypeEnum emailOperationTypeEnum:EmailOperationTypeEnum.values()) {
            if(value.equals(emailOperationTypeEnum.value)) {
                return emailOperationTypeEnum.desc;
            }
        }
        return "";
    }

}