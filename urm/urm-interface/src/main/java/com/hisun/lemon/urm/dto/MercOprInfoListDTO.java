package com.hisun.lemon.urm.dto;

import io.swagger.annotations.ApiModel;

/**
 * <AUTHOR>
 * @create 2017/10/16
 */
@ApiModel("商户操作员列表")
public class MercOprInfoListDTO {

    private String safeId;

    private String loginId;

    private String displayNm;

    private String oprTyp;

    private int pageNum;

    private int pageSize;

    public String getSafeId() {
        return safeId;
    }

    public void setSafeId(String safeId) {
        this.safeId = safeId;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getDisplayNm() {
        return displayNm;
    }

    public void setDisplayNm(String displayNm) {
        this.displayNm = displayNm;
    }

    public String getOprTyp() {
        return oprTyp;
    }

    public void setOprTyp(String oprTyp) {
        this.oprTyp = oprTyp;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String toString() {
        return "MercOprInfoDTO{" +
                "safeId='" + safeId + '\'' +
                ", loginId='" + loginId + '\'' +
                ", displayNm='" + displayNm + '\'' +
                ", oprTyp='" + oprTyp + '\'' +
                ", pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                '}';
    }
}
