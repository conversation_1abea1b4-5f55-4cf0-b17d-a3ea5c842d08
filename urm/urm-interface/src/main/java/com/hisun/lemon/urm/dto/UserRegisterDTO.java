package com.hisun.lemon.urm.dto;

import com.hisun.lemon.urm.constants.URMMessageCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @function UserRegisterDTO
 * @description 用户注册传输对象
 * @date 7/27/2017 Thu
 * @time 10:42 AM
 */
@ApiModel("用户注册")
public class UserRegisterDTO {
    /**
     *  mblNo 手机号码
     */
    @ApiModelProperty(name = "mblNo", value = "手机号码,格式为+区号-号码，例如：+86-15274891107")
    @Length(max = 20)
    private String mblNo;

    /**
     * @Feilds smsTyp 短信类型
     */
    @ApiModelProperty(name = "smsTyp", value = "短信类型,验证码存在时，必输")
    @Length(max = 3)
    private String smsTyp;

    /**
     *  chkNo 短信验证码
     */
    @ApiModelProperty(name = "chkNo", value = "短信验证码")
    @Length(max = 8)
    private String chkNo;

    /**
     * @Feilds smsToken 短信Token
     */
    @ApiModelProperty(name = "smsToken", value = "短信Token,验证码存在时，必输")
    @Length(max = 32)
    private String smsToken;

    /**
     *  email 邮箱
     */
    @ApiModelProperty(name = "email", value = "邮箱",required = true)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    @Length(max = 128)
    private String email;

    /**
     *  emailChkNo 邮箱验证码
     */
    @ApiModelProperty(name = "emailNo", value = "邮箱验证码,必输",required = true)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    @Length(max = 32)
    private String emailNo;

    /**
     *  loginPwd 登录密码
     */
    @ApiModelProperty(name = "loginPwd", value = "登录密码",required = true)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String loginPwd;

    /**
     * loginPwdRandom 登录密码随机数
     */
    @ApiModelProperty(name = "loginPwdRandom", value = "登录密码随机数")
    private String loginPwdRandom;

    /**
     *  payPwd 支付密码
     */
    @ApiModelProperty(name = "payPwd", value = "支付密码",required = true)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String payPwd;

    /**
     * payPwdRandom 支付密码随机数
     */
    @ApiModelProperty(name = "seaRandom", value = "密码随机数")
    private String seaRandom;

    /**
     * payPwdRandom 支付密码随机数
     */
    @ApiModelProperty(name = "payPwdRandom", value = "支付密码随机数")
    private String payPwdRandom;

    /**
     *  safeMblNo 密保手机号
     */
    @ApiModelProperty(name = "safeMblNo", value = "密保手机号")
    @Length(max = 20)
    private String safeMblNo;
    /**
     *  safeEmail 密保邮箱
     */
    @ApiModelProperty(name = "safeEmail", value = "密保邮箱")
    @Length(max = 100)
    private String safeEmail;
    /**
     *  safeQues1 密保问题1
     */
    @ApiModelProperty(name = "safeQues1", value = "密保问题1")
    @Length(max = 256)
    private String safeQues1;
    /**
     *  safeAns1 密保答案1
     */
    @ApiModelProperty(name = "safeAns1", value = "密保答案1")

    @Length(max = 256)
    private String safeAns1;
    /**
     *  safeQues2 密保问题2
     */
    @ApiModelProperty(name = "safeQues2", value = "密保问题2")
    @Length(max = 256)
    private String safeQues2;
    /**
     *  safeAns2 密保答案2
     */
    @ApiModelProperty(name = "safeAns2", value = "密保答案2")
    @Length(max = 256)
    private String safeAns2;
    /**
     *  safeQues3 密保问题3
     */
    @ApiModelProperty(name = "safeQues3", value = "密保问题3")
    @Length(max = 256)
    private String safeQues3;
    /**
     *  safeAns3 密保答案3
     */
    @ApiModelProperty(name = "safeAns3", value = "密保答案3")
    @Length(max = 256)
    private String safeAns3;
    /**
     *  safeQues4 密保问题4
     */
    @ApiModelProperty(name = "safeQues4", value = "密保问题4")
    @Length(max = 256)
    private String safeQues4;
    /**
     *  safeAns4 密保答案4
     */
    @ApiModelProperty(name = "safeAns4", value = "密保答案4")
    @Length(max = 256)
    private String safeAns4;
    /**
     *  safeQues5 密保问题5
     */
    @ApiModelProperty(name = "safeQues5", value = "密保问题5")
    @Length(max = 256)
    private String safeQues5;
    /**
     *  safeAns5 密保答案5
     */
    @ApiModelProperty(name = "safeAns5", value = "密保答案5")
    @Length(max = 256)
    private String safeAns5;
    /**
     *  idType 证件类型
     */
    @ApiModelProperty(name = "idType", value = "证件类型")
    @Length(max = 2)
    private String idType;
    /**
     *  idNo 证件号码
     */
    @ApiModelProperty(name = "idNo", value = "证件号码")
    @Length(max = 64)
    private String idNo;
    /**
     *  usrNm 用户姓名
     */
    @ApiModelProperty(name = "usrNm", value = "用户姓名")
    @Length(max = 64)
    private String usrNm;
    /**
     *  usrGender 用户性别 M：男 F：女
     */
    @ApiModelProperty(name = "usrGender", value = "用户性别")
    @Length(max = 1)
    private String usrGender;
    /**
     *  usrNation 用户归属国家
     */
    @ApiModelProperty(name = "usrNation", value = "用户归属国家")
    @Length(max = 64)
    private String usrNation;
    /**
     *  usrBirthDt 出生日期
     */
    @ApiModelProperty(name = "usrBirthDr", value = "出生日期")
    @Length(max = 8)
    private String usrBirthDt;
    /**
     *  issuAuth 签发机关
     */
    @ApiModelProperty(name = "issuAuth", value = "签发机关")
    @Length(max = 128)
    private String issuAuth;
    /**
     *  idEffDt 证件有效期起始
     */
    @ApiModelProperty(name = "idEffDt", value = "证件有效期起始")
    @Length(max = 8)
    private String idEffDt;
    /**
     *  idExpDt 证件有效期截止
     */
    @ApiModelProperty(name = "idExpDt", value = "证件有效期结束")
    @Length(max = 8)
    private String idExpDt;

    public String getSeaRandom() {
        return seaRandom;
    }

    public void setSeaRandom(String seaRandom) {
        this.seaRandom = seaRandom;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getChkNo() {
        return chkNo;
    }

    public void setChkNo(String chkNo) {
        this.chkNo = chkNo;
    }

    public String getEmailNo() {
        return emailNo;
    }

    public void setEmailNo(String emailNo) {
        this.emailNo = emailNo;
    }

    public String getSmsTyp() {
        return smsTyp;
    }

    public void setSmsTyp(String smsTyp) {
        this.smsTyp = smsTyp;
    }

    public String getSmsToken() {
        return smsToken;
    }

    public void setSmsToken(String smsToken) {
        this.smsToken = smsToken;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getLoginPwd() {
        return loginPwd;
    }

    public void setLoginPwd(String loginPwd) {
        this.loginPwd = loginPwd;
    }

    public String getPayPwd() {
        return payPwd;
    }

    public void setPayPwd(String payPwd) {
        this.payPwd = payPwd;
    }

    public String getSafeMblNo() {
        return safeMblNo;
    }

    public void setSafeMblNo(String safeMblNo) {
        this.safeMblNo = safeMblNo;
    }

    public String getSafeEmail() {
        return safeEmail;
    }

    public void setSafeEmail(String safeEmail) {
        this.safeEmail = safeEmail;
    }

    public String getSafeQues1() {
        return safeQues1;
    }

    public void setSafeQues1(String safeQues1) {
        this.safeQues1 = safeQues1;
    }

    public String getSafeAns1() {
        return safeAns1;
    }

    public void setSafeAns1(String safeAns1) {
        this.safeAns1 = safeAns1;
    }

    public String getSafeQues2() {
        return safeQues2;
    }

    public void setSafeQues2(String safeQues2) {
        this.safeQues2 = safeQues2;
    }

    public String getSafeAns2() {
        return safeAns2;
    }

    public void setSafeAns2(String safeAns2) {
        this.safeAns2 = safeAns2;
    }

    public String getSafeQues3() {
        return safeQues3;
    }

    public void setSafeQues3(String safeQues3) {
        this.safeQues3 = safeQues3;
    }

    public String getSafeAns3() {
        return safeAns3;
    }

    public void setSafeAns3(String safeAns3) {
        this.safeAns3 = safeAns3;
    }

    public String getSafeQues4() {
        return safeQues4;
    }

    public void setSafeQues4(String safeQues4) {
        this.safeQues4 = safeQues4;
    }

    public String getSafeAns4() {
        return safeAns4;
    }

    public void setSafeAns4(String safeAns4) {
        this.safeAns4 = safeAns4;
    }

    public String getSafeQues5() {
        return safeQues5;
    }

    public void setSafeQues5(String safeQues5) {
        this.safeQues5 = safeQues5;
    }

    public String getSafeAns5() {
        return safeAns5;
    }

    public void setSafeAns5(String safeAns5) {
        this.safeAns5 = safeAns5;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getUsrNm() {
        return usrNm;
    }

    public void setUsrNm(String usrNm) {
        this.usrNm = usrNm;
    }

    public String getUsrGender() {
        return usrGender;
    }

    public void setUsrGender(String usrGender) {
        this.usrGender = usrGender;
    }

    public String getUsrNation() {
        return usrNation;
    }

    public void setUsrNation(String usrNation) {
        this.usrNation = usrNation;
    }

    public String getUsrBirthDt() {
        return usrBirthDt;
    }

    public void setUsrBirthDt(String usrBirthDt) {
        this.usrBirthDt = usrBirthDt;
    }

    public String getIssuAuth() {
        return issuAuth;
    }

    public void setIssuAuth(String issuAuth) {
        this.issuAuth = issuAuth;
    }

    public String getIdEffDt() {
        return idEffDt;
    }

    public void setIdEffDt(String idEffDt) {
        this.idEffDt = idEffDt;
    }

    public String getIdExpDt() {
        return idExpDt;
    }

    public void setIdExpDt(String idExpDt) {
        this.idExpDt = idExpDt;
    }

    public String getLoginPwdRandom() {
        return loginPwdRandom;
    }

    public void setLoginPwdRandom(String loginPwdRandom) {
        this.loginPwdRandom = loginPwdRandom;
    }

    public String getPayPwdRandom() {
        return payPwdRandom;
    }

    public void setPayPwdRandom(String payPwdRandom) {
        this.payPwdRandom = payPwdRandom;
    }
}
