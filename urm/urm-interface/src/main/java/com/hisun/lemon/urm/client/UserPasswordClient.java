package com.hisun.lemon.urm.client;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.urm.dto.ChangPwdDTO;
import com.hisun.lemon.urm.dto.UserPwdSafeInfDTO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @function UserPasswordClient
 * @description 用户密码信息管理接口
 * @date 7/25/2017 Tue
 * @time 10:17 AM
 */
@FeignClient("URM")
public interface UserPasswordClient {
    /**
     * 修改登录密码
     * @param changPwdDTO
     * @return
     */
    @PutMapping("/urm/loginpwd/update")
    public GenericRspDTO<NoBody> updLoginPwd(@Validated @RequestBody GenericDTO<ChangPwdDTO> changPwdDTO);

    /**
     * 重置登录密码
     * @param changPwdDTO
     * @return
     */
    @PutMapping("/urm/loginpwd/reset")
    public GenericRspDTO<NoBody> resetLoginPwd(@Validated @RequestBody GenericDTO<ChangPwdDTO> changPwdDTO);

    /**
     * 修改支付密码
     * @param changPwdDTO
     * @return
     */
    @PutMapping("/urm/paypwd/update")
    public GenericRspDTO<NoBody> updPayPwd(@Validated @RequestBody GenericDTO<ChangPwdDTO> changPwdDTO);

    /**
     * 重置支付密码
     * @param changPwdDTO
     * @return
     */
    @PutMapping("/urm/paypwd/reset")
    public GenericRspDTO<NoBody> resetPayPwd(@Validated @RequestBody GenericDTO<ChangPwdDTO> changPwdDTO);

    /**
     * 修改密保信息
     * @param safeInfDTO
     * @return
     */
    @PutMapping("/urm/pwdsafeinf/update")
    public GenericRspDTO<NoBody> updatePwdSafeInf(@Validated @RequestBody GenericDTO<UserPwdSafeInfDTO> safeInfDTO);

    /**
     * 商户密钥重置
     * @param userId
     * @return
     */
    @PostMapping("/urm/cprkey/reset/{userId}")
    public GenericRspDTO<String> resetCprTradingKey(@Validated @PathVariable("userId") String userId);

    /**
     * 支付密码后台重置
     * @param userId
     * @return
     */
    @PutMapping("/urm/paypwd/reset/{userId}")
    public GenericRspDTO<NoBody> resetRandomPayPwd(@Validated @PathVariable("userId") String userId);

    /**
     * 登录密码后台重置
     * @param loginId
     * @return
     */
    @PutMapping("/urm/loginpwd/reset/{loginId}")
    public GenericRspDTO<NoBody> resetRandomLoginPwd(@Validated @PathVariable("loginId") String loginId);

    /**
     * 查询商户密钥
     * @param userId
     * @param genericDTO
     * @return
     */
    @GetMapping("/urm/cprkey/{userId}")
    public GenericRspDTO<String> queryCprTradingKey(@Validated @PathVariable("userId") String userId,
                                                    GenericDTO<NoBody> genericDTO);
}
