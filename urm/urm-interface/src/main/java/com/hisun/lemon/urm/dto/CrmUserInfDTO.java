package com.hisun.lemon.urm.dto;

/**
 * <AUTHOR>
 * @function CrmUserInfDTO
 * @description CRM返回Body数据
 * @date 9/15/2017 Fri
 * @time 11:59 PM
 */
public class CrmUserInfDTO {
    private String userId;//用户唯一主键
    private String countryCode;//国家地区码，默认855
    private String userPhone; //手机号
    private String userName; //用户姓名
    private String userIndentityId;//用户身份证id
    private String userEmail;//用户电子邮件账号
    private String userJob;//用户工作
    private String userSex;//用户性别
    private String userBirthday;//用户生日
    private String userWechatId;//用户微信id
    private String userFacebookId;//用户facebookid
    private String token;// 登录token,主要用于交叉全 seatel产品通用，具体待定

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserIndentityId() {
        return userIndentityId;
    }

    public void setUserIndentityId(String userIndentityId) {
        this.userIndentityId = userIndentityId;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getUserJob() {
        return userJob;
    }

    public void setUserJob(String userJob) {
        this.userJob = userJob;
    }

    public String getUserSex() {
        return userSex;
    }

    public void setUserSex(String userSex) {
        this.userSex = userSex;
    }

    public String getUserBirthday() {
        return userBirthday;
    }

    public void setUserBirthday(String userBirthday) {
        this.userBirthday = userBirthday;
    }

    public String getUserWechatId() {
        return userWechatId;
    }

    public void setUserWechatId(String userWechatId) {
        this.userWechatId = userWechatId;
    }

    public String getUserFacebookId() {
        return userFacebookId;
    }

    public void setUserFacebookId(String userFacebookId) {
        this.userFacebookId = userFacebookId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
}
