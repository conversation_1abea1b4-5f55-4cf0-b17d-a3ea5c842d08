package com.hisun.lemon.urm.dto;

import com.hisun.lemon.urm.constants.URMMessageCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @function UserPreLoginReqDTO
 * @description 用户预登录检查传输对象
 * @date 7/25/2017 Tue
 * @time 2:29 PM
 */
@ApiModel("用户预登录检查传输对象")
public class UserPreLoginRspDTO {
    /**
     * 用户类型
     */
    @ApiModelProperty(name = "userType", value = "用户类型", required = true)
    private String userType;

    /**
     * 是否允许使用手势密码 0：允许，1：不允许
     */
    @ApiModelProperty(name = "isHandPwd", value = "是否允许使用手势密码 0：允许，1：不允许")
    private String isHandPwd;

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getIsHandPwd() {
        return isHandPwd;
    }

    public void setIsHandPwd(String isHandPwd) {
        this.isHandPwd = isHandPwd;
    }
}
