package com.hisun.lemon.urm.client;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.urm.dto.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @function UserBasicInfClient
 * @description 用户基本信息处理接口
 * @date 7/25/2017 Tue
 * @time 9:38 AM
 */
@FeignClient("URM")
public interface UserBasicInfClient1 {

    /**
     * 商户开户
     * @param merRegisterDTO
     * @return
     */
    @PostMapping("/urm1/merusers")
    GenericRspDTO<String> openMerUser1(@RequestBody GenericDTO<MerRegisterDTO> merRegisterDTO);
}
