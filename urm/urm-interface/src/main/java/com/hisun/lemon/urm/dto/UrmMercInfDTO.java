package com.hisun.lemon.urm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDate;

@ApiModel("下属商户列表基本信息")
public class UrmMercInfDTO {

    @ApiModelProperty(name = "userId", value = "用户ID")
    @Length(max = 16)
    private String userId;

    @ApiModelProperty(name = "mercName", value = "商户名称")
    @Length(max = 100)
    private String mercName;

    @ApiModelProperty(name = "email", value = "电子邮件")
    private String email;

    @ApiModelProperty(name = "usrRegDt", value = "用户注册日期")
    private LocalDate usrRegDt;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMercName() {
        return mercName;
    }

    public void setMercName(String mercName) {
        this.mercName = mercName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public LocalDate getUsrRegDt() {
        return usrRegDt;
    }

    public void setUsrRegDt(LocalDate usrRegDt) {
        this.usrRegDt = usrRegDt;
    }
}
