package com.hisun.lemon.urm.interceptor;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.LemonHolder;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.lemon.urm.constants.URMMessageCode;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @create 2017/10/19
 */
@Aspect
@Component
public class MercOprAspect {

    private static final Logger logger = LoggerFactory.getLogger(MercOprAspect.class);

    @Resource
    private UserBasicInfClient userBasicInfClient;

    @Around("@annotation(MercOprAccess)")
    public Object checkMercOprAccess(ProceedingJoinPoint point) throws Throwable {

        logger.info("----Merchant Operator Permission Control----");

        String loginId = LemonHolder.getLemonData().getLoginName();
        if (JudgeUtils.isNull(loginId)) {
            logger.info("Login id is null");
            return point.proceed();
        }
        logger.info("Login id is : " + loginId);

        GenericDTO genericDTO = null;
        Object[] args = point.getArgs();
        for (Object object : args) {
            if (JudgeUtils.equals(object.getClass(), GenericDTO.class)) {
                genericDTO = (GenericDTO) object;
            }
        }

        if (null == genericDTO) {
            return point.proceed();
        }
        logger.info(genericDTO.toString());

        String channel = genericDTO.getChannel();
        if (JudgeUtils.notEquals(channel, "MERA")
                && JudgeUtils.notEquals(channel, "MERI")
                && JudgeUtils.notEquals(channel, "MERP")) {
            logger.info("Not Merchant channel");
            return point.proceed();
        }
        logger.info("The channel is " + channel);

        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        MercOprAccess mercOprAccess = method.getAnnotation(MercOprAccess.class);
        int ruleNum = mercOprAccess.role();
        logger.info("The rule num : " + ruleNum);

        String auth = null;
        try {
            auth = userBasicInfClient.mercOprAuthority(genericDTO, loginId).getBody();
        } catch (LemonException e) {
            if (JudgeUtils.equals(e.getMsgCd(), URMMessageCode.NOT_MERCHANT_OPERATOR))
                return point.proceed();
        }
        if (null == auth) {
            logger.info("The auth is null!");
            return point.proceed();
        }
        char result = auth.charAt(ruleNum - 1);
        if (JudgeUtils.notEquals(result, '1')) {
            throw new LemonException(URMMessageCode.NO_PERMISSION);
        }
        return point.proceed();
    }
}
