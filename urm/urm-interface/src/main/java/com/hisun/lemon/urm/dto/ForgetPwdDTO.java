package com.hisun.lemon.urm.dto;

import com.hisun.lemon.urm.constants.URMMessageCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @function ChangPwdDTO
 * @description 忘记密码对象
 * @date 7/27/2017 Thu
 * @time 5:38 PM
 */
@ApiModel("忘记密码对象")
public class ForgetPwdDTO {

    /**
     * 用户登录ID
     */
    @ApiModelProperty(name = "loginId", value = "用户登录ID", required = true)
    @Length(max = 20)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String loginId;

    /**
     * 类型标识
     * @return
     */
    @ApiModelProperty(name = "flag", value = "类型标识，1:注册 2:忘记/重置密码", required = true)
    private String flag;

    /**
     * 用户新密码
     */
    @ApiModelProperty(name = "newPwd", value = "用户新密码,必输",required = true)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    @Length(max = 32)
    private String newPwd;

    /**
     *  emailChkNo 邮箱验证码
     */
    @ApiModelProperty(name = "emailNo", value = "邮箱验证码,必输",required = true)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    @Length(max = 32)
    private String emailNo;

    /**
     *  payPwd 支付密码
     */
    @ApiModelProperty(name = "payPwdNo", value = "支付密码随机数,必输",required = true)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    @Length(max = 32)
    private String payPwdNo;

    public String getFlag() {
        return flag;
    }
    public void setFlag(String flag) {
        this.flag = flag;
    }
    public String getLoginId() {
        return loginId;
    }
    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }
    public String getEmailNo() {
        return emailNo;
    }
    public void setEmailNo(String emailNo) {
        this.emailNo = emailNo;
    }
    public String getPayPwdNo() {
        return payPwdNo;
    }
    public void setPayPwdNo(String payPwdNo) {
        this.payPwdNo = payPwdNo;
    }
    public String getNewPwd() {
        return newPwd;
    }
    public void setNewPwd(String newPwd) {
        this.newPwd = newPwd;
    }

}
