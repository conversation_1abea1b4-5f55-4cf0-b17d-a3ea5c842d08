package com.hisun.lemon.urm.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.constants.URMMessageCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @function UserLogInfDTO
 * @description 用户登录信息修改
 * @date 9/5/2017 Tue
 * @time 3:09 PM
 */
@Api("用户登录信息修改")
@ClientValidated
public class UserLogInfDTO {

    /**
     * loginId 登录ID，可以传手机号
     */
    @ApiModelProperty(name = "loginId", value = "用户手机号", required = true)
    @Length(max = 20)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String loginId;

    /**
     * avatarPath 用户头像路径
     */
    @ApiModelProperty(name = "avatarPath", value = "用户头像的路径")
    @Length(max = 128)
    private String avatarPath;

    /**
     * displayNm 用户昵称
     */
    @ApiModelProperty(name = "displayNm", value = "用户昵称")
    @Length(max = 60)
    private String displayNm;

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getAvatarPath() {
        return avatarPath;
    }

    public void setAvatarPath(String avatarPath) {
        this.avatarPath = avatarPath;
    }

    public String getDisplayNm() {
        return displayNm;
    }

    public void setDisplayNm(String displayNm) {
        this.displayNm = displayNm;
    }
}
