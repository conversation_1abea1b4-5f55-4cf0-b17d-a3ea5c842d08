package com.hisun.lemon.urm.client;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.urm.dto.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * <AUTHOR>
 * @function UserAuthenticationClient
 * @description 用户密码验证处理接口
 * @date 7/25/2017 Tue
 * @time 10:19 AM
 */
@FeignClient("URM")
public interface UserAuthenticationClient {
    /**
     * 用户登录
     * @param userLoginDTO
     * @return
     */
    @PostMapping("/urm/authentication/login")
    public GenericRspDTO<UserBasicInfDTO> userLogin(@Validated @RequestBody GenericDTO<UserLoginDTO> userLoginDTO);

    /**
     * 支付密码校验
     * @param checkPayPwdDTO
     * @return
     */
    @PutMapping("/urm/authentication/checkpaypwd")
    public GenericRspDTO<NoBody> checkPayPwd(@Validated @RequestBody GenericDTO<CheckPayPwdDTO> checkPayPwdDTO);

    /**
     * 支付密码校验
     * @param checkPayPwdDTO
     * @return
     */
    @PutMapping("/urm/authentication/sea/checkpaypwd")
    public GenericRspDTO<NoBody> checkPayPasswordSea(@Validated @RequestBody GenericDTO<CheckPayPwdSeaDTO> checkPayPwdDTO);


    /**
     * 登录密码校验
     * @param checkLoginPwdDTO
     * @return
     */
    @PutMapping("/urm/authentication/checkloginpwd")
    public GenericRspDTO<NoBody> checkLoginPwd(@Validated @RequestBody GenericDTO<CheckLoginPwdDTO> checkLoginPwdDTO);
}
