package com.hisun.lemon.urm.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import io.swagger.annotations.ApiModel;

/**
 * <AUTHOR>
 * @function CrmLoginReqDTO
 * @description CRM服务密码鉴权请求接口
 * @date 9/15/2017 Fri
 * @time 10:40 PM
 */
@ApiModel("CRM服务密码鉴权请求接口")
@ClientValidated
public class CrmLoginReqDTO {
    //国家地区码
    private String countryCode;

    //用户手机号
    private String userPhone;

    //用户密码
    private String userPassword;

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public String getUserPassword() {
        return userPassword;
    }

    public void setUserPassword(String userPassword) {
        this.userPassword = userPassword;
    }
}
