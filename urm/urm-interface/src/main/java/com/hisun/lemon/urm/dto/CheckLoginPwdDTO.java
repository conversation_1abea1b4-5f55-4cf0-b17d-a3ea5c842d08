package com.hisun.lemon.urm.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.constants.URMMessageCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @function CheckLoginPwdDTO
 * @description 校验登录密码
 * @date 8/10/2017 Thu
 * @time 11:10 AM
 */
@ApiModel("校验登录密码")
@ClientValidated
public class CheckLoginPwdDTO {
    /**
     * loginId 登录ID
     */
    @ApiModelProperty(name = "loginId", value = "登录ID", required = true)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String loginId;

    /**
     * loginPwd 登录密码
     */
    @ApiModelProperty(name = "loginPwd", value = "登录密码", required = true)
    @Length(max = 256)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String loginPwd;

    /**
     * loginPwdRandom 登录密码随机数
     */
    @ApiModelProperty(name = "loginPwdRandom", value = "登录密码随机数")
    private String loginPwdRandom;

    public String getLoginPwdRandom() {
        return loginPwdRandom;
    }

    public void setLoginPwdRandom(String loginPwdRandom) {
        this.loginPwdRandom = loginPwdRandom;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getLoginPwd() {
        return loginPwd;
    }

    public void setLoginPwd(String loginPwd) {
        this.loginPwd = loginPwd;
    }
}
