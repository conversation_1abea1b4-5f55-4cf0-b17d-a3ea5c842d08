package com.hisun.lemon.urm.dto;

import com.hisun.lemon.urm.constants.URMMessageCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @function UserPwdSafeInfDTO
 * @description 密保信息传输对象
 * @date 8/2/2017 Wed
 * @time 6:29 PM
 */
@ApiModel("密保信息")
public class UserPwdSafeInfDTO {
    /**
     * 用户登录ID
     */
    @ApiModelProperty(name = "loginId", value = "用户登录ID", required = true)
    @Length(max = 20)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String loginId;

    /**
     * 支付密码
     */
    @ApiModelProperty(name = "payPwd", value = "支付密码", required = true)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String payPwd;

    /**
     * 支付密码随机数
     */
    @ApiModelProperty(name = "payPwdRandom", value = "支付密码随机数", required = true)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String payPwdRandom;

    /**
     * 密保手机号
     */
    @ApiModelProperty(name = "safeMblNo", value = "密保手机号")
    @Length(max = 20)
    private String safeMblNO;

    /**
     * 密保问题1
     */
    @ApiModelProperty(name = "safeQues1", value = "密保问题1")
    @Length(max = 256)
    private String safeQues1;

    /**
     * 密保答案1
     */
    @ApiModelProperty(name = "safeAns1", value = "密保答案1")
    @Length(max = 256)
    private String safeAns1;

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getPayPwd() {
        return payPwd;
    }

    public void setPayPwd(String payPwd) {
        this.payPwd = payPwd;
    }

    public String getSafeMblNO() {
        return safeMblNO;
    }

    public void setSafeMblNO(String safeMblNO) {
        this.safeMblNO = safeMblNO;
    }

    public String getSafeQues1() {
        return safeQues1;
    }

    public void setSafeQues1(String safeQues1) {
        this.safeQues1 = safeQues1;
    }

    public String getSafeAns1() {
        return safeAns1;
    }

    public void setSafeAns1(String safeAns1) {
        this.safeAns1 = safeAns1;
    }

    public String getPayPwdRandom() {
        return payPwdRandom;
    }

    public void setPayPwdRandom(String payPwdRandom) {
        this.payPwdRandom = payPwdRandom;
    }
}
