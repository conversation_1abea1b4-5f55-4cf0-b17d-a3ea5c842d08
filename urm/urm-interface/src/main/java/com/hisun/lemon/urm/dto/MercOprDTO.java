package com.hisun.lemon.urm.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.constants.URMMessageCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @create 2017/10/12
 */
@ApiModel("商户操作员")
@ClientValidated
public class MercOprDTO {

    @ApiModelProperty(name = "loginId", value = "用户登录ID", required = true)
    @Length(max = 20)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String loginId;

    @ApiModelProperty(name = "mblNo", value = "手机号", required = true)
    @Length(max = 20)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String mblNO;

    @ApiModelProperty(name = "displayNm", value = "操作员姓名")
    @Length(max = 60)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String displayNm;

    @ApiModelProperty(name = "loginPwd", value = "登录密码", required = true)
    private String loginPwd;

    public String getLoginPwdRandom() {
        return loginPwdRandom;
    }

    public void setLoginPwdRandom(String loginPwdRandom) {
        this.loginPwdRandom = loginPwdRandom;
    }

    @ApiModelProperty(name  = "loginPwdRandom", value= "登录密码随机数", required = true)
    private String loginPwdRandom;

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getMblNO() {
        return mblNO;
    }

    public void setMblNO(String mblNO) {
        this.mblNO = mblNO;
    }

    public String getDisplayNm() {
        return displayNm;
    }

    public void setDisplayNm(String displayNm) {
        this.displayNm = displayNm;
    }

    public String getLoginPwd() {
        return loginPwd;
    }

    public void setLoginPwd(String loginPsw) {
        this.loginPwd = loginPsw;
    }

    @Override
    public String toString() {
        return "MercOprDTO{" +
                "loginId='" + loginId + '\'' +
                ", mblNO='" + mblNO + '\'' +
                ", displayNm='" + displayNm + '\'' +
                ", loginPsw='" + loginPwd + '\'' +
                '}';
    }
}
