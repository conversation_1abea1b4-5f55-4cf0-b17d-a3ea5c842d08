package com.hisun.lemon.urm.dto;

import com.hisun.lemon.urm.constants.URMMessageCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @create 2017/12/13
 */
@ApiModel("批量开户传输对象")
public class BatchOpenUserDTO {

    @ApiModelProperty(name = "loginPwd", value = "登录密码", required = true)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String loginPwd;

    @ApiModelProperty(name = "payPwd", value = "支付密码", required = true)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String payPwd;

    @ApiModelProperty(name = "safeQues1", value = "密保问题1", required = true)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    @Length(max = 256)
    private String safeQues1;

    @ApiModelProperty(name = "safeAns1", value = "密保答案1", required = true)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    @Length(max = 256)
    private String safeAns1;

    public String getLoginPwd() {
        return loginPwd;
    }

    public void setLoginPwd(String loginPwd) {
        this.loginPwd = loginPwd;
    }

    public String getPayPwd() {
        return payPwd;
    }

    public void setPayPwd(String payPwd) {
        this.payPwd = payPwd;
    }

    public String getSafeQues1() {
        return safeQues1;
    }

    public void setSafeQues1(String safeQues1) {
        this.safeQues1 = safeQues1;
    }

    public String getSafeAns1() {
        return safeAns1;
    }

    public void setSafeAns1(String safeAns1) {
        this.safeAns1 = safeAns1;
    }

    @Override
    public String toString() {
        return "BatchOpenUserDTO{" +
                "loginPwd='" + loginPwd + '\'' +
                ", payPwd='" + payPwd + '\'' +
                ", safeQues1='" + safeQues1 + '\'' +
                ", safeAns1='" + safeAns1 + '\'' +
                '}';
    }
}
