package com.hisun.lemon.urm.dto;

import com.hisun.lemon.urm.constants.URMMessageCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @function UserPreLoginReqDTO
 * @description 用户预登录检查传输对象
 * @date 7/25/2017 Tue
 * @time 2:29 PM
 */
@ApiModel("用户预登录检查传输对象")
public class UserPreLoginReqDTO {
    /**
     * 用户登录ID
     */
    @ApiModelProperty(name = "loginId", value = "登录ID", required = true)
    @Length(max = 20)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String loginId;

    /**
     * 登录密码随机数
     */
    @ApiModelProperty(name = "clientId", value = "新设备Id")
    private String clientId;

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }
}
