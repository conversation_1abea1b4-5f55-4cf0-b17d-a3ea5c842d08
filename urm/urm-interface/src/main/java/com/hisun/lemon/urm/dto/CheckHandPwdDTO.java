package com.hisun.lemon.urm.dto;

import com.hisun.lemon.framework.validation.ClientValidated;
import com.hisun.lemon.urm.constants.URMMessageCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @function CheckHandPwdDTO
 * @description 校验手势密码
 * @date 8/10/2017 Thu
 * @time 11:10 AM
 */
@ApiModel("校验手势密码")
@ClientValidated
public class CheckHandPwdDTO {
    /**
     * loginId 登录ID
     */
    @ApiModelProperty(name = "loginId", value = "登录ID", required = false)
    @Length(max = 20)
    //@NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String loginId;

    /**
     * handPwd 登录密码
     */
    @ApiModelProperty(name = "handPwd", value = "手势密码", required = true)
    @Length(max = 256)
    @NotBlank(message = URMMessageCode.PARAM_IS_NULL)
    private String handPwd;

    public String getHandPwd() {
        return handPwd;
    }

    public void setHandPwd(String handPwd) {
        this.handPwd = handPwd;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }


}
