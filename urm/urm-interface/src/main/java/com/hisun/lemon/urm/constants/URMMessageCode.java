package com.hisun.lemon.urm.constants;

/**
 * <AUTHOR>
 * @function URMMessageCode
 * @description URM模块错误码设置
 * @date 7/25/2017 Tue
 * @time 3:14 PM
 */
public class URMMessageCode {



    private URMMessageCode() {

    }

    /**
     * 用户号为空
     */
    public static final String USER_ID_IS_NULL = "URM10000";

    /**
     * 密码为空
     */
    public static final String PWD_IS_NULL = "URM10001";

    /**
     * 密保信息为空
     */
    public static final String SAFE_INF_IS_NULL = "URM10002";

    /**
     * 参数为空
     */
    public static final String PARAM_IS_NULL = "URM10003";

    /**
     * 用户不存在
     */
    public static final String USR_NOT_EXIST = "URM30000";

    /**
     * 用户验证失败
     */
    public static final String USR_AUTH_FAILURE = "URM30001";

    /**
     * 用户手机号非法
     */
    public static final String ILLEGAL_MBL_NO = "URM30002";

    /**
     * 用户基本信息为空
     */
    public static final String USR_BASIC_INF_IS_NULL = "URM30003";

    /**
     * 用户安全信息为空
     */
    public static final String USR_SAFE_INF_IS_NULL = "URM30004";

    /**
     * 支付密码校验错误
     */
    public static final String PAY_PWD_CHECK_FAIL = "URM30005";

    /**
     * 没有有效的密码
     */
    public static final String NO_EFFECTIVE_PWD = "URM30006";

    /**
     * 用户密保问题校验错误
     */
    public static final String SAFE_QUES_CHECK_FAIL = "URM30007";

    /**
     * 用户已注册
     */
    public static final String USR_ALREADY_REGISTERED = "URM30008";

    /**
     * 该证件已被他人使用，请重新输入
     */
    public static final String ID_EXCEED_MAX_NUM = "URM30009";

    /**
     * 登录密码检查失败
     */
    public static final String LOG_PWD_CHECK_FAIL = "URM30010";

    /**
     * 支付密码被锁定
     */
    public static final String PAY_PWD_ALREADY_LOCK = "URM30011";

    /**
     * 登录密码被锁定
     */
    public static final String LOG_PWD_ALREADY_LOCK = "URM30012";

    /**
     * 开户失败
     */
    public static final String OPEN_USER_FAIL = "URM30013";

    /**
     * 不支持一般用户进行此操作
     */
    public static final String ORDINARY_USER_NOT_SUPPORT = "URM30014";

    /**
     * 管理员账号已被使用
     */
    public static final String LOGID_ALREADY_USED = "URM30015";

    /**
     * 暂不支持此用户类型
     */
    public static final String USRLVL_NOT_SUPPORT = "URM30016";

    /**
     * 商户信息不全
     */
    public static final String MER_INF_IS_NULL = "URM30017";

    /**
     * 用户不能登录商户APP
     */
    public static final String NOT_MER_USER = "URM30018";

    /**
     * 商户操作员不能登录用户APP
     */
    public static final String NOT_ORDINARY_USER = "URM30019";

    /**
     * 不合法的证件信息
     */
    public static final String ILLEGAL_ID_INF = "URM30020";

    /**
     * 密码转加密失败
     */
    public static final String TRANSLATE_PIN_FAILURE = "URM30021";

    /**
     * RSA解密失败
     */
    public static final String RSA_DECRYPT_FAILURE = "URM30022";

    /**
     * 3DES解密失败
     */
    public static final String TRIPLEDES_DECRYPT_FAILURE = "URM30023";

    /**
     * ZPK转PVK加密失败
     */
    public static final String TRANSLATE_ZPK_TO_PVK_FAILURE = "URM30024";

    /**
     * 明文支付密码生成PINOFFSET 失败
     */
    public static final String PLAINTEXT_PWD_TRANSLATE_FAILURE = "URM30025";

    /**
     * 初始化密码失败
     */
    public static final String INIT_PWD_FAILURE = "URM30026";

    /**
     * PIN模式错误
     */
    public static final String PIN_MODE_WRONG = "URM30027";

    /**
     * CRM通讯失败
     */
    public static final String CONNECT_CRM_FAILURE = "URM30028";

    /**
     * CRM鉴权失败
     */
    public static final String CRM_AUTH_FAILURE = "URM30029";

    /**
     * 用户快捷卡验证失败
     */
    public static final String BANK_CRD_CHECK_FAIL = "URM30030";

    /**
     * 用户不可以直接重置支付
     */
    public static final String NOT_IMMEDIATE_RESET_PAYPWD = "URM30031";

    /**
     * 用户已实名，不能再次实名
     */
    public static final String USER_ALREADY_REALNM = "URM30032";

    /**
     * 请重新输入密码
     */
    public static final String RETYPE_PASSWORD = "URM30033";

    /**
     * 加密机处理失败(加密机通讯异常)
     */
    public static final String HSM_HANDLE_FAILURE = "URM20000";

    /**
     * 该用户不是商户操作员
     */
    public static final String NOT_MERCHANT_OPERATOR = "URM30034";

    /**
     * 权限不足
     */
    public static final String NO_PERMISSION = "URM30035";

    /**
     * 登录id已被使用
     */
    public static final String LOGIN_ID_IS_EXISTS = "URM30036";

    /**
     * 用户已销户
     */
    public static final String USER_ALREADY_CANCEL = "URM30037";

    /**
     * 解密失败
     */
    public static final String RSA2_DECRYPT_FAILURE = "URM30038";

    /**
     * 手势密码为空
     */
    public static final String HAND_PWD_ISNULL = "URM30039";

    /**
     * 手势密码未开通
     */
    public static final String HAND_PWD_NOT_OPEN = "URM30040";

    /**
     * 手势密码错误
     */
    public static final String HAND_PWD_ERROR = "URM30041";

    /**
     * 手势密码异常
     */
    //public static final String HAND_PWD_EXCEPTION = "URM30042";

    /**
     * 手势密码被锁定
     */
    public static final String HAND_PWD_ALREADY_LOCK = "URM30042";

    /**
     * 手势密码校验错误
     */
    public static final String Hand_PWD_CHECK_FAIL = "URM30043";

    /**
     * 手势密码标志为空
     */
    public static final String HAND_FLG_ISNULL = "URM30044";

    /**
     * 手势密码设置错误
     */
    public static final String HAND_FLG_SET_ERROR = "URM30045";

    /**
     * 手势密码标志错误
     */
    public static final String HAND_FLG_ERROR = "URM30046";

    /**
     * 手势密码未设置
     */
    public static final String HAND_PWD_NOT_SET = "URM30047";

    /**
     * 新旧手势密码一致
     */
    public static final String HAND_PWD_NOT_SAME = "URM30048";

    /**
     * 登录密码错误
     */
    public static final String LOG_PWD_ERROR = "URM30049";

    /**
     * 商户注销
     */
    public static final String CANCEL_THE_ACCOUNT = "URM30050";

    /**
     * 邮箱验证失败
     */
    public static final String EMAIL_AUTH_FAIL = "URM30051";

    /**
     * 邮箱已存在
     */
    public static final String EMAIL_EXIST = "URM30052";
    /**
     * 邮箱不存在
     */
    public static final String EMAIL_NOT_EXIST = "URM30053";

    /**
     * 邮箱验证码已发送
     */
    public static final String EMAIL_CODE_EXIST = "URM30054";

    /**
     * 错误次数过多，请稍后再试
     */
    public static final String EMAIL_TIMES_ERROR = "URM30055" ;

    /**
     * Kyb认证信息上传失败
     */
    public static final String KYB_SUBMIT_ERROR = "URM30056";

    /**
     * 存在待审核的kyb认证申请或已经认证成功
     */
    public static final String KYB_CERT_EXIST = "URM30057";

    /**
     * 用户未认证
     */
    public static final String USER_NOT_AUTH = "URM30058";

}
