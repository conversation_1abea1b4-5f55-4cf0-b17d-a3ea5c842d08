package com.hisun.lemon.urm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 发送邮箱验证码DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/9 09:31
 */
@ApiModel("发送邮箱验证码DTO")
public class EmailCodeSendDto {

    /**
     * 邮箱
     */
    @ApiModelProperty(name = "email", value = "邮箱", required = true)
    private String email;

    /**
     * 类型标识
     * @return
     */
    @ApiModelProperty(name = "flag", value = "类型标识，1:注册 2:忘记/重置密码", required = true)
    private String flag;


    public String getEmail() {
        return email;
    }
    public void setEmail(String email) {
        this.email = email;
    }
    public String getFlag() {
        return flag;
    }
    public void setFlag(String flag) {
        this.flag = flag;
    }
}
