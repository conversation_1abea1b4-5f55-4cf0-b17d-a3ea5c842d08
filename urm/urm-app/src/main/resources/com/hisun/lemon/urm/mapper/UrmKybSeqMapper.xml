<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.urm.dao.UrmKybSeqDao">

    <resultMap id="urmKybSeqResultMap" type="com.hisun.lemon.urm.entity.UrmKybSeqDO">
        <!-- 主键字段映射 -->
        <id column="kyb_id" property="kybId" jdbcType="INTEGER"/>

        <!-- 普通字段映射 -->
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="examine_nm" property="examineNm" jdbcType="VARCHAR"/>
        <result column="examine_status" property="examineStatus" jdbcType="VARCHAR"/>
        <result column="kyb_info" property="kybInfo" jdbcType="LONGVARCHAR"/> <!-- 若数据库是text类型，用LONGVARCHAR -->
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="insert" parameterType="com.hisun.lemon.urm.entity.UrmKybSeqDO">
        insert into urm_kyb_seq
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">USER_ID,</if>
            <if test="examineNm != null">EXAMINE_NM,</if>
            <if test="examineStatus != null">EXAMINE_STATUS,</if>
            <if test="kybInfo != null">KYB_INFO,</if>
            <if test="remark != null">REMARK,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId,jdbcType=VARCHAR},</if>
            <if test="examineNm != null">#{examineNm,jdbcType=VARCHAR},</if>
            <if test="examineStatus != null">#{examineStatus,jdbcType=VARCHAR},</if>
            <if test="kybInfo != null">#{kybInfo,jdbcType=LONGVARCHAR},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.urm.entity.UrmKybSeqDO">
        update urm_kyb_seq
        <set>
            <if test="examineStatus != null">
                EXAMINE_STATUS = #{examineStatus,jdbcType=VARCHAR},
            </if>
            <if test="kybInfo != null">
                KYB_INFO = #{kybInfo,jdbcType=LONGVARCHAR},
            </if>
            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR}
            </if>
        </set>
        where KYB_ID = #{kybId,jdbcType=VARCHAR}
    </update>

    <select id="getKybList" resultMap="urmKybSeqResultMap" parameterType="java.lang.String">
        select kyb_id,
               user_id,
               examine_nm,
               examine_status,
               kyb_info,
               remark
        from urm_kyb_seq where user_id = #{userId,jdbcType=VARCHAR}
    </select>

</mapper>