<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.urm.dao.IQuesDao">

    <select id="findQuesByQuesNo" parameterType="java.lang.Integer" resultType="com.hisun.lemon.urm.entity.QuesDo">
        select ques_id as quesId, ques_no as quesNo, safe_ques as safeQues from urm_safe_ques_enum
        where ques_no = #{quesNo,jdbcType=INTEGER} and 13 >= ques_id
    </select>

    <select id="findQuesList" parameterType="java.lang.Integer" resultType="java.lang.String">
        select safe_ques as safeQues from urm_safe_ques_enum where ques_no = #{quesNo,jdbcType=INTEGER}
    </select>

    <select id="findNoByQues" parameterType="java.lang.String" resultType="com.hisun.lemon.urm.entity.QuesDo">
        select ques_id as quesId, ques_no as quesNo, safe_ques as safeQues from urm_safe_ques_enum
        where safe_ques = #{safeQues,jdbcType=VARCHAR}
    </select>
</mapper>