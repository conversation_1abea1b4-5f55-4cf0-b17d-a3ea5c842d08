<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.urm.dao.IUrmSsoApplyInfDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.urm.entity.UrmSsoApplyInfDO" >
        <id column="MSG_ID" property="msgId" jdbcType="VARCHAR" />
        <result column="SAFE_ID" property="safeId" jdbcType="VARCHAR" />
        <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
        <result column="SSO_DT" property="ssoDt" jdbcType="DATE" />
        <result column="SSO_TM" property="ssoTm" jdbcType="TIME" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        MSG_ID, SAFE_ID, USER_ID, SSO_DT, SSO_TM
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from urm_sso_apply_inf
        where MSG_ID = #{msgId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from urm_sso_apply_inf
        where MSG_ID = #{msgId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.urm.entity.UrmSsoApplyInfDO" >
        insert into urm_sso_apply_inf
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="msgId != null" >
                MSG_ID,
            </if>
            <if test="safeId != null" >
                SAFE_ID,
            </if>
            <if test="userId != null" >
                USER_ID,
            </if>
            <if test="ssoDt != null" >
                SSO_DT,
            </if>
            <if test="ssoTm != null" >
                SSO_TM,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="msgId != null" >
                #{msgId,jdbcType=VARCHAR},
            </if>
            <if test="safeId != null" >
                #{safeId,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="ssoDt != null" >
                #{ssoDt,jdbcType=DATE},
            </if>
            <if test="ssoTm != null" >
                #{ssoTm,jdbcType=TIME},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.urm.entity.UrmSsoApplyInfDO" >
        update urm_sso_apply_inf
        <set >
            <if test="safeId != null" >
                SAFE_ID = #{safeId,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                USER_ID = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="ssoDt != null" >
                SSO_DT = #{ssoDt,jdbcType=DATE},
            </if>
            <if test="ssoTm != null" >
                SSO_TM = #{ssoTm,jdbcType=TIME},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where MSG_ID = #{msgId,jdbcType=VARCHAR}
    </update>
</mapper>