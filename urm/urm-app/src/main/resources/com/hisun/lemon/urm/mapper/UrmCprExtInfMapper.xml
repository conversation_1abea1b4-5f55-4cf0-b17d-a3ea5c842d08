<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.urm.dao.IUrmCprExtInfDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.urm.entity.UrmCprExtInfDO" >
        <id column="USER_ID" property="userId" jdbcType="VARCHAR" />
        <result column="MERC_NAME" property="mercName" jdbcType="VARCHAR" />
        <result column="MERC_SHORT_NAME" property="mercShortName" jdbcType="VARCHAR" />
        <result column="CPR_REG_NM_CN" property="cprRegNmCn" jdbcType="VARCHAR" />
        <result column="CPR_OPER_NM_CN" property="cprOperNmCn" jdbcType="VARCHAR" />
        <result column="PRIN_NM" property="prinNm" jdbcType="VARCHAR" />
        <result column="CRP_NM" property="crpNm" jdbcType="VARCHAR" />
        <result column="CRP_ID_TYP" property="crpIdTyp" jdbcType="VARCHAR" />
        <result column="CRP_ID_NO" property="crpIdNo" jdbcType="VARCHAR" />
        <result column="COMERC_REG" property="comercReg" jdbcType="VARCHAR" />
        <result column="SOCIAL_CRD_CD" property="socialCrdCd" jdbcType="VARCHAR" />
        <result column="ORG_CD" property="orgCd" jdbcType="VARCHAR" />
        <result column="BUSI_LISC" property="busiLisc" jdbcType="VARCHAR" />
        <result column="TAX_CERT_ID" property="taxCertId" jdbcType="VARCHAR" />
        <result column="WEB_NM" property="webNm" jdbcType="VARCHAR" />
        <result column="WEB_URL" property="webUrl" jdbcType="VARCHAR" />
        <result column="MER_REG_ADDR" property="merRegAddr" jdbcType="VARCHAR" />
        <result column="MER_ADDR_LONGITUDE" property="merAddrLongitude" jdbcType="DECIMAL" />
        <result column="MER_ADDR_LATITUDE" property="merAddrLatitude" jdbcType="DECIMAL" />
        <result column="MGT_SCP" property="mgtScp" jdbcType="VARCHAR" />
        <result column="NEED_INV_FLG" property="needInvFlg" jdbcType="VARCHAR" />
        <result column="INV_MOD" property="invMod" jdbcType="VARCHAR" />
        <result column="INV_TIT" property="invTit" jdbcType="VARCHAR" />
        <result column="INV_MAIL_ADDR" property="invMailAddr" jdbcType="VARCHAR" />
        <result column="INV_MAIL_ZIP" property="invMailZip" jdbcType="VARCHAR" />
        <result column="OPN_BUS_DT" property="opnBusDt" jdbcType="DATE" />
        <result column="BUS_LIC_EXP_DT" property="busLicExpDt" jdbcType="DATE" />
        <result column="MERC_TRD_CLS" property="mercTrdCls" jdbcType="VARCHAR" />
        <result column="MERC_TRD_DESC" property="mercTrdDesc" jdbcType="VARCHAR" />
        <result column="CPR_TYP" property="cprTyp" jdbcType="VARCHAR" />
        <result column="MER_LVL" property="merLvl" jdbcType="VARCHAR" />
        <result column="BEL_MERC" property="belongMerc" jdbcType="VARCHAR" />
        <result column="CS_TEL_NO" property="csTelNo" jdbcType="VARCHAR" />
        <result column="MERC_HOT_LIN" property="mercHotLin" jdbcType="VARCHAR" />
        <result column="CUS_MGR" property="cusMgr" jdbcType="VARCHAR" />
        <result column="CUS_MGR_NM" property="cusMgrNm" jdbcType="VARCHAR" />
        <result column="REFEREE_MBL_NO" property="refereeMblNo" jdbcType="VARCHAR" />
        <result column="RCV_MAG_AMT" property="rcvMagAmt" jdbcType="DECIMAL" />
        <result column="LAST_UPD_OPR" property="lastUpdOpr" jdbcType="VARCHAR" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        USER_ID, MERC_NAME, MERC_SHORT_NAME, CPR_REG_NM_CN, CPR_OPER_NM_CN, PRIN_NM, CRP_NM, CRP_ID_TYP, CRP_ID_NO,
        COMERC_REG, SOCIAL_CRD_CD, ORG_CD, BUSI_LISC, TAX_CERT_ID, WEB_NM, WEB_URL, MER_REG_ADDR, MER_ADDR_LONGITUDE,
        MER_ADDR_LATITUDE, MGT_SCP, NEED_INV_FLG, INV_MOD, INV_TIT, INV_MAIL_ADDR, INV_MAIL_ZIP, OPN_BUS_DT,
        BUS_LIC_EXP_DT, MERC_TRD_CLS, MERC_TRD_DESC, CPR_TYP, MER_LVL, CS_TEL_NO, MERC_HOT_LIN, CUS_MGR, CUS_MGR_NM,
        REFEREE_MBL_NO, RCV_MAG_AMT, LAST_UPD_OPR, BEL_MERC
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from urm_cpr_ext_inf
        where USER_ID = #{userId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from urm_cpr_ext_inf
        where USER_ID = #{userId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.urm.entity.UrmCprExtInfDO" >
        insert into urm_cpr_ext_inf
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                USER_ID,
            </if>
            <if test="mercName != null" >
                MERC_NAME,
            </if>
            <if test="mercShortName != null" >
                MERC_SHORT_NAME,
            </if>
            <if test="cprRegNmCn != null" >
                CPR_REG_NM_CN,
            </if>
            <if test="cprOperNmCn != null" >
                CPR_OPER_NM_CN,
            </if>
            <if test="prinNm != null" >
                PRIN_NM,
            </if>
            <if test="crpNm != null" >
                CRP_NM,
            </if>
            <if test="crpIdTyp!= null" >
                CRP_ID_TYP,
            </if>
            <if test="crpIdNo!= null" >
                CRP_ID_NO,
            </if>
            <if test="opnBusDt != null">
                OPN_BUS_DT,
            </if>
            <if test="busLicExpDt!= null">
                BUS_LIC_EXP_DT,
            </if>
            <if test="refereeMblNo != null">
                REFEREE_MBL_NO,
            </if>
            <if test="comercReg != null" >
                COMERC_REG,
            </if>
            <if test="socialCrdCd != null" >
                SOCIAL_CRD_CD,
            </if>
            <if test="orgCd != null" >
                ORG_CD,
            </if>
            <if test="busiLisc != null" >
                BUSI_LISC,
            </if>
            <if test="taxCertId != null" >
                TAX_CERT_ID,
            </if>
            <if test="webNm != null" >
                WEB_NM,
            </if>
            <if test="webUrl != null" >
                WEB_URL,
            </if>
            <if test="merRegAddr != null" >
                MER_REG_ADDR,
            </if>
            <if test="merAddrLongitude != null" >
                MER_ADDR_LONGITUDE,
            </if>
            <if test="merAddrLatitude != null" >
                MER_ADDR_LATITUDE,
            </if>
            <if test="mgtScp != null" >
                MGT_SCP,
            </if>
            <if test="needInvFlg != null" >
                NEED_INV_FLG,
            </if>
            <if test="invMod != null" >
                INV_MOD,
            </if>
            <if test="invTit != null" >
                INV_TIT,
            </if>
            <if test="invMailAddr != null" >
                INV_MAIL_ADDR,
            </if>
            <if test="invMailZip != null" >
                INV_MAIL_ZIP,
            </if>
            <if test="mercTrdCls != null" >
                MERC_TRD_CLS,
            </if>
            <if test="mercTrdDesc != null" >
                MERC_TRD_DESC,
            </if>
            <if test="cprTyp != null" >
                CPR_TYP,
            </if>
            <if test="merLvl != null" >
                MER_LVL,
            </if>
            <if test="belongMerc != null" >
                BEL_MERC,
            </if>
            <if test="csTelNo != null" >
                CS_TEL_NO,
            </if>
            <if test="mercHotLin != null" >
                MERC_HOT_LIN,
            </if>
            <if test="cusMgr != null" >
                CUS_MGR,
            </if>
            <if test="cusMgrNm != null" >
                CUS_MGR_NM,
            </if>
            <if test="rcvMagAmt != null" >
                RCV_MAG_AMT,
            </if>
            <if test="lastUpdOpr != null" >
                LAST_UPD_OPR,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="mercName != null" >
                #{mercName,jdbcType=VARCHAR},
            </if>
            <if test="mercShortName != null" >
                #{mercShortName,jdbcType=VARCHAR},
            </if>
            <if test="cprRegNmCn != null" >
                #{cprRegNmCn,jdbcType=VARCHAR},
            </if>
            <if test="cprOperNmCn != null" >
                #{cprOperNmCn,jdbcType=VARCHAR},
            </if>
            <if test="prinNm != null" >
                #{prinNm,jdbcType=VARCHAR},
            </if>
            <if test="crpNm != null" >
                #{crpNm,jdbcType=VARCHAR},
            </if>
            <if test="crpIdTyp!= null" >
                #{crpIdTyp,jdbcType=VARCHAR},
            </if>
            <if test="crpIdNo!= null" >
                #{crpIdNo,jdbcType=VARCHAR},
            </if>
            <if test="opnBusDt != null">
                #{opnBusDt, jdbcType=DATE},
            </if>
            <if test="busLicExpDt!= null">
                #{busLicExpDt, jdbcType=DATE},
            </if>
            <if test="refereeMblNo != null">
                #{refereeMblNo, jdbcType=DATE},
            </if>
            <if test="comercReg != null" >
                #{comercReg,jdbcType=VARCHAR},
            </if>
            <if test="socialCrdCd != null" >
                #{socialCrdCd,jdbcType=VARCHAR},
            </if>
            <if test="orgCd != null" >
                #{orgCd,jdbcType=VARCHAR},
            </if>
            <if test="busiLisc != null" >
                #{busiLisc,jdbcType=VARCHAR},
            </if>
            <if test="taxCertId != null" >
                #{taxCertId,jdbcType=VARCHAR},
            </if>
            <if test="webNm != null" >
                #{webNm,jdbcType=VARCHAR},
            </if>
            <if test="webUrl != null" >
                #{webUrl,jdbcType=VARCHAR},
            </if>
            <if test="merRegAddr != null" >
                #{merRegAddr,jdbcType=VARCHAR},
            </if>
            <if test="merAddrLongitude != null" >
                #{merAddrLongitude,jdbcType=DECIMAL},
            </if>
            <if test="merAddrLatitude != null" >
                #{merAddrLatitude,jdbcType=DECIMAL},
            </if>
            <if test="mgtScp != null" >
                #{mgtScp,jdbcType=VARCHAR},
            </if>
            <if test="needInvFlg != null" >
                #{needInvFlg,jdbcType=VARCHAR},
            </if>
            <if test="invMod != null" >
                #{invMod,jdbcType=VARCHAR},
            </if>
            <if test="invTit != null" >
                #{invTit,jdbcType=VARCHAR},
            </if>
            <if test="invMailAddr != null" >
                #{invMailAddr,jdbcType=VARCHAR},
            </if>
            <if test="invMailZip != null" >
                #{invMailZip,jdbcType=VARCHAR},
            </if>
            <if test="mercTrdCls != null" >
                #{mercTrdCls,jdbcType=VARCHAR},
            </if>
            <if test="mercTrdDesc != null" >
                #{mercTrdDesc,jdbcType=VARCHAR},
            </if>
            <if test="cprTyp != null" >
                #{cprTyp,jdbcType=VARCHAR},
            </if>
            <if test="merLvl != null" >
                #{merLvl,jdbcType=VARCHAR},
            </if>
            <if test="belongMerc != null" >
                #{belongMerc,jdbcType=VARCHAR},
            </if>
            <if test="csTelNo != null" >
                #{csTelNo,jdbcType=VARCHAR},
            </if>
            <if test="mercHotLin != null" >
                #{mercHotLin,jdbcType=VARCHAR},
            </if>
            <if test="cusMgr != null" >
                #{cusMgr,jdbcType=VARCHAR},
            </if>
            <if test="cusMgrNm != null" >
                #{cusMgrNm,jdbcType=VARCHAR},
            </if>
            <if test="rcvMagAmt != null" >
                #{rcvMagAmt,jdbcType=DECIMAL},
            </if>
            <if test="lastUpdOpr != null" >
                #{lastUpdOpr,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.urm.entity.UrmCprExtInfDO" >
        update urm_cpr_ext_inf
        <set >
            <if test="mercName != null" >
                MERC_NAME = #{mercName,jdbcType=VARCHAR},
            </if>
            <if test="mercShortName != null" >
                MERC_SHORT_NAME = #{mercShortName,jdbcType=VARCHAR},
            </if>
            <if test="cprRegNmCn != null" >
                CPR_REG_NM_CN = #{cprRegNmCn,jdbcType=VARCHAR},
            </if>
            <if test="cprOperNmCn != null" >
                CPR_OPER_NM_CN = #{cprOperNmCn,jdbcType=VARCHAR},
            </if>
            <if test="prinNm != null" >
                PRIN_NM = #{prinNm,jdbcType=VARCHAR},
            </if>
            <if test="crpNm != null" >
                CRP_NM = #{crpNm,jdbcType=VARCHAR},
            </if>
            <if test="crpIdTyp!= null" >
                CRP_ID_TYP = #{crpIdTyp,jdbcType=VARCHAR},
            </if>
            <if test="crpIdNo!= null" >
                CRP_ID_NO = #{crpIdNo,jdbcType=VARCHAR},
            </if>
            <if test="opnBusDt != null">
                OPN_BUS_DT = #{opnBusDt, jdbcType=DATE},
            </if>
            <if test="busLicExpDt!= null">
                BUS_LIC_EXP_DT = #{busLicExpDt, jdbcType=DATE},
            </if>
            <if test="refereeMblNo != null">
                REFEREE_MBL_NO = #{refereeMblNo, jdbcType=DATE},
            </if>
            <if test="comercReg != null" >
                COMERC_REG,
            </if>
            <if test="comercReg != null" >
                COMERC_REG = #{comercReg,jdbcType=VARCHAR},
            </if>
            <if test="socialCrdCd != null" >
                SOCIAL_CRD_CD = #{socialCrdCd,jdbcType=VARCHAR},
            </if>
            <if test="orgCd != null" >
                ORG_CD = #{orgCd,jdbcType=VARCHAR},
            </if>
            <if test="busiLisc != null" >
                BUSI_LISC = #{busiLisc,jdbcType=VARCHAR},
            </if>
            <if test="taxCertId != null" >
                TAX_CERT_ID = #{taxCertId,jdbcType=VARCHAR},
            </if>
            <if test="webNm != null" >
                WEB_NM = #{webNm,jdbcType=VARCHAR},
            </if>
            <if test="webUrl != null" >
                WEB_URL = #{webUrl,jdbcType=VARCHAR},
            </if>
            <if test="merRegAddr != null" >
                MER_REG_ADDR = #{merRegAddr,jdbcType=VARCHAR},
            </if>
            <if test="merAddrLongitude != null" >
                MER_ADDR_LONGITUDE = #{merAddrLongitude,jdbcType=DECIMAL},
            </if>
            <if test="merAddrLatitude != null" >
                MER_ADDR_LATITUDE = #{merAddrLatitude,jdbcType=DECIMAL},
            </if>
            <if test="mgtScp != null" >
                MGT_SCP = #{mgtScp,jdbcType=VARCHAR},
            </if>
            <if test="needInvFlg != null" >
                NEED_INV_FLG = #{needInvFlg,jdbcType=VARCHAR},
            </if>
            <if test="invMod != null" >
                INV_MOD = #{invMod,jdbcType=VARCHAR},
            </if>
            <if test="invTit != null" >
                INV_TIT = #{invTit,jdbcType=VARCHAR},
            </if>
            <if test="invMailAddr != null" >
                INV_MAIL_ADDR = #{invMailAddr,jdbcType=VARCHAR},
            </if>
            <if test="invMailZip != null" >
                INV_MAIL_ZIP = #{invMailZip,jdbcType=VARCHAR},
            </if>
            <if test="mercTrdCls != null" >
                MERC_TRD_CLS = #{mercTrdCls,jdbcType=VARCHAR},
            </if>
            <if test="mercTrdDesc != null" >
                MERC_TRD_DESC = #{mercTrdDesc,jdbcType=VARCHAR},
            </if>
            <if test="cprTyp != null" >
                CPR_TYP = #{cprTyp,jdbcType=VARCHAR},
            </if>
            <if test="merLvl != null" >
                MER_LVL = #{merLvl,jdbcType=VARCHAR},
            </if>
            <if test="belongMerc != null" >
                BEL_MERC = #{belongMerc,jdbcType=VARCHAR},
            </if>
            <if test="csTelNo != null" >
                CS_TEL_NO = #{csTelNo,jdbcType=VARCHAR},
            </if>
            <if test="mercHotLin != null" >
                MERC_HOT_LIN = #{mercHotLin,jdbcType=VARCHAR},
            </if>
            <if test="cusMgr != null" >
                CUS_MGR = #{cusMgr,jdbcType=VARCHAR},
            </if>
            <if test="cusMgrNm != null" >
                CUS_MGR_NM = #{cusMgrNm,jdbcType=VARCHAR},
            </if>
            <if test="rcvMagAmt != null" >
                RCV_MAG_AMT = #{rcvMagAmt,jdbcType=DECIMAL},
            </if>
            <if test="lastUpdOpr != null" >
                LAST_UPD_OPR = #{lastUpdOpr,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where USER_ID = #{userId,jdbcType=VARCHAR}
    </update>

    <select id="getAffiliateMerchant" resultType="java.lang.String" parameterType="java.lang.String" >
        select USER_ID from urm_cpr_ext_inf where BEL_MERC = #{userId,jdbcType=VARCHAR}
    </select>
</mapper>