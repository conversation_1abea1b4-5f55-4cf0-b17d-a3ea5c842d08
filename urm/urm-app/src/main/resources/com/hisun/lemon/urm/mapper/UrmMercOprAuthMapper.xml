<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.urm.dao.IUrmMercOprAuthDao">

    <resultMap id="BaseResultMap" type="com.hisun.lemon.urm.entity.MercOprAuthDo">
        <id column="login_id" property="loginId" jdbcType="VARCHAR"/>
        <result column="authority" property="authority" jdbcType="VARCHAR"/>
        <result column="upd_opr_id" property="updOprId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List" >
        login_id, authority, upd_opr_id, create_time, modify_time
    </sql>

    <insert id="insert" parameterType="com.hisun.lemon.urm.entity.MercOprAuthDo" >
        insert into urm_merc_opr_auth
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="loginId != null" >
                LOGIN_ID,
            </if>
            <if test="authority != null" >
                authority,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="loginId != null" >
                #{loginId,jdbcType=VARCHAR},
            </if>
            <if test="authority != null" >
                #{authority,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <delete id="delete" parameterType="java.lang.String" >
        delete from urm_merc_opr_auth
        where login_id = #{loginId,jdbcType=VARCHAR}
    </delete>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from urm_merc_opr_auth
        where login_id = #{loginId,jdbcType=VARCHAR}
    </select>

    <update id="update" parameterType="com.hisun.lemon.urm.entity.MercOprAuthDo" >
        update urm_merc_opr_auth
        <set>
            <if test="authority != null" >
                authority = #{authority,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where LOGIN_ID = #{loginId,jdbcType=VARCHAR}
    </update>
</mapper>