<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.urm.dao.IUrmUserRegHistoryDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.urm.entity.UrmUserRegHistoryDO" >
        <id column="USER_ID" property="userId" jdbcType="VARCHAR" />
        <result column="MBL_NO" property="mblNo" jdbcType="VARCHAR" />
        <result column="USR_LVL" property="usrLvl" jdbcType="CHAR" />
        <result column="ID_CHK_FLG" property="idChkFlg" jdbcType="CHAR" />
        <result column="USR_REG_CNL" property="usrRegCnl" jdbcType="VARCHAR" />
        <result column="USR_REG_IP" property="usrRegIp" jdbcType="VARCHAR" />
        <result column="USR_REG_DT" property="usrRegDt" jdbcType="DATE" />
        <result column="USR_REG_TM" property="usrRegTm" jdbcType="TIME" />
        <result column="USR_CLS_CNL" property="usrClsCnl" jdbcType="VARCHAR" />
        <result column="USR_CLS_IP" property="usrClsIp" jdbcType="VARCHAR" />
        <result column="USR_CLS_DT" property="usrClsDt" jdbcType="DATE" />
        <result column="USR_CLS_TM" property="usrClsTm" jdbcType="TIME" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        USER_ID, MBL_NO, USR_LVL, ID_CHK_FLG, USR_REG_CNL, USR_REG_IP, USR_REG_DT, USR_REG_TM, 
        USR_CLS_CNL, USR_CLS_IP, USR_CLS_DT, USR_CLS_TME
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from urm_user_reg_history
        where USER_ID = #{userId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from urm_user_reg_history
        where USER_ID = #{userId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.urm.entity.UrmUserRegHistoryDO" >
        insert into urm_user_reg_history
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                USER_ID,
            </if>
            <if test="mblNo != null" >
                MBL_NO,
            </if>
            <if test="usrLvl != null" >
                USR_LVL,
            </if>
            <if test="idChkFlg != null" >
                ID_CHK_FLG,
            </if>
            <if test="usrRegCnl != null" >
                USR_REG_CNL,
            </if>
            <if test="usrRegIp != null" >
                USR_REG_IP,
            </if>
            <if test="usrRegDt != null" >
                USR_REG_DT,
            </if>
            <if test="usrRegTm != null" >
                USR_REG_TM,
            </if>
            <if test="usrClsCnl != null" >
                USR_CLS_CNL,
            </if>
            <if test="usrClsIp != null" >
                USR_CLS_IP,
            </if>
            <if test="usrClsDt != null" >
                USR_CLS_DT,
            </if>
            <if test="usrClsTm != null" >
                USR_CLS_TM,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="mblNo != null" >
                #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="usrLvl != null" >
                #{usrLvl,jdbcType=CHAR},
            </if>
            <if test="idChkFlg != null" >
                #{idChkFlg,jdbcType=CHAR},
            </if>
            <if test="usrRegCnl != null" >
                #{usrRegCnl,jdbcType=VARCHAR},
            </if>
            <if test="usrRegIp != null" >
                #{usrRegIp,jdbcType=VARCHAR},
            </if>
            <if test="usrRegDt != null" >
                #{usrRegDt,jdbcType=DATE},
            </if>
            <if test="usrRegTm != null" >
                #{usrRegTm,jdbcType=TIME},
            </if>
            <if test="usrClsCnl != null" >
                #{usrClsCnl,jdbcType=VARCHAR},
            </if>
            <if test="usrClsIp != null" >
                #{usrClsIp,jdbcType=VARCHAR},
            </if>
            <if test="usrClsDt != null" >
                #{usrClsDt,jdbcType=DATE},
            </if>
            <if test="usrClsTm != null" >
                #{usrClsTm,jdbcType=TIME},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.urm.entity.UrmUserRegHistoryDO" >
        update urm_user_reg_history
        <set >
            <if test="mblNo != null" >
                MBL_NO = #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="usrLvl != null" >
                USR_LVL = #{usrLvl,jdbcType=CHAR},
            </if>
            <if test="idChkFlg != null" >
                ID_CHK_FLG = #{idChkFlg,jdbcType=CHAR},
            </if>
            <if test="usrRegCnl != null" >
                USR_REG_CNL = #{usrRegCnl,jdbcType=VARCHAR},
            </if>
            <if test="usrRegIp != null" >
                USR_REG_IP = #{usrRegIp,jdbcType=VARCHAR},
            </if>
            <if test="usrRegDt != null" >
                USR_REG_DT = #{usrRegDt,jdbcType=DATE},
            </if>
            <if test="usrRegTm != null" >
                USR_REG_TM = #{usrRegTm,jdbcType=TIME},
            </if>
            <if test="usrClsCnl != null" >
                USR_CLS_CNL = #{usrClsCnl,jdbcType=VARCHAR},
            </if>
            <if test="usrClsIp != null" >
                USR_CLS_IP = #{usrClsIp,jdbcType=VARCHAR},
            </if>
            <if test="usrClsDt != null" >
                USR_CLS_DT = #{usrClsDt,jdbcType=DATE},
            </if>
            <if test="usrClsTm != null" >
                USR_CLS_TM = #{usrClsTm,jdbcType=TIME},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where USER_ID = #{userId,jdbcType=VARCHAR}
    </update>
</mapper>