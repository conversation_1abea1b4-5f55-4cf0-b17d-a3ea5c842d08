<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.urm.dao.IBatchOpenUserDao">

    <select id="getList" resultType="com.hisun.lemon.urm.entity.BatchOpenUserDo">
        select mbl_no as mblNo from tmp_batch_user where result is null
    </select>

    <update id="updateResult" parameterType="com.hisun.lemon.urm.entity.BatchOpenUserDo">
        update tmp_batch_user set result = #{result,jdbcType=VARCHAR} where mbl_no = #{mblNo,jdbcType=VARCHAR}
    </update>
</mapper>