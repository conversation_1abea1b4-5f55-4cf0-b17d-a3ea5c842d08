<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.urm.dao.IUrmUserBasicInfDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.urm.entity.UrmUserBasicInfDO" >
        <id column="USER_ID" property="userId" jdbcType="VARCHAR" />
        <result column="MBL_NO" property="mblNo" jdbcType="VARCHAR" />
        <result column="USR_STS" property="usrSts" jdbcType="CHAR" />
        <result column="USR_LVL" property="usrLvl" jdbcType="CHAR" />
        <result column="ID_CHK_FLG" property="idChkFlg" jdbcType="CHAR" />
        <result column="ID_CHK_NO" property="idChkNo" jdbcType="VARCHAR" />
        <result column="ID_CHK_DT" property="idChkDt" jdbcType="DATE" />
        <result column="ID_CHK_TM" property="idChkTm" jdbcType="TIME" />
        <result column="ID_TYPE" property="idType" jdbcType="VARCHAR" />
        <result column="ID_NO" property="idNo" jdbcType="VARCHAR" />
        <result column="ID_NO_HID" property="idNoHid" jdbcType="VARCHAR" />
        <result column="USR_NM" property="usrNm" jdbcType="VARCHAR" />
        <result column="USR_NM_HID" property="usrNmHid" jdbcType="VARCHAR" />
        <result column="USR_GENDER" property="usrGender" jdbcType="CHAR" />
        <result column="USR_NATION" property="usrNation" jdbcType="VARCHAR" />
        <result column="USR_BIRTH_DT" property="usrBirthDt" jdbcType="VARCHAR" />
        <result column="ISSU_AUTH" property="issuAuth" jdbcType="VARCHAR" />
        <result column="ID_EFF_DT" property="idEffDt" jdbcType="VARCHAR" />
        <result column="ID_EXP_DT" property="idExpDt" jdbcType="VARCHAR" />
        <result column="USR_REG_CNL" property="usrRegCnl" jdbcType="VARCHAR" />
        <result column="USR_REG_IP" property="usrRegIp" jdbcType="VARCHAR" />
        <result column="USR_REG_DT" property="usrRegDt" jdbcType="DATE" />
        <result column="USR_REG_TM" property="usrRegTm" jdbcType="TIME" />
        <result column="USR_CLS_DT" property="usrClsDt" jdbcType="DATE" />
        <result column="USR_CLS_TM" property="usrClsTm" jdbcType="TIME" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
        <result column="KYB_CERT" property="kybCert" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List" >
        USER_ID, MBL_NO, USR_STS, USR_LVL, ID_CHK_FLG, ID_CHK_NO, ID_CHK_DT, ID_CHK_TM, ID_TYPE,
        ID_NO, ID_NO_HID, USR_NM, USR_NM_HID, USR_GENDER, USR_NATION, USR_BIRTH_DT, ISSU_AUTH, 
        ID_EFF_DT, ID_EXP_DT, USR_REG_CNL, USR_REG_IP, USR_REG_DT, USR_REG_TM, USR_CLS_DT, 
        USR_CLS_TM,KYB_CERT
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from urm_user_basic_inf
        where USER_ID = #{userId,jdbcType=VARCHAR}
    </select>

    <select id="getByLoginId" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from urm_user_basic_inf
        where USER_ID = (SELECT USER_ID from urm_safe_inf where safe_id =
        (select safe_id from urm_safe_login where login_id = #{loginId,jdbcType=VARCHAR})
        )
    </select>

    <select id="getCrpUser" resultType="String" >
        select USER_ID
        from urm_user_basic_inf
        where (USR_LVL = '2' OR USR_LVL = '3') AND USR_STS = '0'
    </select>

    <select id="countId" resultType="int" parameterType="java.lang.String">
        select COUNT(*)
        from urm_user_basic_inf
        where ID_NO = #{idNo, jdbcType=VARCHAR}
          and ID_TYPE = #{idType, jdbcType=VARCHAR}
          and USER_ID != #{userId, jdbcType=VARCHAR}
          and USR_STS = '0'
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from urm_user_basic_inf
        where USER_ID = #{userId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.urm.entity.UrmUserBasicInfDO" >
        insert into urm_user_basic_inf
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                USER_ID,
            </if>
            <if test="mblNo != null" >
                MBL_NO,
            </if>
            <if test="usrSts != null" >
                USR_STS,
            </if>
            <if test="usrLvl!= null" >
                USR_LVL,
            </if>
            <if test="idChkFlg != null" >
                ID_CHK_FLG,
            </if>
            <if test="idChkNo != null" >
                ID_CHK_NO,
            </if>
            <if test="idChkDt != null" >
                ID_CHK_DT,
            </if>
            <if test="idChkTm != null" >
                ID_CHK_TM,
            </if>
            <if test="idType != null" >
                ID_TYPE,
            </if>
            <if test="idNo != null" >
                ID_NO,
            </if>
            <if test="idNoHid != null" >
                ID_NO_HID,
            </if>
            <if test="usrNm != null" >
                USR_NM,
            </if>
            <if test="usrNmHid != null" >
                USR_NM_HID,
            </if>
            <if test="usrGender != null" >
                USR_GENDER,
            </if>
            <if test="usrNation != null" >
                USR_NATION,
            </if>
            <if test="usrBirthDt != null" >
                USR_BIRTH_DT,
            </if>
            <if test="issuAuth != null" >
                ISSU_AUTH,
            </if>
            <if test="idEffDt != null" >
                ID_EFF_DT,
            </if>
            <if test="idExpDt != null" >
                ID_EXP_DT,
            </if>
            <if test="usrRegCnl != null" >
                USR_REG_CNL,
            </if>
            <if test="usrRegIp != null" >
                USR_REG_IP,
            </if>
            <if test="usrRegDt != null" >
                USR_REG_DT,
            </if>
            <if test="usrRegTm != null" >
                USR_REG_TM,
            </if>
            <if test="usrClsDt != null" >
                USR_CLS_DT,
            </if>
            <if test="usrClsTm != null" >
                USR_CLS_TM,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="mblNo != null" >
                #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="usrSts != null" >
                #{usrSts,jdbcType=CHAR},
            </if>
            <if test="usrLvl != null" >
                #{usrLvl,jdbcType=CHAR},
            </if>
            <if test="idChkFlg != null" >
                #{idChkFlg,jdbcType=CHAR},
            </if>
            <if test="idChkNo != null" >
                #{idChkNo,jdbcType=VARCHAR},
            </if>
            <if test="idChkDt != null" >
                #{idChkDt,jdbcType=DATE},
            </if>
            <if test="idChkTm != null" >
                #{idChkTm,jdbcType=TIME},
            </if>
            <if test="idType != null" >
                #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null" >
                #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="idNoHid != null" >
                #{idNoHid,jdbcType=VARCHAR},
            </if>
            <if test="usrNm != null" >
                #{usrNm,jdbcType=VARCHAR},
            </if>
            <if test="usrNmHid != null" >
                #{usrNmHid,jdbcType=VARCHAR},
            </if>
            <if test="usrGender != null" >
                #{usrGender,jdbcType=CHAR},
            </if>
            <if test="usrNation != null" >
                #{usrNation,jdbcType=VARCHAR},
            </if>
            <if test="usrBirthDt != null" >
                #{usrBirthDt,jdbcType=VARCHAR},
            </if>
            <if test="issuAuth != null" >
                #{issuAuth,jdbcType=VARCHAR},
            </if>
            <if test="idEffDt != null" >
                #{idEffDt,jdbcType=VARCHAR},
            </if>
            <if test="idExpDt != null" >
                #{idExpDt,jdbcType=VARCHAR},
            </if>
            <if test="usrRegCnl != null" >
                #{usrRegCnl,jdbcType=VARCHAR},
            </if>
            <if test="usrRegIp != null" >
                #{usrRegIp,jdbcType=VARCHAR},
            </if>
            <if test="usrRegDt != null" >
                #{usrRegDt,jdbcType=DATE},
            </if>
            <if test="usrRegTm != null" >
                #{usrRegTm,jdbcType=TIME},
            </if>
            <if test="usrClsDt != null" >
                #{usrClsDt,jdbcType=DATE},
            </if>
            <if test="usrClsTm != null" >
                #{usrClsTm,jdbcType=TIME},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.urm.entity.UrmUserBasicInfDO" >
        update urm_user_basic_inf
        <set >
            <if test="mblNo != null" >
                MBL_NO = #{mblNo,jdbcType=CHAR},
            </if>
            <if test="usrSts != null" >
                USR_STS = #{usrSts,jdbcType=CHAR},
            </if>
            <if test="usrLvl!= null" >
                USR_LVL = #{usrLvl,jdbcType=CHAR},
            </if>
            <if test="idChkFlg != null" >
                ID_CHK_FLG = #{idChkFlg,jdbcType=CHAR},
            </if>
            <if test="idChkNo != null" >
                ID_CHK_NO = #{idChkNo,jdbcType=VARCHAR},
            </if>
            <if test="idChkDt != null" >
                ID_CHK_DT = #{idChkDt,jdbcType=DATE},
            </if>
            <if test="idChkTm != null" >
                ID_CHK_TM = #{idChkTm,jdbcType=TIME},
            </if>
            <if test="idType != null" >
                ID_TYPE = #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null" >
                ID_NO = #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="idNoHid != null" >
                ID_NO_HID = #{idNoHid,jdbcType=VARCHAR},
            </if>
            <if test="usrNm != null" >
                USR_NM = #{usrNm,jdbcType=VARCHAR},
            </if>
            <if test="usrNmHid != null" >
                USR_NM_HID = #{usrNmHid,jdbcType=VARCHAR},
            </if>
            <if test="usrGender != null" >
                USR_GENDER = #{usrGender,jdbcType=CHAR},
            </if>
            <if test="usrNation != null" >
                USR_NATION = #{usrNation,jdbcType=VARCHAR},
            </if>
            <if test="usrBirthDt != null" >
                USR_BIRTH_DT = #{usrBirthDt,jdbcType=VARCHAR},
            </if>
            <if test="issuAuth != null" >
                ISSU_AUTH = #{issuAuth,jdbcType=VARCHAR},
            </if>
            <if test="idEffDt != null" >
                ID_EFF_DT = #{idEffDt,jdbcType=VARCHAR},
            </if>
            <if test="idExpDt != null" >
                ID_EXP_DT = #{idExpDt,jdbcType=VARCHAR},
            </if>
            <if test="usrRegCnl != null" >
                USR_REG_CNL = #{usrRegCnl,jdbcType=VARCHAR},
            </if>
            <if test="usrRegIp != null" >
                USR_REG_IP = #{usrRegIp,jdbcType=VARCHAR},
            </if>
            <if test="usrRegDt != null" >
                USR_REG_DT = #{usrRegDt,jdbcType=DATE},
            </if>
            <if test="usrRegTm != null" >
                USR_REG_TM = #{usrRegTm,jdbcType=TIME},
            </if>
            <if test="usrClsDt != null" >
                USR_CLS_DT = #{usrClsDt,jdbcType=DATE},
            </if>
            <if test="usrClsTm != null" >
                USR_CLS_TM = #{usrClsTm,jdbcType=TIME},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where USER_ID = #{userId,jdbcType=VARCHAR}
    </update>

    <select id="countIdNo" resultType="int" parameterType="java.lang.String">
        select COUNT(*)
        from urm_user_basic_inf
        where ID_NO = #{idNo, jdbcType=VARCHAR}
    </select>
    
    <select id="getListInfo" resultType="com.hisun.lemon.urm.entity.MercInfoList">
        select a.EMAIL, a.USER_ID as userId, b.MERC_NAME as mercName, b.CREATE_TIME as usrRegDt from urm_safe_inf a, urm_cpr_ext_inf b
        where a.USER_ID = b.USER_ID and a.user_id in
        <foreach item="item" index="index" collection="userList"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="mercId != null" >
            and b.USER_ID like concat('%',#{mercId,jdbcType=VARCHAR},'%')
        </if>
        <if test="mercNm != null" >
            and b.MERC_NAME like concat('%',#{mercNm,jdbcType=VARCHAR},'%')
        </if>
        and a.OPR_TYP = '2'
    </select>
</mapper>