<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.urm.dao.IUrmForeignAccMapDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.urm.entity.UrmForeignAccMapDO" >
        <id column="FOREIGN_ID" property="foreignId" jdbcType="VARCHAR" />
        <id column="PLAT_USER_ID" property="platUserId" jdbcType="VARCHAR" />
        <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
        <result column="SAFE_ID" property="safeId" jdbcType="VARCHAR" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        FOREIGN_ID, PLAT_USER_ID, USER_ID, SAFE_ID
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="map" >
        select 
        <include refid="Base_Column_List" />
        from urm_foreign_acc_map
        where FOREIGN_ID = #{foreignId,jdbcType=VARCHAR}
          and PLAT_USER_ID = #{platUserId,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="map" >
        delete from urm_foreign_acc_map
        where FOREIGN_ID = #{foreignId,jdbcType=VARCHAR}
          and PLAT_USER_ID = #{platUserId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.urm.entity.UrmForeignAccMapDO" >
        insert into urm_foreign_acc_map
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="foreignId != null" >
                FOREIGN_ID,
            </if>
            <if test="platUserId != null" >
                PLAT_USER_ID,
            </if>
            <if test="userId != null" >
                USER_ID,
            </if>
            <if test="safeId != null" >
                SAFE_ID,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="foreignId != null" >
                #{foreignId,jdbcType=VARCHAR},
            </if>
            <if test="platUserId != null" >
                #{platUserId,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="safeId != null" >
                #{safeId,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.urm.entity.UrmForeignAccMapDO" >
        update urm_foreign_acc_map
        <set >
            <if test="userId != null" >
                USER_ID = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="safeId != null" >
                SAFE_ID = #{safeId,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where FOREIGN_ID = #{foreignId,jdbcType=VARCHAR}
          and PLAT_USER_ID = #{platUserId,jdbcType=VARCHAR}
    </update>
</mapper>