redis :
  host : *************
  password : seatelRedis
  
rabbitmq :
  addresses : *************:5672
  password : Rabbitmq123

#Multiple dataSources
dataSource :
  lemon :
    type : com.alibaba.druid.pool.DruidDataSource
    driverClassName : com.mysql.cj.jdbc.Driver
    url : **************************************************************************************
    username : lemon
    password : lemon@123
    initialSize : 5
    minIdle : 5
    maxActive : 20
    # 配置获取连接等待超时的时间
    maxWait : 30000
    # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 
    timeBetweenEvictionRunsMillis : 60000
    # 配置一个连接在池中最小生存的时间，单位是毫秒 
    minEvictableIdleTimeMillis : 300000
    validationQuery : SELECT 1
    testWhileIdle : true
    testOnBorrow : false
    testOnReturn : false
    # 打开PSCache，并且指定每个连接上PSCache的大小 
    poolPreparedStatements : true
    maxPoolPreparedStatementPerConnectionSize : 20
    # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙 
    filters : stat,wall,log4j
    # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
    connectionProperties : druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    # 合并多个DruidDataSource的监控数据
    useGlobalDataSourceStat : true
  primary :
    type : com.alibaba.druid.pool.DruidDataSource
    driverClassName : com.mysql.cj.jdbc.Driver
    url : **********************************************************************************************
    username : urm
    password : urm
    initialSize : 5
    minIdle : 5
    maxActive : 20
    # 配置获取连接等待超时的时间
    maxWait : 60000
    # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 
    timeBetweenEvictionRunsMillis : 60000
    # 配置一个连接在池中最小生存的时间，单位是毫秒 
    minEvictableIdleTimeMillis : 300000
    validationQuery : SELECT 1
    testWhileIdle : true
    testOnBorrow : false
    testOnReturn : false
    # 打开PSCache，并且指定每个连接上PSCache的大小 
    poolPreparedStatements : true
    maxPoolPreparedStatementPerConnectionSize : 20
    # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙 
    filters : stat,wall,log4j
    # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
    connectionProperties : druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    # 合并多个DruidDataSource的监控数据
    useGlobalDataSourceStat : true

    
eureka :
  zone : http://172.22.181.72:9002/eureka/,http://172.22.181.73:9002/eureka/
  
key :
  pvk :
    SYS_LOG_PVK : 'F254A355C4A54E549FB01B8389E4F0A1'
    SYS_PAY_PVK : 'D0B41A5DE642A4B8'

  zpk :
    APP_ZPK : '4B84FBD10245AF59C7D6825F7B2DC63D'
    SYS_PAY_ZPK : '13E5CC245117A14B'

  pubkey :
    APP_PUB_KEY : '30818902818100AFFCCADB3C3AEC670C14CC8D8960D20937C06B17EAFDFA8680DFDBE14694A3B4789264C499C9328C69E7343B4C187E9AD6537C7EC9933D692BD46449EDCF5382B1059EEBF700CA13B168B74AB2FBF742B63EE1DDA71C16ABF22B8DF5BC3DEDB256CBEB07195057740046498CCEE75DF628FA739BE90731BFA05349A88AAC61FB0203010001'

  prikey :
    PRI_KEY_IDX : '01'

hsm:
  host: **************
  port: 5050

crm :
  host : http://*************:8889