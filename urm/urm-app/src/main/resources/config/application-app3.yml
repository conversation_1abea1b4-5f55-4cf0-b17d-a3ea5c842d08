server :
  port : 9033

redis :
  host : **********
  password : Hisunpay2017
  
rabbitmq :
  addresses : **********:5672
  password : Rabbitmq123
  
#Multiple dataSources
dataSource :
  lemon :
    type : com.alibaba.druid.pool.DruidDataSource
    driverClassName : com.mysql.cj.jdbc.Driver
    url : ***************************************************************************************
    username : lemon
    password : lemon@123
    initialSize : 5
    minIdle : 5
    maxActive : 20
    # 配置获取连接等待超时的时间
    maxWait : 30000
    # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 
    timeBetweenEvictionRunsMillis : 60000
    # 配置一个连接在池中最小生存的时间，单位是毫秒 
    minEvictableIdleTimeMillis : 300000
    validationQuery : SELECT 1
    testWhileIdle : true
    testOnBorrow : false
    testOnReturn : false
    # 打开PSCache，并且指定每个连接上PSCache的大小 
    poolPreparedStatements : true
    maxPoolPreparedStatementPerConnectionSize : 20
    # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙 
    filters : stat,wall,log4j
    # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
    connectionProperties : druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    # 合并多个DruidDataSource的监控数据
    useGlobalDataSourceStat : true
  primary :
    type : com.alibaba.druid.pool.DruidDataSource
    driverClassName : com.mysql.cj.jdbc.Driver
    url : ***********************************************************************************************
    username : urm
    password : urm
    initialSize : 5
    minIdle : 5
    maxActive : 20
    # 配置获取连接等待超时的时间
    maxWait : 60000
    # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 
    timeBetweenEvictionRunsMillis : 60000
    # 配置一个连接在池中最小生存的时间，单位是毫秒 
    minEvictableIdleTimeMillis : 300000
    validationQuery : SELECT 1
    testWhileIdle : true
    testOnBorrow : false
    testOnReturn : false
    # 打开PSCache，并且指定每个连接上PSCache的大小 
    poolPreparedStatements : true
    maxPoolPreparedStatementPerConnectionSize : 20
    # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙 
    filters : stat,wall,log4j
    # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
    connectionProperties : druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    # 合并多个DruidDataSource的监控数据
    useGlobalDataSourceStat : true

    
eureka :
#  zone : http://************:9002/eureka/,http://************:9002/eureka/
  zone : http://**********:9002/eureka/
  instance :
    ip-address : **********

key :
  pvk :
    SYS_LOG_PVK : 'F216FB126D6502D05783DBE2CC1DC5BA'
    SYS_PAY_PVK : '372F7DAA46BDF8C8'

  zpk :
    APP_ZPK : '4961F18392540BDA09A6D3867CD79414'
    SYS_PAY_ZPK : '30C8AC78B0D77139'

  pubkey :
    APP_PUB_KEY : '30818902818100CE77F54D371BF98D29B55537BCA579EF95BC82CBF817147A49D0867E3FD0BB25C900CE8FB563F3F9BF786078CFBA3AE4DE8399E856F235AC806F7CC442DFCD1F86FD6453F6285B1CBCC58D24E5943C33EB5B2E436E949C3A156A264D9395EEAF0CDBDC1240F1ADD9B66B276B87B631E024F467014BF3DEBE0F9C2FB967C202410203010001'

  prikey :
    PRI_KEY_IDX : '02'

