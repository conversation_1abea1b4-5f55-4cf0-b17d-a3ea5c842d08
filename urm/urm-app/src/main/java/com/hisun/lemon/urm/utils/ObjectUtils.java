package com.hisun.lemon.urm.utils;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.ReflectionUtils;
import com.hisun.lemon.urm.dto.CrmUserInfDTO;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @function ObjectUtils
 * @description 处理对象的应用
 * @date 8/8/2017 Tue
 * @time 6:42 PM
 */
public final class ObjectUtils {
    public final static <T> T objectNull(T object) {
        Boolean isNull = true;
        for (Field f : object.getClass().getDeclaredFields()) {
            f.setAccessible(true);
            try {
                if (f.get(object) != null) { //判断字段是否为空，并且对象属性中的基本都会转为对象类型来判断
                    isNull = false;
                    break;
                }
            } catch (IllegalAccessException e) {
                LemonException.throwLemonException(e);
            }
        }
        if (isNull) {
            return null;
        }
        return object;
    }

    public final static Map<String, String> objectToMap(Object t) {
        if(objectNull(t) == null){
            return null;
        }
        //获取关联的所有类，本类以及所有父类
        Class objectClazz = t.getClass();
        List<Class> baseClazzs= new ArrayList<Class>();
        do {
            baseClazzs.add(objectClazz);
            objectClazz = objectClazz.getSuperclass();
        } while (objectClazz !=null && objectClazz != Object.class);

        Map<String, String> map = new HashMap<String, String>();
        for(Class clazz : baseClazzs){
            Field[] declaredFields = clazz.getDeclaredFields();
            for (Field field : declaredFields) {
                int mod = field.getModifiers();
                //过滤 static 和 final 类型
                if(Modifier.isStatic(mod) || Modifier.isFinal(mod)){
                    continue;
                }
                try {
                    field.setAccessible(true);
                    map.put(field.getName(), (String) field.get(t));
                } catch (Exception e) {
                    return null;
                }
            }
        }
        return map;
    }

    public static void main(String[] args) throws Exception {
        CrmUserInfDTO userInfDTO = new CrmUserInfDTO();
        userInfDTO.setCountryCode("86");
        userInfDTO.setUserPhone("15274891107");
        System.out.println(objectToMap(userInfDTO).toString());
    }
}
