package com.hisun.lemon.urm.service.impl;

import com.hisun.lemon.urm.entity.Email;
import com.hisun.lemon.urm.service.IEmailService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring4.SpringTemplateEngine;

import javax.mail.internet.MimeMessage;
import java.io.File;
import java.util.Locale;
import java.util.Map;

@Service
public class EmailServiceImpl implements IEmailService {

    private static final Logger logger = LoggerFactory.getLogger(EmailServiceImpl.class);

    @Value("${spring.mail.username}")
    private String from;

    private JavaMailSender javaMailSender;
    private SpringTemplateEngine springTemplateEngine;

    @Autowired
    public void EmailServiceImpl(JavaMailSender javaMailSender, SpringTemplateEngine springTemplateEngine) {
        this.javaMailSender = javaMailSender;
        this.springTemplateEngine = springTemplateEngine;
    }

    /**
     * @param email
     * @param fileMap
     * @param variables
     * @param locale
     * @throws Exception
     */
    @Async
    @Override
    public void sendEmail(Email email, Map<String, String> fileMap, Map<String, Object> variables, Locale locale) {
        try {
            // Prepare message using a Spring helper
            MimeMessage mimeMessage = javaMailSender.createMimeMessage();
            MimeMessageHelper messageHelper = new MimeMessageHelper(mimeMessage, true, "utf-8");
            if (StringUtils.isBlank(email.getFrom())) {
                messageHelper.setFrom(from);
                logger.debug("send email from: {}", from);
            } else {
                messageHelper.setFrom(email.getFrom());
                logger.debug("send email from: {}", email.getFrom());
            }
            messageHelper.setSubject(email.getSubject());
            logger.debug("send email subject: {}", email.getSubject());

            // Create plain TEXT or HTML body using Thymeleaf
            if (StringUtils.isBlank(email.getTemplate())) {
                messageHelper.setText(email.getText());
            } else {
                // Prepare the evaluation context
                final Context ctx = new Context(locale);
                if (variables == null) {
                    ctx.setVariable("content", email.getText());
                } else {
                    ctx.setVariables(variables);
                }

                // Create the HTML body using Thymeleaf
                final String htmlContent = springTemplateEngine.process(email.getTemplate(), ctx);
                messageHelper.setText(htmlContent, true);

                logger.debug("send email template: {}", email.getTemplate());
            }
            logger.debug("send email text: {}", email.getText());

            messageHelper.setTo(email.getTo());
            String tos = "";
            for (String to : email.getTo()) {
                tos += to + ",";
            }
            logger.debug("send email to: {}", tos);


            if (StringUtils.isNoneBlank(email.getCc())) {
                messageHelper.setCc(email.getCc());
                String ccs = "";
                for (String cc : email.getCc()) {
                    ccs += cc + ",";
                }
                logger.debug("send email cc: {}", ccs);
            }

            if (StringUtils.isNoneBlank(email.getBcc())) {
                messageHelper.setBcc(email.getBcc());
                String bccs = "";
                for (String bcc : email.getBcc()) {
                    bccs += bcc + ",";
                }
                logger.debug("send email bcc: {}", bccs);
            }

            if (StringUtils.isNoneBlank(email.getReplyTo())) {
                messageHelper.setReplyTo(email.getReplyTo());
                logger.debug("send email replyTo:" + email.getReplyTo());
            }

            FileSystemResource file;
            if (StringUtils.isNoneBlank(email.getFiles())) {
                for (String s : email.getFiles()) {
                    file = new FileSystemResource(new File(s));
                    String fileName = fileMap.get(file.getFilename());
                    messageHelper.addAttachment(fileName, file);
                    logger.debug("send email attach:" + s);
                }
            }

            javaMailSender.send(mimeMessage);
            logger.debug("send email .... success");

        } catch (Exception e) {
            logger.error("send email .... failed:" + e);
        }
    }
}
