/*
 * @ClassName UrmSsoApplyInfDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-24 14:36:45
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDate;
import java.time.LocalTime;

public class UrmSsoApplyInfDO extends BaseDO {
    /**
     *  msgId 请求流水号
     */
    private String msgId;
    /**
     *  safeId 安全表主键
     */
    private String safeId;
    /**
     *  userId 内部用户号
     */
    private String userId;
    /**
     *  ssoDt SSO日期
     */
    private LocalDate ssoDt;
    /**
     *  ssoTm SSO时间
     */
    private LocalTime ssoTm;

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public String getSafeId() {
        return safeId;
    }

    public void setSafeId(String safeId) {
        this.safeId = safeId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public LocalDate getSsoDt() {
        return ssoDt;
    }

    public void setSsoDt(LocalDate ssoDt) {
        this.ssoDt = ssoDt;
    }

    public LocalTime getSsoTm() {
        return ssoTm;
    }

    public void setSsoTm(LocalTime ssoTm) {
        this.ssoTm = ssoTm;
    }
}