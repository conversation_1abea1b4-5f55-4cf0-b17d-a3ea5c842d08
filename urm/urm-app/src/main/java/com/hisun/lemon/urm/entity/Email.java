package com.hisun.lemon.urm.entity;

public class Email {
    /**
     * 发件人
     */
    private String from;
    /**
     * 主题
     */
    private String subject;
    /**
     * 内容
     */
    private String text;
    /**
     * 收件人
     */
    private String[] to;
    /**
     * 抄送
     */
    private String[] cc;
    /**
     * 密件抄送
     */
    private String[] bcc;
    /**
     * 回复
     */
    private String replyTo;
    /**
     * 附件
     */
    private String[] files;
    /**
     * 模板
     */
    private String template;

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String[] getTo() {
        return to;
    }

    public void setTo(String[] to) {
        this.to = to;
    }

    public String[] getCc() {
        return cc;
    }

    public void setCc(String[] cc) {
        this.cc = cc;
    }

    public String[] getBcc() {
        return bcc;
    }

    public void setBcc(String[] bcc) {
        this.bcc = bcc;
    }

    public String getReplyTo() {
        return replyTo;
    }

    public void setReplyTo(String replyTo) {
        this.replyTo = replyTo;
    }

    public String[] getFiles() {
        return files;
    }

    public void setFiles(String[] files) {
        this.files = files;
    }

    public String getTemplate() {
        return template;
    }

    public void setTemplate(String template) {
        this.template = template;
    }
}
