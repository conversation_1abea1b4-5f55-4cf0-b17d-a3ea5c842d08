package com.hisun.lemon.urm.common;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.mkm.client.MarketActivityClient;
import com.hisun.lemon.mkm.req.dto.QueryUserMkmToolReqDTO;
import com.hisun.lemon.mkm.res.dto.QueryUserMkmToolRspDTO;
import com.hisun.lemon.mkm.res.dto.SeaCcyDetail;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @function MkmService
 * @description 营销服务接口
 * @date 9/19/2017 Tue
 * @time 4:42 PM
 */
@Component
public class MkmService {
    private static final Logger logger = LoggerFactory.getLogger(MkmService.class);

    @Resource
    private MarketActivityClient mkm;

    public BigDecimal queryMkmBal(String userId) {
        GenericDTO<QueryUserMkmToolReqDTO> mkmBalReq = new GenericDTO<>();
        QueryUserMkmToolReqDTO mkmTool = new QueryUserMkmToolReqDTO();
        mkmTool.setMkTool("02");//海币
        mkmTool.setUserId(userId);
        mkmBalReq.setBody(mkmTool);
        GenericRspDTO<QueryUserMkmToolRspDTO> mkmBalRsp = mkm.queryUserMkmTool(mkmBalReq);
        if (JudgeUtils.isNotSuccess(mkmBalRsp.getMsgCd())) {
            LemonException.throwLemonException(mkmBalRsp.getMsgCd());
        }
        if (JudgeUtils.isNull(mkmBalRsp) || JudgeUtils.isNull(mkmBalRsp.getBody()) || JudgeUtils.isNull(mkmBalRsp
                .getBody().getSeaCcyDetal())) {
            return BigDecimal.valueOf(0,2);
        }
        SeaCcyDetail seaDetail = mkmBalRsp.getBody().getSeaCcyDetal();
        //海币的兑换比例为1:100
        BigDecimal seaBal = BigDecimal.valueOf(seaDetail.getCount()/100, 2);
        return seaBal;
    }
}
