/*    */ package com.hisun.lemon.urm.utils;
/*    */ 
/*    */ import java.security.PrivateKey;
/*    */ import java.security.PublicKey;
/*    */ import java.security.cert.X509Certificate;
/*    */ 
/*    */ public class SignedPack
/*    */ {
/*    */   private byte[] signData;
/*    */   private PublicKey pubKey;
/*    */   private X509Certificate cert;
/*    */   private PrivateKey priKey;
/*    */ 
/*    */   public X509Certificate getCert()
/*    */   {
/* 16 */     return this.cert;
/*    */   }
/*    */ 
/*    */   public void setCert(X509Certificate cert) {
/* 20 */     this.cert = cert;
/*    */   }
/*    */ 
/*    */   public PublicKey getPubKey() {
/* 24 */     return this.pubKey;
/*    */   }
/*    */ 
/*    */   public void setPubKey(PublicKey pubKey) {
/* 28 */     this.pubKey = pubKey;
/*    */   }
/*    */ 
/*    */   public byte[] getSignData() {
/* 32 */     return this.signData;
/*    */   }
/*    */ 
/*    */   public void setSignData(byte[] signData) {
/* 36 */     this.signData = signData;
/*    */   }
/*    */ 
/*    */   public PrivateKey getPriKey() {
/* 40 */     return this.priKey;
/*    */   }
/*    */ 
/*    */   public void setPriKey(PrivateKey priKey) {
/* 44 */     this.priKey = priKey;
/*    */   }
/*    */ }

/* Location:           C:\Users\<USER>\Desktop\cap_dxt_sdk\
 * Qualified Name:     com.hyt.cap.sdk.SignedPack
 * JD-Core Version:    0.6.0
 */