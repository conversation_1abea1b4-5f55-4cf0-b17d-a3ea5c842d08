package com.hisun.lemon.urm.utils;

import com.hisun.lemon.common.utils.JudgeUtils;

/**
 * <AUTHOR>
 * @function KeyDataHideUtils
 * @description 关键信息脱敏
 * @date 8/26/2017 Sat
 * @time 11:30 AM
 */
public class KeyDataHideUtils {
    public static String idNoHide(String idNo) {
        if (JudgeUtils.isBlank(idNo)) {
            return idNo;
        }
        int length = idNo.length();
        if (length <= 4) {
            return idNo;
        } else {
            StringBuffer strBuf = new StringBuffer();
            for (int i = 0; i < idNo.length() - 4 ; i++) {
                strBuf = strBuf.append("*");
            }
            return strBuf.toString() + idNo.substring(idNo.length() - 4);
        }
    }

    public static String usrNmHide(String usrNm) {
        return null;
    }
}
