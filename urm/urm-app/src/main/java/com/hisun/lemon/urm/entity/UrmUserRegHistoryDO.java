/*
 * @ClassName UrmUserRegHistory
 * @Description 
 * @version 1.0
 * @Date 2017-09-14 11:53:15
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDate;
import java.time.LocalTime;

public class UrmUserRegHistoryDO extends BaseDO {
    /**
     *  userId 内部用户号
     */
    private String userId;
    /**
     *  mblNo 手机号码
     */
    private String mblNo;
    /**
     *  usrLvl 用户级别 0:普通用户 1:企业用户 2:个人商家 3：企业商家
     */
    private String usrLvl;
    /**
     *  idChkFlg 实名标志 0：非实名 1：实名
     */
    private String idChkFlg;
    /**
     *  usrRegCnl 注册渠道
     */
    private String usrRegCnl;
    /**
     *  usrRegIp 注册IP
     */
    private String usrRegIp;
    /**
     *  usrRegDt 注册日期
     */
    private LocalDate usrRegDt;
    /**
     *  usrRegTm 注册时间
     */
    private LocalTime usrRegTm;
    /**
     *  usrClsCnl 销户渠道
     */
    private String usrClsCnl;
    /**
     *  usrClsIp 销户IP
     */
    private String usrClsIp;
    /**
     *  usrClsDt 销户日期
     */
    private LocalDate usrClsDt;
    /**
     *  usrClsTm 销户时间
     */
    private LocalTime usrClsTm;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getUsrLvl() {
        return usrLvl;
    }

    public void setUsrLvl(String usrLvl) {
        this.usrLvl = usrLvl;
    }

    public String getIdChkFlg() {
        return idChkFlg;
    }

    public void setIdChkFlg(String idChkFlg) {
        this.idChkFlg = idChkFlg;
    }

    public String getUsrRegCnl() {
        return usrRegCnl;
    }

    public void setUsrRegCnl(String usrRegCnl) {
        this.usrRegCnl = usrRegCnl;
    }

    public String getUsrRegIp() {
        return usrRegIp;
    }

    public void setUsrRegIp(String usrRegIp) {
        this.usrRegIp = usrRegIp;
    }

    public LocalDate getUsrRegDt() {
        return usrRegDt;
    }

    public void setUsrRegDt(LocalDate usrRegDt) {
        this.usrRegDt = usrRegDt;
    }

    public LocalTime getUsrRegTm() {
        return usrRegTm;
    }

    public void setUsrRegTm(LocalTime usrRegTm) {
        this.usrRegTm = usrRegTm;
    }

    public String getUsrClsCnl() {
        return usrClsCnl;
    }

    public void setUsrClsCnl(String usrClsCnl) {
        this.usrClsCnl = usrClsCnl;
    }

    public String getUsrClsIp() {
        return usrClsIp;
    }

    public void setUsrClsIp(String usrClsIp) {
        this.usrClsIp = usrClsIp;
    }

    public LocalDate getUsrClsDt() {
        return usrClsDt;
    }

    public void setUsrClsDt(LocalDate usrClsDt) {
        this.usrClsDt = usrClsDt;
    }

    public LocalTime getUsrClsTm() {
        return usrClsTm;
    }

    public void setUsrClsTm(LocalTime usrClsTm) {
        this.usrClsTm = usrClsTm;
    }
}