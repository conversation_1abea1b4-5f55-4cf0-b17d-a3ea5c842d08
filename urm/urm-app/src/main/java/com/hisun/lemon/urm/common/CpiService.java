package com.hisun.lemon.urm.common;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.client.CardClient;
import com.hisun.lemon.cpi.dto.AgrInfoRspDTO;
import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.framework.data.GenericRspDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @function CpiService
 * @description 资金流入服务接口
 * @date 9/19/2017 Tue
 * @time 4:42 PM
 */
@Component
public class CpiService {
    private static final Logger logger = LoggerFactory.getLogger(CpiService.class);

    @Resource
    private CardClient card;

    @Resource
    private SensitiveDataHandle sens;

    public Boolean existBankCard(String userId, String crdNo) {
        List<AgrInfoRspDTO.CardAgrInfo> cardAgrInfos = queryBankCard(userId);
        for (AgrInfoRspDTO.CardAgrInfo cardAgrInfo : cardAgrInfos) {
            String crdNoEnc = cardAgrInfo.getCrdNoEnc();
            if (JudgeUtils.isNotBlank(crdNoEnc)) {
                String tmpCrdNo = sens.decryptSensitiveData(crdNoEnc);
                if (JudgeUtils.equals(crdNo, tmpCrdNo)) {
                    return true;
                }
            }
        }
        return false;
    }

    public Boolean existBankCard(String userId) {
        List<AgrInfoRspDTO.CardAgrInfo> cardAgrInfos = queryBankCard(userId);
        if (JudgeUtils.isNotEmpty(cardAgrInfos)) {
            return true;
        }
        return false;
    }

    private List<AgrInfoRspDTO.CardAgrInfo> queryBankCard(String userId) {
        GenericRspDTO<AgrInfoRspDTO> cardsInfo = card.queryCardsInfo(CorpBusTyp.SIGN, CorpBusSubTyp.FAST_SIGN, userId, null);
        if (JudgeUtils.isNotSuccess(cardsInfo.getMsgCd())) {
            LemonException.throwLemonException(cardsInfo.getMsgCd());
        }
        return cardsInfo.getBody().getList();
    }
}
