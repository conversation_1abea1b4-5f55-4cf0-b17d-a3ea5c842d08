package com.hisun.lemon.urm.service.impl;

import com.hisun.lemon.cmm.client.ConstantParamClient;
import com.hisun.lemon.cmm.dto.ConstantParamRspDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.RandomUtils;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.urm.common.AcmService;
import com.hisun.lemon.urm.common.CrmService;
import com.hisun.lemon.urm.common.SensitiveDataHandle;
import com.hisun.lemon.urm.common.SmsSend;
import com.hisun.lemon.urm.constants.URMConstants;
import com.hisun.lemon.urm.constants.URMMessageCode;
import com.hisun.lemon.urm.dao.*;
import com.hisun.lemon.urm.entity.*;
import com.hisun.lemon.urm.service.IUserBasicInfService;
import com.hisun.lemon.urm.service.IUserBasicInfService1;
import com.hisun.lemon.urm.utils.KeyDataHideUtils;
import com.hisun.lemon.urm.utils.MobileHandlerUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static com.hisun.lemon.urm.utils.CipherProcessorUtils1.*;

//import com.hisun.lemon.urm.service.IEmailService;

/**
 * @funciton UserManagementDao
 * @description 用户管理传输对象
 * <AUTHOR>
 * @date 7/8/2017 FRI
 * @time 4:44 PM
 */
@Transactional
@Service
public class UserBasicInfServiceImpl1 extends BaseService implements IUserBasicInfService1 {
    private static final Logger logger = LoggerFactory.getLogger(UserBasicInfServiceImpl1.class);
    @Resource
    private IUrmUserBasicInfDao userBasicInfDao;

    @Resource
    private IUrmSafeInfDao safeInfDao;

    @Resource
    private IUrmCprExtInfDao cprExtInfDao;

    @Resource
    private IUrmCprItfAuthDao cprItfAuthDao;

    @Resource
    private IUrmSafeLoginDao safeLoginDao;

    @Resource
    private IUrmUserRegHistoryDao userRegHistoryDao;

    @Resource
    private AcmService acm;

    @Resource
    private SensitiveDataHandle sensData;

    @Resource
    private SmsSend sms;

    @Resource
    private CrmService crm;

    @Resource
    private MessageSource messageSource;

    @Resource
    private IQuesDao quesDao;

    @Override
    public String openUser1(UrmUserBasicInfDO userBasicInfDO, UrmSafeInfDO safeInfDO, UrmCprExtInfDO
            cprExtInfDO, UrmSafeLoginDO safeLoginDO) {
        String usrLvl = userBasicInfDO.getUsrLvl();
        //默认为888888商户起始编号
        String countryCode = URMConstants.MER_STATIC_ID;
        if (JudgeUtils.equals(usrLvl, URMConstants.ORDINARY_USER) || JudgeUtils.equals(usrLvl, URMConstants
                .INDIVIDUAL_BUSINESS)) {
            String mblNo = userBasicInfDO.getMblNo();
            countryCode = MobileHandlerUtils.getNationCode(mblNo);
            if (JudgeUtils.isBlank(countryCode)) {
                LemonException.throwLemonException(URMMessageCode.ILLEGAL_MBL_NO);
            }
            countryCode = String.format("%06d", Integer.valueOf(countryCode));
        }
        String userId = IdGenUtils.generateId("UserId", countryCode, 10);
        String acNo = "";
        //更新用户基本信息
        if (JudgeUtils.isNotNull(userBasicInfDO)) {
            if (JudgeUtils.isBlank(userBasicInfDO.getUsrLvl())) {
                usrLvl = URMConstants.ORDINARY_USER;
            }
            if (JudgeUtils.equals(usrLvl, URMConstants.ORDINARY_USER)) {
                String idNo = userBasicInfDO.getIdNo();
                //TODO 检查证件号是否合法
                String idChkFlg = URMConstants.NOT_REAL_NM_FLG;
//                if (JudgeUtils.isNotBlank(userBasicInfDO.getIdNo()) && JudgeUtils.isNotBlank(userBasicInfDO.getUsrNm())) {
//                    idChkFlg = URMConstants.REAL_NM_FLG;
//                }
                userBasicInfDO.setIdChkFlg(idChkFlg);
                if (JudgeUtils.equals(idChkFlg, URMConstants.REAL_NM_FLG)) {
                    userBasicInfDO.setIdNoHid(KeyDataHideUtils.idNoHide(idNo));
                    userBasicInfDO.setUsrNmHid(userBasicInfDO.getUsrNm());
                }
                if (JudgeUtils.isNotBlank(idNo)) {
                    userBasicInfDO.setIdNoHid(KeyDataHideUtils.idNoHide(idNo));
                    try {
                        idNo = sensData.encryptSensitiveData(idNo);
                        userBasicInfDO.setIdNo(idNo);
                    } catch (Exception e) {
                        LemonException.throwLemonException(e);
                    }

                    //证件不能存在五个以上
//                    int count = basicInfDao.countIdNo(idNo);
//                    int maxSameIdNo = URMConstants.MAX_SAME_ID_NO;
//                    if (count >= maxSameIdNo) {
//                        LemonException.throwLemonException(URMMessageCode.ID_EXCEED_MAX_NUM);
//                    }
                }


            }
            userBasicInfDO.setUserId(userId);
            userBasicInfDO.setUsrLvl(usrLvl);
            userBasicInfDO.setUsrRegDt(DateTimeUtils.getCurrentLocalDate());
            userBasicInfDO.setUsrRegTm(DateTimeUtils.getCurrentLocalTime());
            userBasicInfDO.setUsrRegCnl(LemonUtils.getChannel());
            userBasicInfDO.setUsrRegIp(LemonUtils.getClientIp());
            userBasicInfDao.insert(userBasicInfDO);
            acNo = acm.openUserAccount(userId);
            if (JudgeUtils.isBlank(acNo)) {
                LemonException.throwLemonException(URMMessageCode.OPEN_USER_FAIL);
            }
        } else {
            LemonException.throwBusinessException(URMMessageCode.USR_BASIC_INF_IS_NULL);
        }

        //更新用户安全信息表
        if (JudgeUtils.equals(usrLvl, URMConstants.ORDINARY_USER)) {
            if (JudgeUtils.isNull(safeInfDO)) {
                LemonException.throwLemonException(URMMessageCode.USR_SAFE_INF_IS_NULL);
            }
            if (JudgeUtils.isNull(safeInfDO.getSafeAns1()) || JudgeUtils.isNull(safeInfDO.getSafeQues1())) {
                LemonException.throwBusinessException(URMMessageCode.USR_SAFE_INF_IS_NULL);
            }
        }
        if (JudgeUtils.isNull(safeInfDO)) {
            safeInfDO = new UrmSafeInfDO();
        }
        String safeQues1 = safeInfDO.getSafeQues1();
        String safeAns1 = safeInfDO.getSafeAns1();

        if (JudgeUtils.equals(userBasicInfDO.getUsrLvl(), URMConstants.ORDINARY_USER)
                && JudgeUtils.equals(LemonUtils.getSource(), URMConstants.INTERNAL_GATE)) {
            safeQues1 = quesDao.findQuesByQuesNo(Integer.valueOf(safeQues1)).getSafeQues();
        }

        if (JudgeUtils.isNotBlank(safeAns1) && JudgeUtils.isNotBlank(safeQues1)) {
            try {
                safeQues1 = sensData.encryptSensitiveData(safeQues1);
                safeAns1 = sensData.encryptSensitiveData(safeAns1);
                safeInfDO.setSafeQues1(safeQues1);
                safeInfDO.setSafeAns1(safeAns1);
            } catch (Exception e) {
                LemonException.throwLemonException(e);
            }
        }
        String safeId = IdGenUtils.generateId("SafeId", 16);
        safeInfDO.setSafeSts(URMConstants.SAFE_STS_EFF);
        safeInfDO.setSafeId(safeId);
        safeInfDO.setUserId(userId);
        if (JudgeUtils.equals(usrLvl, URMConstants.ORDINARY_USER)) {
            safeInfDO.setOprTyp(URMConstants.USR_TYP);
        } else {
            safeInfDO.setOprTyp(URMConstants.MER_ADMIN_TYP);
        }
        safeInfDO.setSafeStsw(" ");
        safeInfDO.setPwdSalt(userId);
        String loginPwd = safeInfDO.getLoginPwd();
        String payPwd = safeInfDO.getPayPwd();
        String loginPwdPlaintext = null;
        String payPwdPlaintext = null;
        try {
            if (JudgeUtils.notEquals(LemonUtils.getSource(), URMConstants.INTERNAL_GATE)) {
                if (JudgeUtils.isNull(loginPwd)) {
                    Map<String, String> loginPwdMap = initLoginPwd(userId);
                    loginPwd = loginPwdMap.get("loginPwd");
                    loginPwdPlaintext = loginPwdMap.get("loginPwdPlaintext");
                    logger.debug("loginPwdPlaintext: " + loginPwdPlaintext);
                    safeInfDO.setLoginPwd(loginPwd);
                } else {
                    if (loginPwd.length() != URMConstants.FIFTEEN_PWD_LEN) {
                        loginPwd = convertPwdfromZpkToPvk(userId, loginPwd);
                    }
                    safeInfDO.setLoginPwd(loginPwd);
                }
                if (JudgeUtils.isNull(payPwd)) {
                    Map<String, String> payPwdMap = initPayPwd(acNo);
                    payPwd = payPwdMap.get("payPwdPinOffset");
                    payPwdPlaintext = payPwdMap.get("payPwdPlaintext");
                    logger.debug("payPwdPlaintext: " + payPwdPlaintext);
                    safeInfDO.setPayPwd(payPwd);
                } else if (JudgeUtils.equals(LemonUtils.getSource(), URMConstants.SOURCE_BATCH_OPEN)) {
                    //体验卡批量开户，支付密码明文传入处理
                    payPwd = generatePayPinOffset(acNo, payPwd);
                    safeInfDO.setPayPwd(payPwd);
                } else {
                    payPwd = convertPinToPinOffset(acNo, payPwd);
                    safeInfDO.setPayPwd(payPwd);
                }
            } else {
                safeInfDO.setLoginPwd("");
                if (JudgeUtils.isNotBlank(payPwd)) {
                    payPwd = generatePayPinOffset(acNo, payPwd);
                    safeInfDO.setPayPwd(payPwd);
                }
            }
            safeInfDO.setLoginFailCnt(Byte.valueOf("0", 10));
        } catch (Throwable e) {
            LemonException.throwLemonException(e);
        }
        safeInfDO.setPayFailCnt(Byte.valueOf("0", 10));
        safeInfDO.setCreDt(DateTimeUtils.getCurrentLocalDate());
        safeInfDO.setCreTm(DateTimeUtils.getCurrentLocalTime());
        safeInfDao.insert(safeInfDO);


        //更新用户登录信息表
        String loginId = "";
        String displayNm = "";
        String loginTyp = "";
        if (JudgeUtils.isNotNull(safeLoginDO)) {
            loginId = safeLoginDO.getLoginId();
            displayNm = safeLoginDO.getDisplayNm();
        } else {
            safeLoginDO = new UrmSafeLoginDO();
        }
        if (JudgeUtils.equals(usrLvl, URMConstants.ORDINARY_USER)) {
            loginId = safeInfDO.getMblNo();
            loginTyp = URMConstants.MBL_LOGIN_ID;
            if (JudgeUtils.isNotBlank(userBasicInfDO.getUsrNm())) {
                displayNm = userBasicInfDO.getUsrNm();
            }
        } else if (JudgeUtils.equals(usrLvl, URMConstants.ENTERPRISE_BUSINESS)) {
            if (JudgeUtils.isNotBlank(loginId)) {
                loginTyp = URMConstants.USER_DEFINE_LOGIN_ID;
                loginId = safeLoginDO.getLoginId();
            } else {
                loginTyp = URMConstants.SYS_LOGIN_ID;
                loginId = "MER" + userId.substring(6);
            }
        } else {
            LemonException.throwLemonException(URMMessageCode.USRLVL_NOT_SUPPORT);
        }
        safeLoginDO.setSafeId(safeId);
        safeLoginDO.setLoginId(loginId);
        safeLoginDO.setLoginTyp(loginTyp);
        safeLoginDO.setDisplayNm(displayNm);
        safeLoginDao.insert(safeLoginDO);
        logger.info("loginId:" + loginId + " displayNm:" + displayNm);

        //更新商户扩展信息表
        if (JudgeUtils.isNotNull(cprExtInfDO)) {
            cprExtInfDO.setUserId(userId);
            cprExtInfDao.insert(cprExtInfDO);
        }

        //更新商户接口密钥信息表
        if (JudgeUtils.notEquals(usrLvl, URMConstants.ORDINARY_USER)) {
            UrmCprItfAuthDO cprItfAuthDO = new UrmCprItfAuthDO();
            cprItfAuthDO.setUserId(userId);
            cprItfAuthDO.setItfNm("");
            cprItfAuthDO.setVersion(URMConstants.CPR_KEY_VER);
            cprItfAuthDO.setVerifyType(URMConstants.CPR_KEY_TYP);
            String secretKey = RandomUtils.randomLetterFixLength(32);
            cprItfAuthDO.setSecretKey(secretKey);
            cprItfAuthDO.setSts(URMConstants.CPR_KEY_EFF);
            cprItfAuthDao.insert(cprItfAuthDO);

            String[] emailAddr = {safeInfDO.getEmail()};
            if (JudgeUtils.isNotEmpty(emailAddr)) {
                Locale locale = LocaleContextHolder.getLocale();
                String subject = messageSource.getMessage("email.merckey.subject", null, locale);
                String name = userBasicInfDO.getUsrNm();
                String content = messageSource.getMessage("email.merckey.content", null, locale) + secretKey;
                String end = messageSource.getMessage("email.merckey.end", null, locale);
                Email email = new Email();
                email.setSubject(subject);
                email.setTo(emailAddr);
                email.setTemplate("email/merckey");
                Map<String, Object> variables = new HashMap<>();
                variables.put("name", name);
                variables.put("content", content);
                variables.put("end", end);
                logger.info("subject: {}", subject);
                logger.info("name: {}", name);
                logger.info("content: {}", content);
                logger.info("end: {}", end);
                try {
//                    emailService.sendEmail(email, null, variables, locale);
                } catch (Exception e) {
                    logger.error("发送邮件失败"+e.getMessage());
                    //do nothing
                }
            }
        }

        //登记开销户历史
        UrmUserRegHistoryDO userRegHistory = new UrmUserRegHistoryDO();
        BeanUtils.copyProperties(userRegHistory, userBasicInfDO);
        userRegHistoryDao.insert(userRegHistory);

        //商户管理员下发密码短信
        if(JudgeUtils.equals(safeInfDO.getOprTyp(), URMConstants.MER_ADMIN_TYP) && JudgeUtils.isNotBlank(safeInfDO
                .getMblNo())) {
            String mblNo = safeInfDO.getMblNo();
            Map<String, String> smsMap = new HashMap<>();
            smsMap.put("loginId", loginId);
            if (JudgeUtils.isNotBlank(loginPwdPlaintext) && JudgeUtils.isNotBlank(payPwdPlaintext)) {
                smsMap.put("loginPwd", loginPwdPlaintext);
                smsMap.put("payPwd", payPwdPlaintext);
                String smsType = URMConstants.SMS_TYP;
                String templateId = URMConstants.MER_ADMIN_TEMP_ID;
                try {
                    sms.smsSend(mblNo, smsType, templateId, smsMap);
                } catch (Exception e) {
                    if (e instanceof LemonException) {
                        LemonException lemon = (LemonException) e;
                        logger.info("SMS SEND FAIL :" + lemon.getMsgCd());
                    } else {
                        logger.info("SMS SERVER NOT AVAILABLE");
                    }
                }
            }
        }

        //通知CRM用户开户
        if (JudgeUtils.equals(userBasicInfDO.getUsrLvl(), URMConstants.ORDINARY_USER) && JudgeUtils.notEquals(LemonUtils
                .getSource(), URMConstants.INTERNAL_GATE)) {
            crm.notifyCrm(userBasicInfDO, safeInfDO);
        }
        return userId;
    }


}
