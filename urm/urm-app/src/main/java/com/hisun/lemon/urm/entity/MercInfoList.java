package com.hisun.lemon.urm.entity;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @create 2018/2/5
 */
public class MercInfoList {

    private String userId;

    private String mercName;

    private String email;

    private LocalDate usrRegDt;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMercName() {
        return mercName;
    }

    public void setMercName(String mercName) {
        this.mercName = mercName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public LocalDate getUsrRegDt() {
        return usrRegDt;
    }

    public void setUsrRegDt(LocalDate usrRegDt) {
        this.usrRegDt = usrRegDt;
    }

    @Override
    public String toString() {
        return "MercInfoList{" +
                "userId='" + userId + '\'' +
                ", mercName='" + mercName + '\'' +
                ", email='" + email + '\'' +
                ", usrRegDt=" + usrRegDt +
                '}';
    }
}
