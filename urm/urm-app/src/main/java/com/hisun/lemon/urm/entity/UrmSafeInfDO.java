/*
 * @ClassName UrmSafeInfDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-24 14:36:45
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

public class UrmSafeInfDO extends BaseDO {
    /**
     *  safeId 用户安全ID号
     */
    private String safeId;
    /**
     *  userId 内部用户号
     */
    private String userId;
    /**
     *  mblNo 手机号码
     */
    private String mblNo;
    /**
     *  email 邮箱
     */
    private String email;
    /**
     *  oprTyp 登录用户类别，0，普通用户，1 商户操作员，商户操作员只能LOGIN_ID登录
     */
    private String oprTyp;
    /**
     *  safeSts 状态 0：失效 1：生效
     */
    private String safeSts;
    /**
     *  safeStsw 状态字
     */
    private String safeStsw;
    /**
     *  pwdSalt 加密随机因子
     */
    private String pwdSalt;
    /**
     *  loginPwd 登录密码
     */
    private String loginPwd;
    /**
     *  loginFailCnt 登录密码错误次数
     */
    private Byte loginFailCnt;
    /**
     *  loginLckDt 登录密码锁定日期
     */
    private LocalDate loginLckDt;
    /**
     *  loginLckTm 登录密码锁定时间
     */
    private LocalDateTime loginLckTm;
    /**
     *  payPwd 支付密码
     */
    private String payPwd;
    /**
     *  payFailCnt 支付密码错误次数
     */
    private Byte payFailCnt;
    /**
     *  payLckDt 支付密码锁定日期
     */
    private LocalDate payLckDt;
    /**
     *  payLckTm 支付密码锁定时间
     */
    private LocalDateTime payLckTm;
    /**
     *  safeMblNo 密保手机号
     */
    private String safeMblNo;
    /**
     *  safeEmail 密保邮箱
     */
    private String safeEmail;
    /**
     *  safeQues1 密保问题1
     */
    private String safeQues1;
    /**
     *  safeAns1 密保答案1
     */
    private String safeAns1;
    /**
     *  safeQues2 密保问题2
     */
    private String safeQues2;
    /**
     *  safeAns2 密保答案2
     */
    private String safeAns2;
    /**
     *  safeQues3 密保问题3
     */
    private String safeQues3;
    /**
     *  safeAns3 密保答案3
     */
    private String safeAns3;
    /**
     *  safeQues4 密保问题4
     */
    private String safeQues4;
    /**
     *  safeAns4 密保答案4
     */
    private String safeAns4;
    /**
     *  safeQues5 密保问题5
     */
    private String safeQues5;
    /**
     *  safeAns5 密保答案5
     */
    private String safeAns5;
    /**
     *  creDt 创建日期
     */
    private LocalDate creDt;
    /**
     *  creTm 创建时间
     */
    private LocalTime creTm;
    /**
     *  expDt 失效日期
     */
    private LocalDate expDt;
    /**
     *  expTm 失效时间
     */
    private LocalTime expTm;
    /**
     *  updBusCnl 最后更新渠道
     */
    private String updBusCnl;
    /**
     *  updIpAdr 最后更新IP地址
     */
    private String updIpAdr;
    /**
     *  lastLoginDt 最后登录日期
     */
    private LocalDate lastLoginDt;
    /**
     *  lastLoginTm 最后登录时间
     */
    private LocalTime lastLoginTm;

    /**
     * 手势密码
     */
    private String handPwd;

    /**
     * 手势密码标志：1开启, 2关闭
     */
    private String handFlg;

    /**
     *  handFailCnt 手势密码错误次数
     */
    private Byte handFailCnt;

    public String getHandPwd() {
        return handPwd;
    }

    public void setHandPwd(String handPwd) {
        this.handPwd = handPwd;
    }

    public String getHandFlg() {
        return handFlg;
    }

    public void setHandFlg(String handFlg) {
        this.handFlg = handFlg;
    }

    public String getSafeId() {
        return safeId;
    }

    public void setSafeId(String safeId) {
        this.safeId = safeId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getOprTyp() {
        return oprTyp;
    }

    public void setOprTyp(String oprTyp) {
        this.oprTyp = oprTyp;
    }

    public String getSafeSts() {
        return safeSts;
    }

    public void setSafeSts(String safeSts) {
        this.safeSts = safeSts;
    }

    public String getSafeStsw() {
        return safeStsw;
    }

    public void setSafeStsw(String safeStsw) {
        this.safeStsw = safeStsw;
    }

    public String getPwdSalt() {
        return pwdSalt;
    }

    public void setPwdSalt(String pwdSalt) {
        this.pwdSalt = pwdSalt;
    }

    public String getLoginPwd() {
        return loginPwd;
    }

    public void setLoginPwd(String loginPwd) {
        this.loginPwd = loginPwd;
    }

    public Byte getLoginFailCnt() {
        return loginFailCnt;
    }

    public void setLoginFailCnt(Byte loginFailCnt) {
        this.loginFailCnt = loginFailCnt;
    }

    public LocalDate getLoginLckDt() {
        return loginLckDt;
    }

    public void setLoginLckDt(LocalDate loginLckDt) {
        this.loginLckDt = loginLckDt;
    }

    public LocalDateTime getLoginLckTm() {
        return loginLckTm;
    }

    public void setLoginLckTm(LocalDateTime loginLckTm) {
        this.loginLckTm = loginLckTm;
    }

    public String getPayPwd() {
        return payPwd;
    }

    public void setPayPwd(String payPwd) {
        this.payPwd = payPwd;
    }

    public Byte getPayFailCnt() {
        return payFailCnt;
    }

    public void setPayFailCnt(Byte payFailCnt) {
        this.payFailCnt = payFailCnt;
    }

    public LocalDate getPayLckDt() {
        return payLckDt;
    }

    public void setPayLckDt(LocalDate payLckDt) {
        this.payLckDt = payLckDt;
    }

    public LocalDateTime getPayLckTm() {
        return payLckTm;
    }

    public void setPayLckTm(LocalDateTime payLckTm) {
        this.payLckTm = payLckTm;
    }

    public String getSafeMblNo() {
        return safeMblNo;
    }

    public void setSafeMblNo(String safeMblNo) {
        this.safeMblNo = safeMblNo;
    }

    public String getSafeEmail() {
        return safeEmail;
    }

    public void setSafeEmail(String safeEmail) {
        this.safeEmail = safeEmail;
    }

    public String getSafeQues1() {
        return safeQues1;
    }

    public void setSafeQues1(String safeQues1) {
        this.safeQues1 = safeQues1;
    }

    public String getSafeAns1() {
        return safeAns1;
    }

    public void setSafeAns1(String safeAns1) {
        this.safeAns1 = safeAns1;
    }

    public String getSafeQues2() {
        return safeQues2;
    }

    public void setSafeQues2(String safeQues2) {
        this.safeQues2 = safeQues2;
    }

    public String getSafeAns2() {
        return safeAns2;
    }

    public void setSafeAns2(String safeAns2) {
        this.safeAns2 = safeAns2;
    }

    public String getSafeQues3() {
        return safeQues3;
    }

    public void setSafeQues3(String safeQues3) {
        this.safeQues3 = safeQues3;
    }

    public String getSafeAns3() {
        return safeAns3;
    }

    public void setSafeAns3(String safeAns3) {
        this.safeAns3 = safeAns3;
    }

    public String getSafeQues4() {
        return safeQues4;
    }

    public void setSafeQues4(String safeQues4) {
        this.safeQues4 = safeQues4;
    }

    public String getSafeAns4() {
        return safeAns4;
    }

    public void setSafeAns4(String safeAns4) {
        this.safeAns4 = safeAns4;
    }

    public String getSafeQues5() {
        return safeQues5;
    }

    public void setSafeQues5(String safeQues5) {
        this.safeQues5 = safeQues5;
    }

    public String getSafeAns5() {
        return safeAns5;
    }

    public void setSafeAns5(String safeAns5) {
        this.safeAns5 = safeAns5;
    }

    public LocalDate getCreDt() {
        return creDt;
    }

    public void setCreDt(LocalDate creDt) {
        this.creDt = creDt;
    }

    public LocalTime getCreTm() {
        return creTm;
    }

    public void setCreTm(LocalTime creTm) {
        this.creTm = creTm;
    }

    public LocalDate getExpDt() {
        return expDt;
    }

    public void setExpDt(LocalDate expDt) {
        this.expDt = expDt;
    }

    public LocalTime getExpTm() {
        return expTm;
    }

    public void setExpTm(LocalTime expTm) {
        this.expTm = expTm;
    }

    public String getUpdBusCnl() {
        return updBusCnl;
    }

    public void setUpdBusCnl(String updBusCnl) {
        this.updBusCnl = updBusCnl;
    }

    public String getUpdIpAdr() {
        return updIpAdr;
    }

    public void setUpdIpAdr(String updIpAdr) {
        this.updIpAdr = updIpAdr;
    }

    public LocalDate getLastLoginDt() {
        return lastLoginDt;
    }

    public void setLastLoginDt(LocalDate lastLoginDt) {
        this.lastLoginDt = lastLoginDt;
    }

    public LocalTime getLastLoginTm() {
        return lastLoginTm;
    }

    public void setLastLoginTm(LocalTime lastLoginTm) {
        this.lastLoginTm = lastLoginTm;
    }

    public Byte getHandFailCnt() {
        return handFailCnt;
    }

    public void setHandFailCnt(Byte handFailCnt) {
        this.handFailCnt = handFailCnt;
    }

}