package com.hisun.lemon.urm.common;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.rsm.client.RiskCheckClient;
import com.hisun.lemon.rsm.dto.req.checkstatus.RiskCheckUserStatusReqDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @function RsmCompent
 * @description 实时风控检查组件
 * @date 9/6/2017 Wed
 * @time 10:26 AM
 */
@Component
public class RiskCheck {
    private static final Logger logger = LoggerFactory.getLogger(RiskCheck.class);

    @Autowired
    private RiskCheckClient riskCheck;

    public void checkUser(String userId) {
        RiskCheckUserStatusReqDTO checkUser = new RiskCheckUserStatusReqDTO();
        checkUser.setId(userId);
        checkUser.setIdTyp("01"); //01-用户号/商户号，02-银行卡，03-身份证
        GenericRspDTO<NoBody> genericRspDTO = riskCheck.checkUserStatus(checkUser);
//        if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())) {
        if (JudgeUtils.equals(genericRspDTO.getMsgCd(), "RSM30002") ||
                JudgeUtils.equals(genericRspDTO.getMsgCd(), "RSM30015") || JudgeUtils.equals(genericRspDTO.getMsgCd(), "RSM30011")) {
            LemonException.throwLemonException(genericRspDTO.getMsgCd());
        }
    }
}
