package com.hisun.lemon.urm.utils;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.urm.constants.URMMessageCode;

/**
 * <AUTHOR>
 * @function MobileHandlerUtils
 * @description 手机号处理工具类
 * @date 8/10/2017 Thu
 * @time 2:44 PM
 */
public class MobileHandlerUtils {
    public static String getNationCode(String mblNo) {
        String nation = "";
        String[] mbl = mblNo.split("-");
        if (mbl.length == 1) {
            return nation;
        }
        String[] strArray = mbl[0].split("\\+");
        if (JudgeUtils.isNotNull(strArray)) {
            if (strArray.length == 1) {
                nation = strArray[0];
            } else if (strArray.length == 2) {
                nation = strArray[1];
            } else {
                LemonException.throwLemonException(URMMessageCode.ILLEGAL_MBL_NO);
            }
        }
        return nation;
    }

    public static String getMobileNumber(String mblNo) {
        String[] mbl = mblNo.split("-");
        if (mbl.length == 1) {
            return mbl[0];
        } else {
            return mbl[1];
        }
    }
}
