/*
 * @ClassName UrmUserBasicInfDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-24 14:36:45
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.time.LocalDate;
import java.time.LocalTime;

public class UrmUserBasicInfDO extends BaseDO {
    /**
     *  userId 内部用户号
     */
    private String userId;
    /**
     *  mblNo 用户手机号
     */
    private String mblNo;
    /**
     *  usrSts 用户状态 0:开户 1:销户
     */
    private String usrSts;
    /**
     *  usrLvl 用户级别 0:普通用户 1:企业用户 2:个人商家 3：企业商家
     */
    private String usrLvl;
    /**
     *  idChkFlg 实名标志 0：非实名 1：实名
     */
    private String idChkFlg;
    /**
     *  idChkNo 认证流水号
     */
    private String idChkNo;
    /**
     *  idChkDt 认证日期
     */
    private LocalDate idChkDt;
    /**
     *  idChkTm 认证时间
     */
    private LocalTime idChkTm;
    /**
     *  idType 证件类型
     */
    private String idType;
    /**
     *  idNo 证件号码
     */
    private String idNo;
    /**
     *  idNoHid 脱敏证件号码
     */
    private String idNoHid;
    /**
     *  usrNm 用户姓名
     */
    private String usrNm;
    /**
     *  usrNmHid 脱敏用户姓名
     */
    private String usrNmHid;
    /**
     *  usrGender 用户性别 M：男 F：女
     */
    private String usrGender;
    /**
     *  usrNation 用户归属国家
     */
    private String usrNation;
    /**
     *  usrBirthDt 出生日期
     */
    private String usrBirthDt;
    /**
     *  issuAuth 签发机关
     */
    private String issuAuth;
    /**
     *  idEffDt 证件有效期起始
     */
    private String idEffDt;
    /**
     *  idExpDt 证件有效期截止
     */
    private String idExpDt;
    /**
     *  usrRegCnl 注册渠道
     */
    private String usrRegCnl;
    /**
     *  usrRegIp 注册IP
     */
    private String usrRegIp;
    /**
     *  usrRegDt 注册日期
     */
    private LocalDate usrRegDt;
    /**
     *  usrRegTm 注册时间
     */
    private LocalTime usrRegTm;
    /**
     *  usrClsDt 销户日期
     */
    private LocalDate usrClsDt;
    /**
     *  usrClsTm 销户时间
     */
    private LocalTime usrClsTm;
    /**
     *  kybCert kyb认证
     */
    private String kybCert;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getUsrSts() {
        return usrSts;
    }

    public void setUsrSts(String usrSts) {
        this.usrSts = usrSts;
    }

    public String getUsrLvl() {
        return usrLvl;
    }

    public void setUsrLvl(String psnCrpFlag) {
        this.usrLvl = psnCrpFlag;
    }

    public String getIdChkFlg() {
        return idChkFlg;
    }

    public void setIdChkFlg(String idChkFlg) {
        this.idChkFlg = idChkFlg;
    }

    public String getIdChkNo() {
        return idChkNo;
    }

    public void setIdChkNo(String idChkNo) {
        this.idChkNo = idChkNo;
    }

    public LocalDate getIdChkDt() {
        return idChkDt;
    }

    public void setIdChkDt(LocalDate idChkDt) {
        this.idChkDt = idChkDt;
    }

    public LocalTime getIdChkTm() {
        return idChkTm;
    }

    public void setIdChkTm(LocalTime idChkTm) {
        this.idChkTm = idChkTm;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getIdNoHid() {
        return idNoHid;
    }

    public void setIdNoHid(String idNoHid) {
        this.idNoHid = idNoHid;
    }

    public String getUsrNm() {
        return usrNm;
    }

    public void setUsrNm(String usrNm) {
        this.usrNm = usrNm;
    }

    public String getUsrNmHid() {
        return usrNmHid;
    }

    public void setUsrNmHid(String usrNmHid) {
        this.usrNmHid = usrNmHid;
    }

    public String getUsrGender() {
        return usrGender;
    }

    public void setUsrGender(String usrGender) {
        this.usrGender = usrGender;
    }

    public String getUsrNation() {
        return usrNation;
    }

    public void setUsrNation(String usrNation) {
        this.usrNation = usrNation;
    }

    public String getUsrBirthDt() {
        return usrBirthDt;
    }

    public void setUsrBirthDt(String usrBirthDt) {
        this.usrBirthDt = usrBirthDt;
    }

    public String getIssuAuth() {
        return issuAuth;
    }

    public void setIssuAuth(String issuAuth) {
        this.issuAuth = issuAuth;
    }

    public String getIdEffDt() {
        return idEffDt;
    }

    public void setIdEffDt(String idEffDt) {
        this.idEffDt = idEffDt;
    }

    public String getIdExpDt() {
        return idExpDt;
    }

    public void setIdExpDt(String idExpDt) {
        this.idExpDt = idExpDt;
    }

    public String getUsrRegCnl() {
        return usrRegCnl;
    }

    public void setUsrRegCnl(String usrRegCnl) {
        this.usrRegCnl = usrRegCnl;
    }

    public String getUsrRegIp() {
        return usrRegIp;
    }

    public void setUsrRegIp(String usrRegIp) {
        this.usrRegIp = usrRegIp;
    }

    public LocalDate getUsrRegDt() {
        return usrRegDt;
    }

    public void setUsrRegDt(LocalDate usrRegDt) {
        this.usrRegDt = usrRegDt;
    }

    public LocalTime getUsrRegTm() {
        return usrRegTm;
    }

    public void setUsrRegTm(LocalTime usrRegTm) {
        this.usrRegTm = usrRegTm;
    }

    public LocalDate getUsrClsDt() {
        return usrClsDt;
    }

    public void setUsrClsDt(LocalDate usrClsDt) {
        this.usrClsDt = usrClsDt;
    }

    public LocalTime getUsrClsTm() {
        return usrClsTm;
    }

    public void setUsrClsTm(LocalTime usrClsTm) {
        this.usrClsTm = usrClsTm;
    }

    public String getKybCert() {
        return kybCert;
    }

    public void setKybCert(String kybCert) {
        this.kybCert = kybCert;
    }
}