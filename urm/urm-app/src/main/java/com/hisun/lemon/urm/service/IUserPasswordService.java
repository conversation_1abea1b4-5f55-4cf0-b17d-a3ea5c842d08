package com.hisun.lemon.urm.service;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.urm.dto.ForgetPwdDTO;
import com.hisun.lemon.urm.entity.UrmSafeInfDO;

/**
 * <AUTHOR>
 * @date 2017年7月5日
 * @time 上午10:46:30
 *
 */
public interface IUserPasswordService {
    /**
     * 修改登录密码
     * @param loginId
     * @param newLoginPwd
     * @param oldLoginPwd
     * @return
     */
    public Boolean updLoginPwd(String loginId, String oldLoginPwd, String newLoginPwd);

    /**
     * 重置登录密码
     * @param loginId
     * @param newLoginPwd
     * @return
     */
    public Boolean resetLoginPwd(String loginId, String newLoginPwd);

    /**
     * 修改支付密码
     * @param userId
     * @param oldPayPwd
     * @param newPayPwd
     * @return
     */
    public Boolean updPayPwd(String userId, String oldPayPwd, String newPayPwd);

    /**
     * 重置支付密码
     * @param newPayPwd
     * @param userId
     * @return
     */
    public Boolean resetPayPwd(String userId, String newPayPwd);

    /**
     * 修改密保信息
     * @param loginId
     * @param safeMblNo
     * @param safeQues1
     * @param safeAns1
     * @return
     */
    public Boolean updPwdSafeInf(String loginId, String safeMblNo, String safeQues1, String safeAns1);

    /**
     * 重置商户密钥
     * @param userId
     * @return
     */
    public String resetCprTradingKey(String userId);

    /**
     * 支付密码后台重置
     * @param userId
     * @return
     */
    public void resetRandomPayPwd(String userId);

    /**
     * 登录密码后台重置
     * @param loginId
     * @return
     */
    public void resetRandomLoginPwd(String loginId);

    /**
     * 重置手势密码
     *
     * @param loginId
     * @param newHandPwd
     * @return
     */
    public Boolean resetHandPwd(String loginId, String newHandPwd, String handPwd);

    /**
     * 设置手势密码（开启关闭）
     *
     * @param flg
     * @return
     */
    public Boolean setHandPwdFlg(String userId, String flg, String flgOld);

    /**
     * 校验用户是否30天未登录，清理手势密码记录
     *
     * @param safeInfDO
     * @return
     */
    public Boolean checkHandPwdValid(UrmSafeInfDO safeInfDO);

    /**
     * 忘记密码
     *
     * @param forgetPwdDTO
     */
    void forgetLoginPwd(GenericDTO<ForgetPwdDTO> forgetPwdDTO);
}
