/*
 * @ClassName IUrmCprExtInfDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-24 14:36:45
 */
package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.UrmCprExtInfDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IUrmCprExtInfDao extends BaseDao<UrmCprExtInfDO> {

    List<String> getAffiliateMerchant(@Param("userId") String userId);
}