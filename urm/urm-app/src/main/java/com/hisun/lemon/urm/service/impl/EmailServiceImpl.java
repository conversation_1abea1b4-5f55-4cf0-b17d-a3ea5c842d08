package com.hisun.lemon.urm.service.impl;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.urm.Enum.EmailOperationTypeEnum;
import com.hisun.lemon.urm.constants.URMMessageCode;
import com.hisun.lemon.urm.dao.IUrmSafeLoginDao;
import com.hisun.lemon.urm.dto.EmailAuthDto;
import com.hisun.lemon.urm.dto.EmailCodeSendDto;
import com.hisun.lemon.urm.entity.UrmSafeLoginDO;
import com.hisun.lemon.urm.service.IEmailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 邮箱服务接口实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/9 09:35
 */
@Service
public class EmailServiceImpl implements IEmailService {
    private final static Logger logger = LoggerFactory.getLogger(EmailServiceImpl.class);

    @Autowired
    IUrmSafeLoginDao urmSafeLoginDao;

    @Autowired
    @Qualifier("redisTemplateString")
    private RedisTemplate<String, String> redisTemplate;
    /**
     * 邮箱验证码校验
     * @param req
     * @return
     */
    @Override
    public GenericRspDTO<String> emailCodeAuth(GenericDTO<EmailAuthDto> req) {
        String times = "email:code:times:" + req.getBody().getFlag() + ":" + req.getBody().getEmail();
        String errorTimes = redisTemplate.opsForValue().get(times);
        if(errorTimes != null && Integer.parseInt(errorTimes) >= 5){
            LemonException.throwBusinessException(URMMessageCode.EMAIL_TIMES_ERROR);
        }

        String inputCode = req.getBody().getCode();
        String email = req.getBody().getEmail();

        String redisKey = "email:code:" + req.getBody().getFlag() + ":" + req.getBody().getEmail();
        String storedCode = redisTemplate.opsForValue().get(redisKey);
        logger.info("验证码：" + storedCode);
        if(inputCode == null || !inputCode.equals(storedCode)){
            //记录发送次数
            int timesNum = 0;
            if (errorTimes != null) {
                timesNum = Integer.parseInt(errorTimes);
            }
            timesNum++;
            String timesString = String.valueOf(timesNum);
            redisTemplate.opsForValue().set(times, timesString, 15, TimeUnit.MINUTES);
            LemonException.throwBusinessException(URMMessageCode.EMAIL_AUTH_FAIL);
        }
        //删除验证码
        redisTemplate.delete(redisKey);

        //生成随机数存入redis
        String emailNo = String.format("%04d", new Random().nextInt(9999));
        String redisRandomKey = "email:random:" + req.getBody().getFlag() + ":" + req.getBody().getEmail();
        redisTemplate.opsForValue().set(redisRandomKey, emailNo, 1, TimeUnit.HOURS);
        return GenericRspDTO.newSuccessInstance(emailNo);
    }

    /**
     * 发送邮箱验证码
     * @param req
     * @return
     */
    @Override
    public GenericRspDTO<NoBody> emailCodeSend(GenericDTO<EmailCodeSendDto> req) {


        //  邮箱是否已注册
        checkEmailIsExit(req.getBody());

        // 生产6位数验证码
        String verificationCode = String.format("%06d", new Random().nextInt(999999));
        verificationCode = "111111";
        logger.info("验证码：" + verificationCode);

        // 存入redis
        String redisKey = "email:code:" + req.getBody().getFlag() + ":" + req.getBody().getEmail();
        String time = "email:code:time:" + req.getBody().getFlag() + ":" + req.getBody().getEmail();

        //如果已经存在
        if (redisTemplate.hasKey(time)) {
            LemonException.throwBusinessException(URMMessageCode.EMAIL_CODE_EXIST);
        }
        //邮箱验证码 5分钟有效
        redisTemplate.opsForValue().set(redisKey, verificationCode, 5, TimeUnit.MINUTES);
        //1分钟内只能发送一次验证码
        redisTemplate.opsForValue().set(time, verificationCode, 1, TimeUnit.MINUTES);

        //发送邮件
        try  {
//            emailApi.sendGeneralEmail("邮箱验证码", verificationCode, req.getEmail());
        } catch (Exception e) {
            logger.error("发送邮件失败", e);
        }
        return GenericRspDTO.newSuccessInstance();
    }

    /**
     * 邮箱是否已注册
     *
     * @param dto 邮箱
     * @return
     */
    @Override
    public GenericRspDTO<NoBody> checkEmailIsExit(EmailCodeSendDto dto) {
        // 获取邮箱验证码需要判断邮箱是否已经注册
        UrmSafeLoginDO urmSafeLoginDO = urmSafeLoginDao.get(dto.getEmail());

        if (EmailOperationTypeEnum.REGISTER.getValue().equals(dto.getFlag())) {
            if (urmSafeLoginDO != null) {
                LemonException.throwBusinessException(URMMessageCode.EMAIL_EXIST);
            }
        }

        if (EmailOperationTypeEnum.RESET.getValue().equals(dto.getFlag())) {
            if (urmSafeLoginDO == null) {
                LemonException.throwBusinessException(URMMessageCode.EMAIL_NOT_EXIST);
            }
        }
        return null;
    }

}
