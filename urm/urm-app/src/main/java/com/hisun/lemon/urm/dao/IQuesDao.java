package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.QuesDo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/11/3
 */
@Mapper
public interface IQuesDao extends BaseDao<QuesDo> {

    QuesDo findQuesByQuesNo(int quesNo);

    List<String> findQuesList(int quesNo);

    QuesDo findNoByQues(String safeQues);
}
