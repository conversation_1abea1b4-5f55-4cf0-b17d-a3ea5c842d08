package com.hisun.lemon.urm.service;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.urm.dto.ReSetCheckPayPwdDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @function IUserAuthenticationService
 * @description 验证用户密码服务
 * @date 7/25/2017 Tue
 * @time 10:07 AM
 */
public interface IUserAuthenticationService {
    /**
     * 用户登录
     */
    public Map<String, Object> userLogin(String loginId, String loginPwd, String handPwd);

    /**
     * 用户支付密码校验
     * @param userId
     * @param payPwd
     */
    public Boolean checkPayPwd(String userId, String payPwd);

    /**
     * 用户登录密码校验
     * @param loginId
     * @param loginPwd
     * @return
     */
    public Boolean checkLoginPwd(String loginId, String loginPwd);

    /**
     * 用户密保问题答案校验
     * @param loginId
     * @param safeQues
     * @param safeAns
     * @return
     */
    public Boolean checkSafeQues(String loginId, String safeQues, String safeAns);

    public Boolean checkSafeQues(String loginId, List<String> safeQuesNum, String safeAns);

    /**
     * 用户手势密码校验
     * @param loginId
     * @param handPwd
     * @return
     */
    public Boolean checkHandPwd(String loginId, String handPwd);


    /**
     * 重置密码校验支付密码
     * @param
     * @return
     */
    String resetPwdCheckPayPwd(GenericDTO<ReSetCheckPayPwdDTO> checkResetPwdDTO);


}
