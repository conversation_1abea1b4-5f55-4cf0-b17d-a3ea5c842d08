package com.hisun.lemon.urm.common;

import com.hisun.lemon.cmm.client.SmsServerClient;
import com.hisun.lemon.cmm.dto.SmsSendReqDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.utils.LemonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @function SmsSend
 * @description 短信下发组件
 * @date 9/15/2017 Fri
 * @time 8:28 PM
 */
@Component
public class SmsSend {
    private static final Logger logger = LoggerFactory.getLogger(SmsSend.class);

    @Resource
    private SmsServerClient smsServerClient;

    public void smsSend(String mblNo, String smsType, String locale, String templateId, Map<String, String> map) {
        GenericDTO<SmsSendReqDTO> smsSendReqDTO = new GenericDTO<>();
        SmsSendReqDTO reqDTO = new SmsSendReqDTO();
        reqDTO.setMblNo(mblNo);
        reqDTO.setSmsType(smsType);
        reqDTO.setSmsLanguage(locale);
        reqDTO.setSmsTemplateId(templateId);
        reqDTO.setReplaceFieldMap(map);
        smsSendReqDTO.setBody(reqDTO);
        GenericRspDTO<NoBody> genericRspDTO = smsServerClient.smsSend(smsSendReqDTO);
        if (JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())) {
            LemonException.throwLemonException(genericRspDTO.getMsgCd());
        }
    }

    public void smsSend(String mblNo, String smsType, String templateId, Map<String, String> map) {
        smsSend(mblNo, smsType, "en", templateId, map);
    }
}
