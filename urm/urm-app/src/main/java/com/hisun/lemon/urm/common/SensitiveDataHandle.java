package com.hisun.lemon.urm.common;

import com.hisun.lemon.cmm.client.CmmServerClient;
import com.hisun.lemon.cmm.dto.CommonEncryptReqDTO;
import com.hisun.lemon.cmm.dto.CommonEncryptRspDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.chrono.JapaneseChronology;


/**
 * <AUTHOR>
 * @function SensitiveDataHandle
 * @description 敏感数据处理服务
 * @date 9/15/2017 Fri
 * @time 6:20 PM
 */
@Component
public class SensitiveDataHandle {
    private static final Logger logger = LoggerFactory.getLogger(SensitiveDataHandle.class);

    @Autowired
    private CmmServerClient cmmServerClient;

    public String encryptSensitiveData(String data) {
        return cryptography(data, "encrypt");
    }

    public String decryptSensitiveData(String data) {
        return cryptography(data, "decrypt");
    }

    private String cryptography(String data, String type) {
        GenericDTO<CommonEncryptReqDTO> encryptReqDTO = new GenericDTO<>();
        CommonEncryptReqDTO reqDTO = new CommonEncryptReqDTO();
        reqDTO.setData(data);
        reqDTO.setType(type);
        encryptReqDTO.setBody(reqDTO);
        GenericRspDTO<CommonEncryptRspDTO> rspDTO = cmmServerClient.encrypt(encryptReqDTO);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            LemonException.throwLemonException(rspDTO.getMsgCd());
        }
        return rspDTO.getBody().getData();
    }
}
