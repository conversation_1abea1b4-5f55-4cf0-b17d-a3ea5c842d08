package com.hisun.lemon.urm.utils;

import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.urm.controller.UserAuthenticationController;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import java.io.File;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;

/**
 * Created by Administrator on 2017/12/11.
 */
public class RSAUtil2 {
    private static final Logger logger = LoggerFactory.getLogger(RSAUtil2.class);

    public static String decryptByKey(byte[] encryptData) throws Exception
    {
        String path = ClassLoader.getSystemResource("").getPath() + "resources" + File.separator;
        String privateKey = LemonUtils.getProperty("p12Cert.privateKey");
        logger.info("privateKey : " + privateKey);
        byte[] keyBytes = Base64.decodeBase64(privateKey.getBytes());
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateK = keyFactory.generatePrivate(pkcs8KeySpec);
        try {
            logger.info("encryptData" + encryptData);
            byte[] pinBlock = Base64.decodeBase64(encryptData);
            BouncyCastleProvider bcp = new BouncyCastleProvider();
            Cipher dcipher = Cipher.getInstance("RSA/ECB/PKCS1Padding", bcp);
            dcipher.init(2, privateK);
            int blockSize = dcipher.getBlockSize();
            int outputSize = dcipher.getOutputSize(pinBlock.length);
            int leavedSize = pinBlock.length % blockSize;
            int blocksSize = leavedSize != 0 ? pinBlock.length / blockSize + 1 :
                    pinBlock.length / blockSize;
            byte[] pinData = new byte[outputSize * blocksSize];
            int i = 0;
            while (pinBlock.length - i * blockSize > 0) {
                if (pinBlock.length - i * blockSize > blockSize)
                    dcipher.doFinal(pinBlock, i * blockSize, blockSize,
                            pinData, i * outputSize);
                else {
                    dcipher.doFinal(pinBlock, i * blockSize, pinBlock.length -
                            i * blockSize, pinData, i * outputSize);
                }
                i++;
            }
            pinBlock = (byte[])null;
            return (new String(pinData, "UTF-8"));
        } catch (Exception localException) {
            localException.printStackTrace();
            logger.info("RSAERRPR: " + localException.getMessage());
        }
        return null;
    }
}
