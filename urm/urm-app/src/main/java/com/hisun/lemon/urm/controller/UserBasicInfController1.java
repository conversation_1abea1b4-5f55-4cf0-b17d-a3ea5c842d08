package com.hisun.lemon.urm.controller;

import com.hisun.lemon.cmm.client.SmsServerClient;
import com.hisun.lemon.cmm.dto.SmsCheckReqDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.NumberUtils;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.*;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.framework.utils.RandomTemplete;
import com.hisun.lemon.jcommon.phonenumber.PhoneNumberUtils;
import com.hisun.lemon.urm.common.SensitiveDataHandle;
import com.hisun.lemon.urm.constants.URMConstants;
import com.hisun.lemon.urm.constants.URMMessageCode;
import com.hisun.lemon.urm.dao.IUrmSafeInfDao;
import com.hisun.lemon.urm.dao.IUrmUserBasicInfDao;
import com.hisun.lemon.urm.dto.*;
import com.hisun.lemon.urm.entity.*;
import com.hisun.lemon.urm.service.IUserBasicInfService;
import com.hisun.lemon.urm.service.IUserBasicInfService1;
import com.hisun.lemon.urm.utils.ObjectUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static com.hisun.lemon.urm.utils.CipherProcessorUtils.convertRSAEncryptedPin;

/**
 * <AUTHOR>
 * @funciton UserBasicInfController
 * @description 用户基本信息管理
 * @date 7/8/2017 FRI
 * @time 3:57 PM
 */
@Api(tags = "UserBasicInfController", description = "用户基本信息管理")
@RestController
@RequestMapping(value = "/urm1")
public class UserBasicInfController1 extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(UserBasicInfController1.class);

    @Resource
    private IUserBasicInfService basicInfService;

    @Resource
    private IUserBasicInfService1 basicInfService1;

    @Resource
    private IUrmUserBasicInfDao basicInfDao;

    @Resource
    private IUrmSafeInfDao safeInfDao;

    @Autowired
    private SmsServerClient smsServerClient;

    @Autowired
    private SensitiveDataHandle sensData;

    @Resource(name = "bindingTokenRandomTemplete")
    private RandomTemplete randomTemplate;

    @ApiOperation(value = "用户开户", notes = "用户开户")
    @ApiResponse(code = 200, message = "用户开户")
    @PostMapping(value = "/users")
    public GenericRspDTO<NoBody> openUser1(@Validated @RequestBody GenericDTO<UserRegisterDTO> userRegisterDTO) {
        String mblNo = userRegisterDTO.getBody().getMblNo();
        String chkNo = userRegisterDTO.getBody().getChkNo();
        String smsTyp = userRegisterDTO.getBody().getSmsTyp();
        String smsToken = userRegisterDTO.getBody().getSmsToken();
        String usrNation = userRegisterDTO.getBody().getUsrNation();
        UrmUserBasicInfDO basicInfDO = basicInfDao.getByLoginId(mblNo);
        if (JudgeUtils.isNotNull(basicInfDO) && JudgeUtils.equals(URMConstants.USR_OPEN, basicInfDO.getUsrSts())) {
            LemonException.throwLemonException(URMMessageCode.USR_ALREADY_REGISTERED);
        }
        if (JudgeUtils.isNotBlank(mblNo)) {
            //校验手机号码是否有效
//            if (!PhoneNumberUtils.isValidNumber(mblNo)) {
//                LemonException.throwBusinessException(URMMessageCode.ILLEGAL_MBL_NO);
//            }
            if (JudgeUtils.isBlank(usrNation)) {
                usrNation = PhoneNumberUtils.getGeocoder(mblNo);
            }
            if (JudgeUtils.isNotBlank(chkNo)) {
                GenericDTO<SmsCheckReqDTO> genericSmsReq = new GenericDTO<SmsCheckReqDTO>();
                SmsCheckReqDTO smsReqDTO = new SmsCheckReqDTO();
                smsReqDTO.setMblNo(mblNo);
                smsReqDTO.setSmsCode(chkNo);
                smsReqDTO.setType(smsTyp);
                smsReqDTO.setToken(smsToken);
                genericSmsReq.setBody(smsReqDTO);
                smsServerClient.smsCodeCheck(genericSmsReq);
            }
        }
        UrmUserBasicInfDO userBasicInfDO = new UrmUserBasicInfDO();
        UrmSafeInfDO safeInfDO = new UrmSafeInfDO();
        UrmCprExtInfDO cprExtInfDo = null;
        UrmSafeLoginDO safeLoginDO = null;
        BeanUtils.copyProperties(userBasicInfDO, userRegisterDTO.getBody());
        userBasicInfDO.setUsrNation(usrNation);

        if (JudgeUtils.isNotBlank(userBasicInfDO.getIdNo())) {
            basicInfService.checkIdNo(userBasicInfDO.getIdNo());
        }

        BeanUtils.copyProperties(safeInfDO, userRegisterDTO.getBody());
        userBasicInfDO = ObjectUtils.objectNull(userBasicInfDO);
        safeInfDO = ObjectUtils.objectNull(safeInfDO);
        if (JudgeUtils.isNotNull(safeInfDO)) {
            if (JudgeUtils.notEquals(LemonUtils.getSource(), URMConstants.INTERNAL_GATE)) {
                if (JudgeUtils.isNotBlank(safeInfDO.getLoginPwd()) || JudgeUtils.isNotBlank(safeInfDO.getPayPwd())) {
                    String randomServer = randomTemplate.acquireOnce(URMConstants.PWD_RANDOM);
                    if (JudgeUtils.isNotBlank(safeInfDO.getLoginPwd())) {
                        String loginPwdRandom = userRegisterDTO.getBody().getLoginPwdRandom();
                        String loginPwd = convertRSAEncryptedPin(loginPwdRandom, randomServer,
                                safeInfDO.getLoginPwd(), URMConstants.LOG_PWD_MODE);
                        safeInfDO.setLoginPwd(loginPwd);
                    }
                    if (JudgeUtils.isNotBlank(safeInfDO.getPayPwd())) {
                        String payPwdRandom = userRegisterDTO.getBody().getPayPwdRandom();
                        String payPwd = convertRSAEncryptedPin(payPwdRandom, randomServer, safeInfDO.getPayPwd(),
                                URMConstants.PAY_PWD_MODE);
                        safeInfDO.setPayPwd(payPwd);
                    }
                }
            } else {
                String payPwd = userRegisterDTO.getBody().getPayPwd();
                if (!NumberUtils.isDigits(payPwd) || payPwd.length() != 6) {
                    LemonException.throwLemonException(URMMessageCode.PIN_MODE_WRONG);
                }
            }
        }
        userBasicInfDO.setUsrLvl(URMConstants.ORDINARY_USER);
        this.basicInfService.openUser(userBasicInfDO, safeInfDO, cprExtInfDo, safeLoginDO, "");
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "商户开户", notes = "商户开户")
    @ApiResponse(code = 200, message = "商户开户")
    @PostMapping(value = "/merusers")
    public GenericRspDTO<String> openMerUser1(@Validated @RequestBody GenericDTO<MerRegisterDTO> merRegisterDTO) {
        String mblNo = merRegisterDTO.getBody().getMblNo();
        String loginId = merRegisterDTO.getBody().getLoginId();
        UrmSafeInfDO safeInfDO = safeInfDao.get(loginId);
        if (JudgeUtils.isNotNull(safeInfDO) && JudgeUtils.equals(URMConstants.SAFE_STS_EFF, safeInfDO.getSafeSts())) {
            LemonException.throwLemonException(URMMessageCode.LOGID_ALREADY_USED);
        }
        if (JudgeUtils.isNotBlank(mblNo)) {
            //校验手机号码是否有效
//            if (!PhoneNumberUtils.isValidNumber(mblNo)) {
//                LemonException.throwBusinessException(URMMessageCode.ILLEGAL_MBL_NO);
//            }
        }
        UrmUserBasicInfDO userBasicInfDO = new UrmUserBasicInfDO();
        String usrLvl = URMConstants.ENTERPRISE_BUSINESS;
        userBasicInfDO.setUsrLvl(usrLvl);
        UrmCprExtInfDO cprExtInfDo = new UrmCprExtInfDO();
        UrmSafeInfDO safeInfDO1 = new UrmSafeInfDO();
        UrmSafeLoginDO safeLoginDO = new UrmSafeLoginDO();
        BeanUtils.copyProperties(cprExtInfDo, merRegisterDTO.getBody());
        BeanUtils.copyProperties(safeInfDO1, merRegisterDTO.getBody());
        BeanUtils.copyProperties(safeLoginDO, merRegisterDTO.getBody());
        userBasicInfDO = ObjectUtils.objectNull(userBasicInfDO);
        cprExtInfDo = ObjectUtils.objectNull(cprExtInfDo);
        safeInfDO1 = ObjectUtils.objectNull(safeInfDO1);
        safeLoginDO = ObjectUtils.objectNull(safeLoginDO);
        if (JudgeUtils.isNull(cprExtInfDo)) {
            LemonException.throwLemonException(URMMessageCode.MER_INF_IS_NULL);
        }
        String userId = this.basicInfService1.openUser1(userBasicInfDO, safeInfDO1, cprExtInfDo, safeLoginDO);
        return GenericRspDTO.newSuccessInstance(userId);
    }

}