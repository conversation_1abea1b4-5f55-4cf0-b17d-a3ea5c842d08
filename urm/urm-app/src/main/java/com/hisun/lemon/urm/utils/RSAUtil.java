package com.hisun.lemon.urm.utils;

import com.hisun.lemon.common.exception.LemonException;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.encoders.Base64;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;

/**
 * Created by 吕海龙 on 2017/9/2 08:16
 */
public class RSAUtil {

    private static Cipher cipher;

    static {
        try {
            cipher = Cipher.getInstance("RSA");
        } catch (NoSuchAlgorithmException e) {
            LemonException.throwLemonException(e);
        } catch (NoSuchPaddingException e) {
            LemonException.throwLemonException(e);
        }
    }

    /**
     * 使用公钥对明文进行加密，返回BASE64编码的字符串
     *
     * @param publicKey
     * @param plainText
     * @return
     */
    public static String encrypt(PublicKey publicKey, byte[] plainText) {
        try {
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            byte[] enBytes = cipher.doFinal(plainText);
            return new String(Base64.encode(enBytes));
        } catch (InvalidKeyException e) {
            LemonException.throwLemonException(e);
        } catch (IllegalBlockSizeException e) {
            LemonException.throwLemonException(e);
        } catch (BadPaddingException e) {
            LemonException.throwLemonException(e);
        }
        return null;
    }



}
