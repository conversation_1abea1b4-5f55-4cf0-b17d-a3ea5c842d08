package com.hisun.lemon.urm.service.impl;

import com.hisun.lemon.cmm.client.CmmServerClient;
import com.hisun.lemon.cmm.client.ConstantParamClient;
import com.hisun.lemon.cmm.dto.AppInfoReqDTO;
import com.hisun.lemon.cmm.dto.AppInfoRspDTO;
import com.hisun.lemon.cmm.dto.ConstantParamRspDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.*;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.urm.common.*;
import com.hisun.lemon.urm.constants.URMConstants;
import com.hisun.lemon.urm.constants.URMMessageCode;
import com.hisun.lemon.urm.dao.IUrmCprItfAuthDao;
import com.hisun.lemon.urm.dao.IUrmSafeDtlDao;
import com.hisun.lemon.urm.dao.IUrmSafeInfDao;
import com.hisun.lemon.urm.dao.IUrmSafeLoginDao;
import com.hisun.lemon.urm.dto.ForgetPwdDTO;
import com.hisun.lemon.urm.entity.UrmCprItfAuthDO;
import com.hisun.lemon.urm.entity.UrmSafeDtlDO;
import com.hisun.lemon.urm.entity.UrmSafeInfDO;
import com.hisun.lemon.urm.entity.UrmSafeLoginDO;
import com.hisun.lemon.urm.service.IUserPasswordService;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.hisun.lemon.urm.utils.CipherProcessorUtils.*;

@Transactional
@Service
public class UserPasswordServiceImpl extends BaseService implements IUserPasswordService {
    private static final Logger logger = LoggerFactory.getLogger(UserPasswordServiceImpl.class);
    @Resource
    private IUrmSafeLoginDao safeLoginDao;

    @Resource
    private IUrmSafeInfDao safeInfDao;

    @Resource
    private IUrmSafeDtlDao safeDtlDao;

    @Resource
    private IUrmCprItfAuthDao cprItfAuthDao;

    @Autowired
    private CmmServerClient cmmServerClient;

    @Resource
    private AcmService acm;

    @Resource
    private RiskCheck riskCheck;

    @Resource
    private SensitiveDataHandle sensData;

    @Resource
    private SmsSend sms;

    @Resource
    private CrmService crm;

    @Resource
    private ConstantParamClient paramClient;

    @Autowired
    @Qualifier("redisTemplateString")
    RedisTemplate<String, String> redisTemplate;
    /**
     * 修改登录密码
     *
     * @param loginId
     * @param oldLoginPwd
     * @param newLoginPwd
     * @return
     */
    @Override
    public Boolean updLoginPwd(String loginId, String oldLoginPwd, String newLoginPwd) {
        //查询用户安全ID
        UrmSafeInfDO safeInfDO = safeInfDao.get(loginId);
        String userId = safeInfDO.getUserId();
        if (JudgeUtils.isNull(safeInfDO)) {
            LemonException.throwLemonException(URMMessageCode.USR_NOT_EXIST);
        }
        //检查用户状态
        riskCheck.checkUser(safeInfDO.getUserId());

        String loginPwd = safeInfDO.getLoginPwd();
        if (oldLoginPwd.length() != 6 ) {
            if (!verifyLoginPwd(oldLoginPwd, userId, loginPwd)) {
                LemonException.throwLemonException(URMMessageCode.LOG_PWD_CHECK_FAIL);
            }
        } else {
            crm.verifyCrmLoginPwd(loginId, oldLoginPwd);
        }
        String pwd ;
        if (newLoginPwd.length() != URMConstants.FIFTEEN_PWD_LEN) {
            pwd = convertPwdfromZpkToPvk(userId, newLoginPwd);
        } else {
            pwd = newLoginPwd;
        }
        String safeId = safeInfDO.getSafeId();
        changePwd(URMConstants.LOGIN_PWD_UPD, safeId, pwd);
        safeInfDao.updateHandInfo(userId);
        return true;
    }

    /**
     * 重置登录密码
     *
     * @param loginId
     * @param newLoginPwd
     * @return
     */
    @Override
    public Boolean resetLoginPwd(String loginId, String newLoginPwd) {
        //查询用户安全ID
        UrmSafeLoginDO safeLoginDO = safeLoginDao.get(loginId);
        if (JudgeUtils.isNull(safeLoginDO)){
            LemonException.throwLemonException(URMMessageCode.USR_NOT_EXIST);
        }
        String safeId = safeLoginDO.getSafeId();
        UrmSafeInfDO safeInfDO = safeInfDao.get(loginId);
        String userId = safeInfDO.getUserId();
        String pwd ;
        if (newLoginPwd.length() != URMConstants.FIFTEEN_PWD_LEN) {
            pwd = convertPwdfromZpkToPvk(userId, newLoginPwd);
        } else {
            pwd = newLoginPwd;
        }
        changePwd(URMConstants.LOGIN_PWD_RESET, safeId, pwd);
        safeInfDao.updateHandInfo(userId);
        return true;
    }

    /**
     * 修改支付密码
     *
     * @param userId
     * @param oldPayPwd
     * @param newPayPwd
     * @return
     */
    @Override
    public Boolean updPayPwd(String userId, String oldPayPwd, String newPayPwd) {
        //查询用户的安全ID
//        UrmSafeInfDO safeInf = safeInfDao.get(loginId);
//        if (JudgeUtils.isNull(safeInf)) {
//            LemonException.throwLemonException(URMMessageCode.USR_NOT_EXIST);
//        }
//        String userId = safeInf.getUserId();
        riskCheck.checkUser(userId);
        String acNo = acm.queryAcNo(userId);
        String pwd = convertPinToPinOffset(acNo, newPayPwd);
        List<UrmSafeInfDO> safeInfDOS = safeInfDao.findByUserId(userId);
        for (UrmSafeInfDO safeInfDO : safeInfDOS) {
            String safeId = safeInfDO.getSafeId();
            String payPwd = safeInfDO.getPayPwd();
            if (verifyPayPwd(oldPayPwd, acNo, payPwd)) {
                changePwd(URMConstants.PAY_PWD_UPD, safeId, pwd);
            } else {
                LemonException.throwLemonException(URMMessageCode.PAY_PWD_CHECK_FAIL);
            }
        }
        return true;
    }

    /**
     * 重置支付密码
     *
     * @param userId
     * @param newPayPwd
     * @return
     */
    @Override
    public Boolean resetPayPwd(String userId, String newPayPwd) {
        riskCheck.checkUser(userId);
        String acNo = acm.queryAcNo(userId);
        String pwd = convertPinToPinOffset(acNo, newPayPwd);
        List<UrmSafeInfDO> safeInfDOS = safeInfDao.findByUserId(userId);
        for (UrmSafeInfDO safeInfDO : safeInfDOS) {
            String safeId = safeInfDO.getSafeId();
            changePwd(URMConstants.PAY_PWD_RESET, safeId, pwd);
        }
        return true;
    }

    /**
     * 用户密码更新历史登记
     * @param dtlTyp
     * @param safeId
     * @param pwd
     */
    public void changePwd(String dtlTyp, String safeId, String pwd) {
        logger.info("safeId:" + safeId + ", pwd:" + pwd);
        UrmSafeInfDO safeInfDO = safeInfDao.getBySafeId(safeId);
        //登记密码变更历史
        if (JudgeUtils.isNull(safeInfDO)) {
            LemonException.throwBusinessException(URMMessageCode.PWD_IS_NULL);
        }

        //检查用户状态
        riskCheck.checkUser(safeInfDO.getUserId());
        UrmSafeDtlDO safeDtlDO = new UrmSafeDtlDO();
        BeanUtils.copyProperties(safeDtlDO, safeInfDO);
        safeDtlDO.setDtlTyp(dtlTyp);
        safeDtlDO.setMsgId(LemonUtils.getMsgId());
        safeDtlDao.insert(safeDtlDO);

        //更新新密码
        if (JudgeUtils.equals(dtlTyp, URMConstants.HAND_PWD_RESET) || JudgeUtils.equals(dtlTyp, URMConstants
                .HAND_PWD_UPD)) {
            safeInfDO.setHandFailCnt(NumberUtils.toByte("0"));
            safeInfDO.setHandPwd(pwd);
            safeInfDO.setHandFlg(URMConstants.HAND_FLG_O);

        }else if (JudgeUtils.equals(dtlTyp, URMConstants.LOGIN_PWD_RESET) || JudgeUtils.equals(dtlTyp, URMConstants
                .LOGIN_PWD_UPD)) {
            safeInfDO.setLoginLckDt(DateTimeUtils.getCurrentLocalDate().minusDays(1));
            safeInfDO.setLoginLckTm(LocalDateTime.from(DateTimeUtils.parseLocalTime("235959")));
            safeInfDO.setLoginFailCnt(NumberUtils.toByte("0"));
            safeInfDO.setLoginPwd(pwd);
        } else {
            safeInfDO.setPayLckDt(DateTimeUtils.getCurrentLocalDate().minusDays(1));
            safeInfDO.setPayLckTm(LocalDateTime.from(DateTimeUtils.parseLocalTime("235959")));
            safeInfDO.setPayFailCnt(NumberUtils.toByte("0"));
            safeInfDO.setPayPwd(pwd);
        }
        safeInfDao.update(safeInfDO);
    }

    /**
     * 修改密保信息
     *
     * @param loginId
     * @param safeMblNo
     * @param safeQues1
     * @param safeAns1
     * @return
     */
    @Override
    public Boolean updPwdSafeInf(String loginId, String safeMblNo, String safeQues1, String safeAns1) {
        //检查用户状态
        if (JudgeUtils.isNotBlank(LemonUtils.getUserId())) {
            riskCheck.checkUser(LemonUtils.getUserId());
        }

        UrmSafeInfDO safeInfDO = new UrmSafeInfDO();
        UrmSafeLoginDO safeLoginDO = safeLoginDao.get(loginId);
        String safeId = safeLoginDO.getSafeId();
        String safeMblNoEnc = safeMblNo;
        String safeQues1Enc = safeQues1;
        String safeAns1Enc = safeAns1;
        try {
            if (JudgeUtils.isNotBlank(safeMblNo)) {
                safeMblNoEnc = sensData.encryptSensitiveData(safeMblNo);
            }
            if (JudgeUtils.isNotBlank(safeQues1)) {
                safeQues1Enc = sensData.encryptSensitiveData(safeQues1);
            }
            if (JudgeUtils.isNotBlank(safeAns1)) {
                safeAns1Enc = sensData.encryptSensitiveData(safeAns1);
            }
        } catch (Exception e) {
            LemonException.throwLemonException(e);
        }
        safeInfDO.setSafeId(safeId);
        safeInfDO.setSafeMblNo(safeMblNoEnc);
        safeInfDO.setSafeQues1(safeQues1Enc);
        safeInfDO.setSafeAns1(safeAns1Enc);
        safeInfDao.update(safeInfDO);
        return true;
    }

    /**
     * 重置商户密钥
     *
     * @param userId
     * @return
     */
    @Override
    public String resetCprTradingKey(String userId) {
        //检查用户状态
        riskCheck.checkUser(userId);

        String secretKey = RandomUtils.randomLetterFixLength(32);
        UrmCprItfAuthDO cprItfAuthDO = new UrmCprItfAuthDO();
        cprItfAuthDO.setUserId(userId);
        cprItfAuthDO.setSecretKey(secretKey);
        cprItfAuthDao.update(cprItfAuthDO);
        return secretKey;
    }

    /**
     * 重置商户支付密码
     *
     * @param userId
     * @return
     */
    @Override
    public void resetRandomPayPwd(String userId) {
        //查询用户的安全ID
        List<UrmSafeInfDO> safeInfDOS = safeInfDao.findByUserId(userId);

        //检查用户状态
        riskCheck.checkUser(userId);

        if (JudgeUtils.isEmpty(safeInfDOS)) {
            LemonException.throwLemonException(URMMessageCode.USR_NOT_EXIST);
        }
        String acNo = acm.queryAcNo(userId);
        Map<String, String> map = initPayPwd(acNo);
        String newPayPwd = map.get("payPwdPinOffset");
        String pwdPlaintext = map.get("payPwdPlaintext");
        logger.debug("payPwdPlaintext:" + pwdPlaintext);
        for (UrmSafeInfDO safeInfDO : safeInfDOS) {
            String safeId = safeInfDO.getSafeId();
            changePwd(URMConstants.PAY_PWD_RESET, safeId, newPayPwd);
            if (JudgeUtils.equals(safeInfDO.getOprTyp(), URMConstants.MER_ADMIN_TYP)
                    || JudgeUtils.equals(safeInfDO.getOprTyp(), URMConstants.USR_TYP)) {
                String mblNo = safeInfDO.getMblNo();
                String smsType = URMConstants.SMS_TYP;
                String templateId = URMConstants.PAY_PWD_TEMP_ID;
                Map<String, String> smsMap = new HashMap<>();
                smsMap.put("payPwd", pwdPlaintext);
                if (JudgeUtils.equals(safeInfDO.getOprTyp(), URMConstants.MER_ADMIN_TYP)) {
                    smsMap.put("userId", userId);
                }
                if (JudgeUtils.equals(safeInfDO.getOprTyp(), URMConstants.USR_TYP)) {
                    smsMap.put("userId", safeInfDO.getMblNo());
                }
                if (JudgeUtils.isNotBlank(mblNo)) {
                    sms.smsSend(mblNo, smsType, templateId, smsMap);
                }
            }
        }
    }

    /**
     * 登录密码后台重置
     *
     * @param loginId
     * @return
     */
    @Override
    public void resetRandomLoginPwd(String loginId) {
        UrmSafeInfDO safeInfDO = safeInfDao.get(loginId);
        String userId = safeInfDO.getUserId();

        //检查用户状态
        riskCheck.checkUser(userId);

        Map<String, String> pwdMap = initLoginPwd(userId);
        String loginPwdPlaintext = pwdMap.get("loginPwdPlaintext");
        String newLoginPwd = pwdMap.get("loginPwd");

        String mblNo = safeInfDO.getMblNo();
        String safeId = safeInfDO.getSafeId();
        changePwd(URMConstants.LOGIN_PWD_RESET, safeId, newLoginPwd);
        String smsType = URMConstants.SMS_TYP;
        String templateId = URMConstants.LOG_PWD_TEMP_ID;
        Map<String, String> smsMap = new HashMap<>();
        smsMap.put("loginPwd", loginPwdPlaintext);
        smsMap.put("loginId", loginId);
        if (JudgeUtils.isNotBlank(mblNo)) {
            sms.smsSend(mblNo, smsType, templateId, smsMap);
        }
    }

    /**
     * 重置手势密码
     *
     * @param loginId
     * @param handPwdDb
     * @param handPwd
     * @return
     */
    @Override
    public Boolean resetHandPwd(String loginId, String handPwdDb, String handPwd) {
        //查询用户安全ID
        UrmSafeLoginDO safeLoginDO = safeLoginDao.get(loginId);
        String safeId = safeLoginDO.getSafeId();
        UrmSafeInfDO safeInfDO = safeInfDao.get(loginId);
        if(safeInfDO.getHandPwd() != null && !"".equals(safeInfDO.getHandPwd())){
            String tmpPwd = sensData.decryptSensitiveData(safeInfDO.getHandPwd());
            if(handPwd.equals(tmpPwd)){
                LemonException.throwLemonException(URMMessageCode.HAND_PWD_NOT_SAME);
            }
        }
        changePwd(URMConstants.HAND_PWD_RESET, safeId, handPwdDb);
        return true;
    }
    /**
     * 重置手势密码
     *
     * @param flg
     * @param flgOld
     * @return
     */
    @Override
    public Boolean setHandPwdFlg(String userId, String flg, String flgOld){
        //String userId = LemonUtils.getUserId();
        int num = safeInfDao.updateFlg(userId, flg, flgOld);
        if(num != 1){
            return false;
        }
        return true;
    }

    @Override
    public Boolean checkHandPwdValid(UrmSafeInfDO safeInfDO){
        String userId = safeInfDO.getUserId();
        GenericDTO<AppInfoReqDTO> reqDTO = new GenericDTO();
        AppInfoReqDTO appInfoReqDTO = new AppInfoReqDTO();
        appInfoReqDTO.setUserId(userId);
        reqDTO.setBody(appInfoReqDTO);
        GenericRspDTO<AppInfoRspDTO> genericRspDTO = cmmServerClient.logInfo(reqDTO);
        if(JudgeUtils.isNotSuccess(genericRspDTO.getMsgCd())){
            logger.info("query_user_loginInfo error:" + genericRspDTO.getMsgCd());
            return false;
        }
        AppInfoRspDTO  appInfoRspDTO = genericRspDTO.getBody();
        if(appInfoRspDTO == null){
            logger.info("query_user_loginInfo error:" + null);
            return false;
        }
        //app 用户登录日期超过30 天
        LocalDate tm = appInfoRspDTO.getTmSmp().toLocalDate();
        LocalDate localDate = DateTimeUtils.getCurrentLocalDate();
        int maxHandPwdCnt = 0;
        int handFailCnt = 0;
        if(safeInfDO.getHandFailCnt()!=null){
            handFailCnt= safeInfDO.getHandFailCnt().intValue();
        }
        try {
            GenericRspDTO<ConstantParamRspDTO> paramRspDTO = paramClient.params("HAND_PWD_LOCK_CNT");
            maxHandPwdCnt = Integer.valueOf(paramRspDTO.getBody().getParmVal());
        } catch (Exception e) {
            //do nothing
        }
        if (handFailCnt >= maxHandPwdCnt) {
            int rs = safeInfDao.updateHandInfo(userId);
            return true;
        }
        if(tm.plusDays(30).isBefore(localDate)  && (safeInfDO.getHandFlg() != null) && (!safeInfDO.getHandFlg().equals(URMConstants.HAND_FLG_N))){
            int rs = safeInfDao.updateHandInfo(userId);
            return true;
        }
        return false;
    }

    @Override
    public void forgetLoginPwd(GenericDTO<ForgetPwdDTO> forgetPwdDTO) {

        //校验邮箱随机数
        String redisRandomKey = "email:random:2:" + forgetPwdDTO.getBody().getLoginId();
        String redisEmailNo = redisTemplate.opsForValue().get(redisRandomKey);
        if (!forgetPwdDTO.getBody().getEmailNo().equals(redisEmailNo)) {
            LemonException.throwBusinessException(URMMessageCode.EMAIL_AUTH_FAIL);
        }
        //移除验证码
        redisTemplate.delete(redisRandomKey);

        //校验支付密码

        String redisKey = "urm:pwdReset:" + forgetPwdDTO.getBody().getLoginId();
        String redisPwdNo = redisTemplate.opsForValue().get(redisKey);
        if (!forgetPwdDTO.getBody().getPayPwdNo().equals(redisPwdNo)) {
            LemonException.throwBusinessException(URMMessageCode.PAY_PWD_CHECK_FAIL);
        }

        //重置密码
        UrmSafeInfDO safeInfDO = safeInfDao.get(forgetPwdDTO.getBody().getLoginId());
        if (safeInfDO == null) {
            LemonException.throwBusinessException(URMMessageCode.USR_NOT_EXIST);

        }
        String newPwd = forgetPwdDTO.getBody().getNewPwd();
        if (JudgeUtils.isNotBlank(newPwd)) {
            newPwd = DigestUtils.md5Hex(newPwd + safeInfDO.getUserId());
        }

        safeInfDO.setLoginPwd(newPwd);
        safeInfDao.update(safeInfDO);
        //移除验证码
        redisTemplate.delete(redisKey);

    }
}
