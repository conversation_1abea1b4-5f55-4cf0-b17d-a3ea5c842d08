/*
 * @ClassName UrmSafeLoginDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-24 14:36:45
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;

public class UrmSafeLoginDO extends BaseDO {
    /**
     *  loginId 登录ID,可以为系统生成登录号，手机号码等
     */
    private String loginId;
    /**
     *  safeId 安全表主键
     */
    private String safeId;
    /**
     *  loginTyp 登录ID的类型：0，系统生成登录号，1 手机号码 2 邮箱 3 昵称
     */
    private String loginTyp;

    /**
     *  displayNm 显示姓名
     * @return
     */
    private String displayNm;

    /**
     *  avatarPath 头像路径
     * @return
     */
    private String avatarPath;

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getSafeId() {
        return safeId;
    }

    public void setSafeId(String safeId) {
        this.safeId = safeId;
    }

    public String getLoginTyp() {
        return loginTyp;
    }

    public void setLoginTyp(String loginTyp) {
        this.loginTyp = loginTyp;
    }

    public String getDisplayNm() {
        return displayNm;
    }

    public void setDisplayNm(String displayNm) {
        this.displayNm = displayNm;
    }

    public String getAvatarPath() {
        return avatarPath;
    }

    public void setAvatarPath(String avatarPath) {
        this.avatarPath = avatarPath;
    }
}