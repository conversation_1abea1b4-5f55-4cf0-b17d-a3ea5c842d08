package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;

/**
 * <AUTHOR>
 * @create 2017/11/3
 */
public class QuesDo extends BaseDO {

    private int quesId;

    private int quesNo;

    private String safeQues;

    public int getQuesId() {
        return quesId;
    }

    public void setQuesId(int quesId) {
        this.quesId = quesId;
    }

    public int getQuesNo() {
        return quesNo;
    }

    public void setQuesNo(int quesNo) {
        this.quesNo = quesNo;
    }

    public String getSafeQues() {
        return safeQues;
    }

    public void setSafeQues(String safeQues) {
        this.safeQues = safeQues;
    }

    @Override
    public String toString() {
        return "QuesDo{" +
                "quesId=" + quesId +
                ", quesNo=" + quesNo +
                ", safeQues='" + safeQues + '\'' +
                '}';
    }
}
