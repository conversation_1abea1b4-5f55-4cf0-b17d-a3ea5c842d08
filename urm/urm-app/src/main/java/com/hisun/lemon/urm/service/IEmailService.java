package com.hisun.lemon.urm.service;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.urm.dto.EmailAuthDto;
import com.hisun.lemon.urm.dto.EmailCodeSendDto;

/**
 * 邮箱服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/9 09:36
 */
public interface IEmailService {
    GenericRspDTO<String> emailCodeAuth(GenericDTO<EmailAuthDto> req);

    GenericRspDTO<NoBody> emailCodeSend(GenericDTO<EmailCodeSendDto> req);

    GenericRspDTO<NoBody> checkEmailIsExit(EmailCodeSendDto dto);
}
