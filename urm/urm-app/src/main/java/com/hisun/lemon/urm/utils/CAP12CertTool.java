/*    */ package com.hisun.lemon.urm.utils;
/*    */ 
/*    */ import java.io.File;
/*    */ import java.io.FileInputStream;
/*    */ import java.io.FileNotFoundException;
/*    */ import java.io.IOException;
/*    */ import java.io.InputStream;
/*    */ import java.security.InvalidKeyException;
/*    */ import java.security.KeyStore;
/*    */ import java.security.NoSuchAlgorithmException;
/*    */ import java.security.PrivateKey;
/*    */ import java.security.PublicKey;
/*    */ import java.security.Signature;
/*    */ import java.security.SignatureException;
/*    */ import java.security.cert.Certificate;
/*    */ import java.security.cert.X509Certificate;
/*    */ import java.util.Enumeration;
/*    */ 
/*    */ public class CAP12CertTool
/*    */ {
/*    */   private static SignedPack signedPack;
/*    */ 
/*    */   public CAP12CertTool(InputStream fileInputStream, String keyPass)
/*    */     throws SecurityException
/*    */   {
/* 26 */     signedPack = getP12(fileInputStream, keyPass);
/*    */   }
/*    */ 
/*    */   public CAP12CertTool(String path, String keyPass) throws SecurityException, FileNotFoundException
/*    */   {
/* 31 */     FileInputStream fileInputStream = new FileInputStream(new File(path));
/*    */ 
/* 33 */     signedPack = getP12(fileInputStream, keyPass);
/*    */   }
/*    */ 
/*    */   private SignedPack getP12(InputStream fileInputStream, String keyPass) throws SecurityException
/*    */   {
/* 38 */     SignedPack sp = new SignedPack();
/*    */     try {
/* 40 */       KeyStore ks = KeyStore.getInstance("PKCS12");
/* 41 */       char[] nPassword = (char[])null;
/* 42 */       if ((keyPass == null) || (keyPass.trim().equals("")))
/* 43 */         nPassword = (char[])null;
/*    */       else {
/* 45 */         nPassword = keyPass.toCharArray();
/*    */       }
/* 47 */       ks.load(fileInputStream, nPassword);
/* 48 */       Enumeration enum2 = ks.aliases();
/* 49 */       String keyAlias = null;
/* 50 */       if (enum2.hasMoreElements()) {
/* 51 */         keyAlias = (String)enum2.nextElement();
/*    */       }
/* 53 */       PrivateKey priKey = (PrivateKey)ks.getKey(keyAlias, nPassword);
/* 54 */       Certificate cert = ks.getCertificate(keyAlias);
/* 55 */       PublicKey pubKey = cert.getPublicKey();
/* 56 */       sp.setCert((X509Certificate)cert);
/* 57 */       sp.setPubKey(pubKey);
/* 58 */       sp.setPriKey(priKey);
/*    */     } catch (Exception e) {
/* 60 */       e.printStackTrace();
/* 61 */       throw new SecurityException(e.getMessage());
/*    */     } finally {
/* 63 */       if (fileInputStream != null)
/*    */         try {
/* 65 */           fileInputStream.close();
/*    */         } catch (IOException localIOException) {
/*    */         }
/*    */     }
/* 69 */     return sp;
/*    */   }
/*    */ 
/*    */   public X509Certificate getCert() {
/* 73 */     return signedPack.getCert();
/*    */   }
/*    */ 
/*    */   public PublicKey getPublicKey() {
/* 77 */     return signedPack.getPubKey();
/*    */   }
/*    */ 
/*    */   public PrivateKey getPrivateKey() {
/* 81 */     return signedPack.getPriKey();
/*    */   }
/*    */ 
/*    */   public byte[] getSignData(byte[] indata) throws SecurityException {
/* 85 */     byte[] res = (byte[])null;
/*    */     try {
/* 87 */       Signature signet = Signature.getInstance("SHA1WITHRSA");
/* 88 */       signet.initSign(getPrivateKey());
/* 89 */       signet.update(indata);
/* 90 */       res = signet.sign();
/*    */     } catch (InvalidKeyException e) {
/* 92 */       throw new SecurityException(e.getMessage());
/*    */     } catch (NoSuchAlgorithmException e) {
/* 94 */       throw new SecurityException(e.getMessage());
/*    */     } catch (SignatureException e) {
/* 96 */       throw new SecurityException(e.getMessage());
/*    */     }
/* 98 */     return res;
/*    */   }
/*    */ }

/* Location:           C:\Users\<USER>\Desktop\cap_dxt_sdk\
 * Qualified Name:     com.hyt.cap.sdk.CAP12CertTool
 * JD-Core Version:    0.6.0
 */