package com.hisun.lemon.urm.controller;

import com.hisun.lemon.cmm.client.SmsServerClient;
import com.hisun.lemon.cmm.dto.SmsCodeReqDTO;
import com.hisun.lemon.cmm.dto.SmsCodeRspDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.urm.constants.URMMessageCode;
import com.hisun.lemon.urm.dao.IUrmUserBasicInfDao;
import com.hisun.lemon.urm.dto.SmsCodeHandlerDTO;
import com.hisun.lemon.urm.entity.UrmUserBasicInfDO;
import com.hisun.lemon.urm.service.IUserBasicInfService;
import com.hisun.lemon.urm.utils.ObjectUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @function UserSmsCodeController
 * @description 用户模块短信验证码
 * @date 8/10/2017 Thu
 * @time 5:42 PM
 */
@Api(tags = "UserSmsCodeController", description = "用户模块短信验证码")
@RestController
@RequestMapping("/urm/sms")
public class UserSmsCodeController extends BaseController {
    @Resource
    private SmsServerClient smsClient;

    @Resource
    private IUserBasicInfService  basicInfService;

    @ApiModelProperty(value = "短信验证码下发", notes = "sendShortMessage")
    @ApiResponse(code = 200, message = "短信验证码下发")
    @PostMapping("/sendshortmessage")
    public GenericRspDTO<SmsCodeHandlerDTO> sendShortMessage(@Validated @RequestBody GenericDTO<SmsCodeHandlerDTO>
                                                               smsCodeHandlerDTO) {
        GenericDTO<SmsCodeReqDTO> smsCodeReqDTO = new GenericDTO<SmsCodeReqDTO>();
        String mblNo = smsCodeReqDTO.getBody().getMblNo();
        if (JudgeUtils.isNotBlank(mblNo)) {
            Map<String, Object> userInfMap = basicInfService.queryUserByLoginId(mblNo);
            UrmUserBasicInfDO userBasicInfDO = (UrmUserBasicInfDO) userInfMap.get("basicInf");
            userBasicInfDO = ObjectUtils.objectNull(userBasicInfDO);
            if (JudgeUtils.isNotNull(userBasicInfDO)) {
                LemonException.throwLemonException(URMMessageCode.USR_ALREADY_REGISTERED);
            }
        }
        BeanUtils.copyProperties(smsCodeReqDTO.getBody(), smsCodeHandlerDTO);
        GenericRspDTO<SmsCodeRspDTO> smsCodeRspDTO = smsClient.smsCodeSend(smsCodeReqDTO);
        GenericRspDTO<SmsCodeHandlerDTO> smsSendRsp = new GenericRspDTO<SmsCodeHandlerDTO>();
        BeanUtils.copyProperties(smsSendRsp.getBody(), smsCodeRspDTO.getBody());
        return smsSendRsp;
    }
}
