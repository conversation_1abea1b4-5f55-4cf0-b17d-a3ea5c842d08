package com.hisun.lemon.urm.common;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.inv.client.QueryInformationClient;
import com.hisun.lemon.inv.dto.InvUserInfoDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @function InvService
 * @description 理财服务接口
 * @date 9/19/2017 Tue
 * @time 4:43 PM
 */
@Component
public class InvService {
    private static final Logger logger = LoggerFactory.getLogger(InvService.class);

    @Resource
    private QueryInformationClient invQuery;

    public BigDecimal queryInvBal(String userId) {
        GenericRspDTO<InvUserInfoDTO> invUserInfoDTO = new GenericRspDTO<>();
        invUserInfoDTO = invQuery.usrBalQuery(userId);
        if (JudgeUtils.isNotSuccess(invUserInfoDTO.getMsgCd())) {
            LemonException.throwLemonException(invUserInfoDTO.getMsgCd());
        }
        if (JudgeUtils.isNull(invUserInfoDTO) || JudgeUtils.isNull(invUserInfoDTO.getBody()) || JudgeUtils.isNull
                (invUserInfoDTO.getBody().getTotalCurrentAmt())) {
           return BigDecimal.valueOf(0, 2);
        }
        return invUserInfoDTO.getBody().getTotalCurrentAmt();
    }
}
