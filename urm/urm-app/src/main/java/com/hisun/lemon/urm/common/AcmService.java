package com.hisun.lemon.urm.common;

import com.hisun.lemon.acm.client.AccountManagementClient;
import com.hisun.lemon.acm.dto.QueryAcBalRspDTO;
import com.hisun.lemon.acm.dto.UserAccountDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @function AcmService
 * @description 账务服务接口
 * @date 9/19/2017 Tue
 * @time 5:56 PM
 */
@Component
public class AcmService {
    private static final Logger logger = LoggerFactory.getLogger(AcmService.class);

    @Resource
    private AccountManagementClient acmManageClient;

    public BigDecimal queryAcTotBal(String userId) {
        List<QueryAcBalRspDTO> balRspDTOS = queryAcBal(userId);
        BigDecimal totAcBal = BigDecimal.valueOf(0, 2);
        if (JudgeUtils.isEmpty(balRspDTOS)) {
            return totAcBal;
        }
        for (QueryAcBalRspDTO balRspDTO : balRspDTOS) {
            totAcBal = totAcBal.add(balRspDTO.getAcCurBal());
        }
        return totAcBal;
    }

    public List<QueryAcBalRspDTO> queryAcBal(String userId) {
        UserAccountDTO user = new UserAccountDTO();
        user.setUserId(userId);
        GenericRspDTO<List<QueryAcBalRspDTO>> rspDTO = acmManageClient.queryAcBal(user);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            LemonException.throwLemonException(rspDTO.getMsgCd());
        }
        return rspDTO.getBody();
    }

    public String openUserAccount(String userId) {
        GenericRspDTO<String> rspDTO = acmManageClient.openUserAccount(userId);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            LemonException.throwLemonException(rspDTO.getMsgCd());
        }
        return rspDTO.getBody();
    }

    public String queryAcNo(String userId) {
        GenericRspDTO<String> rspDTO = acmManageClient.queryAcNo(userId);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            LemonException.throwLemonException(rspDTO.getMsgCd());
        }
        return rspDTO.getBody();
    }

    public void closeUserAccount(String userId) {
        GenericRspDTO<NoBody> rspDTO = acmManageClient.closeUserAccount(userId);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            LemonException.throwLemonException(rspDTO.getMsgCd());
        }
    }
}
