package com.hisun.lemon.urm.service;

import com.hisun.lemon.urm.entity.*;

import java.util.List;
import java.util.Map;

/**
 * @funciton UserManagementDao
 * @description 用户管理传输对象
 * <AUTHOR>
 * @date 7/8/2017 FRI
 * @time 4:44 PM
 */
public interface IUserBasicInfService1 {
    /**
     * 用户开户
     * @param userBasicInfDO
     * @param safeInfDO
     * @param cprExtInfDO
     */
    String openUser1(UrmUserBasicInfDO userBasicInfDO, UrmSafeInfDO safeInfDO, UrmCprExtInfDO cprExtInfDO,
                           UrmSafeLoginDO safeLoginDO);

}
