/*
 * @ClassName UrmForeignAccMapDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-24 14:36:45
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;

public class UrmForeignAccMapDO extends BaseDO {
    /**
     *  foreignId 外部用户号
     */
    private String foreignId;
    /**
     *  platUserId 平台商／商户ID
     */
    private String platUserId;
    /**
     *  userId 内部用户号
     */
    private String userId;
    /**
     *  safeId 管理员安全信息ID
     */
    private String safeId;

    public String getForeignId() {
        return foreignId;
    }

    public void setForeignId(String foreignId) {
        this.foreignId = foreignId;
    }

    public String getPlatUserId() {
        return platUserId;
    }

    public void setPlatUserId(String platUserId) {
        this.platUserId = platUserId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSafeId() {
        return safeId;
    }

    public void setSafeId(String safeId) {
        this.safeId = safeId;
    }
}