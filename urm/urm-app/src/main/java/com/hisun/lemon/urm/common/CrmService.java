package com.hisun.lemon.urm.common;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.urm.constants.URMConstants;
import com.hisun.lemon.urm.constants.URMMessageCode;
import com.hisun.lemon.urm.dto.CrmLoginReqDTO;
import com.hisun.lemon.urm.dto.CrmRspDTO;
import com.hisun.lemon.urm.dto.CrmUserInfDTO;
import com.hisun.lemon.urm.entity.UrmSafeInfDO;
import com.hisun.lemon.urm.entity.UrmUserBasicInfDO;
import com.hisun.lemon.urm.utils.HttpReqUtils;
import com.hisun.lemon.urm.utils.MobileHandlerUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @function CrmService
 * @description CRM 服务
 * @date 9/18/2017 Mon
 * @time 5:05 PM
 */
@Component
public class CrmService {
    private static final Logger logger = LoggerFactory.getLogger(CrmService.class);

    public void verifyCrmLoginPwd(String loginId, String loginPwd) {
        String crmHost = LemonUtils.getProperty("crm.host");
        CrmLoginReqDTO crmLoginReqDTO = new CrmLoginReqDTO();
        crmLoginReqDTO.setCountryCode(MobileHandlerUtils.getNationCode(loginId));
        crmLoginReqDTO.setUserPassword(loginPwd);
        crmLoginReqDTO.setUserPhone(MobileHandlerUtils.getMobileNumber(loginId));
        CrmRspDTO<CrmUserInfDTO> rspDTO = new CrmRspDTO<>();
        rspDTO = HttpReqUtils.request(crmHost + URMConstants.CRM_LOGIN_URL, crmLoginReqDTO,
                (Class<CrmRspDTO<CrmUserInfDTO>>) rspDTO.getClass());
        logger.info(rspDTO.getStatus());
        if (JudgeUtils.equals(rspDTO.getStatus(), URMConstants.CRM_SUCC)) {
            //TODO 暂不处理CRM的返回数据
        } else {
            LemonException.throwLemonException(URMMessageCode.CRM_AUTH_FAILURE);
        }
    }

    public void notifyCrm(UrmUserBasicInfDO userBasicInfDO, UrmSafeInfDO safeInfDO) {
        String crmHost = LemonUtils.getProperty("crm.host");
        CrmUserInfDTO crmUserInfDTO = new CrmUserInfDTO();
        crmUserInfDTO.setCountryCode(MobileHandlerUtils.getNationCode(userBasicInfDO.getMblNo()));
        crmUserInfDTO.setUserName(userBasicInfDO.getUsrNm());
        crmUserInfDTO.setUserPhone(MobileHandlerUtils.getMobileNumber(userBasicInfDO.getMblNo()));
        crmUserInfDTO.setUserSex(userBasicInfDO.getUsrGender());
        crmUserInfDTO.setUserBirthday(userBasicInfDO.getUsrBirthDt());
        crmUserInfDTO.setUserEmail(safeInfDO.getEmail());
        try {
            CrmRspDTO<NoBody> rspDTO = new CrmRspDTO<>();
            rspDTO = HttpReqUtils.request(crmHost + URMConstants.CRM_REG_URL, crmUserInfDTO,
                    (Class<CrmRspDTO<NoBody>>) rspDTO.getClass());
            logger.info(rspDTO.getStatus());
        } catch (Exception e) {
            //TODO 不对异常做处理
        }
    }
}
