package com.hisun.lemon.urm.entity;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class UltimateBeneficialOwnersDO {

    @ApiModelProperty(name = "ultimateBeneficialOwnersFilePath", value = "实际受益人资料文件路径", dataType = "String")
    private List<String> ultimateBeneficialOwnersFilePath;

    @ApiModelProperty(name = "ultimateBeneficialOwnersRoleDesc", value = "实际受益人角色说明", dataType = "String")
    private String ultimateBeneficialOwnersRoleDesc;

    public List<String> getUltimateBeneficialOwnersFilePath() {
        return ultimateBeneficialOwnersFilePath;
    }

    public void setUltimateBeneficialOwnersFilePath(List<String> ultimateBeneficialOwnersFilePath) {
        this.ultimateBeneficialOwnersFilePath = ultimateBeneficialOwnersFilePath;
    }

    public String getUltimateBeneficialOwnersRoleDesc() {
        return ultimateBeneficialOwnersRoleDesc;
    }

    public void setUltimateBeneficialOwnersRoleDesc(String ultimateBeneficialOwnersRoleDesc) {
        this.ultimateBeneficialOwnersRoleDesc = ultimateBeneficialOwnersRoleDesc;
    }
}
