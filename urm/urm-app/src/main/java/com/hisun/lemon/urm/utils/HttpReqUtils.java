package com.hisun.lemon.urm.utils;

import com.google.gson.Gson;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.urm.constants.URMMessageCode;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @function HttpReqUtils
 * @description HTTP协议请求工具
 * @date 9/15/2017 Fri
 * @time 11:23 PM
 */
public class HttpReqUtils {
    private static final Logger logger = LoggerFactory.getLogger(HttpReqUtils.class);

    public static <T> T request(String url, Object t, Class<T> clazz) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost method = new HttpPost(url);
        Gson gson = new Gson();
        Map<String, String> mapBean = ObjectUtils.objectToMap(t);
        List<NameValuePair> nvps = new ArrayList<>();
        for (Map.Entry<String, String> entry : mapBean.entrySet()) {
            if (JudgeUtils.isNotNull(entry.getValue())) {
                nvps.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
            }
        }
        T response = null;
        try {
            method.setEntity(new UrlEncodedFormEntity(nvps));
            HttpResponse res = httpClient.execute(method);
            if(res.getStatusLine().getStatusCode() == HttpStatus.SC_OK){
                HttpEntity entity = res.getEntity();
                String result = EntityUtils.toString(entity);// 返回json格式
                logger.info("CRM RESPONSE:" + result);
                response = gson.fromJson(result, clazz);
            }
        } catch (Exception e) {
            LemonException.throwLemonException(URMMessageCode.CONNECT_CRM_FAILURE, e);
        }
        return response;
    }
}
