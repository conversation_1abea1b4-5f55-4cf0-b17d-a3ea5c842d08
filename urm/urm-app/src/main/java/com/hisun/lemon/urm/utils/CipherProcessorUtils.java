package com.hisun.lemon.urm.utils;

import com.hisun.hsm.HSMClient;
import com.hisun.hsm.command.HSMResult;
import com.hisun.hsm.constant.HSMConstant;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.extension.ExtensionLoader;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.NumberUtils;
import com.hisun.lemon.common.utils.RandomUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.urm.constants.URMConstants;
import com.hisun.lemon.urm.constants.URMMessageCode;
import org.apache.commons.codec.binary.Hex;
import org.bouncycastle.asn1.ASN1Integer;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.util.encoders.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.RSAPublicKeySpec;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @function CipherProcessorUtils
 * @description 密码处理功能组件
 * @date 9/11/2017 Mon
 * @time 3:11 PM
 */
public class CipherProcessorUtils {
    private static final Logger logger = LoggerFactory.getLogger(CipherProcessorUtils.class);

    private static final String SYS_PAY_PVK = LemonUtils.getProperty("key.pvk.SYS_PAY_PVK");
    private static final String SYS_PAY_ZPK = LemonUtils.getProperty("key.zpk.SYS_PAY_ZPK");
    private static final String SYS_LOG_PVK = LemonUtils.getProperty("key.pvk.SYS_LOG_PVK");
    private static final String APP_ZPK = LemonUtils.getProperty("key.zpk.APP_ZPK");
    private static final String PUB_KEY = LemonUtils.getProperty("key.pubkey.APP_PUB_KEY");
    private static final String PRI_KEY_IDX = LemonUtils.getProperty("key.prikey.PRI_KEY_IDX");

    private static HSMClient hsmClient;

    private static HSMClient getHsmClient() {
        if (null == hsmClient) {
            hsmClient = ExtensionLoader.getSpringBean("hsmClient", HSMClient.class);
        }
        return hsmClient;
    }

    public static Map<String, String> encryptPlaintextPin(String plaintextPwd, String random) {
        Map<String, String> map = new HashMap<>();
        // 服务端随机数
        byte[] binaryRS = random.getBytes();
        logger.debug("randomSever:" + new String(random.getBytes()));

        // 客户端随机数
        byte[] binaryRC = binaryRS;

        // 密码
        String password = plaintextPwd;
        logger.debug("Password:" + plaintextPwd);

        // construct 3DES key and IV
        byte[] tripleDESKey = new byte[24];
        byte[] iv = new byte[8];
        System.arraycopy(binaryRS, 0, tripleDESKey, 0, 12);
        System.arraycopy(binaryRC, 0, tripleDESKey, 12, 12);
        System.arraycopy(binaryRS, 12, iv, 0, 4);
        System.arraycopy(binaryRC, 12, iv, 4, 4);

        // 3des加密
        try {
            byte[] passwordCipher = ThreeDESUtil.des3EncodeCBC(tripleDESKey, iv, password.getBytes());
            map.put("pwd", Base64.toBase64String(passwordCipher));
            logger.debug("3DES encrypted password:" + new String(Base64.encode(passwordCipher)));
        } catch (Exception e) {
            LemonException.throwLemonException(e);
        }

        // HSM生成的RSA公钥
        String publicRSAKeyHEX = PUB_KEY;
        logger.debug("RSA public key:" + publicRSAKeyHEX);

        // RSA公钥加密RC
        try {
            ASN1Sequence sequence = ASN1Sequence.getInstance(Hex.decodeHex(publicRSAKeyHEX.toCharArray()));
            ASN1Integer modulus = ASN1Integer.getInstance(sequence.getObjectAt(0));
            ASN1Integer exponent = ASN1Integer.getInstance(sequence.getObjectAt(1));
            RSAPublicKeySpec keySpec = new RSAPublicKeySpec(modulus.getPositiveValue(), exponent.getPositiveValue());
            KeyFactory factory = KeyFactory.getInstance("RSA");
            PublicKey publicKey = factory.generatePublic(keySpec);
            String encryptData = RSAUtil.encrypt(publicKey, binaryRC);
            logger.debug("RSA encrypted RC:" + encryptData);
            map.put("randomClient", encryptData);
        } catch (Exception e) {
            LemonException.throwLemonException(e);
        }
        return map;
    }

    public static Map<String, String> initLoginPwd(String userId) {
        userId = userId.substring(2, 6) + userId.substring(8, 16);
        Map<String, String> map = new HashMap<>();
        String plaintextPwd = RandomUtils.randomStringFixLength(8);

        //体验卡批量开户默认登录密码
        if (JudgeUtils.equals(LemonUtils.getSource(), URMConstants.SOURCE_BATCH_OPEN)) {
            plaintextPwd = "a1234a1234";
        }

        map.put("loginPwdPlaintext", plaintextPwd);
        String loginPwd = imitatePwdHandle(plaintextPwd);
        HSMResult hsm = new HSMResult();
        String zpk = APP_ZPK;
        String pvk = SYS_LOG_PVK;
        try {
            hsm = getHsmClient().convertPinFromZpkToPvk(zpk, pvk, userId, loginPwd);
        } catch (Throwable e) {
            LemonException.throwLemonException(URMMessageCode.HSM_HANDLE_FAILURE, e);
        }
        if (JudgeUtils.notEquals(hsm.getRecode(), HSMConstant.SUCCESS)) {
            LemonException.throwLemonException(URMMessageCode.INIT_PWD_FAILURE);
        }
        map.put("loginPwd", hsm.getPinCiphertext());
        return map;
    }

    private static String judgePinMode(String pwd, String type) {
        //type: plain明文
        String pinMode = "";
        if (JudgeUtils.equals(type, "plain")) {
            if (pwd.length() == 6) {
                if (!NumberUtils.isDigits(pwd)) {
                    LemonException.throwLemonException(URMMessageCode.PIN_MODE_WRONG);
                }
                pinMode = URMConstants.PAY_PWD_MODE;
            } else if (pwd.length() >= 8 && pwd.length() <= 15) {
                pinMode = URMConstants.LOG_PWD_MODE;
            } else {
                LemonException.throwLemonException(URMMessageCode.PIN_MODE_WRONG);
            }
        } else {
            if (pwd.length() == 16) {
                pinMode = URMConstants.PAY_PWD_MODE;
            } else if (pwd.length() == 32) {
                pinMode = URMConstants.LOG_PWD_MODE;
            } else {
                LemonException.throwLemonException(URMMessageCode.PIN_MODE_WRONG);
            }
        }
        return pinMode;
    }

    public static String imitatePwdHandle(String pwdPlaintext) {
        String random = RandomUtils.randomNumeric(16);
        logger.info("random:" + random);
        Map<String, String> map = encryptPlaintextPin(pwdPlaintext, random);
        String randomClient = map.get("randomClient");
        String tmpPwd = map.get("pwd");
        String pinMode = judgePinMode(pwdPlaintext, "plain");
        if (JudgeUtils.equals(LemonUtils.getSource(), URMConstants.INTERNAL_GATE)) {
            random = Base64.toBase64String(random.getBytes());
        }
        String pwd = convertRSAEncryptedPin(randomClient, random, tmpPwd, pinMode);
        logger.debug("payPwdCipher:" + pwd);
        return pwd;
    }

    public static String generatePayPinOffset(String acNo, String payPwdPlaintext) {
        String payPwd = imitatePwdHandle(payPwdPlaintext);
        return convertPinToPinOffset(acNo, payPwd);
    }

    public static Map<String, String> initPayPwd(String acNo) {
        Map<String, String> map = new HashMap<>();
        String pvk = SYS_PAY_PVK;
        HSMResult hsm = new HSMResult();
        acNo = acNo.substring(0, 4) + acNo.substring(6, 14);
        try {
            hsm = getHsmClient().generatePayPin(acNo, pvk);
        } catch (Throwable e) {
            LemonException.throwLemonException(URMMessageCode.HSM_HANDLE_FAILURE, e);
        }
        if (JudgeUtils.notEquals(hsm.getRecode(), HSMConstant.SUCCESS)) {
            LemonException.throwLemonException(URMMessageCode.INIT_PWD_FAILURE);
        }
        map.put("payPwdPlaintext", hsm.getPin());
        map.put("payPwdPinOffset", hsm.getPinOffset());
        return map;
    }

    public static String decryptRSAEncryptedRandom(String randomCiphertext) {
        HSMResult hsm = new HSMResult();
        try {
            hsm = getHsmClient().privateKeyDecryption(PRI_KEY_IDX, randomCiphertext);
        } catch (Throwable e) {
            LemonException.throwLemonException(URMMessageCode.HSM_HANDLE_FAILURE, e);
        }
        if (JudgeUtils.notEquals(hsm.getRecode(), HSMConstant.SUCCESS)) {
            LemonException.throwLemonException(URMMessageCode.RSA_DECRYPT_FAILURE);
        }
        byte[] bytes = hsm.getPlainText();
        String random = Base64.toBase64String(bytes);
        return random;
    }

    public static String decrypt3DESEncryptedPin(String randomClient, String randomServer, String passwordCipherText) {
        logger.info("RS:" + randomServer + ",RC:" + randomClient + ",PWD:" + passwordCipherText);
        String password = null;
        byte[] passwordBytes = Base64.decode(passwordCipherText.getBytes());
        byte[] tripleDESKey = new byte[24];
        byte[] iv = new byte[8];
        //注意：randomClient是64位编码的字符串，randomServer是UTF-8编码的字符串
        System.arraycopy(randomServer.getBytes(), 0, tripleDESKey, 0, 12);
        System.arraycopy(Base64.decode(randomClient), 0, tripleDESKey, 12, 12);
        System.arraycopy(randomServer.getBytes(), 12, iv, 0, 4);
        System.arraycopy(Base64.decode(randomClient), 12, iv, 4, 4);
        // ****** 3DES解密 ******
        // 省略HSM使用RSA私钥将RSA公钥加密的RC密文解密，从而省略由RC、RS构造3des密钥和向量
        try {
            password = new String(ThreeDESUtil.des3DecodeCBC(tripleDESKey, iv, passwordBytes));
            logger.info("PWD:" + password);
        } catch (Throwable e) {
            LemonException.throwLemonException(URMMessageCode.TRIPLEDES_DECRYPT_FAILURE, e);
        }
        return password;
    }

    public static String convertRSAEncryptedPin(String pwdRandom, String randomServer, String
            passwordCipherText, String pinMode) {
        //TODO 根据渠道获取ZPK
        String source = LemonUtils.getSource();
        String zpk = APP_ZPK;
        HSMResult hsm = new HSMResult();
        logger.debug("randomServer:" + randomServer + ",pwdRandom:" + pwdRandom + "," +
                "pinMode:" + pinMode + "," + "password:" + passwordCipherText);
        //IGW的随机数进行了ISO-8859-1的Base64编码,不进行转码
        if (JudgeUtils.notEquals(LemonUtils.getSource(), URMConstants.INTERNAL_GATE)) {
            randomServer = Base64.toBase64String(randomServer.getBytes());
        }
        if (JudgeUtils.isBlank(randomServer)) {
            LemonException.throwLemonException(URMMessageCode.RETYPE_PASSWORD);
        }
        try {
            hsm = getHsmClient().convertRSAEncryptedPIN(PRI_KEY_IDX, pwdRandom, pinMode,
                    passwordCipherText, randomServer, zpk);
        } catch (Throwable e) {
            LemonException.throwLemonException(URMMessageCode.HSM_HANDLE_FAILURE, e);
        }
        if (JudgeUtils.notEquals(hsm.getRecode(), HSMConstant.SUCCESS)) {
            logger.debug("HsmRecode:" + hsm.getRecode());
            LemonException.throwLemonException(URMMessageCode.TRANSLATE_PIN_FAILURE);
        }
        return hsm.getPinCiphertext();
    }

    public static String convertRSAEncryptedPinNew(String pwdRandom, String randomServer, String
            passwordCipherText, String pinMode) {
        //TODO 根据渠道获取ZPK
        String source = LemonUtils.getSource();
        String zpk = APP_ZPK;
        HSMResult hsm = new HSMResult();
        logger.debug("randomServer:" + randomServer + ",pwdRandom:" + pwdRandom + "," +
                "pinMode:" + pinMode + "," + "password:" + passwordCipherText);
        //IGW的随机数进行了ISO-8859-1的Base64编码,不进行转码
        randomServer = Base64.toBase64String(randomServer.getBytes());

        if (JudgeUtils.isBlank(randomServer)) {
            LemonException.throwLemonException(URMMessageCode.RETYPE_PASSWORD);
        }
        try {
            hsm = getHsmClient().convertRSAEncryptedPIN(PRI_KEY_IDX, pwdRandom, pinMode,
                    passwordCipherText, randomServer, zpk);
        } catch (Throwable e) {
            LemonException.throwLemonException(URMMessageCode.HSM_HANDLE_FAILURE, e);
        }
        if (JudgeUtils.notEquals(hsm.getRecode(), HSMConstant.SUCCESS)) {
            logger.debug("HsmRecode:" + hsm.getRecode());
            LemonException.throwLemonException(URMMessageCode.TRANSLATE_PIN_FAILURE);
        }
        return hsm.getPinCiphertext();
    }

    public static String convertPwdfromZpkToPvk(String userId, String loginPwd) {
        HSMResult hsm = new HSMResult();
        String zpk = APP_ZPK;
        String pvk = SYS_LOG_PVK;
        userId = userId.substring(2, 6) + userId.substring(8, 16);
        try {
            hsm = getHsmClient().convertPinFromZpkToPvk(zpk, pvk, userId, loginPwd);
        } catch (Throwable e) {
            LemonException.throwLemonException(URMMessageCode.HSM_HANDLE_FAILURE, e);
        }
        if (JudgeUtils.notEquals(hsm.getRecode(), HSMConstant.SUCCESS)) {
            LemonException.throwLemonException(URMMessageCode.TRANSLATE_ZPK_TO_PVK_FAILURE);
        }
        return hsm.getPinCiphertext();
    }

    public static String convertPinToPinOffset(String acNo, String payPwd) {
        String zpk1 = APP_ZPK;
        String zpk2 = SYS_PAY_ZPK;
        String pvk = SYS_PAY_PVK;
        acNo = acNo.substring(0, 4) + acNo.substring(6, 14);
        HSMResult hsm = new HSMResult();
        try {
            hsm = getHsmClient().generatPinOffset(zpk1, payPwd, zpk2, acNo, pvk);
        } catch (Throwable e) {
            LemonException.throwLemonException(URMMessageCode.HSM_HANDLE_FAILURE, e);
        }
        if (JudgeUtils.notEquals(hsm.getRecode(), HSMConstant.SUCCESS)) {
            LemonException.throwLemonException(URMMessageCode.PLAINTEXT_PWD_TRANSLATE_FAILURE);
        }
        return hsm.getPinOffset();
    }

    public static boolean verifyLoginPwd(String zpkEncryptedPIN, String userId, String loginPwd) {
        if (JudgeUtils.equals(zpkEncryptedPIN, loginPwd)) {
            return true;
        }
        return false;
//        //TODO 根据渠道获取ZPK ，以及系统的PVK
//        String source = LemonUtils.getSource();
//        String zpk = APP_ZPK;
//        String pvk = SYS_LOG_PVK;
//        if (zpkEncryptedPIN.length() == 48) {
//            if (JudgeUtils.equals(zpkEncryptedPIN, loginPwd)) {
//                return true;
//            }
//            return false;
//        }
//        userId = userId.substring(2, 6) + userId.substring(8, 16);
//        HSMResult hsm = new HSMResult();
//        try {
//            hsm = getHsmClient().convertPinFromZpkToPvk(zpk, pvk, userId, zpkEncryptedPIN);
//        } catch (Throwable e) {
//            LemonException.throwLemonException(URMMessageCode.HSM_HANDLE_FAILURE, e);
//        }
//        if (JudgeUtils.notEquals(hsm.getRecode(), HSMConstant.SUCCESS)) {
//            LemonException.throwLemonException(URMMessageCode.TRANSLATE_ZPK_TO_PVK_FAILURE);
//        }
//        String pwd = hsm.getPinCiphertext();
//        if (JudgeUtils.equals(pwd, loginPwd)) {
//            return true;
//        }
//        return false;
    }

    public static boolean verifyPayPwd(String zpk1EncryptedPIN, String acNo, String payPwd) {
        //TODO 根据渠道获取对应的ZPK1以及系统zpk和pvk
        String source = LemonUtils.getSource();
        String zpk1 = APP_ZPK;
        String zpk = SYS_PAY_ZPK;
        String pvk = SYS_PAY_PVK;
        HSMResult hsm = new HSMResult();
        acNo = acNo.substring(0, 4) + acNo.substring(6, 14);
        try {
            hsm = getHsmClient().verifyPinOffset(zpk1, zpk1EncryptedPIN, zpk, acNo, pvk, payPwd);
        } catch (Throwable e) {
            LemonException.throwLemonException(URMMessageCode.HSM_HANDLE_FAILURE, e);
        }
        if (JudgeUtils.equals(hsm.getRecode(), HSMConstant.SUCCESS)) {
            return true;
        }
        return false;
    }
}
