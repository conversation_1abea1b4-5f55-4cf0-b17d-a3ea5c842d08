package com.hisun.lemon.urm.utils;

import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.urm.constants.URMConstants;
import com.hisun.lemon.urm.dto.UserRealNameDTO;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @function VerifyIdentityUtils
 * @description 校验证件号合法性
 * @date 9/5/2017 Tue
 * @time 3:59 PM
 */
public class VerifyIdentityUtils {
    public static boolean verifyIdentity(UserRealNameDTO realNameDTO) {
//      0  身份证
//      1  护照
//      2  军官证
//      3  士兵证
//      4  港澳台居民往来通行证
//      5  临时身份证
//      6  户口本
//      7  其他
//      8  EMPL工作准证
//      9  警官证
//      10 识别证
//      11 驾驶执照
//      12 永久居留证
//      13 保险证
//      14 出生证
//      15 社保卡
        String idNo = realNameDTO.getIdNo();
        String idType = realNameDTO.getIdType();
        String usrCountry = realNameDTO.getUsrCountry();
        String usrNm = realNameDTO.getUsrNm();
        if (JudgeUtils.equals(usrCountry, URMConstants.ZH_CN)) {
            if (JudgeUtils.equals(idType, URMConstants.ID_CARD_TYP)) {
                //用户姓名只能为汉字或汉字与"·"
                String pattern = "[\\u4e00-\\u9fa5]+";
                Pattern p = Pattern.compile(pattern);
                usrNm = usrNm.replace("·", "");
                usrNm = usrNm.replace(".", "");
                Matcher matcher = p.matcher(usrNm);
                String tmp = matcher.replaceAll("");
                if (tmp.length() > 0) {
                    return false;
                }
                //检查IDNO是否合法
                if(idNo != null && idNo.length() != 15 && idNo.length() != 18){
                    return false;
                }
                return verifyIdNo(idNo);
            }
            return true;
        } else {
            return verifyIdType(idType);
        }
    }

    private static boolean verifyIdNo(String idNo) {
        int PowerGene[] = { 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2, 1 };
        char Remainder[] = { '1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2' };
        int district[] = { 11, 12, 13, 14, 15, 21, 22, 23, 31, 32, 33, 34, 35, 36, 37, 41, 42, 43, 44, 45, 46, 50, 51,
                52, 53, 54, 61, 62, 63, 64, 65 };

        // 验证长度
        int idNoLen = idNo.trim().length();
        if (idNoLen != 15 && idNoLen != 18) {
            return false;
        }

        // 验证省标识
        int brflg = NumberUtils.toInt(idNo.substring(0, 2));
        int num = district.length;
        boolean result = false;
        if (district[0] > brflg || district[num - 1] <brflg ) {
            return false;
        }
        for (int i = 0; i < num; i++) {
            if (district[i] == brflg) {
                result = true;
                break;
            } else {
                int tmp = district[i + (num - i)/2];
                if (tmp > brflg) {
                    num = num - (num - i)/2;
                } else if (tmp < brflg) {
                    i += (num - i)/2;
                } else {
                    result = true;
                    break;
                }
            }
        }
        if (!result) {
            return result;
        }

        /* 如果身份证为15位,则加为17位 */
        if (idNoLen == 15) {
            idNo = idNo.substring(0, 6) + "19" + idNo.substring(6);
        }
        String date = idNo.substring(6, 14);
        try {
            DateTimeUtils.parseLocalDate(date);
        } catch (Exception e) {
            return false;
        }

     	/* 身份证号的每一位和对应的加权因子相乘,累加和再除以11,得到的余数查找相应的校验位. */
        int iSum = 0;
        char ch;
        for (int i = 0; i < 17; i++) {
            ch = idNo.charAt(i);
            if (ch < '0' || ch > '9') {
                return false;
            }
            iSum += (ch - '0') * PowerGene[i];
        }
        int remain = iSum % 11;

        if (idNo.length() == 17) {
            idNo = idNo + Remainder[remain];
        }
        if (Character.toUpperCase(idNo.charAt(17)) != Remainder[remain]) {
            return false;
        }
        return true;
    }

    private static boolean verifyIdType(String idType) {
        int type[] = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15 };
        if (JudgeUtils.isBlank(idType) || !NumberUtils.isDigits(idType)) {
            return false;
        }
        boolean result = false;
        int num = type.length;
        int idTypeNum = NumberUtils.toInt(idType);
        for (int i = 0; i < num; i++) {
            if (type[i] == idTypeNum){
                result = true;
                break;
            } else {
                int mid = type[ (num - i)/2 + i ];
                if (mid > idTypeNum) {
                    num -= (num - i) / 2;
                } else if (mid < idTypeNum) {
                    i += (num - i)/2;
                } else {
                    result = true;
                    break;
                }
            }
        }
        return result;
    }

    public static void main(String[] args) {
        for (int i = 0; i< 20; i++) {
            System.out.println(i + ":" + verifyIdType(Integer.toString(i)));
        }
        for (int i = 11; i < 66; i++) {
            System.out.println(i + ":" + verifyIdNo(Integer.toString(i) + "0000000000000"));
        }
    }
}
