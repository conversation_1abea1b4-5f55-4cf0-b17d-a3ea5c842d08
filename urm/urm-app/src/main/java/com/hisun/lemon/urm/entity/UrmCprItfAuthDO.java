/*
 * @ClassName UrmCprItfAuthDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-24 14:36:45
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;

public class UrmCprItfAuthDO extends BaseDO {
    /**
     *  userId 内部用户号
     */
    private String userId;
    /**
     *  itfNm 接口名称
     */
    private String itfNm;
    /**
     *  version 接口版本
     */
    private String version;
    /**
     *  verifyType 接口签名类型 MD5
     */
    private String verifyType;
    /**
     *  secretKey 接口密钥
     */
    private String secretKey;
    /**
     *  sts 接口状态  1:生效 0:失效
     */
    private String sts;
    /**
     *  macItem 请求签名字段串
     */
    private String macItem;
    /**
     *  rspMacItem 返回签名字段串
     */
    private String rspMacItem;
    /**
     *  lastUpdOpr 最后更新柜员
     */
    private String lastUpdOpr;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getItfNm() {
        return itfNm;
    }

    public void setItfNm(String itfNm) {
        this.itfNm = itfNm;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getVerifyType() {
        return verifyType;
    }

    public void setVerifyType(String verifyType) {
        this.verifyType = verifyType;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getSts() {
        return sts;
    }

    public void setSts(String sts) {
        this.sts = sts;
    }

    public String getMacItem() {
        return macItem;
    }

    public void setMacItem(String macItem) {
        this.macItem = macItem;
    }

    public String getRspMacItem() {
        return rspMacItem;
    }

    public void setRspMacItem(String rspMacItem) {
        this.rspMacItem = rspMacItem;
    }

    public String getLastUpdOpr() {
        return lastUpdOpr;
    }

    public void setLastUpdOpr(String lastUpdOpr) {
        this.lastUpdOpr = lastUpdOpr;
    }
}