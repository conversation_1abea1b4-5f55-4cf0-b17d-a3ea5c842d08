package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;

import java.util.Date;


public class UrmKybSeqDO extends BaseDO {

    private Integer kybId;

    private String userId;

    private String examineNm;

    private String examineStatus;

    private String kybInfo;

    private String remark;

    public Integer getKybId() {
        return kybId;
    }

    public void setKybId(Integer kybId) {
        this.kybId = kybId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getExamineNm() {
        return examineNm;
    }

    public void setExamineNm(String examineNm) {
        this.examineNm = examineNm;
    }

    public String getExamineStatus() {
        return examineStatus;
    }

    public void setExamineStatus(String examineStatus) {
        this.examineStatus = examineStatus;
    }

    public String getKybInfo() {
        return kybInfo;
    }

    public void setKybInfo(String kybInfo) {
        this.kybInfo = kybInfo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}