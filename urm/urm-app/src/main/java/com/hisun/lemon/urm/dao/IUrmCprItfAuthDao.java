/*
 * @ClassName IUrmCprItfAuthDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-24 14:36:45
 */
package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.UrmCprItfAuthDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface IUrmCprItfAuthDao extends BaseDao<UrmCprItfAuthDO> {
    /**
     * 查询交易权限
     * @param cprItfAuthDO
     * @return
     */
    public UrmCprItfAuthDO get(UrmCprItfAuthDO cprItfAuthDO);
}