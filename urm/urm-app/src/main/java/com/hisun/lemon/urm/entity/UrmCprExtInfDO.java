/*
 * @ClassName UrmCprExtInfDO
 * @Description 
 * @version 1.0
 * @Date 2017-07-24 14:36:45
 */
package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;
import java.math.BigDecimal;
import java.time.LocalDate;

public class UrmCprExtInfDO extends BaseDO {
    /**
     *  userId 内部用户号
     */
    private String userId;
    /**
     *  mercName 商户名称
     */
    private String mercName;
    /**
     *  mercShortName 商户简称
     */
    private String mercShortName;
    /**
     *  cprRegNmCn 注册名称(中文)
     */
    private String cprRegNmCn;
    /**
     *  cprOperNmCn 经营名称（中文）
     */
    private String cprOperNmCn;
    /**
     *  prinNm 负责人名称
     */
    private String prinNm;

    /**
     *  crpNm 法人名称
     */
    private String crpNm;

    /**
     * crpIdTyp 法人证件类型
     */
    private String crpIdTyp;

    /**
     * crpIdNo 法人证件号
     */
    private String crpIdNo;

    /**
     * opnBusDt 开业日期
     */
    private LocalDate opnBusDt;

    /**
     * busLicExpDt (经营年限)营业执照失效日期
     */
    private LocalDate busLicExpDt;

    /**
     * refereeMblNo 推荐人手机号
     */
    private String refereeMblNo;

    /**
     *  comercReg 工商注册号
     */
    private String comercReg;
    /**
     *  socialCrdCd 社会信用代码
     */
    private String socialCrdCd;
    /**
     *  orgCd 组织机构代码
     */
    private String orgCd;
    /**
     *  busiLisc 营业执照
     */
    private String busiLisc;
    /**
     *  taxCertId 税务证明
     */
    private String taxCertId;
    /**
     *  webNm 网站名称
     */
    private String webNm;
    /**
     *  webUrl 网站地址
     */
    private String webUrl;
    /**
     *  merRegAddr 公司注册地址
     */
    private String merRegAddr;
    /**
     *  merAddrLongitude 公司地址的所在经度
     */
    private BigDecimal merAddrLongitude;
    /**
     *  merAddrLatitude 公司地址的所在纬度
     */
    private BigDecimal merAddrLatitude;
    /**
     *  mgtScp 经营范围
     */
    private String mgtScp;
    /**
     *  needInvFlg 是否开具发票 Y：需要；N：不需要；
     */
    private String needInvFlg;
    /**
     *  invMod 开具发票方式 0 - 按季度开；1 - 按月开；2 –按年开；
     */
    private String invMod;
    /**
     *  invTit 发票抬头
     */
    private String invTit;
    /**
     *  invMailAddr 发票邮寄地址
     */
    private String invMailAddr;
    /**
     *  invMailZip 发票邮寄邮编
     */
    private String invMailZip;
    /**
     *  mercTrdCls 商户行业类别
     */
    private String mercTrdCls;
    /**
     *  mercTrdDesc 商户行业描述
     */
    private String mercTrdDesc;
    /**
     *  cprTyp 商户类别 01-国有，02-私有，03-外资，04-合资 08-个人，10-公司，11-个人独资
     */
    private String cprTyp;

    /**
     * merLvl 商户级别
     */
    private String merLvl;

    /**
     *  csTelNo 商户客服电话
     */
    private String csTelNo;
    /**
     *  mercHotLin 商户热线
     */
    private String mercHotLin;
    /**
     *  cusMgr 客户经理编号
     */
    private String cusMgr;
    /**
     *  cusMgrNm 客户经理名称
     */
    private String cusMgrNm;
    /**
     *  rcvMagAmt 应收商户保证金
     */
    private BigDecimal rcvMagAmt;
    /**
     *  lastUpdOpr 最后更新柜员
     */
    private String lastUpdOpr;

    /**
     * belongMerc 所属上级商户
     */
    private String belongMerc;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getMercName() {
        return mercName;
    }

    public void setMercName(String mercName) {
        this.mercName = mercName;
    }

    public String getMercShortName() {
        return mercShortName;
    }

    public void setMercShortName(String mercShortName) {
        this.mercShortName = mercShortName;
    }

    public String getCprRegNmCn() {
        return cprRegNmCn;
    }

    public void setCprRegNmCn(String cprRegNmCn) {
        this.cprRegNmCn = cprRegNmCn;
    }

    public String getCprOperNmCn() {
        return cprOperNmCn;
    }

    public void setCprOperNmCn(String cprOperNmCn) {
        this.cprOperNmCn = cprOperNmCn;
    }

    public String getPrinNm() {
        return prinNm;
    }

    public void setPrinNm(String prinNm) {
        this.prinNm = prinNm;
    }

    public String getCrpNm() {
        return crpNm;
    }

    public LocalDate getOpnBusDt() {
        return opnBusDt;
    }

    public LocalDate getBusLicExpDt() {
        return busLicExpDt;
    }

    public void setBusLicExpDt(LocalDate busLicExpDt) {
        this.busLicExpDt = busLicExpDt;
    }

    public void setOpnBusDt(LocalDate opnBusDt) {
        this.opnBusDt = opnBusDt;
    }

    public String getRefereeMblNo() {
        return refereeMblNo;
    }

    public void setRefereeMblNo(String refereeMblNo) {
        this.refereeMblNo = refereeMblNo;
    }

    public void setCrpNm(String crpNm) {
        this.crpNm = crpNm;
    }

    public String getCrpIdTyp() {
        return crpIdTyp;
    }

    public void setCrpIdTyp(String crpIdTyp) {
        this.crpIdTyp = crpIdTyp;
    }

    public String getCrpIdNo() {
        return crpIdNo;
    }

    public void setCrpIdNo(String crpIdNo) {
        this.crpIdNo = crpIdNo;
    }

    public String getComercReg() {
        return comercReg;
    }

    public void setComercReg(String comercReg) {
        this.comercReg = comercReg;
    }

    public String getSocialCrdCd() {
        return socialCrdCd;
    }

    public void setSocialCrdCd(String socialCrdCd) {
        this.socialCrdCd = socialCrdCd;
    }

    public String getOrgCd() {
        return orgCd;
    }

    public void setOrgCd(String orgCd) {
        this.orgCd = orgCd;
    }

    public String getBusiLisc() {
        return busiLisc;
    }

    public void setBusiLisc(String busiLisc) {
        this.busiLisc = busiLisc;
    }

    public String getTaxCertId() {
        return taxCertId;
    }

    public void setTaxCertId(String taxCertId) {
        this.taxCertId = taxCertId;
    }

    public String getWebNm() {
        return webNm;
    }

    public void setWebNm(String webNm) {
        this.webNm = webNm;
    }

    public String getWebUrl() {
        return webUrl;
    }

    public void setWebUrl(String webUrl) {
        this.webUrl = webUrl;
    }

    public String getMerRegAddr() {
        return merRegAddr;
    }

    public void setMerRegAddr(String merRegAddr) {
        this.merRegAddr = merRegAddr;
    }

    public BigDecimal getMerAddrLongitude() {
        return merAddrLongitude;
    }

    public void setMerAddrLongitude(BigDecimal merAddrLongitude) {
        this.merAddrLongitude = merAddrLongitude;
    }

    public BigDecimal getMerAddrLatitude() {
        return merAddrLatitude;
    }

    public void setMerAddrLatitude(BigDecimal merAddrLatitude) {
        this.merAddrLatitude = merAddrLatitude;
    }

    public String getMgtScp() {
        return mgtScp;
    }

    public void setMgtScp(String mgtScp) {
        this.mgtScp = mgtScp;
    }

    public String getNeedInvFlg() {
        return needInvFlg;
    }

    public void setNeedInvFlg(String needInvFlg) {
        this.needInvFlg = needInvFlg;
    }

    public String getInvMod() {
        return invMod;
    }

    public void setInvMod(String invMod) {
        this.invMod = invMod;
    }

    public String getInvTit() {
        return invTit;
    }

    public void setInvTit(String invTit) {
        this.invTit = invTit;
    }

    public String getInvMailAddr() {
        return invMailAddr;
    }

    public void setInvMailAddr(String invMailAddr) {
        this.invMailAddr = invMailAddr;
    }

    public String getInvMailZip() {
        return invMailZip;
    }

    public void setInvMailZip(String invMailZip) {
        this.invMailZip = invMailZip;
    }

    public String getMercTrdCls() {
        return mercTrdCls;
    }

    public void setMercTrdCls(String mercTrdCls) {
        this.mercTrdCls = mercTrdCls;
    }

    public String getMercTrdDesc() {
        return mercTrdDesc;
    }

    public void setMercTrdDesc(String mercTrdDesc) {
        this.mercTrdDesc = mercTrdDesc;
    }

    public String getCprTyp() {
        return cprTyp;
    }

    public void setCprTyp(String cprTyp) {
        this.cprTyp = cprTyp;
    }

    public String getCsTelNo() {
        return csTelNo;
    }

    public void setCsTelNo(String csTelNo) {
        this.csTelNo = csTelNo;
    }

    public String getMercHotLin() {
        return mercHotLin;
    }

    public void setMercHotLin(String mercHotLin) {
        this.mercHotLin = mercHotLin;
    }

    public String getCusMgr() {
        return cusMgr;
    }

    public void setCusMgr(String cusMgr) {
        this.cusMgr = cusMgr;
    }

    public String getCusMgrNm() {
        return cusMgrNm;
    }

    public void setCusMgrNm(String cusMgrNm) {
        this.cusMgrNm = cusMgrNm;
    }

    public BigDecimal getRcvMagAmt() {
        return rcvMagAmt;
    }

    public void setRcvMagAmt(BigDecimal rcvMagAmt) {
        this.rcvMagAmt = rcvMagAmt;
    }

    public String getLastUpdOpr() {
        return lastUpdOpr;
    }

    public void setLastUpdOpr(String lastUpdOpr) {
        this.lastUpdOpr = lastUpdOpr;
    }

    public String getMerLvl() {
        return merLvl;
    }

    public void setMerLvl(String merLvl) {
        this.merLvl = merLvl;
    }

    public String getBelongMerc() {
        return belongMerc;
    }

    public void setBelongMerc(String belongMerc) {
        this.belongMerc = belongMerc;
    }
}