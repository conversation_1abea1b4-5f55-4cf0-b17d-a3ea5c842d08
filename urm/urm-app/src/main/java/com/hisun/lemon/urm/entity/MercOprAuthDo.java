package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2017/10/19
 */
public class MercOprAuthDo extends BaseDO {

    private String loginId;

    private String authority;

    private String updOprId;

    private LocalDateTime createTime;

    private LocalDateTime modifyTime;

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getAuthority() {
        return authority;
    }

    public void setAuthority(String authority) {
        this.authority = authority;
    }

    public String getUpdOprId() {
        return updOprId;
    }

    public void setUpdOprId(String updOprId) {
        this.updOprId = updOprId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        return "MercOprAuthDo{" +
                "loginId='" + loginId + '\'' +
                ", authority='" + authority + '\'' +
                ", updOprId='" + updOprId + '\'' +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                '}';
    }
}
