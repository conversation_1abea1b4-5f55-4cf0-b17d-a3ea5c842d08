package com.hisun.lemon.urm.entity;

import com.hisun.lemon.framework.data.BaseDO;

/**
 * <AUTHOR>
 * @create 2017/10/16
 */
public class MercOprInfoDO extends BaseDO {

    private String safeId;

    private String loginId;

    private String displayNm;

    private String oprTyp;

    public String getSafeId() {
        return safeId;
    }

    public void setSafeId(String safeId) {
        this.safeId = safeId;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getDisplayNm() {
        return displayNm;
    }

    public void setDisplayNm(String displayNm) {
        this.displayNm = displayNm;
    }

    public String getOprTyp() {
        return oprTyp;
    }

    public void setOprTyp(String oprTyp) {
        this.oprTyp = oprTyp;
    }

    @Override
    public String toString() {
        return "MercOprInfoDTO{" +
                "safeId='" + safeId + '\'' +
                ", loginId='" + loginId + '\'' +
                ", displayNm='" + displayNm + '\'' +
                ", oprTyp='" + oprTyp + '\'' +
                '}';
    }
}
