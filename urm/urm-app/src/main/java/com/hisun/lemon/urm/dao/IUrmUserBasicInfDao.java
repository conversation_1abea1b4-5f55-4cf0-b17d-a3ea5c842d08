/*
 * @ClassName IUrmUserBasicInfDao
 * @Description 
 * @version 1.0
 * @Date 2017-07-24 14:36:45
 */
package com.hisun.lemon.urm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.urm.entity.MercInfoList;
import com.hisun.lemon.urm.entity.UrmUserBasicInfDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IUrmUserBasicInfDao extends BaseDao<UrmUserBasicInfDO> {
    /**
     * 通过登录ID查询用户信息
     */
    public UrmUserBasicInfDO getByLoginId(String loginId);

    /**
     * 统计同一证件的个数
     */
    public int countId(@Param("idNo") String idNo, @Param("idType") String idType, @Param("userId") String userId);

    public int countIdNo(@Param("idNo") String idNo);

    /**
     * 查询所有商户的ID
     */
    public List<String> getCrpUser();

    List<MercInfoList> getListInfo(@Param("userList") List<String> userList,
                                   @Param("mercId") String mercId,
                                   @Param("mercNm") String mercNm);
}