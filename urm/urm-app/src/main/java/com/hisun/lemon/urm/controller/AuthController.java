package com.hisun.lemon.urm.controller;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.urm.dto.EmailAuthDto;
import com.hisun.lemon.urm.dto.EmailCodeSendDto;
import com.hisun.lemon.urm.service.IEmailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @Desc: 认证模块
 * <AUTHOR>
 * @date 2019/01/14 15:44
 * @version V1.0
 */
@RefreshScope
@RestController
@RequestMapping("/mcc")
@Api(value = "认证模块", tags = "认证模块")
public class AuthController {

    private final static Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    private IEmailService emailService;

    @PostMapping("/emailCodeAuth")
    @ApiOperation(value = "邮箱验证码校验", notes = "emailCodeAuth")
    public GenericRspDTO<String> emailCodeAuth(@Valid @RequestBody GenericDTO<EmailAuthDto>  req) {
        logger.info("接收参数：{}", req);
        return emailService.emailCodeAuth(req);
    }

    @PostMapping("/emailCodeSend")
    @ApiOperation(value = "发送邮箱验证码", notes = "emailCodeAuth")
    public GenericRspDTO<NoBody> emailCodeSendAuth(@Valid @RequestBody GenericDTO<EmailCodeSendDto>  req) {
        logger.info("接收参数：{}", req);
        return emailService.emailCodeSend(req);
    }

    @PostMapping("checkEmailIsExit")
    @ApiOperation(value = "检查邮箱是否存在", notes = "checkEmailIsExit")
    public GenericRspDTO<NoBody> checkEmailIsExit(@Valid @RequestBody GenericDTO<EmailCodeSendDto>  req) {
        logger.info("接收参数：{}", req);
        return emailService.checkEmailIsExit(req.getBody());
    }



}
