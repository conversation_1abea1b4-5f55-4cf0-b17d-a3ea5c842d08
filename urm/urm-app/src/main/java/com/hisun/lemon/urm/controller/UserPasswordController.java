package com.hisun.lemon.urm.controller;

import com.hisun.lemon.common.exception.ErrorMsgCode;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.framework.utils.RandomTemplete;
import com.hisun.lemon.urm.common.*;
import com.hisun.lemon.urm.constants.URMConstants;
import com.hisun.lemon.urm.constants.URMMessageCode;
import com.hisun.lemon.urm.dao.IQuesDao;
import com.hisun.lemon.urm.dao.IUrmSafeInfDao;
import com.hisun.lemon.urm.dto.*;
import com.hisun.lemon.urm.entity.MercOprInfoDO;
import com.hisun.lemon.urm.entity.UrmCprItfAuthDO;
import com.hisun.lemon.urm.entity.UrmSafeInfDO;
import com.hisun.lemon.urm.entity.UrmUserBasicInfDO;
import com.hisun.lemon.urm.service.IUserAuthenticationService;
import com.hisun.lemon.urm.service.IUserBasicInfService;
import com.hisun.lemon.urm.service.IUserPasswordService;
import com.hisun.lemon.urm.utils.RSAUtil2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.bouncycastle.util.encoders.Base64;
import org.bouncycastle.util.encoders.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static com.hisun.lemon.urm.utils.CipherProcessorUtils.*;

/**
 * 凭证管理
 *
 * <AUTHOR>
 * @GetMapping @PostMapping @PutMapping @DeleteMapping @PatchMapping
 * @date 2017年7月5日
 * @time 上午10:46:30
 */
@Api(tags = "UserPasswordController", description = "用户密码凭证管理")
@RestController
@RequestMapping("/urm")
public class UserPasswordController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(UserPasswordController.class);
    @Resource
    private IUserPasswordService passwordService;



    @Resource
    private IUserAuthenticationService authenticationService;

    @Resource
    private IUserBasicInfService basicInfService;

    @Resource
    private IUserAuthenticationService iUserAuthenticationService;


    @Resource
    private SensitiveDataHandle sensData;

    @Resource(name = "bindingTokenRandomTemplete")
    private RandomTemplete randomTemplate;

    @Resource
    private IUrmSafeInfDao safeInfDao;

    @Resource
    private CpiService cpi;

    @Resource
    private InvService inv;

    @Resource
    private MkmService mkm;

    @Resource
    private AcmService acm;

    @Resource
    private RiskCheck riskCheck;

    @Resource
    private IQuesDao quesDao;

    @ApiOperation(value = "用户登录密码修改", notes = "updLoginPwd")
    @ApiResponse(code = 200, message = "修改用户登录密码结果")
    @PutMapping("/loginpwd/update")
    public GenericRspDTO<NoBody> updLoginPwd(@Validated @RequestBody GenericDTO<ChangPwdDTO> changPwdDTO) {
        String loginId = changPwdDTO.getBody().getLoginId();
        String oldPwd = changPwdDTO.getBody().getOldPwd();
        String oldPwdRandom = changPwdDTO.getBody().getOldPwdRandom();
        String randomServer = randomTemplate.acquireOnce(URMConstants.PWD_RANDOM);
        int oldPwdLength = Hex.toHexString(Base64.decode(oldPwd)).length();
        if (JudgeUtils.equals(oldPwdLength, URMConstants.OLD_USER_PWD)) {
            String randomClient = decryptRSAEncryptedRandom(oldPwdRandom);
            oldPwd = decrypt3DESEncryptedPin(randomClient, randomServer, oldPwd);
        } else {
            oldPwd = convertRSAEncryptedPin(oldPwdRandom, randomServer, oldPwd, URMConstants.LOG_PWD_MODE);
        }
        String newPwd = changPwdDTO.getBody().getNewPwd();
        String newPwdRandom = changPwdDTO.getBody().getNewPwdRandom();
        newPwd = convertRSAEncryptedPin(newPwdRandom, randomServer, newPwd, URMConstants.LOG_PWD_MODE);
        passwordService.updLoginPwd(loginId, oldPwd, newPwd);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "用户忘记密码", notes = "forgetLoginPwd")
    @ApiResponse(code = 200, message = "忘记密码")
    @PostMapping("/loginpwd/forget")
    public GenericRspDTO<NoBody> forgetLoginPwd(@Validated @RequestBody GenericDTO<ForgetPwdDTO> forgetPwdDTO) {
        passwordService.forgetLoginPwd(forgetPwdDTO);

        return GenericRspDTO.newSuccessInstance();
    }


    @ApiOperation(value = "用户登录密码重置", notes = "resetLoginPwd")
    @ApiResponse(code = 200, message = "重置用户登录密码")
    @PutMapping("/loginpwd/reset")
    public GenericRspDTO<NoBody> resetLoginPwd(@Validated @RequestBody GenericDTO<ChangPwdDTO> changPwdDTO) {
        String loginId = changPwdDTO.getBody().getLoginId();
        String newPwd = changPwdDTO.getBody().getNewPwd();

        UrmSafeInfDO safeInfDO1 = safeInfDao.get(loginId);
        if (JudgeUtils.isNull(safeInfDO1)) {
            LemonException.throwLemonException(URMMessageCode.USR_NOT_EXIST);
        }
        if (JudgeUtils.equals(safeInfDO1.getOprTyp(), URMConstants.MER_ADMIN_TYP) && JudgeUtils.isBlank(LemonUtils.getLoginName())) {
            LemonException.throwLemonException(URMMessageCode.NOT_MERCHANT_OPERATOR);
        }

        if (JudgeUtils.notEquals(loginId, LemonUtils.getLoginName())) {
            if (JudgeUtils.isNotBlank(LemonUtils.getUserId()) && JudgeUtils.isNotBlank(LemonUtils.getLoginName())) {
                UrmSafeInfDO safeInfDO = safeInfDao.get(LemonUtils.getLoginName());
                if (JudgeUtils.isNull(safeInfDO) || JudgeUtils.notEquals(safeInfDO.getOprTyp(), URMConstants.MER_ADMIN_TYP)) {
                    LemonException.throwLemonException(ErrorMsgCode.FORBIDDEN_OPERATION);
                }
                if (JudgeUtils.equals(safeInfDO.getOprTyp(), URMConstants.MER_ADMIN_TYP)) {
                    Boolean isExist = false;
                    List<MercOprInfoDO> list = safeInfDao.queryMercOprInfoList(LemonUtils.getUserId(), null, null, URMConstants.MER_OPE_TYP);
                    for (MercOprInfoDO infoDO : list) {
                        if (JudgeUtils.equals(infoDO.getLoginId(), loginId)) {
                            isExist = true;
                        }
                    }
                    if (!isExist) {
                        return GenericRspDTO.newInstance(ErrorMsgCode.FORBIDDEN_OPERATION.getMsgCd());
                    }
                }
            }
        }
        String newPwdRandom = changPwdDTO.getBody().getNewPwdRandom();
        String randomServer = randomTemplate.acquireOnce(URMConstants.PWD_RANDOM);
        newPwd = convertRSAEncryptedPin(newPwdRandom, randomServer, newPwd, URMConstants.LOG_PWD_MODE);
        passwordService.resetLoginPwd(loginId, newPwd);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "用户支付密码修改", notes = "updPayPwd")
    @ApiResponse(code = 200, message = "重置用户支付密码结果")
    @PutMapping("/paypwd/update")
    public GenericRspDTO<NoBody> updPayPwd(@Validated @RequestBody GenericDTO<ChangPwdDTO> changPwdDTO) {
        String loginId = changPwdDTO.getBody().getLoginId();
        String oldPwd = changPwdDTO.getBody().getOldPwd();
        String oldPwdRandom = changPwdDTO.getBody().getOldPwdRandom();
        String newPwd = changPwdDTO.getBody().getNewPwd();
        String newPwdRandom = changPwdDTO.getBody().getNewPwdRandom();
        if (JudgeUtils.notEquals(LemonUtils.getSource(), URMConstants.INTERNAL_GATE)) {
            String randomServer = randomTemplate.acquireOnce(URMConstants.PWD_RANDOM);
            oldPwd = convertRSAEncryptedPin(oldPwdRandom, randomServer, oldPwd, URMConstants.PAY_PWD_MODE);
            newPwd = convertRSAEncryptedPin(newPwdRandom, randomServer, newPwd, URMConstants.PAY_PWD_MODE);
        } else {
            String oldRandomServer = decryptRSAEncryptedRandom(oldPwdRandom);
            oldPwd = convertRSAEncryptedPin(oldPwdRandom, oldRandomServer, oldPwd, URMConstants.PAY_PWD_MODE);
            String newRandomServer = decryptRSAEncryptedRandom(newPwdRandom);
            newPwd = convertRSAEncryptedPin(newPwdRandom, newRandomServer, newPwd, URMConstants.PAY_PWD_MODE);
        }
        UrmSafeInfDO safeInfDO = safeInfDao.get(loginId);
        if (JudgeUtils.isNull(safeInfDO)) {
            LemonException.throwLemonException(URMMessageCode.USR_NOT_EXIST);
        }
        String userId = safeInfDO.getUserId();
        passwordService.updPayPwd(userId, oldPwd, newPwd);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "用户支付密码重置", notes = "resetPayPwd")
    @ApiResponse(code = 200, message = "重置用户支付密码结果")
    @PutMapping("/paypwd/reset")
    public GenericRspDTO<NoBody> resetPayPwd(@Validated @RequestBody GenericDTO<ChangPwdDTO> changPwdDTO) {
        String loginId = changPwdDTO.getBody().getLoginId();
        String newPwd = changPwdDTO.getBody().getNewPwd();
        String newPwdRandom = changPwdDTO.getBody().getNewPwdRandom();
        String safeQues = changPwdDTO.getBody().getSafeQues();
        String safeAns = changPwdDTO.getBody().getSafeAns();
        String crdNo = changPwdDTO.getBody().getCrdNo();
        UrmSafeInfDO safeInfDO = safeInfDao.get(loginId);
        if (JudgeUtils.isNull(safeInfDO)) {
            LemonException.throwLemonException(URMMessageCode.USR_NOT_EXIST);
        }
        String userId = safeInfDO.getUserId();
        Boolean requiredCheck = false;
        Boolean crdExist = cpi.existBankCard(userId);
        logger.debug("CrdExist: " + crdExist);
        if (crdExist) {
            requiredCheck = crdExist;
        } else if (JudgeUtils.equals(LemonUtils.getSource(), URMConstants.INTERNAL_GATE)) {
            requiredCheck = true;
        } else {
            BigDecimal totBal = acm.queryAcTotBal(userId).add(mkm.queryMkmBal(userId)).add(inv.queryInvBal(userId));
            logger.debug("TotBal: " + totBal.toString());
            if (totBal.compareTo(URMConstants.BAL_STANDARD) > 0) {
                requiredCheck = true;
            }
        }
        if (requiredCheck) {
            if (JudgeUtils.isNotBlank(crdNo)) {
                if (!cpi.existBankCard(userId, crdNo)) {
                    LemonException.throwLemonException(URMMessageCode.BANK_CRD_CHECK_FAIL);
                }
            } else if (JudgeUtils.isNotNull(safeQues) && JudgeUtils.isNotNull(safeAns)) {
                //从IGW过来重置密码从问题列表找出对应问题
                if (JudgeUtils.equals(LemonUtils.getSource(), URMConstants.INTERNAL_GATE)) {
                    List<String> quesList = quesDao.findQuesList(Integer.valueOf(safeQues));
                    boolean flg = authenticationService.checkSafeQues(loginId, quesList, safeAns);
                    if (!flg) {
                        LemonException.throwLemonException(URMMessageCode.SAFE_QUES_CHECK_FAIL);
                    }
                }
            } else {
                LemonException.throwLemonException(URMMessageCode.NOT_IMMEDIATE_RESET_PAYPWD);
            }
        }

        if (JudgeUtils.notEquals(LemonUtils.getSource(), URMConstants.INTERNAL_GATE)) {
            String randomServer = randomTemplate.acquireOnce(URMConstants.PWD_RANDOM);
            newPwd = convertRSAEncryptedPin(newPwdRandom, randomServer, newPwd, URMConstants.PAY_PWD_MODE);
        } else {
            String newRandomServer = decryptRSAEncryptedRandom(newPwdRandom);
            newPwd = convertRSAEncryptedPin(newPwdRandom, newRandomServer, newPwd, URMConstants.PAY_PWD_MODE);
        }
        passwordService.resetPayPwd(userId, newPwd);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "修改用户密保信息", notes = "updatePwdSafeInf")
    @ApiResponse(code = 200, message = "修改用户密保信息")
    @PutMapping("/pwdsafeinf/update")
    public GenericRspDTO<NoBody> updatePwdSafeInf(@Validated @RequestBody GenericDTO<UserPwdSafeInfDTO> pwdSafeInfDTO) {
        String loginId = pwdSafeInfDTO.getBody().getLoginId();
        String payPwd = pwdSafeInfDTO.getBody().getPayPwd();
        String payPwdRandom = pwdSafeInfDTO.getBody().getPayPwdRandom();
        String randomServer = randomTemplate.acquireOnce(URMConstants.PWD_RANDOM);
        payPwd = convertRSAEncryptedPin(payPwdRandom, randomServer, payPwd, URMConstants.PAY_PWD_MODE);
        Map<String, Object> userInf = basicInfService.queryUserByLoginId(loginId);
        UrmUserBasicInfDO basicInfDO = (UrmUserBasicInfDO) userInf.get("basicInf");
        if (!authenticationService.checkPayPwd(basicInfDO.getUserId(), payPwd)) {
            LemonException.throwLemonException(URMMessageCode.PAY_PWD_CHECK_FAIL);
        }
        String safeMblNo = pwdSafeInfDTO.getBody().getSafeMblNO();
        String safeQues1 = pwdSafeInfDTO.getBody().getSafeQues1();
        String safeAns1 = pwdSafeInfDTO.getBody().getSafeAns1();
        if (JudgeUtils.isNotBlank(safeMblNo) && (JudgeUtils.isNotBlank(safeQues1) || JudgeUtils.isNotBlank(safeAns1))) {
            LemonException.throwBusinessException(URMMessageCode.SAFE_INF_IS_NULL);
        }
        passwordService.updPwdSafeInf(loginId, safeMblNo, safeQues1, safeAns1);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "商户密钥重置", notes = "resetCprTradingKey")
    @ApiResponse(code = 200, message = "商户密钥重置")
    @PostMapping("/cprkey/reset/{userId}")
    public GenericRspDTO<String> resetCprTradingKey(@Validated @PathVariable("userId") String userId,
                                                    GenericDTO<NoBody> genericDTO) {
        if (JudgeUtils.isBlank(userId)) {
            LemonException.throwLemonException(URMMessageCode.PARAM_IS_NULL);
        }
        basicInfService.queryUser(userId);
        String secretKey = passwordService.resetCprTradingKey(userId);
        return GenericRspDTO.newSuccessInstance(secretKey);
    }

    @ApiOperation(value = "支付密码后台重置", notes = "resetPayPwd")
    @ApiResponse(code = 200, message = "支付密码后台重置结果")
    @PutMapping("/paypwd/reset/{userId}")
    public GenericRspDTO<NoBody> resetRandomPayPwd(@Validated @PathVariable("userId") String userId,
                                                   GenericDTO<NoBody> genericDTO) {
        passwordService.resetRandomPayPwd(userId);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "登录密码后台重置", notes = "resetRandomLoginPwd")
    @ApiResponse(code = 200, message = "登录密码后台重置结果")
    @PutMapping("/loginpwd/reset/{loginId}")
    public GenericRspDTO<NoBody> resetRandomLoginPwd(@Validated @PathVariable("loginId") String loginId,
                                                     GenericDTO<NoBody> genericDTO) {
        passwordService.resetRandomLoginPwd(loginId);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "商户密钥查询", notes = "queryCprTradingKey")
    @ApiResponse(code = 200, message = "商户密钥查询")
    @GetMapping("/cprkey/{userId}")
    public GenericRspDTO<String> queryCprTradingKey(@Validated @PathVariable("userId") String userId,
                                                    GenericDTO<NoBody> genericDTO) {
        String channel = LemonUtils.getChannel();
        if((channel != null) && ("UPG".equals(channel))){
        }else{
            if (JudgeUtils.notEquals(LemonUtils.getUserId(), userId)) {
                LemonException.throwLemonException(ErrorMsgCode.FORBIDDEN_OPERATION);
            }
        }

        String itfNm = "";
        UrmCprItfAuthDO cprItfAuthDO;
        cprItfAuthDO = basicInfService.queryTradingPrivilege(userId, itfNm, URMConstants.CPR_KEY_VER);
        String secretKey = "";
        if (JudgeUtils.isNotNull(cprItfAuthDO)) {
            secretKey = cprItfAuthDO.getSecretKey();
        }
        return GenericRspDTO.newSuccessInstance(secretKey);
    }

    @ApiOperation(value = "手势密码设置(支持重置)", notes = "手势密码设置（支持重置）")
    @ApiResponse(code = 200, message = "Success")
    @PostMapping(value = "/users/setHandPwd")
    public GenericRspDTO<NoBody> setHandPwd(@Validated @RequestBody GenericDTO<UserHandLoginSetDTO> userHandLoginSetDTO) {
        String handPwd = userHandLoginSetDTO.getBody().getHandPwd();
        String handFlg = userHandLoginSetDTO.getBody().getHandFlg();
        String userId = LemonUtils.getUserId();
        String loginId = LemonUtils.getLoginName();
        logger.info("loginId_info1" + loginId);
        if(loginId == null || "".equals(loginId)){
            loginId = userHandLoginSetDTO.getBody().getLoginId();
            logger.info("loginId_info2" + loginId);

            UrmSafeInfDO safeInfDO = safeInfDao.get(loginId);
            if (JudgeUtils.isNull(safeInfDO)) {
                LemonException.throwLemonException(URMMessageCode.USR_NOT_EXIST);
            }
            userId = safeInfDO.getUserId();
        }
        String randomServer = randomTemplate.acquireOnce(URMConstants.PWD_RANDOM);
        boolean res = true;
        //检查用户状态
        riskCheck.checkUser(userId);
        if(URMConstants.HAND_FLG_S.equals(handFlg)){
            try {
                handPwd = RSAUtil2.decryptByKey(handPwd.getBytes("UTF-8"));
                if (handPwd == null || "".equals(handPwd)) {
                    throw new Exception("Decrypt error !");
                }
                if (randomServer == null || "".equals(randomServer)) {
                    throw new Exception("randomServer null !");
                }
                String []tmp = handPwd.split(",");
                handPwd = tmp[0];
                tmp[1] = tmp[1].substring(0, randomServer.length());
                logger.info("handPwd : " + handPwd + ", radom : " + tmp[1] + ", randomServer : " + randomServer);
                if(!tmp[1].equals(randomServer)){
                    throw new Exception("randomServer error !");
                }
                logger.info("RSA解密成功！");
            } catch (Exception e) {
                e.printStackTrace();
                logger.error("RSA解密失败：" + e.getMessage());
                LemonException.throwLemonException(URMMessageCode.RSA2_DECRYPT_FAILURE);
            }

            //查询用户是否未设置手势密码
            //if (JudgeUtils.equals(URMConstants.HAND_FLG_N, basicInfService.queryHandSet())) {
               // String userId = LemonUtils.getUserId();
                String handPwdDb = sensData.encryptSensitiveData(handPwd);
                passwordService.resetHandPwd(loginId, handPwdDb, handPwd);
           /* } else {
                LemonException.throwLemonException(URMMessageCode.HAND_PWD_EXCEPTION);
            }*/
        }
        /*else if(URMConstants.HAND_FLG_O.equals(handFlg)){
            res = passwordService.setHandPwdFlg(userId, URMConstants.HAND_FLG_O, URMConstants.HAND_FLG_C);
        }*/
        else if(URMConstants.HAND_FLG_C.equals(handFlg)){
            passwordService.setHandPwdFlg(userId, URMConstants.HAND_FLG_N, URMConstants.HAND_FLG_O);
        }else{
            LemonException.throwLemonException(URMMessageCode.HAND_FLG_ERROR);
        }
        if(!res){
            LemonException.throwLemonException(URMMessageCode.HAND_FLG_SET_ERROR);
        }
        return GenericRspDTO.newSuccessInstance();
    }


    @ApiOperation(value = "用户支付密码修改", notes = "updPayPwd")
    @ApiResponse(code = 200, message = "重置用户支付密码结果")
    @PutMapping("/paypwd/sea/update")
    public GenericRspDTO<NoBody> updPayPwdSea(@Validated @RequestBody GenericDTO<ChangPwdSeaDTO> changPwdDTO) {
        String loginId = changPwdDTO.getBody().getLoginId();
        String oldPwd = changPwdDTO.getBody().getOldPwd();
        String oldPwdRandom = changPwdDTO.getBody().getOldPwdRandom();
        String newPwd = changPwdDTO.getBody().getNewPwd();
        String newPwdRandom = changPwdDTO.getBody().getNewPwdRandom();
        String randomServer = changPwdDTO.getBody().getSeaRandom();
        //String oldRandomServer = decryptRSAEncryptedRandom(oldPwdRandom);
        //String newRandomServer = decryptRSAEncryptedRandom(newPwdRandom);
        logger.info("oldPwdRandom" + oldPwdRandom + ", randomServer:" + randomServer + ", oldPwd:" + oldPwd);
        oldPwd = convertRSAEncryptedPinNew(oldPwdRandom, randomServer, oldPwd, URMConstants.PAY_PWD_MODE);
        newPwd = convertRSAEncryptedPinNew(newPwdRandom, randomServer, newPwd, URMConstants.PAY_PWD_MODE);

        UrmSafeInfDO safeInfDO = safeInfDao.get(loginId);
        if (JudgeUtils.isNull(safeInfDO)) {
            LemonException.throwLemonException(URMMessageCode.USR_NOT_EXIST);
        }
        String userId = safeInfDO.getUserId();
        passwordService.updPayPwd(userId, oldPwd, newPwd);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "用户支付密码重置", notes = "resetPayPwd")
    @ApiResponse(code = 200, message = "重置用户支付密码结果")
    @PutMapping("/paypwd/sea/reset")
    public GenericRspDTO<NoBody> resetSeaPayPwd(@Validated @RequestBody GenericDTO<ChangPwdSeaDTO> changPwdDTO) {
        String loginId = changPwdDTO.getBody().getLoginId();
        String newPwd = changPwdDTO.getBody().getNewPwd();
        String newPwdRandom = changPwdDTO.getBody().getNewPwdRandom();
        String safeQues = changPwdDTO.getBody().getSafeQues();
        String safeAns = changPwdDTO.getBody().getSafeAns();
        String crdNo = changPwdDTO.getBody().getCrdNo();
        String randomServer = changPwdDTO.getBody().getSeaRandom();
        UrmSafeInfDO safeInfDO = safeInfDao.get(loginId);
        if (JudgeUtils.isNull(safeInfDO)) {
            LemonException.throwLemonException(URMMessageCode.USR_NOT_EXIST);
        }
        String userId = safeInfDO.getUserId();
        Boolean requiredCheck = false;
        Boolean crdExist = cpi.existBankCard(userId);
        logger.debug("CrdExist: " + crdExist);
        if (crdExist) {
            requiredCheck = crdExist;
        } else if (JudgeUtils.equals(LemonUtils.getSource(), URMConstants.INTERNAL_GATE)) {
            requiredCheck = true;
        } else {
            BigDecimal totBal = acm.queryAcTotBal(userId).add(mkm.queryMkmBal(userId)).add(inv.queryInvBal(userId));
            logger.debug("TotBal: " + totBal.toString());
            if (totBal.compareTo(URMConstants.BAL_STANDARD) > 0) {
                requiredCheck = true;
            }
        }
        if (requiredCheck) {
            if (JudgeUtils.isNotBlank(crdNo)) {
                if (!cpi.existBankCard(userId, crdNo)) {
                    LemonException.throwLemonException(URMMessageCode.BANK_CRD_CHECK_FAIL);
                }
            } else if (JudgeUtils.isNotNull(safeQues) && JudgeUtils.isNotNull(safeAns)) {
                //从IGW过来重置密码从问题列表找出对应问题
                if (JudgeUtils.equals(LemonUtils.getSource(), URMConstants.INTERNAL_GATE)) {
                    List<String> quesList = quesDao.findQuesList(Integer.valueOf(safeQues));
                    boolean flg = authenticationService.checkSafeQues(loginId, quesList, safeAns);
                    if (!flg) {
                        LemonException.throwLemonException(URMMessageCode.SAFE_QUES_CHECK_FAIL);
                    }
                }
            } else {
                LemonException.throwLemonException(URMMessageCode.NOT_IMMEDIATE_RESET_PAYPWD);
            }
        }
        newPwd = convertRSAEncryptedPinNew(newPwdRandom, randomServer, newPwd, URMConstants.PAY_PWD_MODE);
        passwordService.resetPayPwd(userId, newPwd);
        return GenericRspDTO.newSuccessInstance();
    }
}


