package com.hisun.lemon.tam.constants;

public enum DmPlatformEnum {

    USDT_P_TRON("USDT-P-TRON","tron-nile","P","USDT"),
    USDT_F_TRON("USDT-F-TRON","tron-nile","F","USDT"),

    USDT_P_ETH("USDT-P-ETH","ethereum-sepolia","P","USDT"),
    USDT_F_ETH("USDT-F-ETH","ethereum-sepolia","F","USDT"),

    USDC_P_TRON("USDC-P-TRON","tron-nile","P","USDC"),
    USDC_F_TRON("USDC-F-TRON","tron-nile","F","USDC"),

    USDC_P_ETH("USDC-P-ETH","ethereum-sepolia","P","USDC"),
    USDC_F_ETH("USDC-F-ETH","ethereum-sepolia","F","USDC");

    private final String platformId;

    private final String network;

    private final String type;

    private final String ccy;

    private DmPlatformEnum(String platformId, String network, String type, String ccy) {
        this.platformId = platformId;
        this.network = network;
        this.type = type;
        this.ccy = ccy;
    }

    public String getPlatformId() {
        return platformId;
    }

    public String getNetwork() {
        return network;
    }

    public String getType() {
        return type;
    }

    public String getCcy() {
        return ccy;
    }
}
