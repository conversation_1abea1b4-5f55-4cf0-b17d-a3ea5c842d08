package com.hisun.lemon.tam.constants;

public class TamConstants {
	
	/**
	 * 生成转账订单号的前缀
	 */
	public static final String ORD_GEN_PRE="tamOrderNo";
    /**
     * 初始状态 
     */
	public static final String ORD_STS_U="U";
	/**
     * 转账待确认
     */
	public static final String ORD_STS_P="P";
	/**
     * 成功
     */
	public static final String ORD_STS_S="S";
	/**
     * 已退款
     */
	public static final String ORD_STS_R="R";
	/**
     * 失败
     */
	public static final String ORD_STS_F="F";
	/**
     * 超时
     */
	public static final String ORD_STS_EP="E";
	
	/**
	 * 交易类型：转账
	 */
	public static final String TX_TYPE_TRANSFER="03";
	
	/**
	 * 业务类型：转账--转账到账户
	 */
	public static final String BUS_TYPE_TRANSFER_U="0301";
	/**
	 * 业务类型：转账--转账到银行卡
	 */
	public static final String BUS_TYPE_TRANSFER_B="0302";
	/**
	 * 业务类型：转账--面对面收款
	 */
	public static final String BUS_TYPE_TRANSFER_F="0303";
	/**
	 * 业务类型：转账--商户转账到用户
	 */
	public static final String BUS_TYPE_M_TRANSFER_U="0304";

	/**
	 * 交易类型：数币转账
	 */
	public static final String TX_TYPE_DM_TRANSFER="DZ";

	/**
	 * 业务类型：数币站内转账
	 */
	public static final String BUS_TYPE_DM_TRANSFER="DZ01";

	/**
	 * 业务类型：数币链上转账
	 */
	public static final String BUS_TYPE_DM_TRANSFER02="DZ02";

	/**
	 * 交易类型：数币提现
	 */
	public static final String TX_TYPE_DM_WITHDRAW="DX";

	/**
	 * 业务类型：数币站内转账
	 */
	public static final String BUS_TYPE_DM_WITHDRAW="DX01";


	/**
	 * 业务类型：兑换
	 */
	public static final String EXCHANGE_ORDER = "DH01";
	
	/**
	 * 内部科目
	 * 其他应付款-暂收-收银台
	 */
	public static final String AC_ITEM_CSH_PAY="2241030001";

	// 数币-币种-收银台科目账户
	public static final String USDT_AC_ITEM_CSH_PAY = "3241030001";

	public static final String USDC_AC_ITEM_CSH_PAY = "3241030002";


	/**
	 * 内部科目
	 * 其他应付款-支付账户-现金账户
	 */
	public static final String AC_ITEM_PAY_BAL="2241010001";
	
	/**
	 * 内部科目
	 * 手续费收入-支付账户-转账
	 */
	public static final String AC_ITEM_TAM_PAY="6021010003";

	/**
	 * 内部科目
	 * 数币-USDT-手续费收入-支付账户-转账
	 */
	public static final String DM_USDT_AC_ITEM_TAM_PAY="7021010001";


	/**
	 * 内部科目
	 * 数币-USDC-手续费收入-支付账户-转账
	 */
	public static final String DM_USDC_AC_ITEM_TAM_PAY="7021010002";

	/**
	 * 数币链上的币种收款科目账户
	 */
	public static final String DM_USDT_SK_AC_NO="3331020001";
	public static final String DM_USDC_SK_AC_NO="3331020002";

	/**
	 * 数币提现的币种收款科目账户
	 */
	public static final String DM_USDT_WITHDRAW_SK_AC_NO="3331020003";
	public static final String DM_USDC_WITHDRAW_SK_AC_NO="**********";
	
	/**
	 * 内部科目
	 * 应付账款-待结算-批量付款
	 */
	public static final String AC_ITEM_TAM_PAYMENT="**********";
	
	/**
	 * 支付币种：USD 美元
	 */
	public static final String QP_PAY_CCY="USD";

	/**
	 * 基准币种：USDT
	 */
	public static final String USDT = "USDT";
	/**
	 * 透传消息模版编号
	 */
	public static final String SEND_MER_ORDER="********";
	
	/**
	 * 转账到银行卡消息模版编号
	 */
	public static final String SEND_MER_TO_BANK_ORDER="********";
	
	/**
	 * 转账消息模版编号
	 */
	public static final String SEND_MER_TO_USER_ORDER="********";

	/**
	 * 支付方式
	 */
	public static final String BUS_PAY_TYPE="********";


	/** 数币转法币 */
	public static final String DM_TO_FM = "S2F";
	/** 法币转数币 */
	public static final String FM_TO_DM = "F2S";
	/** 待审核 */
	public static final String PENDING = "PENDING";
	/** 初审中 */
	public static final String FIRST_AUDIT = "FIRST_AUDIT";
	/** 复核中 */
	public static final String SECOND_AUDIT = "SECOND_AUDIT";
	/** 审核通过 */
	public static final String APPROVED = "APPROVED";
	/** 拒绝 */
	public static final String REJECTED = "REJECTED";
	/** 成功 */
	public static final String SUCCESS = "SUCCESS";
	/** 失败 */
	public static final String FAILED = "FAILED";

	/** 调用cregis语言 */
	public static final String lang = "zh_CN";

	/** 平台数币本金账户 */
	public static final String P_TYPE = "P";
	/** 平台数币手续费账户 */
	public static final String F_TYPE = "F";

	/** 计费方式枚举值 百分比 */
	public static final String FEE_CALCULATE_PERCENT = "percent";
	/** 计费方式枚举值 固定金额 */
	public static final String FEE_CALCULATE_FIXED = "fixed";

}
