package com.hisun.lemon.tam.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 数币转账请求DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/22 16:03
 */
@ApiModel(value = "DmOutTransferOrderDTO", description = "数币转账请求DTO")
public class DmOutTransferOrderDTO {

    @ApiModelProperty(name = "付款方账号", value = "付款方账号",required = true)
    @NotEmpty(message = "TAM10007")
    private String userMblNo;

    /**
     * remark 备注
     */
    @ApiModelProperty(name = "remark", value = "备注")
    @Length(max = 25)
    private String remark;

    /**
     * busType 业务类型 用户 0301 银行卡 0302 面对面 0303 数币站内转账 DZ01 数币链上转账 DZ02
     */
    @ApiModelProperty(name = "busType", value = "业务类型 数币站内转账 DZ01 数币链上转账 DZ02")
    @Length(max = 4)
    private String busType = "DZ01";

    /**
     * mblNo 转账账户
     */
    @ApiModelProperty(name = "mblNo", value = "转账账户")
    private String mblNo;

    /**
     * mblAddress 收款账户钱包地址
     */
    @ApiModelProperty(name = "mblAddress", value = "收款账户钱包地址")
    @NotEmpty(message = "TAM10035")
    private String mblAddress;
    /**
     * 金额
     */
    @ApiModelProperty(name = "amount", value = "金额")
    @NotNull(message = "TAM10001")
    @Min(value = 0, message = "TAM10001")
    private BigDecimal amount;

    /**
     * 手续费
     */
    @ApiModelProperty(name = "amount", value = "手续费")
    @NotNull(message = "TAM10001")
    @Min(value = 0, message = "TAM10017")
    private BigDecimal fee;

    /**
     * orderCcy 币种
     */
    @ApiModelProperty(name = "orderCcy", value = "币种")
    @Length(max = 4)
    @NotEmpty(message = "TFM10005")
    private String orderCcy;

    @ApiModelProperty(name = "payPassword", value = "支付密码")
    @NotEmpty(message = "TAM10024")
    private String payPassword;

    @ApiModelProperty(name = "fileUrl", value = "数币链上转账交易材料")
    private List<String> fileUrl;

    public List<String> getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(List<String> fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getUserMblNo() {
        return userMblNo;
    }

    public void setUserMblNo(String userMblNo) {
        this.userMblNo = userMblNo;
    }

    public String getOrderCcy() {
        return orderCcy;
    }

    public String getPayPassword() {
        return payPassword;
    }

    public void setPayPassword(String payPassword) {
        this.payPassword = payPassword;
    }

    public void setOrderCcy(String orderCcy) {
        this.orderCcy = orderCcy;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getMblNo() {
        return mblNo;
    }

    public void setMblNo(String mblNo) {
        this.mblNo = mblNo;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public String getMblAddress() {
        return mblAddress;
    }

    public void setMblAddress(String mblAddress) {
        this.mblAddress = mblAddress;
    }
}
