package com.hisun.lemon.csh.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.acm.client.AccountManagementClient;
import com.hisun.lemon.acm.client.AccountingTreatmentClient;
import com.hisun.lemon.acm.constants.ACMConstants;
import com.hisun.lemon.acm.constants.CapTypEnum;
import com.hisun.lemon.acm.dto.AccountingHoldReqDTO;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.acm.dto.UserAccountFreezeDTO;
import com.hisun.lemon.bil.dto.CreateUserBillDTO;
import com.hisun.lemon.bil.dto.UpdateUserBillDTO;
import com.hisun.lemon.cmm.client.CmmServerClient;
import com.hisun.lemon.cmm.dto.CommonEncryptReqDTO;
import com.hisun.lemon.cmm.dto.CommonEncryptRspDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.StringUtils;
import com.hisun.lemon.cpi.client.*;
import com.hisun.lemon.cpi.dto.*;
import com.hisun.lemon.cpi.enums.CorpBusSubTyp;
import com.hisun.lemon.cpi.enums.CorpBusTyp;
import com.hisun.lemon.csh.component.AcmComponent;
import com.hisun.lemon.csh.component.OrderCommonComponent;
import com.hisun.lemon.csh.component.QuickPaymentComponent;
import com.hisun.lemon.csh.constants.CshConstants;
import com.hisun.lemon.csh.ctx.OrderContextHolder;
import com.hisun.lemon.csh.dao.IOrderDao;
import com.hisun.lemon.csh.dao.IPayJrnDao;
import com.hisun.lemon.csh.dao.ITransferOrderDao;
import com.hisun.lemon.csh.dto.HandleFailTranDTO;
import com.hisun.lemon.csh.dto.HandleFinanceDTO;
import com.hisun.lemon.csh.dto.cashier.*;
import com.hisun.lemon.csh.dto.order.ExchangeCoinDTO;
import com.hisun.lemon.csh.dto.order.NotifyResultDTO;
import com.hisun.lemon.csh.dto.payment.*;
import com.hisun.lemon.csh.dto.refund.RefundOrderDTO;
import com.hisun.lemon.csh.entity.OrderDO;
import com.hisun.lemon.csh.entity.PayJrnDO;
import com.hisun.lemon.csh.entity.TransferOrderDO;
import com.hisun.lemon.csh.enums.*;
import com.hisun.lemon.csh.mq.BillSyncHandler;
import com.hisun.lemon.csh.mq.MerchantTransferHandler;
import com.hisun.lemon.csh.mq.PaymentHandler;
import com.hisun.lemon.csh.service.IOrderService;
import com.hisun.lemon.csh.service.IPaytypeService;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.lock.DistributedLocker;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;
import com.hisun.lemon.mkm.res.dto.AutoRealeaseRspDTO;
import com.hisun.lemon.onr.client.NotifySendClient;
import com.hisun.lemon.onr.dto.NotifySendReqDTO;
import com.hisun.lemon.pwm.constants.PwmConstants;
import com.hisun.lemon.rsm.Constants;
import com.hisun.lemon.rsm.client.RiskCheckClient;
import com.hisun.lemon.rsm.dto.req.checkstatus.RiskCheckUserStatusReqDTO;
import com.hisun.lemon.rsm.dto.req.riskJrn.JrnReqDTO;
import com.hisun.lemon.tam.constants.TamConstants;
import com.hisun.lemon.tfm.dto.TradeFeeCaculateRspDTO;
import com.hisun.lemon.urm.client.UserAuthenticationClient;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.lemon.urm.dto.CheckPayPwdDTO;
//import com.hisun.lemon.urm.dto.CheckPayPwdSeaDTO;
import com.hisun.lemon.urm.dto.UserBasicInfDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public abstract class AbstractOrderService extends BaseService implements IOrderService {
	private static final Logger logger = LoggerFactory.getLogger(AbstractOrderService.class);

	protected String txType;
	protected String[] busTypes;

	public String getTxType() {
		return txType;
	}

	public String[] getBusTypes() {
		return busTypes;
	}

	@Resource
	protected OrderTransactionalService orderTransactionalService;

	@Resource
	BillSyncHandler billSyncHandler;
	@Resource
	protected FastpayClient fastPayClient;

	@Resource
	protected CardClient cardClient;

	@Resource
	protected DistributedLocker locker;

	@Resource
	protected OrderCommonComponent orderCommonComponent;

	@Resource
	QuickPaymentComponent quickPaymentComponent;

	@Resource
	protected AcmComponent acmComponent;

	@Resource
	protected RemittanceClient remittanceClient;

	@Resource
	protected UserBasicInfClient userBasicInfClient;

	@Resource
	protected EbankpayClient ebankpayClient;

	@Resource
	protected UserAuthenticationClient userAuthenticationClient;

	@Resource
	protected AccountingTreatmentClient accountingTreatmentClient;
	@Resource
	protected RiskCheckClient riskCheckClient;

	@Resource
	protected PaymentHandler paymentHandler;

	@Resource
	protected IPaytypeService paytypeService;

	@Resource
	RefundOrderServiceImpl refundOrderService;

	@Resource
	TamOrderServiceImpl tamOrderServiceImpl;

	@Resource
	ObjectMapper objectMapper;

	@Resource
	NotifySendClient notifySendClient;

	@Resource
	CmmServerClient cmmServerClient;

	@Resource
	BankClient bankClient;

	@Resource
	protected MerchantTransferHandler merchantTransferHandler;

	@Resource
	AccountManagementClient accountManagementClient;

	@Resource
	private IOrderDao orderDao;

	@Resource
	private ITransferOrderDao transferDao;

	@Resource
	private IPayJrnDao payJrnDao;



	public OrderTransactionalService getOrderTransactionalService() {
		return orderTransactionalService;
	}
	public void setOrderTransactionalService(OrderTransactionalService orderTransactionalService) {
		this.orderTransactionalService = orderTransactionalService;
	}

	public abstract boolean match(String txType);

	protected abstract void checkBeforeInitOrder(OrderDO orderDO);

	protected abstract void checkJrnWithBussiness(GenericDTO inputGenericDto);

	//创建账务处理列表,具体业务实现
	protected abstract List<AccountingReqDTO> createAccQueue(OrderDO orderDO,PayJrnDO payJrnDO);

	protected abstract void notifyBussiness(OrderDO orderDO,PayJrnDO payJrnDO);

	/**
	 * 创建收银订单，返回收银台数据
	 *
	 * @param initCashierDTO
	 * @return
	 */
	@Override
	public CashierViewDTO createCashier(GenericDTO<InitCashierDTO> initCashierDTO) {
        OrderDO orderDO=orderTransactionalService.queryOrderByBusNo(initCashierDTO.getBody().getExtOrderNo());
		if(JudgeUtils.isNotNull(orderDO)){
			LemonException.throwBusinessException("CSH20080");
		}
		orderDO=createOrder(initCashierDTO.getBody());
		checkBeforeInitOrder(orderDO);
		caculateFee(orderDO);
		//理财和转账不发折扣券
//		if(!StringUtils.startsWith(orderDO.getBusType(),TradeType.FINANC.getType())
//				&& !StringUtils.startsWith(orderDO.getBusType(),TradeType.TRANSFER.getType())){
//			giveDiscountCoupon(orderDO);
//		}
		orderTransactionalService.initOrder(orderDO);

        InitCashierDTO initCashierDTO1 = initCashierDTO.getBody();
        if(JudgeUtils.equals(orderDO.getTxType(), TradeType.CONSUME.getType())){
            //设置操作员
            if(JudgeUtils.isNotNull(initCashierDTO1.getExtMap())&&initCashierDTO1.getExtMap().isEmpty()
                    && JudgeUtils.isNotNull(initCashierDTO1.getLoginId())) {
                Map extMap = new HashMap();
                Map map = new HashMap();
                map.put("loginId", initCashierDTO1.getLoginId());
                extMap.put(TradeType.CONSUME.getType(), map);
                initCashierDTO.getBody().setExtMap(extMap);
            }

            //如果付款方不为空，则设置付款方手机
            if(JudgeUtils.isNull(initCashierDTO1.getExtInfo())){
                if(JudgeUtils.isNotBlank(initCashierDTO1.getPayerId())){
                    UserBasicInfDTO userBasicInfDTO = userBasicInfClient.queryUser(initCashierDTO1.getPayerId()).getBody();
                    if(JudgeUtils.isNotNull(userBasicInfDTO)){
                        initCashierDTO.getBody().setExtInfo(userBasicInfDTO.getMblNo()+"|");
                    }
                }
            }
        }

		orderCommonComponent.synchronizeBil(orderDO, OrderCommonComponent.CREATE_BIL,true, initCashierDTO.getBody().getExtInfo(),initCashierDTO.getBody().getExtMap());
		return createCashierView(orderDO);
	}

	protected OrderDO createOrder(InitCashierDTO initCashierDTO){
		OrderDO orderDO=new OrderDO();
		BeanUtils.copyProperties(orderDO, initCashierDTO);
		String ymd= DateTimeUtils.getCurrentDateStr();
		String orderNo= IdGenUtils.generateId(CshConstants.ORD_GEN_PRE + ymd, 15);
		orderDO.setOrderNo(ymd+orderNo);
		orderDO.setOrderTm(DateTimeUtils.getCurrentLocalDateTime());
		orderDO.setOrderStatus(OrderStatus.WAIT_PAY.getValue());

		if(JudgeUtils.isNotNull(initCashierDTO.getEffTm())){
			orderDO.setOrderExpTm(initCashierDTO.getEffTm());
		}else{
			orderDO.setOrderExpTm(DateTimeUtils.getCurrentLocalDateTime().plusDays(2));
		}
		orderDO.setBusOrderNo(initCashierDTO.getExtOrderNo());
		orderDO.setFndOrderNo("");
		orderDO.setPayeeId(initCashierDTO.getPayeeId());
		orderDO.setMercName(initCashierDTO.getPayeeName());
		orderDO.setOrderChannel(initCashierDTO.getSysChannel());
		orderDO.setBusPayType(initCashierDTO.getBusPaytype());
		orderDO.setBalAmt(new BigDecimal(0));
		orderDO.setCrdPayAmt(new BigDecimal(0));
		orderDO.setCouponAmt(new BigDecimal(0));
		orderDO.setCouponType("");
		orderDO.setInvAmt(new BigDecimal(0));
		orderDO.setLeftBalAmt(new BigDecimal(0));
		orderDO.setLeftCardAmt(new BigDecimal(0));
		orderDO.setLeftCouponAmt(new BigDecimal(0));
		orderDO.setLeftInvAmt(new BigDecimal(0));
		orderDO.setLeftTotalAmt(new BigDecimal(0));
		orderDO.setCcy(initCashierDTO.getOrderCcy());
		orderDO.setCapCorg(initCashierDTO.getCrdCorpOrg());
		orderDO.setRemark("");
		orderDO.setPayJrnNo("");
		orderDO.setPayerId(initCashierDTO.getPayerId());
		orderDO.setAppCnl(initCashierDTO.getAppCnl());
		orderDO.setAcTm(DateTimeUtils.getCurrentLocalDate());
		orderDO.setBusType(initCashierDTO.getBusType());
		orderDO.setRemark(initCashierDTO.getRemark());
		orderDO.setGoodsInfo(initCashierDTO.getGoodsDesc());
		String paytype=orderCommonComponent.getPayType(orderDO.getAppCnl(),orderDO.getBusType(),
				orderDO.getPayeeId(), orderDO.getBusPayType());
		//没有找到合适的支付方式
		if(!paytype.contains(CshConstants.YES)){
			throw  new LemonException("CSH20016");
		}
		orderDO.setPayType(paytype);

		return orderDO;

	}

	protected void caculateFee(OrderDO orderDO){
		GenericRspDTO<TradeFeeCaculateRspDTO> rspDTO= acmComponent.caculateFee(orderDO.getBusType(),orderDO.getOrderAmt(),orderDO.getCcy());
		TradeFeeCaculateRspDTO tradeFeeCaculateRspDTO=rspDTO.getBody();
		orderDO.setTotalAmt(tradeFeeCaculateRspDTO.getTradeToalAmt());
		orderDO.setLeftTotalAmt(tradeFeeCaculateRspDTO.getTradeToalAmt());
		orderDO.setFee(tradeFeeCaculateRspDTO.getTradeFee());
		orderDO.setLeftFee(tradeFeeCaculateRspDTO.getTradeFee());
		String calculateMode= tradeFeeCaculateRspDTO.getCalculateMode();
		if(StringUtils.equals(calculateMode, "internal")){
			orderDO.setFeeFlag("IN");
		}else if(StringUtils.equals(calculateMode,"external")){
			orderDO.setFeeFlag("EX");
		}
	}

	protected  CashierViewDTO createCashierView(OrderDO orderDO){
		return orderCommonComponent.createCashierViewDTO(orderDO);
	}


	/**
	 * 生成支付流水，并使用余额+优惠支付
	 *
	 * @param genericOrderDTO
	 */
	@Override
	public PaymentResultDTO createJrnAndBalPay(GenericDTO<BalPaymentDTO> genericOrderDTO) {
		BalPaymentDTO orderDTO = genericOrderDTO.getBody();
		BigDecimal balAmt=orderDTO.getBalAmt();
		String couponType=orderDTO.getCouponType();
		OrderDO dbOrder=checkBeforeJrn(genericOrderDTO,orderDTO.getOrderNo(), orderDTO.getPayeeId(),balAmt, orderDTO.getCouponAmt(), couponType, null);
		//原始订单付款方
		String oriPayerId = dbOrder.getPayerId();
		PayJrnDO payJrnDO=saveJrn(dbOrder, balAmt, Integer.valueOf(CshConstants.CRD_TYPE_NONE), BigDecimal.valueOf(0),
				couponType, orderDTO.getCouponAmt(),orderDTO.getCouponNo(),orderDTO.getOrderCcy());


		//验证支付密码
		checkPayPassword(orderDTO.getPayPassword(),true,orderDTO.getValidateRandom(), "");
        //实时风控
		dbOrder.setCcy(payJrnDO.getCcy());
        dbOrder.setCrdPayType(String.valueOf(payJrnDO.getCrdPayType()));
        dbOrder.setCrdPayAmt(payJrnDO.getCrdPayAmt());
		dbOrder.setBalAmt(orderDTO.getBalAmt());
		dbOrder.setCouponAmt(orderDTO.getCouponAmt());
		dbOrder.setCouponType(orderDTO.getCouponType());
		riskControl(dbOrder);
		//账务处理
		handleAccAndMkm(dbOrder,payJrnDO);

		//更新订单和流水
		OrderDO orderDO=updateJrnAndOrder(payJrnDO);

        prepareNotifyOrder(dbOrder,orderDO);
		//风控累计
		riskAmount(orderDO);

		//同步账单
		orderDO.setPayeeId(dbOrder.getPayeeId());
		boolean payerIdExist = true;
		if(JudgeUtils.isBlank(oriPayerId)){
			payerIdExist = false;
		}

		//付款方手机
        String extInfo = null;
        UserBasicInfDTO userBasicInfDTO = userBasicInfClient.queryUser(dbOrder.getPayerId()).getBody();
        if(JudgeUtils.isNotNull(userBasicInfDTO)){
            extInfo = userBasicInfDTO.getMblNo()+"|";
        }
		orderCommonComponent.synchronizeBil(orderDO,OrderCommonComponent.UPD_BIL,payerIdExist, extInfo,null);

		//通知业务模块
		notifyBussiness(orderDO, payJrnDO);
		return createPaymentResultDTO(orderDO.getOrderNo(),dbOrder.getOrderAmt(),dbOrder.getBusOrderNo(),dbOrder.getGoodsInfo(),orderDO.getPayeeId(),dbOrder.getTxType());
	}


	/**
	 * 生成支付流水，并使用快捷+优惠支付
	 *
	 * @param genericOrderDTO
	 */
	@Override
	public PaymentResultDTO createJrnAndQpPay(GenericDTO<QpPaymentDTO> genericOrderDTO) {
		QpPaymentDTO orderDTO = genericOrderDTO.getBody();
		//获取手机号
		String mblNo=orderDTO.getMblNo();

		//直付标识
		String needBind = orderDTO.getNeedBind();

		BigDecimal crdPayAmt=orderDTO.getCrdPayAmt();
		String couponType=orderDTO.getCouponType();

		OrderDO dbOrder=checkBeforeJrn(genericOrderDTO,orderDTO.getOrderNo(),orderDTO.getPayeeId(),BigDecimal.valueOf(0),
				orderDTO.getCouponAmt(),couponType,crdPayAmt);

		dbOrder.setCapCorg(orderDTO.getCrdCorpOrg());
		dbOrder.setCapCorgNo(orderDTO.getCrdCorpOrg());
		dbOrder.setCapCardType(orderDTO.getCrdAcTyp());
		dbOrder.setLast4CardNo(orderDTO.getLastCardNo());
		//原订单付款方
		String oriPayerId = dbOrder.getPayerId();
		PayJrnDO payJrnDO=saveJrn(dbOrder, BigDecimal.valueOf(0), Integer.valueOf(CshConstants.CRD_TYPE_QP),
				crdPayAmt, couponType, orderDTO.getCouponAmt(),orderDTO.getCouponNo(),orderDTO.getOrderCcy());

		//直付不需要校验直付密码
		if(JudgeUtils.equals(needBind,"0")){
			//验证支付密码
			checkPayPassword(orderDTO.getPayPassword(),true,orderDTO.getValidateRandom(), "");
		}

		//实时风控
		dbOrder.setCcy(payJrnDO.getCcy());
		dbOrder.setCrdPayType(String.valueOf(payJrnDO.getCrdPayType()));
		dbOrder.setCrdPayAmt(payJrnDO.getCrdPayAmt());
		riskControl(dbOrder);

		FastpayReqDTO fastpayReqDTO = new FastpayReqDTO();

		//获取协议号
		String agrNo = orderDTO.getAgrNo();
		//签约支付
		if(JudgeUtils.equals(needBind, "1")) {
			//调用快捷支付签约接口
			CardBindReqDTO cardBindReqDTO = new CardBindReqDTO();
			cardBindReqDTO.setMblNo(mblNo);
			cardBindReqDTO.setCrdCorpOrg(orderDTO.getCrdCorpOrg());
			cardBindReqDTO.setCrdAcTyp(orderDTO.getCrdAcTyp());
			cardBindReqDTO.setCrdUsrNm(orderDTO.getCrdUserNm());
			cardBindReqDTO.setJrnNo(orderDTO.getJrnNo());
			cardBindReqDTO.setChkNo(orderDTO.getChkNo());
            cardBindReqDTO.setSmsToken(orderDTO.getSmsJrnNo());

			GenericDTO<CardBindReqDTO> genericCardBindReqDTO = new GenericDTO<>();
			genericCardBindReqDTO.setBody(cardBindReqDTO);
			GenericRspDTO<CardBindRspDTO> genericCardBindRspDTO = fastPayClient.bindCard(genericCardBindReqDTO);
			CardBindRspDTO cardBindRspDTO = genericCardBindRspDTO.getBody();
			agrNo = cardBindRspDTO.getArgNo();
			if(!JudgeUtils.isSuccess(genericCardBindRspDTO.getMsgCd())) {
				throw new LemonException("CSH20028");
			}

		}

		//调用资金能力快捷付款接口
		fastpayReqDTO.setArgNo(agrNo);
		fastpayReqDTO.setBnkPsnFlg(CshConstants.QP_BNK_PSN_FLAG);
		fastpayReqDTO.setUserTyp(CshConstants.QP_USER_TYPE);
		fastpayReqDTO.setCorpBusTyp(CorpBusTyp.FASTPAY);
		fastpayReqDTO.setCorpBusSubTyp(CorpBusSubTyp.FASTPAY);
		fastpayReqDTO.setOrdCcy(CshConstants.QP_PAY_CCY);
		//快捷支付的补款金额
		fastpayReqDTO.setOrdAmt(orderDTO.getCrdPayAmt());
		fastpayReqDTO.setSmsFlag(orderDTO.getSmsFlag());
		fastpayReqDTO.setSmsJrnNo(orderDTO.getSmsJrnNo());
		fastpayReqDTO.setReqOrdNo(dbOrder.getOrderNo());
		fastpayReqDTO.setReqOrdDt(DateTimeUtils.getCurrentLocalDate());
		fastpayReqDTO.setReqOrdTm(DateTimeUtils.getCurrentLocalTime());

		GenericDTO<FastpayReqDTO> genericFastpayReqDTO = new GenericDTO<>();
		genericFastpayReqDTO.setBody(fastpayReqDTO);
		//调用快捷付款接口
		GenericRspDTO<FastpayRspDTO> genericFastpayRspDTO = fastPayClient.fastpay(genericFastpayReqDTO);
		if(!JudgeUtils.isSuccess(genericFastpayRspDTO.getMsgCd())) {
			throw new LemonException(genericFastpayRspDTO.getMsgCd());
		}

		FastpayRspDTO fastpayRspDTO=genericFastpayRspDTO.getBody();

		payJrnDO.setFndOrderNo(fastpayRspDTO.getOrdNo());
		//账务处理
		handleAccAndMkm(dbOrder, payJrnDO);


		//更新订单和流水
		OrderDO orderDO=updateJrnAndOrder(payJrnDO);

        prepareNotifyOrder(dbOrder,orderDO);

        orderDO.setPayeeId(dbOrder.getPayeeId());
        orderDO.setCapCorg(orderDTO.getCrdCorpOrg());
        orderDO.setLast4CardNo(orderDTO.getLastCardNo());

		//风控累计
		riskAmount(orderDO);

		boolean payerIdExist = true;
		if(JudgeUtils.isBlank(oriPayerId)){
			payerIdExist = false;
		}
		//付款方手机后四位
		if(JudgeUtils.isNotNull(mblNo)) {
            mblNo = mblNo + "|";
        }

		//同步账单
		orderCommonComponent.synchronizeBil(orderDO, OrderCommonComponent.UPD_BIL,payerIdExist, mblNo,null);
		//通知业务模块
		notifyBussiness(orderDO,payJrnDO);
		return createPaymentResultDTO(orderDO.getOrderNo(),dbOrder.getOrderAmt(),dbOrder.getBusOrderNo(),dbOrder.getGoodsInfo(),orderDO.getPayeeId(),dbOrder.getTxType());

	}

	/**
	 * 生成支付流水，并使用页面+优惠支付
	 *
	 * @param genericOrderDTO
	 */
	@Override
	public PaymentResultDTO createJrnAndPagePay(GenericDTO<PpPaymentDTO> genericOrderDTO) {
		PpPaymentDTO orderDTO = genericOrderDTO.getBody();
		BigDecimal crdPayAmt=orderDTO.getCrdPayAmt();
		String couponType=orderDTO.getCouponType();

		OrderDO dbOrder=checkBeforeJrn(genericOrderDTO,orderDTO.getOrderNo(), orderDTO.getPayeeId(),new BigDecimal(0), orderDTO.getCouponAmt(), couponType, crdPayAmt);

		saveJrn(dbOrder, new BigDecimal(0), orderDTO.getCrdPayType(),crdPayAmt, couponType,
				orderDTO.getCouponAmt(),orderDTO.getCouponNo(),orderDTO.getOrderCcy());

		//验证支付密码
		checkPayPassword(orderDTO.getPayPassword(),true,orderDTO.getValidateRandom(), "");
		return createPaymentResultDTO(dbOrder.getOrderNo(),dbOrder.getOrderAmt(),
				dbOrder.getBusOrderNo(),dbOrder.getGoodsInfo(),dbOrder.getPayeeId(),dbOrder.getTxType());
	}

	/**
	 *
	 * @param paymentDTO
	 * @return
	 */
	public BackstageViewDTO backstagePay(GenericDTO<InitBackstageDTO> paymentDTO) {
		InitBackstageDTO backstagePaymentDTO=paymentDTO.getBody();
		String rutCorgNo = backstagePaymentDTO.getRutCorgNo();
		String ymd=DateTimeUtils.getCurrentDateStr();
		String orderNo= IdGenUtils.generateId(CshConstants.ORD_GEN_PRE + ymd, 15);
		orderNo=ymd+orderNo;
		BigDecimal orderAmt=backstagePaymentDTO.getOrderAmt();

		OrderDO orderDO=new OrderDO();
		orderDO.setOrderNo(orderNo);
		orderDO.setOrderTm(DateTimeUtils.getCurrentLocalDateTime());
		orderDO.setOrderStatus(OrderStatus.PRE_PAY.getValue());

		if(JudgeUtils.isNotNull(backstagePaymentDTO.getEffTm())){
			orderDO.setOrderExpTm(backstagePaymentDTO.getEffTm());
		}else{
			orderDO.setOrderExpTm(DateTimeUtils.getCurrentLocalDateTime().plusDays(2));
		}

		orderDO.setBusOrderNo(backstagePaymentDTO.getExtOrderNo());
		orderDO.setFndOrderNo("");
		orderDO.setPayeeId(backstagePaymentDTO.getPayeeId());
		orderDO.setOrderChannel(backstagePaymentDTO.getSysChannel());
		orderDO.setCcy(CshConstants.QP_PAY_CCY);
		orderDO.setBusPayType(backstagePaymentDTO.getBusPaytype());
		orderDO.setBalAmt(BigDecimal.valueOf(0));
		orderDO.setMercName(backstagePaymentDTO.getPayeeName());
		orderDO.setLeftBalAmt(new BigDecimal(0));

		orderDO.setLeftInvAmt(new BigDecimal(0));
		orderDO.setLeftTotalAmt(new BigDecimal(0));
		orderDO.setGoodsInfo(backstagePaymentDTO.getGoodsDesc());
		orderDO.setRemark("");
		orderDO.setPayJrnNo("");
		orderDO.setSysChannel(CshConstants.SYS_CHANNEL_APP);
		orderDO.setAppCnl(backstagePaymentDTO.getAppCnl());
		orderDO.setAcTm(DateTimeUtils.getCurrentLocalDate());
		orderDO.setBusType(backstagePaymentDTO.getBusType());
		orderDO.setTxType(backstagePaymentDTO.getTxType());
		orderDO.setOrderAmt(backstagePaymentDTO.getOrderAmt());
		orderDO.setCrdPayType(CshConstants.CRD_TYPE_NB);
		//设置支付资金合作机构
		orderDO.setCapCorgNo(rutCorgNo);
		String paytype=orderCommonComponent.getPayType(backstagePaymentDTO.getAppCnl(),backstagePaymentDTO.getBusType(),
				backstagePaymentDTO.getPayeeId(), backstagePaymentDTO.getBusPaytype());
		//没有找到合适的支付方式
		if(paytype.indexOf(CshConstants.YES)<0){
			throw new LemonException("CSH20016");
		}
		orderDO.setPayType(paytype);

		checkBeforeInitOrder(orderDO);

		caculateFee(orderDO);
		if(!StringUtils.startsWith(orderDO.getBusType(), TradeType.FINANC.getType())
				&& JudgeUtils.equals(rutCorgNo,CshConstants.PAY_TYPE_SEA)){
			acmComponent.giveDiscountCoupon(orderDO);
		}
		Map coupons=acmComponent.queryCoupons(orderDO.getPayerId(), orderDO.getPayeeId(),orderDO.getOrderNo(),orderDO.getTxType(),orderDO.getOrderAmt());
		//配额
		QuotaDTO quotaDTO=orderCommonComponent.makeQuota(orderAmt, coupons, StringUtils.equals(paytype.substring(2, 3), CshConstants.YES));

		if(!StringUtils.equals(quotaDTO.getType(), CouponType.H_COUPON.getType())){
			orderDO.setCouponNo(quotaDTO.getCouponNo());
			orderDO.setCouponAmt(quotaDTO.getCouponAmt());
			orderDO.setCouponType(quotaDTO.getType());
		}else{
			orderDO.setCouponNo("");
			orderDO.setCouponAmt(quotaDTO.getCouponAmt());
			orderDO.setCouponType(quotaDTO.getType());
		}
		orderDO.setLeftCouponAmt(orderDO.getCouponAmt());
		//校验支付方式
		orderCommonComponent.checkPayType(orderDO,paytype);
		orderDO.setPayType(paytype);

		orderDO.setCrdPayAmt(quotaDTO.getPayAmt());
		orderDO.setLeftCardAmt(quotaDTO.getPayAmt());
		orderDO.setJrnTxTm(DateTimeUtils.getCurrentLocalDateTime());
		orderDO.setPayJrnNo(LemonUtils.getRequestId());
		orderTransactionalService.initOrder(orderDO);

		riskControl(orderDO);

		saveJrn(orderDO, orderDO.getBalAmt(), Integer.valueOf(orderDO.getCrdPayType()),orderDO.getCrdPayAmt(),
				orderDO.getCouponType(), orderDO.getCouponAmt(),orderDO.getCouponNo(),backstagePaymentDTO.getOrderCcy() );

		//设置操作员
		Map extMap = null;
		if(JudgeUtils.equals(backstagePaymentDTO.getTxType(),TradeType.CONSUME.getType())){
			extMap = new HashMap();
			Map map = new HashMap();
			map.put("loginId", backstagePaymentDTO.getLoginId());
			extMap.put(TradeType.CONSUME.getType(), map);
		}

		orderCommonComponent.synchronizeBil(orderDO,OrderCommonComponent.CREATE_BIL,true, null,extMap);
		//调用资金能力下单
		EbankpayReqDTO userScanReqDTO=new EbankpayReqDTO();

		//获取个人企标识判断是用户扫码还是商户扫码
		String bnkPsnFlag = backstagePaymentDTO.getBnkPsnFlg();
		userScanReqDTO.setBnkPsnFlg(bnkPsnFlag);
		userScanReqDTO.setUserNo(orderDO.getPayerId());
		userScanReqDTO.setOpenId(backstagePaymentDTO.getOpenId());
		userScanReqDTO.setMerchantId(orderDO.getPayeeId());
		userScanReqDTO.setCorpBusTyp(CorpBusTyp.EBANKPAY);
		userScanReqDTO.setCrdCorpOrg(backstagePaymentDTO.getRutCorgNo());
		userScanReqDTO.setCrdAcTyp("U");
		//手机号
		userScanReqDTO.setMblNo(backstagePaymentDTO.getMblNo());
		if(JudgeUtils.equals(CshConstants.BNK_PSN_FLAG_USER,bnkPsnFlag)){
			userScanReqDTO.setCorpBusSubTyp(CorpBusSubTyp.EBANKPAY_USERSCAN);
			userScanReqDTO.setUserTyp("U");
		}else if(JudgeUtils.equals(CshConstants.BNK_PSN_FLAG_BUSINESS,bnkPsnFlag)){
			userScanReqDTO.setCorpBusSubTyp(CorpBusSubTyp.EBANKPAY_MERCSCAN);
			userScanReqDTO.setUserTyp("M");
		}else{
			userScanReqDTO.setUserTyp("");
		}
		//传递网银交易类型
		userScanReqDTO.setTradeType(backstagePaymentDTO.getTradeType());
		userScanReqDTO.setOrdAmt(orderDO.getCrdPayAmt());
		userScanReqDTO.setReqOrdNo(orderNo);
		userScanReqDTO.setOrdCcy(CshConstants.QP_PAY_CCY);
		userScanReqDTO.setReqOrdDt(DateTimeUtils.getCurrentLocalDate());
		userScanReqDTO.setReqOrdTm(DateTimeUtils.getCurrentLocalTime());
		userScanReqDTO.setAuthCode(backstagePaymentDTO.getAuthCode());
		userScanReqDTO.setBarCode(backstagePaymentDTO.getBarCode());
		//设置是否是无账户
		userScanReqDTO.setRegisterFlag(backstagePaymentDTO.getRegisterFlag());
		GenericDTO reqDto=new GenericDTO();
		reqDto.setBody(userScanReqDTO);
		GenericRspDTO<EbankpayRspDTO> rspDto=ebankpayClient.ebankpay(reqDto);
		if(!JudgeUtils.isSuccess(rspDto.getMsgCd())){
			//更新订单失败状态
			OrderDO updateOrder = new OrderDO();
			updateOrder.setOrderNo(orderDO.getOrderNo());
			updateOrder.setCapCardType(userScanReqDTO.getCrdAcTyp());
			updateOrder.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
			updateOrder.setOrderStatus(OrderStatus.FAIL.getValue());
			orderTransactionalService.updateOrder(updateOrder);
			prepareNotifyOrder(orderDO,updateOrder);
			updateOrder.setPayerId(null);
			orderCommonComponent.synchronizeBil(updateOrder,OrderCommonComponent.UPD_BIL,true, null,extMap);
			//核销折扣券
			if(StringUtils.equals(orderDO.getCouponType(),CouponType.D_COUPON.getType())
					&& JudgeUtils.isNotBlank(orderDO.getCouponNo())){
				paymentHandler.cancelMkmCouponGive(orderDO.getOrderNo(), orderDO.getCouponType());
			}

			LemonException.throwBusinessException(rspDto.getMsgCd());
		}
		EbankpayRspDTO ebankpayRspDTO = rspDto.getBody();
		BackstageViewDTO backstageViewDTO=new BackstageViewDTO();
		backstageViewDTO.setBalAmt(orderDO.getBalAmt());
		backstageViewDTO.setFeeAmt(orderDO.getFee());
		backstageViewDTO.setOrderNo(orderDO.getOrderNo());
		backstageViewDTO.setOrderAmt(orderDO.getOrderAmt());
		backstageViewDTO.setPayAmt(orderDO.getCrdPayAmt());
		backstageViewDTO.setResultMap(ebankpayRspDTO.getResultMap());
		//更新订单信息
		OrderDO updateOrder = new OrderDO();
		updateOrder.setOrderNo(orderDO.getOrderNo());
		updateOrder.setCapCorg(ebankpayRspDTO.getCrdCorpOrg());
		updateOrder.setCapCardType(userScanReqDTO.getCrdAcTyp());
		updateOrder.setFndOrderNo(ebankpayRspDTO.getOrdNo());
		updateOrder.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
		orderTransactionalService.updateOrder(updateOrder);
		return backstageViewDTO;
	}

	/**
	 * 线下汇款
	 *
	 * @param genericDTO
	 * @return
	 */
	@Override
	public OfflinePaymentResultDTO offlinePayment(GenericDTO<OfflinePaymentDTO> genericDTO) {
		OfflinePaymentDTO rechargePaymentDTO =genericDTO.getBody();

		//充值订单号
		String rechargeOrderNo = rechargePaymentDTO.getOrderNo();
		OrderDO orderDO = this.orderTransactionalService.getOrderDao().getByBusOrder(rechargeOrderNo);
		//原订单校验
		if(JudgeUtils.isNull(orderDO)){
			throw new LemonException("CSH20053");
		}

		//设置补款方式
		orderDO.setCrdPayType(CshConstants.CRD_TYPE_FL);
		//根据手续费扣费类型进行补款金额处理
		String feeFlag = orderDO.getFeeFlag();
		BigDecimal crdPayAmt = orderDO.getOrderAmt();
		if(JudgeUtils.equals(feeFlag,"EX")){
			crdPayAmt = crdPayAmt.add(orderDO.getFee());
		}
		orderDO.setCrdPayAmt(crdPayAmt);
		OrderDO dbOrder=checkBeforeJrn(genericDTO,orderDO.getOrderNo(), orderDO.getPayeeId()
				,new BigDecimal(0), orderDO.getCouponAmt(), CouponType.NONE.getType(), crdPayAmt);

		PayJrnDO payJrnDO=saveJrn(dbOrder, BigDecimal.valueOf(0), Integer.valueOf(CshConstants.CRD_TYPE_FL), orderDO.getOrderAmt(),
				CouponType.NONE.getType(), BigDecimal.valueOf(0),"",orderDO.getCcy());

		/*
		* 屏蔽P状态汇款订单
		 */
		try{
			riskControl(orderDO);
			//查询汇款充值个人信息
			GenericRspDTO<UserBasicInfDTO> genericUserBasicInfDTO = userBasicInfClient.queryUser(rechargePaymentDTO.getPayerId());
			UserBasicInfDTO userBasicInfDTO = genericUserBasicInfDTO.getBody();
			//汇款登记处理
			RemittanceReqDTO remittanceReqDTO = new RemittanceReqDTO();
			remittanceReqDTO.setBnkPsnFlg(CshConstants.QP_BNK_PSN_FLAG);
			remittanceReqDTO.setCorpBusTyp(CorpBusTyp.REMITTANCE);
			remittanceReqDTO.setCorpBusSubTyp(CorpBusSubTyp.REMITTANCE);
			remittanceReqDTO.setCrdCorpOrg(rechargePaymentDTO.getCrdCorpOrg());
			remittanceReqDTO.setCrdNoEnc(rechargePaymentDTO.getCrdNoEnc());
			remittanceReqDTO.setCrdAcTyp(rechargePaymentDTO.getCrdAcTyp());
			remittanceReqDTO.setIdNoEnc(userBasicInfDTO.getIdNo());
			//设置为补款金额
			remittanceReqDTO.setOrdAmt(crdPayAmt);
			remittanceReqDTO.setIdTyp(userBasicInfDTO.getIdType());
			if(JudgeUtils.isNull(orderDO.getCcy())){
				remittanceReqDTO.setOrdCcy(CshConstants.QP_PAY_CCY);
			}else{
				remittanceReqDTO.setOrdCcy(orderDO.getCcy());
			}

			remittanceReqDTO.setReqOrdDt(orderDO.getOrderTm().toLocalDate());
			remittanceReqDTO.setReqOrdTm(orderDO.getOrderTm().toLocalTime());
			remittanceReqDTO.setMblNo(userBasicInfDTO.getMblNo());
			remittanceReqDTO.setCrdUsrNm(userBasicInfDTO.getUsrNm());
			remittanceReqDTO.setReqOrdNo(orderDO.getOrderNo());
			remittanceReqDTO.setUserTyp(CshConstants.QP_USER_TYPE);
			remittanceReqDTO.setUserNo(rechargePaymentDTO.getPayerId());
			remittanceReqDTO.setPicUrl(rechargePaymentDTO.getCashRemittUrl());
			remittanceReqDTO.setRmk(rechargePaymentDTO.getRemark());
			GenericDTO genericRemittanceReqDTO = new GenericDTO();
			genericRemittanceReqDTO.setBody(remittanceReqDTO);
			GenericRspDTO<RemittanceRspDTO> genericRemittanceRspDTO = remittanceClient.remit(genericRemittanceReqDTO);
			RemittanceRspDTO remittanceRspDTO = genericRemittanceRspDTO.getBody();

			if (JudgeUtils.isNotSuccess(genericRemittanceRspDTO.getMsgCd())) {
				LemonException.throwBusinessException(genericRemittanceRspDTO.getMsgCd());
			}
			//更新收银订单表资金能力属性和补款金额
			dbOrder.setFndOrderNo(remittanceRspDTO.getOrdNo());
			dbOrder.setCrdPayAmt(crdPayAmt);
			dbOrder.setOrderStatus(OrderStatus.WAIT_AUDIT.getValue());
			//订单支付流水
			dbOrder.setPayJrnNo(payJrnDO.getPayJrnNo());
			this.orderTransactionalService.updateOrder(dbOrder);

			//审核提交，更新账单
			orderCommonComponent.synchronizeBil(dbOrder,OrderCommonComponent.UPD_BIL,true, rechargePaymentDTO.getRemark()+"|",null);
			OfflinePaymentResultDTO offlinePaymentResultDTO = new OfflinePaymentResultDTO();
			offlinePaymentResultDTO.setMblNo(userBasicInfDTO.getMblNo());
			offlinePaymentResultDTO.setCashierOrderNo(orderDO.getOrderNo());
			offlinePaymentResultDTO.setOrderCcy(orderDO.getCcy());
			offlinePaymentResultDTO.setOrderAmt(orderDO.getOrderAmt());
			offlinePaymentResultDTO.setRemittOrderNo(remittanceRspDTO.getOrdNo());
			offlinePaymentResultDTO.setOrderStatus(remittanceRspDTO.getOrdSts());
			offlinePaymentResultDTO.setPayerId(rechargePaymentDTO.getPayerId());
			offlinePaymentResultDTO.setOrderNo(rechargePaymentDTO.getOrderNo());

			return offlinePaymentResultDTO;

		}catch (LemonException le){
			//处理失败，更改为W状态
			dbOrder.setPayJrnNo(payJrnDO.getPayJrnNo());
			dbOrder.setOrderStatus(OrderStatus.WAIT_PAY.getValue());
			dbOrder.setCrdPayType("");
			dbOrder.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
			this.orderTransactionalService.updateOrder(dbOrder);
			orderCommonComponent.synchronizeBil(dbOrder,OrderCommonComponent.UPD_BIL,true, null,null);
			LemonException.throwBusinessException(le.getMsgCd());
		}

		return null;
	}

	/**
	 * 处理资金能力的回调通知
	 *
	 * @param genericNotifyResultDTO
	 */
	@Override
	public OrderDO handlePpNotice(GenericDTO<NotifyResultDTO> genericNotifyResultDTO) {
		//查询流水
		NotifyResultDTO notifyResultDTO = genericNotifyResultDTO.getBody();
		try{
			locker.lock("CSH_LOCK"+notifyResultDTO.getCshOrderNo(),18,22,
					()->{
						OrderDO orderDO = this.orderTransactionalService.queryOrderNotNone(notifyResultDTO.getCshOrderNo());

						if(JudgeUtils.equals(notifyResultDTO.getOrderStatus(),OrderStatus.SUCC.getValue())){
							if(StringUtils.equals(OrderStatus.SUCC.getValue(), orderDO.getOrderStatus())){
								return null;
							}else{

								if(!JudgeUtils.equalsAny(orderDO.getOrderStatus(),OrderStatus.WAIT_PAY.getValue(),OrderStatus.PRE_PAY.getValue(),
										OrderStatus.EXPIRE.getValue(),OrderStatus.FAIL.getValue(),OrderStatus.WAIT_AUDIT.getValue())){
									throw new LemonException("CSH10005");
								}else{
									//如果是汇款类型审核成功，设置审核备注
									if(JudgeUtils.equals(orderDO.getBusType(), BussinessType.RECHARGE_OFFLINE.getValue())){
										orderDO.setRemark(notifyResultDTO.getRemark());
									}
									//成功处理
									handleSuccess(notifyResultDTO.getFndOrderNo(),notifyResultDTO.getCrdPayAmt().setScale(2),orderDO);
                                }
							}
						}else if(JudgeUtils.equals(notifyResultDTO.getOrderStatus(),OrderStatus.FAIL.getValue())){
							//线下汇款登记审核失败通知
							if(JudgeUtils.equals(orderDO.getBusType(), PwmConstants.BUS_TYPE_RECHARGE_OFL)){
								//更新错误信息到订单和流水
								orderDO.setOrderStatus(OrderStatus.FAIL.getValue());
								orderDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
								//设置审核失败原由
								orderDO.setRemark(notifyResultDTO.getRemark());
								PayJrnDO payJrnDO = this.orderTransactionalService.queryJrnNotNone(orderDO.getPayJrnNo());

								payJrnDO.setJrnStatus(CshConstants.JRN_STS_F);
								payJrnDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());

								orderTransactionalService.updateJrnAndOrder(orderDO,payJrnDO);
								orderCommonComponent.synchronizeBil(orderDO,OrderCommonComponent.UPD_BIL,true, null,null);
								//调用外部模块，发送失败通知
								notifyBussiness(orderDO,payJrnDO);
								//按照交易类型判断，失败通知
							}else if(JudgeUtils.equals(orderDO.getTxType(),TradeType.CONSUME.getType())){
                                //更新订单失败状态
                                OrderDO updateOrder = new OrderDO();
                                updateOrder.setOrderNo(orderDO.getOrderNo());
								updateOrder.setBusType(orderDO.getBusType());
								updateOrder.setTxType(orderDO.getTxType());
								updateOrder.setOrderAmt(orderDO.getOrderAmt());
								updateOrder.setPayeeId(orderDO.getPayeeId());
								updateOrder.setMercName(orderDO.getMercName());
                                updateOrder.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
                                updateOrder.setOrderStatus(OrderStatus.FAIL.getValue());
                                orderTransactionalService.updateOrder(updateOrder);

                                orderCommonComponent.synchronizeBil(updateOrder,OrderCommonComponent.UPD_BIL,false, null,null);
                                //通知
								NotifySendReqDTO notifySendReqDTO = new NotifySendReqDTO();
								notifySendReqDTO.setPayUserId(orderDO.getPayerId());
								notifySendReqDTO.setPayAmt(notifyResultDTO.getCrdPayAmt());
								notifySendReqDTO.setFee(orderDO.getFee());
								notifySendReqDTO.setOnrOrderNo(orderDO.getBusOrderNo());
								notifySendReqDTO.setPayDt(DateTimeUtils.getCurrentLocalDate());
								notifySendReqDTO.setOrderSts(updateOrder.getOrderStatus());
								notifySendReqDTO.setPayTm(DateTimeUtils.getCurrentLocalTime());

								GenericDTO<NotifySendReqDTO> genericDTO = new GenericDTO<>();
								genericDTO.setBody(notifySendReqDTO);
								notifySendClient.notifyMerchant(genericDTO);
								logger.debug("扫码结果通知商户成功,{}",ObjectMapperHelper.writeValueAsString(objectMapper, genericDTO.getBody(), true));
                                //核销折扣券
                                if(StringUtils.equals(orderDO.getCouponType(),CouponType.D_COUPON.getType())
                                        && JudgeUtils.isNotBlank(orderDO.getCouponNo())){
                                    paymentHandler.cancelMkmCouponGive(orderDO.getOrderNo(), orderDO.getCouponType());
                                }
                            }

							//更新错误信息到订单和流水

							//调用外部模块，发送失败通知
							return null;
						}else{
						    //其他状态如P状态则不进行处理
                        }
						return null;
					});
		}catch (Exception e){
			throw LemonException.create(e);
		}

		return null;
	}

	@Override
	public AcledaPaymentResultDTO createOrderJrnAndAcledaQpPay(GenericDTO<AcledaQpPaymentDTO> genericDTO) {

		AcledaQpPaymentDTO acledaQpPaymentDTO = genericDTO.getBody();
		//资金路由
		String rutCorgNo = acledaQpPaymentDTO.getCrdCorpOrg();
		String ymd=DateTimeUtils.getCurrentDateStr();
		String orderNo= IdGenUtils.generateId(CshConstants.ORD_GEN_PRE + ymd, 15);
		orderNo=ymd+orderNo;
		BigDecimal orderAmt=acledaQpPaymentDTO.getCrdPayAmt();

		OrderDO orderDO=new OrderDO();
		orderDO.setOrderNo(orderNo);
		orderDO.setOrderTm(DateTimeUtils.getCurrentLocalDateTime());
		orderDO.setOrderStatus(OrderStatus.PRE_PAY.getValue());

		if(JudgeUtils.isNotNull(acledaQpPaymentDTO.getEffTm())){
			orderDO.setOrderExpTm(acledaQpPaymentDTO.getEffTm());
		}else{
			orderDO.setOrderExpTm(DateTimeUtils.getCurrentLocalDateTime().plusDays(2));
		}

		orderDO.setBusOrderNo(acledaQpPaymentDTO.getOrderNo());
		orderDO.setFndOrderNo("");
		orderDO.setPayeeId(acledaQpPaymentDTO.getPayeeId());
		orderDO.setOrderChannel(acledaQpPaymentDTO.getSysChannel());
		orderDO.setCcy(CshConstants.QP_PAY_CCY);
		orderDO.setBusPayType("");
		orderDO.setBalAmt(BigDecimal.valueOf(0));
		orderDO.setMercName("");
		orderDO.setLeftBalAmt(new BigDecimal(0));

		orderDO.setLeftInvAmt(new BigDecimal(0));
		orderDO.setLeftTotalAmt(new BigDecimal(0));
		orderDO.setGoodsInfo(acledaQpPaymentDTO.getGoodsDesc());
		orderDO.setRemark("");
		orderDO.setPayJrnNo("");
		orderDO.setSysChannel(CshConstants.SYS_CHANNEL_APP);
		orderDO.setAppCnl(acledaQpPaymentDTO.getAppCnl());
		orderDO.setAcTm(DateTimeUtils.getCurrentLocalDate());
		orderDO.setBusType(acledaQpPaymentDTO.getBusType());
		orderDO.setTxType(acledaQpPaymentDTO.getTxType());
		orderDO.setOrderAmt(orderAmt);
		orderDO.setCrdPayType(CshConstants.CRD_TYPE_QP);
		//设置支付资金合作机构
		orderDO.setCapCorgNo(rutCorgNo);
		String paytype=orderCommonComponent.getPayType(acledaQpPaymentDTO.getAppCnl(),acledaQpPaymentDTO.getBusType(),
				acledaQpPaymentDTO.getPayeeId(), null);
		//没有找到合适的支付方式
		if(paytype.indexOf(CshConstants.YES)<0){
			throw new LemonException("CSH20016");
		}
		orderDO.setPayType(paytype);

		checkBeforeInitOrder(orderDO);

		caculateFee(orderDO);
		orderDO.setLeftCouponAmt(orderDO.getCouponAmt());
		//校验支付方式
		orderCommonComponent.checkPayType(orderDO,paytype);
		orderDO.setPayType(paytype);

		orderDO.setCrdPayAmt(orderAmt);
		orderDO.setLeftCardAmt(orderAmt);
		orderDO.setJrnTxTm(DateTimeUtils.getCurrentLocalDateTime());
		orderDO.setPayJrnNo(LemonUtils.getRequestId());
		orderTransactionalService.initOrder(orderDO);

		//风控
		riskControl(orderDO);

		saveJrn(orderDO, orderDO.getBalAmt(), Integer.valueOf(orderDO.getCrdPayType()),orderDO.getCrdPayAmt(),
				orderDO.getCouponType(), orderDO.getCouponAmt(),orderDO.getCouponNo(),acledaQpPaymentDTO.getOrdCcy());

		orderCommonComponent.synchronizeBil(orderDO,OrderCommonComponent.CREATE_BIL,true, null,null);
		//调用ACLEDA快捷下单
		GenericDTO repDto = new GenericDTO<>();
		FastpayAcledaReqDTO fastpayAcledaReqDTO = new FastpayAcledaReqDTO();
		repDto.setBody(fastpayAcledaReqDTO);
		GenericRspDTO<FastpayAcledaRspDTO> rspDto = fastPayClient.fastpayAcleda(repDto);

		if(!JudgeUtils.isSuccess(rspDto.getMsgCd())){
			//更新订单失败状态
			OrderDO updateOrder = new OrderDO();
			updateOrder.setOrderNo(orderDO.getOrderNo());
			updateOrder.setCapCardType(acledaQpPaymentDTO.getCrdAcTyp());
			updateOrder.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
			updateOrder.setOrderStatus(OrderStatus.FAIL.getValue());
			orderTransactionalService.updateOrder(updateOrder);
			prepareNotifyOrder(orderDO,updateOrder);
			updateOrder.setPayerId(acledaQpPaymentDTO.getPayerId());
			orderCommonComponent.synchronizeBil(updateOrder,OrderCommonComponent.UPD_BIL,true, null,null);
			LemonException.throwBusinessException(rspDto.getMsgCd());
		}
		FastpayAcledaRspDTO fastpayAcledaRspDTO = rspDto.getBody();
		AcledaPaymentResultDTO acledaPaymentResultDTO = new AcledaPaymentResultDTO();
		//设置返回值
		String sessionId = fastpayAcledaRspDTO.getSessionId();
		String paymentTokenId = fastpayAcledaRspDTO.getPaymentTokenId();
		acledaPaymentResultDTO.setPaymentTokenId(paymentTokenId);
		acledaPaymentResultDTO.setSessionId(sessionId);
		acledaPaymentResultDTO.setBusOrderNo(orderDO.getBusOrderNo());
        acledaPaymentResultDTO.setFee(orderDO.getFee());
        acledaPaymentResultDTO.setFeeFlag(orderDO.getFeeFlag());
        acledaPaymentResultDTO.setOrderNo(orderDO.getOrderNo());
		//更新订单信息
		OrderDO updateOrder = new OrderDO();
		updateOrder.setOrderNo(orderDO.getOrderNo());
		updateOrder.setCapCorg(acledaQpPaymentDTO.getCrdCorpOrg());
		updateOrder.setCapCardType(acledaQpPaymentDTO.getCrdAcTyp());
		updateOrder.setFndOrderNo(fastpayAcledaRspDTO.getOrdNo());
		updateOrder.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
		orderTransactionalService.updateOrder(updateOrder);
		return acledaPaymentResultDTO;

	}

	@Override
	public AcledaTransferRspDTO createAcledaOrder(GenericDTO<AcledaTransferReqDTO> genericDTO){
		AcledaTransferReqDTO acledaTransferReqDTO = genericDTO.getBody();
		//资金路由
		String rutCorgNo = acledaTransferReqDTO.getCrdCorpOrg();
		String ymd=DateTimeUtils.getCurrentDateStr();
		String orderNo= IdGenUtils.generateId(CshConstants.ORD_GEN_PRE + ymd, 15);
		orderNo=ymd+orderNo;
		BigDecimal orderAmt=acledaTransferReqDTO.getCrdPayAmt();

		OrderDO orderDO=new OrderDO();
		orderDO.setOrderNo(orderNo);
		orderDO.setOrderTm(DateTimeUtils.getCurrentLocalDateTime());
		orderDO.setOrderStatus(OrderStatus.PRE_PAY.getValue());

		if(JudgeUtils.isNotNull(acledaTransferReqDTO.getEffTm())){
			orderDO.setOrderExpTm(acledaTransferReqDTO.getEffTm());
		}else{
			orderDO.setOrderExpTm(DateTimeUtils.getCurrentLocalDateTime().plusDays(2));
		}

		orderDO.setBusOrderNo(acledaTransferReqDTO.getBusOrderNo());
		orderDO.setFndOrderNo("");
		orderDO.setPayeeId(acledaTransferReqDTO.getPayeeId());
		orderDO.setOrderChannel(acledaTransferReqDTO.getSysChannel());
		orderDO.setCcy(CshConstants.QP_PAY_CCY);
		orderDO.setBusPayType("");
		orderDO.setBalAmt(BigDecimal.valueOf(0));
		orderDO.setMercName("");
		orderDO.setLeftBalAmt(new BigDecimal(0));

		orderDO.setLeftInvAmt(new BigDecimal(0));
		orderDO.setLeftTotalAmt(new BigDecimal(0));
		orderDO.setGoodsInfo(acledaTransferReqDTO.getGoodsDesc());
		orderDO.setRemark("");
		orderDO.setPayJrnNo("");
		orderDO.setSysChannel(CshConstants.SYS_CHANNEL_APP);
		orderDO.setAppCnl(acledaTransferReqDTO.getAppCnl());
		orderDO.setAcTm(DateTimeUtils.getCurrentLocalDate());
		orderDO.setBusType(acledaTransferReqDTO.getBusType());
		orderDO.setTxType(acledaTransferReqDTO.getTxType());
		orderDO.setOrderAmt(orderAmt);
		orderDO.setCrdPayType(CshConstants.CRD_TYPE_QP);
		//设置支付资金合作机构
		orderDO.setCapCorgNo(rutCorgNo);
		String paytype=orderCommonComponent.getPayType(acledaTransferReqDTO.getAppCnl(),acledaTransferReqDTO.getBusType(),
				acledaTransferReqDTO.getPayeeId(), null);
		//没有找到合适的支付方式
		if(paytype.indexOf(CshConstants.YES)<0){
			throw new LemonException("CSH20016");
		}
		orderDO.setPayType(paytype);

		checkBeforeInitOrder(orderDO);

		caculateFee(orderDO);
		orderDO.setLeftCouponAmt(orderDO.getCouponAmt());
		//校验支付方式
		orderCommonComponent.checkPayType(orderDO,paytype);
		orderDO.setPayType(paytype);

		orderDO.setCrdPayAmt(orderAmt);
		orderDO.setLeftCardAmt(orderAmt);
		orderDO.setJrnTxTm(DateTimeUtils.getCurrentLocalDateTime());
		orderDO.setPayJrnNo(LemonUtils.getRequestId());
		orderTransactionalService.initOrder(orderDO);

		//风控
		riskControl(orderDO);

		//调用资金能力与acleda银行交互获取交易ID和支付ID
		GenericDTO<TransferAcledaReqDTO> generictransferDTO = new GenericDTO<>();
		TransferAcledaReqDTO transferAcledaReqDTO = new TransferAcledaReqDTO();
		transferAcledaReqDTO.setCorpBusTyp(CorpBusTyp.WITHDRAW);
		transferAcledaReqDTO.setCorpBusSubTyp(CorpBusSubTyp.CARD_WITHDRAW);
		transferAcledaReqDTO.setDirection(String.valueOf(acledaTransferReqDTO.getDirection()));
		transferAcledaReqDTO.setOrdAmt(acledaTransferReqDTO.getCrdPayAmt());
		//设置收银台订单号
		transferAcledaReqDTO.setReqOrdNo(orderDO.getOrderNo());
		transferAcledaReqDTO.setUserId(acledaTransferReqDTO.getPayerId());
		transferAcledaReqDTO.setReqOrdNo(orderDO.getOrderNo());
		transferAcledaReqDTO.setRmk(orderDO.getGoodsInfo());
		generictransferDTO.setBody(transferAcledaReqDTO);
		GenericRspDTO<TransferAcledaRspDTO> transferAcledaRspDTO = bankClient.transferAcledaOpen(generictransferDTO);
		TransferAcledaRspDTO transferAcledaResult = transferAcledaRspDTO.getBody();

		//账务处理
//		借：其他应付款-支付账户-现金账户    102
//		贷：其他应付款-暂收-收银台          102
		AccountingReqDTO userAccountReqDTO = null; // 用户现金账户账务对象
		AccountingReqDTO cshItemReqDTO = null; // 暂收收银台账务对象
		String balCapType = CapTypEnum.CAP_TYP_CASH.getCapTyp();
		String acmJrnNo = IdGenUtils.generateIdWithDate(CshConstants.ORD_GEN_PRE, 14);
		acmJrnNo = orderDO.getBusType() + acmJrnNo;
		List<AccountingReqDTO> backUserList = new ArrayList();
		logger.info("转账到银行卡======================账务处理" + acmJrnNo);
		// 借：其他应付款-支付账户-现金账户    102
		 userAccountReqDTO= acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), acmJrnNo,
				orderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, transferAcledaReqDTO.getOrdAmt(), null, ACMConstants.ITM_AC_TYP,
				balCapType, ACMConstants.AC_D_FLG, AcItem.O_BAL.getValue(),  AcItem.O_BAL.getValue(), null,
				null, null, "转账到ACLEDA银行卡");
		//贷：其他应付款-暂收-收银台          102
		cshItemReqDTO = acmComponent.createAccountingReqDTO(orderDO.getOrderNo(), acmJrnNo,
				orderDO.getTxType(), ACMConstants.ACCOUNTING_NOMARL, transferAcledaReqDTO.getOrdAmt(), null, ACMConstants.ITM_AC_TYP,
				balCapType, ACMConstants.AC_C_FLG, AcItem.O_CSH.getValue(),
				AcItem.O_CSH.getValue(), null, null, null, "转账到银行卡");
		backUserList.add(cshItemReqDTO);
		backUserList.add(userAccountReqDTO);
		acmComponent.requestAc(backUserList);

		//设置返回值
		AcledaTransferRspDTO acledaTransferRspDTO = new AcledaTransferRspDTO();
		BeanUtils.copyProperties(acledaTransferRspDTO,transferAcledaResult);
		AcledaTransferRspDTO.AccountReqDTO accountReqDTO1 = acledaTransferRspDTO.new AccountReqDTO();
		AcledaTransferRspDTO.AccountReqDTO accountReqDTO2 = acledaTransferRspDTO.new AccountReqDTO();
		BeanUtils.copyProperties(accountReqDTO1,userAccountReqDTO);
		BeanUtils.copyProperties(accountReqDTO2,cshItemReqDTO);
		List<AcledaTransferRspDTO.AccountReqDTO> accountReqDTOList = new ArrayList<>();
		accountReqDTOList.add(accountReqDTO1);
		accountReqDTOList.add(accountReqDTO2);
		acledaTransferRspDTO.setAccountReqList(accountReqDTOList);
		acledaTransferRspDTO.setFee(orderDO.getFee());
		acledaTransferRspDTO.setFeeFlag(orderDO.getFeeFlag());
		acledaTransferRspDTO.setOrderNo(orderDO.getOrderNo());
		acledaTransferRspDTO.setSessionId(transferAcledaResult.getSessionId());
		acledaTransferRspDTO.setPaymentTokenId(transferAcledaResult.getPaymentTokenId());
		return acledaTransferRspDTO;

	}


	protected PaymentResultDTO createPaymentResultDTO(String orderNo,BigDecimal orderAmt,String busOrderNo,String desc,String payeeId,String txType){
		PaymentResultDTO paymentResultDTO=new PaymentResultDTO();
		paymentResultDTO.setOrderAmt(orderAmt);
		paymentResultDTO.setBusOrderNo(busOrderNo);
		paymentResultDTO.setOrderNo(orderNo);
		paymentResultDTO.setGoodsDesc(desc);

		if(JudgeUtils.isNotBlank(txType) && JudgeUtils.equals(txType,TradeType.CONSUME.getType())){
            try{
                UserBasicInfDTO userBasicInfDTO = userBasicInfClient.queryUser(payeeId).getBody();
                if(JudgeUtils.isNotNull(userBasicInfDTO)){
                    paymentResultDTO.setMerchantName(userBasicInfDTO.getMercName());
                    paymentResultDTO.setMblNo(userBasicInfDTO.getMblNo());
                }
            }catch (LemonException e){
                logger.error("订单号："+orderNo +",查询收款方id:"+payeeId+",信息失败" + e.getMsgCd());
            }
        }
		return  paymentResultDTO;
	}


	protected void checkPayPassword(String password,boolean check,String validateRandom, String seaRandom){
		checkPayPassword(LemonUtils.getUserId(),password,check,validateRandom, seaRandom);
	}

	protected void checkPayPassword(String userId,String password,boolean check,String validateRandom, String seaRandom){
		if(check){
			if(StringUtils.isBlank(password)){
				LemonException.throwBusinessException("CSH20076");
			}


			GenericRspDTO rspDTO = null;
			if(StringUtils.isBlank(userId)){
				userId = LemonUtils.getUserId();
			}
			if(seaRandom != null && !"".equals(seaRandom)){
//				CheckPayPwdSeaDTO checkPayPwdSeaDTO =  new CheckPayPwdSeaDTO();
//				checkPayPwdSeaDTO.setUserId(userId);
//				checkPayPwdSeaDTO.setPayPwdRandom(validateRandom);
//				checkPayPwdSeaDTO.setPayPwd(password);
//				checkPayPwdSeaDTO.setSeaRandom(seaRandom);
				GenericDTO ckPwdDto=new GenericDTO();
	//			ckPwdDto.setBody(checkPayPwdSeaDTO);
				rspDTO = userAuthenticationClient.checkPayPasswordSea(ckPwdDto);
			}else{
				CheckPayPwdDTO checkPayPwdDTO=new CheckPayPwdDTO();
//				checkPayPwdDTO.setUserId(userId);
//				checkPayPwdDTO.setPayPwdRandom(validateRandom);
				checkPayPwdDTO.setPayPwd(password);
				GenericDTO ckPwdDto=new GenericDTO();
				ckPwdDto.setBody(checkPayPwdDTO);
				rspDTO = userAuthenticationClient.checkPayPwd(ckPwdDto);
			}


			if(JudgeUtils.isNotSuccess(rspDTO.getMsgCd())){
				logger.error("验证支付密码失败："+rspDTO.getMsgCd());
				String msgCd=rspDTO.getMsgCd().substring(0, 3);
				if(JudgeUtils.equals(msgCd, "SYS"))
				{
					LemonException.throwBusinessException("CSH20103");
				}else{
					LemonException.throwBusinessException(rspDTO.getMsgCd());
				}

			}
		}
	}

	protected OrderDO checkBeforeJrn(GenericDTO dto,String orderNo,String payeeId,BigDecimal balAmt,BigDecimal couponAmt,String couponType,BigDecimal crdPayAmt){
		checkJrnWithBussiness(dto);
		BigDecimal usdCoupon=orderCommonComponent.computeCoupon(couponAmt, couponType);

		BigDecimal totalAmt=BigDecimal.valueOf(0);
		if(balAmt!=null){
			totalAmt=totalAmt.add(balAmt);
		}
		if(crdPayAmt!=null){
			totalAmt=totalAmt.add(crdPayAmt);
		}

		totalAmt=totalAmt.add(usdCoupon);

		if(totalAmt.compareTo(BigDecimal.valueOf(0))<=0){
			throw new LemonException("CSH20001");
		}
		if(usdCoupon.compareTo(totalAmt.multiply(BigDecimal.valueOf(CshConstants.USE_COUPON_RATE)))>0){
			if(StringUtils.equals(couponType,CouponType.H_COUPON.getType())){
				throw new LemonException("CSH20020");
			}
		}
		OrderDO dbOrderDO=orderTransactionalService.queryOrderNotNone(orderNo);
		BigDecimal dbTotalAmt = dbOrderDO.getTotalAmt();

		//判断订单状态
		String dbOrderStatus=dbOrderDO.getOrderStatus();
		//汇款审核失败
		boolean remitFalse = JudgeUtils.equals(dbOrderDO.getBusType(),PwmConstants.BUS_TYPE_RECHARGE_OFL) && StringUtils.equalsIgnoreCase(dbOrderStatus, OrderStatus.FAIL.getValue());
		//订单状态不为待支付
		if(!StringUtils.equalsIgnoreCase(dbOrderStatus, OrderStatus.WAIT_PAY.getValue())
				&& !StringUtils.equalsIgnoreCase(dbOrderStatus, OrderStatus.PRE_PAY.getValue())
				&& !remitFalse){
			throw new LemonException("CSH20002");
		}
		String data2 = ObjectMapperHelper.writeValueAsString(objectMapper, dbOrderDO, true);
		logger.debug("===0。0===原数据库数据:{}",data2);
		if(totalAmt.compareTo(dbTotalAmt)!=0){
			throw new LemonException("CSH20003");
		}

		if(dbOrderDO.getOrderExpTm().isBefore(DateTimeUtils.getCurrentLocalDateTime())){
			orderCommonComponent.handleOrderExp(dbOrderDO);
			LemonException.throwBusinessException("CSH20040");
		}

		if(StringUtils.isNoneBlank(payeeId) && !StringUtils.equals(payeeId, dbOrderDO.getPayeeId())){
			throw new LemonException("CSH20004");
		}

		return dbOrderDO;
	}

	protected PayJrnDO saveJrn(OrderDO dbOrderDO,BigDecimal balAmt,int crdPayType,BigDecimal crdAmt,
							   String couponType,BigDecimal couponAmt,String couponNo,String ccy){
		PayJrnDO payJrnDO=new PayJrnDO();
		payJrnDO.setBalAmt(balAmt);
		payJrnDO.setCouponAmt(couponAmt);
		payJrnDO.setCouponType(couponType);
		if(!StringUtils.equals(couponType,CouponType.H_COUPON.getType())
				&& !StringUtils.equals(couponType,CouponType.NONE.getType())){
			payJrnDO.setCouponNo(couponNo);
		}else{
			payJrnDO.setCouponNo(null);
		}
		payJrnDO.setInvAmt(new BigDecimal(0));
		payJrnDO.setCrdPayAmt(crdAmt);
		payJrnDO.setCrdPayType(crdPayType);
		payJrnDO.setPayJrnNo(LemonUtils.getRequestId());
		payJrnDO.setPayOrderNo(dbOrderDO.getOrderNo());
		payJrnDO.setJrnStatus(CshConstants.JRN_STS_U);
		payJrnDO.setBusOrderNo(dbOrderDO.getBusOrderNo());

		payJrnDO.setTxType(dbOrderDO.getTxType());
		payJrnDO.setOrderAmt(dbOrderDO.getOrderAmt());
		payJrnDO.setFndOrderNo("");
		payJrnDO.setTxTm(DateTimeUtils.getCurrentLocalDateTime());
		payJrnDO.setPayOrdTm(payJrnDO.getTxTm());
		if(StringUtils.isBlank(dbOrderDO.getPayerId())){
			payJrnDO.setUserId(LemonUtils.getUserId());
		}else{
			payJrnDO.setUserId(dbOrderDO.getPayerId());
		}

		if(StringUtils.isBlank(ccy)){
			payJrnDO.setCcy("USD");
		}else{
			payJrnDO.setCcy(ccy);
		}
        //填充同步更新订单时补款方式
        dbOrderDO.setCrdPayType(String.valueOf(crdPayType));

		if(StringUtils.equals(OrderStatus.PRE_PAY.getValue(),dbOrderDO.getOrderStatus())){
			logger.debug("仅初始化流水:{}",dbOrderDO.getOrderNo());
			orderTransactionalService.initJrn(null,payJrnDO);
		}else{
			logger.debug("初始化流水且同步账单:{}",dbOrderDO.getOrderNo());
			OrderDO updOrderDO1=new OrderDO();
			updOrderDO1.setOrderNo(dbOrderDO.getOrderNo());
			updOrderDO1.setOrderStatus(OrderStatus.PRE_PAY.getValue());
            updOrderDO1.setCapCorg(dbOrderDO.getCapCorg());
            updOrderDO1.setCapCardType(dbOrderDO.getCapCardType());
			boolean payerIdExist = true;
			if(JudgeUtils.isBlank(dbOrderDO.getPayerId())){
				payerIdExist = false;
			}
			updOrderDO1.setPayeeId(dbOrderDO.getPayeeId());
			updOrderDO1.setJrnTxTm(DateTimeUtils.getCurrentLocalDateTime());
			orderTransactionalService.initJrn(updOrderDO1,payJrnDO);
			prepareNotifyOrder(dbOrderDO, updOrderDO1);
			orderCommonComponent.synchronizeBil(updOrderDO1,OrderCommonComponent.UPD_BIL,payerIdExist, null,null);
		}
		return payJrnDO;
	}

	//账务与营销处理
	protected void handleAccAndMkm(OrderDO orderDO,PayJrnDO payJrnDO){
		String dataOrder = ObjectMapperHelper.writeValueAsString(objectMapper, orderDO, true);
		logger.debug("=======================账务和营销处理 params print start=======================");
		logger.debug("orderDO::"+dataOrder);
		String dataJrn = ObjectMapperHelper.writeValueAsString(objectMapper, payJrnDO, true);
		logger.debug("payJrnDO::"+dataJrn);
		logger.debug("=======================账务和营销处理 params print over=======================");
		List<AccountingReqDTO> accList=createAccQueue( orderDO, payJrnDO);

		BigDecimal dAmt = BigDecimal.ZERO;
		BigDecimal cAmt = BigDecimal.ZERO;
		for (AccountingReqDTO dto : accList) {
			if (JudgeUtils.isNotNull(dto)) {
				if (JudgeUtils.equals(dto.getDcFlg(), ACMConstants.AC_D_FLG)) {
					dAmt = dAmt.add(dto.getTxAmt());
				} else {
					cAmt = cAmt.add(dto.getTxAmt());
				}
			}
		}

		// 借贷平衡校验
		if (cAmt.compareTo(dAmt) != 0) {
			LemonException.throwBusinessException("CSH20052");
		}

		//消费营销权
		acmComponent.useCoupon(orderDO.getOrderNo(), payJrnDO.getCouponType(), payJrnDO.getCouponNo(),
				orderDO.getTotalAmt(), payJrnDO.getUserId(), orderDO.getPayeeId(),payJrnDO.getCouponAmt());

		GenericDTO<List<AccountingReqDTO>> userAccDto = new GenericDTO<>();
		userAccDto.setBody(accList);
		//生成科目收支明细
		GenericRspDTO<NoBody> accountingTreatment = accountingTreatmentClient.accountingTreatment(userAccDto);
		if(!JudgeUtils.isSuccess(accountingTreatment.getMsgCd())){
			logger.error("订单"+orderDO.getOrderNo()+"账务失败："+accountingTreatment.getMsgCd());
			paymentHandler.cancelMkmCoupon(orderDO.getOrderNo(),orderDO.getOrderAmt(),orderDO.getCouponType());
			LemonException.throwBusinessException(accountingTreatment.getMsgCd());
		}

		if(StringUtils.equals(orderDO.getTxType(), TradeType.CONSUME.getType())
				|| StringUtils.equals(orderDO.getTxType(),TradeType.PAYMENT.getType())){
			//登记商户手续费
			paymentHandler.registMerChantFee(orderDO);
		}
		//登记用户手续费
		if(orderDO.getFee().compareTo(BigDecimal.ZERO)>0){
			paymentHandler.registUserFee(orderDO);
		}

	}

	protected OrderDO updateJrnAndOrder(PayJrnDO payJrnDO){
		OrderDO updOrderDO=new OrderDO();
		updOrderDO.setOrderNo(payJrnDO.getPayOrderNo());
		updOrderDO.setTxType(payJrnDO.getTxType());
		updOrderDO.setPayJrnNo(payJrnDO.getPayJrnNo());
		updOrderDO.setCrdPayAmt(payJrnDO.getCrdPayAmt());
		updOrderDO.setOrderAmt(payJrnDO.getOrderAmt());
		if(JudgeUtils.isNotNull(payJrnDO.getCrdPayType())){
			updOrderDO.setCrdPayType(String.valueOf(payJrnDO.getCrdPayType()));
		}
		updOrderDO.setBalAmt(payJrnDO.getBalAmt());
		updOrderDO.setLeftBalAmt(payJrnDO.getBalAmt());
		updOrderDO.setCouponAmt(payJrnDO.getCouponAmt());
		updOrderDO.setCouponType(payJrnDO.getCouponType());
		updOrderDO.setLeftCouponAmt(payJrnDO.getCouponAmt());
		updOrderDO.setCouponNo(payJrnDO.getCouponNo());
		updOrderDO.setLeftCardAmt(payJrnDO.getCrdPayAmt());
		updOrderDO.setCrdPayType(payJrnDO.getCrdPayType() + "");
		updOrderDO.setOrderStatus(OrderStatus.SUCC.getValue());
		updOrderDO.setAcTm(LemonUtils.getAccDate());
		updOrderDO.setCcy(payJrnDO.getCcy());
		updOrderDO.setOrderSuccTm(DateTimeUtils.getCurrentLocalDateTime());
		updOrderDO.setFndOrderNo(payJrnDO.getFndOrderNo());
		PayJrnDO updPayJrnDO =new PayJrnDO();
		updPayJrnDO.setPayJrnNo(updOrderDO.getPayJrnNo());
		updPayJrnDO.setFndOrderNo(payJrnDO.getFndOrderNo());
		updPayJrnDO.setJrnStatus(CshConstants.JRN_STS_S);
		updPayJrnDO.setPayOrdTm(DateTimeUtils.getCurrentLocalDateTime());
		orderTransactionalService.updateJrnAndOrder(updOrderDO,updPayJrnDO);
		return updOrderDO;
	}

	//实时风控
	protected void riskControl(OrderDO orderDO){
		List<JrnReqDTO> jrnReqDTOList = new ArrayList<>();
		String busType = orderDO.getBusType();

		JrnReqDTO jrnReqDTO=new JrnReqDTO();
		jrnReqDTO.setCcy(orderDO.getCcy());
		jrnReqDTO.setPayCrdNo(null);

		if(StringUtils.isNotBlank(orderDO.getPayeeId())){
			jrnReqDTO.setStlUserId(orderDO.getPayeeId());
			if(StringUtils.equals(orderDO.getBusType(), "0302")){
				jrnReqDTO.setStlUserTyp("02");
			}else{
				jrnReqDTO.setStlUserTyp("01");
			}
		}

		jrnReqDTO.setTxCnl(CshConstants.RSK_CNL_APP);
		jrnReqDTO.setPayUserTyp("01");

		if(JudgeUtils.isBlank(orderDO.getPayerId())){
		    if(JudgeUtils.equals(orderDO.getTxType(),PwmConstants.TX_TYPE_RECHANGE)){
                jrnReqDTO.setPayUserId(LemonUtils.getUserId());
            }
        }else{
            jrnReqDTO.setPayUserId(orderDO.getPayerId());
        }

		jrnReqDTO.setTxAmt(orderDO.getOrderAmt());
		if(JudgeUtils.equalsAny(busType, BussinessType.RECHARGE_HALL.getValue(),BussinessType.RECHARGE_OFFLINE.getValue())) {
			//免密支付:pswFlg=1
			jrnReqDTO.addTxData("pswFlg=1");
		}else{
			//非免密:pswFlg=0
			jrnReqDTO.addTxData("pswFlg=0");
		}
		jrnReqDTO.setTxOrdNo(orderDO.getOrderNo());
		jrnReqDTO.setTxJrnNo(LemonUtils.getRequestId());
		jrnReqDTO.setTxSts("0");
		jrnReqDTO.setTxDate(DateTimeUtils.getCurrentLocalDate());
		jrnReqDTO.setTxTime(DateTimeUtils.getCurrentLocalTime());
		jrnReqDTO.setTxTyp(orderDO.getTxType());
		//补款方式
		String crdPayType = orderDO.getCrdPayType();

		//无补款
		if(JudgeUtils.equals(crdPayType,CshConstants.CRD_TYPE_NONE) && orderDO.getCrdPayAmt().compareTo(BigDecimal.valueOf(0)) == 0) {
			//无优惠 账户余额
			if (JudgeUtils.isNotNull(orderDO.getBalAmt()) && orderDO.getBalAmt().compareTo(BigDecimal.valueOf(0)) > 0) {
				JrnReqDTO accJrnReqDTO=new JrnReqDTO();
				BeanUtils.copyProperties(accJrnReqDTO,jrnReqDTO);
				accJrnReqDTO.setPayTyp(Constants.PAY_TYP_ACCOUNT);
				accJrnReqDTO.setTxAmt(orderDO.getBalAmt());
				logger.debug("风控余额金额：" + orderDO.getBalAmt());
				jrnReqDTOList.add(accJrnReqDTO);
			}
			//海币优惠
			if (JudgeUtils.isNotNull(orderDO.getCouponAmt()) && orderDO.getCouponAmt().compareTo(BigDecimal.valueOf(0)) > 0) {
				if(JudgeUtils.equals(orderDO.getCouponType(),CouponType.H_COUPON.getType())){
					JrnReqDTO seaJrnReqDTO=new JrnReqDTO();
					BeanUtils.copyProperties(seaJrnReqDTO,jrnReqDTO);
					seaJrnReqDTO.setPayTyp(Constants.PAY_TYP_SEATEL);
					seaJrnReqDTO.setTxAmt(orderDO.getCouponAmt().divide(BigDecimal.valueOf(CshConstants.M_USD_RATE)));
					logger.debug("风控海币优惠金额：" + orderDO.getCouponAmt().divide(BigDecimal.valueOf(CshConstants.M_USD_RATE)));
					jrnReqDTOList.add(seaJrnReqDTO);
				}
			}
		}
		//有补款
		else{
			switch (crdPayType) {
				//快捷
				case CshConstants.CRD_TYPE_QP:
					JrnReqDTO qpJrnReqDTO=new JrnReqDTO();
					BeanUtils.copyProperties(qpJrnReqDTO,jrnReqDTO);
					qpJrnReqDTO.setPayTyp(Constants.PAY_TYP_QUICK_PAY);
					qpJrnReqDTO.setTxAmt(orderDO.getCrdPayAmt());
					//快捷设置银行卡号
					QpPaymentDTO requestDto=(QpPaymentDTO)OrderContextHolder.getRequestDto();
					String needBind = requestDto.getNeedBind();
					String cardNo="";
					if(JudgeUtils.equals(needBind,"0")){
						GenericDTO<CommonEncryptReqDTO> reqDTO = new GenericDTO<>();
						CommonEncryptReqDTO req = new CommonEncryptReqDTO();
						req.setData(requestDto.getCrdNoEnc());
						req.setType("decrypt");
						reqDTO.setBody(req);
						GenericRspDTO<CommonEncryptRspDTO> resp = cmmServerClient.encrypt(reqDTO);
						if(JudgeUtils.isNotSuccess(resp.getMsgCd())){
							LemonException.throwBusinessException(resp.getMsgCd());
						}
						CommonEncryptRspDTO commonEncryptRspDTO = resp.getBody();
						cardNo = commonEncryptRspDTO.getData();
					}else if(JudgeUtils.equals(needBind,"1")){
						cardNo = requestDto.getCardNo();
					}

					qpJrnReqDTO.setPayCrdNo(cardNo);
					jrnReqDTOList.add(qpJrnReqDTO);
					break;
				//网银
				case CshConstants.CRD_TYPE_NB:
					JrnReqDTO ebankJrnReqDTO = new JrnReqDTO();
					BeanUtils.copyProperties(ebankJrnReqDTO, jrnReqDTO);
					//获取支付路径机构
					String rutCorgNo = orderDO.getCapCorgNo();
					logger.debug("网银支付合作机构:" + rutCorgNo);
					if(JudgeUtils.isBlank(rutCorgNo)){
						break;
					}
					if(JudgeUtils.equals(rutCorgNo,CshConstants.PAY_TYPE_WX)){
						ebankJrnReqDTO.setPayTyp(Constants.PAY_TYP_WECHAT);
					}else if(JudgeUtils.equals(rutCorgNo,CshConstants.PAY_TYPE_ALI)){
						ebankJrnReqDTO.setPayTyp(Constants.PAY_TYP_ALIPAY);
					}else if(JudgeUtils.equals(rutCorgNo,CshConstants.PAY_TYPE_ICBC)){
						ebankJrnReqDTO.setPayTyp(Constants.PAY_TYP_QUICK_PAY);
					}else if(JudgeUtils.equals(rutCorgNo,CshConstants.PAY_TYPE_BEST)){
						ebankJrnReqDTO.setPayTyp(Constants.PAY_TYP_BESTPAY);
					}else{

					}
					ebankJrnReqDTO.setStlUserId(orderDO.getPayeeId());
					ebankJrnReqDTO.setTxAmt(orderDO.getCrdPayAmt());
					ebankJrnReqDTO.setStlUserTyp(Constants.ID_TYP_USER_NO);
					logger.debug("网银交易风控金额:" + orderDO.getCrdPayAmt());
					jrnReqDTOList.add(ebankJrnReqDTO);
					break;
				//线下转账
				case CshConstants.CRD_TYPE_FL:
					JrnReqDTO offlieJrnReqDTO=new JrnReqDTO();
					BeanUtils.copyProperties(offlieJrnReqDTO,jrnReqDTO);
					offlieJrnReqDTO.setPayTyp(Constants.PAY_TYP_OFFLINE);
					offlieJrnReqDTO.setTxAmt(orderDO.getCrdPayAmt());
					logger.debug("线下汇款交易风控金额:" + orderDO.getCrdPayAmt());
					jrnReqDTOList.add(offlieJrnReqDTO);
					break;
				default:
					break;
			}
		}
		//全部类型需要再添加
		JrnReqDTO allJrnReqDTO=new JrnReqDTO();
		BeanUtils.copyProperties(allJrnReqDTO,jrnReqDTO);
		allJrnReqDTO.setPayTyp(Constants.PAY_TYP_ALL);
		allJrnReqDTO.setTxAmt(orderDO.getTotalAmt());
		jrnReqDTOList.add(allJrnReqDTO);

		GenericRspDTO<Map<String, String>> rspDto = riskCheckClient.batchCheck(jrnReqDTOList);
		if(JudgeUtils.isNotSuccess(rspDto.getMsgCd())){
			Map<String,String> jrnRspMap = rspDto.getBody();
			logger.error("订单"+orderDO.getOrderNo()+"实时风控检查失败:"+rspDto.getMsgCd());
			String throwErrCode=null;
			if(JudgeUtils.isNotNull(jrnRspMap)&& JudgeUtils.isNotEmpty(jrnRspMap)){
				for (Map.Entry<String, String> entry : jrnRspMap.entrySet()) {
					if(JudgeUtils.isNull(throwErrCode)){
						throwErrCode=entry.getValue();
					}
					logger.error(entry.getKey() + "--->" + entry.getValue());
				}
			}
			if(JudgeUtils.isNotNull(throwErrCode)){
				logger.error("抛出异常：{}",throwErrCode);
				LemonException.throwBusinessException(throwErrCode);
			}else{
				LemonException.throwBusinessException(rspDto.getMsgCd());
			}
		}

	}
	//风控累计
	protected void riskAmount(OrderDO orderDO) {
		List<JrnReqDTO> jrnReqDTOList = new ArrayList<>();
		String busType = orderDO.getBusType();
		JrnReqDTO jrnReqDTO = new JrnReqDTO();
		jrnReqDTO.setCcy(orderDO.getCcy());
		jrnReqDTO.setPayCrdNo(null);
		if (StringUtils.isNotBlank(orderDO.getPayeeId())) {
			if (StringUtils.equals(orderDO.getBusType(), "0302")) {
				jrnReqDTO.setStlUserTyp("02");
			} else {
				jrnReqDTO.setStlUserTyp("01");
			}
			jrnReqDTO.setStlUserId(orderDO.getPayeeId());
		}
		jrnReqDTO.setPayUserTyp("01");
		jrnReqDTO.setPayUserId(orderDO.getPayerId());

		jrnReqDTO.setTxAmt(orderDO.getOrderAmt());
		jrnReqDTO.setTxCnl(CshConstants.RSK_CNL_APP);
		jrnReqDTO.setTxTyp(orderDO.getTxType());
		if (JudgeUtils.equalsAny(busType, BussinessType.RECHARGE_HALL.getValue(),BussinessType.RECHARGE_OFFLINE.getValue())) {
			//免密支付:pswFlg=1
			jrnReqDTO.addTxData("pswFlg=1");
		} else {
			//非免密:pswFlg=0
			jrnReqDTO.addTxData("pswFlg=0");
		}

		jrnReqDTO.setTxOrdNo(orderDO.getOrderNo());
		jrnReqDTO.setTxJrnNo(LemonUtils.getRequestId());
		jrnReqDTO.setTxSts("0");
		jrnReqDTO.setTxDate(DateTimeUtils.getCurrentLocalDate());
		jrnReqDTO.setTxTime(DateTimeUtils.getCurrentLocalTime());
		//补款方式
		String crdPayType = orderDO.getCrdPayType();
		if (JudgeUtils.isNull(orderDO.getCrdPayAmt())) {
			orderDO.setCrdPayAmt(BigDecimal.valueOf(0));
		}
		//无优惠 账户余额
		if (JudgeUtils.isNotNull(orderDO.getBalAmt()) && orderDO.getBalAmt().compareTo(BigDecimal.valueOf(0)) > 0) {
			JrnReqDTO accJrnReqDTO = new JrnReqDTO();
			BeanUtils.copyProperties(accJrnReqDTO, jrnReqDTO);
			//支付类型
			accJrnReqDTO.setPayTyp(Constants.PAY_TYP_ACCOUNT);
			accJrnReqDTO.setTxAmt(orderDO.getBalAmt());
			accJrnReqDTO.setStlUserId(orderDO.getPayeeId());
			accJrnReqDTO.setStlUserTyp(Constants.ID_TYP_USER_NO);
			logger.debug("交易余额累计:" + orderDO.getBalAmt());
			jrnReqDTOList.add(accJrnReqDTO);

		}
		//海币优惠
		if (JudgeUtils.isNotNull(orderDO.getCouponAmt()) && orderDO.getCouponAmt().compareTo(BigDecimal.valueOf(0)) > 0) {
			if (JudgeUtils.equals(orderDO.getCouponType(), CouponType.H_COUPON.getType())) {
				JrnReqDTO seaJrnReqDTO = new JrnReqDTO();
				BeanUtils.copyProperties(seaJrnReqDTO, jrnReqDTO);
				seaJrnReqDTO.setPayTyp(Constants.PAY_TYP_SEATEL);
				seaJrnReqDTO.setStlUserId(orderDO.getPayeeId());
				seaJrnReqDTO.setTxAmt(orderDO.getCouponAmt().divide(BigDecimal.valueOf(CshConstants.M_USD_RATE)));
				seaJrnReqDTO.setStlUserTyp(Constants.ID_TYP_USER_NO);
				logger.debug("交易海币累计:" + orderDO.getCouponAmt().divide(BigDecimal.valueOf(CshConstants.M_USD_RATE)));
				jrnReqDTOList.add(seaJrnReqDTO);
			}
		}

		switch (crdPayType) {
			//快捷
			case CshConstants.CRD_TYPE_QP:
				JrnReqDTO qpJrnReqDTO = new JrnReqDTO();
				BeanUtils.copyProperties(qpJrnReqDTO, jrnReqDTO);
				qpJrnReqDTO.setPayTyp(Constants.PAY_TYP_QUICK_PAY);
				qpJrnReqDTO.setStlUserId(orderDO.getPayeeId());
				qpJrnReqDTO.setTxAmt(orderDO.getCrdPayAmt());
				qpJrnReqDTO.setStlUserTyp(Constants.ID_TYP_USER_NO);
				logger.debug("快捷交易补款累计:" + orderDO.getCrdPayAmt());
				jrnReqDTOList.add(qpJrnReqDTO);
				break;
			//网银
			case CshConstants.CRD_TYPE_NB:
				JrnReqDTO ebankJrnReqDTO = new JrnReqDTO();
				BeanUtils.copyProperties(ebankJrnReqDTO, jrnReqDTO);
				//获取支付路径机构
				String rutCorgNo = orderDO.getCapCorgNo();
				logger.debug("网银支付合作机构:" + rutCorgNo);

				if(JudgeUtils.equals(rutCorgNo,CshConstants.PAY_TYPE_WX)){
					ebankJrnReqDTO.setPayTyp(Constants.PAY_TYP_WECHAT);
				}else if(JudgeUtils.equals(rutCorgNo,CshConstants.PAY_TYPE_ALI)){
					ebankJrnReqDTO.setPayTyp(Constants.PAY_TYP_ALIPAY);
				}else if(JudgeUtils.equals(rutCorgNo,CshConstants.PAY_TYPE_ICBC)){
					ebankJrnReqDTO.setPayTyp(Constants.PAY_TYP_QUICK_PAY);
				}else if(JudgeUtils.equals(rutCorgNo,CshConstants.PAY_TYPE_BEST)){
					ebankJrnReqDTO.setPayTyp(Constants.PAY_TYP_BESTPAY);
				}else{

				}
				ebankJrnReqDTO.setStlUserId(orderDO.getPayeeId());
				ebankJrnReqDTO.setTxAmt(orderDO.getCrdPayAmt());
				ebankJrnReqDTO.setStlUserTyp(Constants.ID_TYP_USER_NO);
				logger.debug("网银交易补款累计:" + orderDO.getCrdPayAmt());
				jrnReqDTOList.add(ebankJrnReqDTO);
				break;
			//线下转账
			case CshConstants.CRD_TYPE_FL:
				JrnReqDTO offlieJrnReqDTO = new JrnReqDTO();
				BeanUtils.copyProperties(offlieJrnReqDTO, jrnReqDTO);
				offlieJrnReqDTO.setPayTyp(Constants.PAY_TYP_OFFLINE);
				offlieJrnReqDTO.setStlUserId(orderDO.getPayeeId());
				offlieJrnReqDTO.setPayUserId(orderDO.getPayerId());
				offlieJrnReqDTO.setPayUserTyp(Constants.ID_TYP_USER_NO);
				offlieJrnReqDTO.setTxAmt(orderDO.getCrdPayAmt());
				offlieJrnReqDTO.setStlUserTyp(Constants.ID_TYP_USER_NO);
				logger.debug("交易线下汇款累计:" + orderDO.getCrdPayAmt());
				jrnReqDTOList.add(offlieJrnReqDTO);
				break;
			default:
				break;
		}
		GenericDTO<List<JrnReqDTO>> genericDTO = new GenericDTO();
		genericDTO.setBody(jrnReqDTOList);

		try{
			GenericRspDTO<NoBody> rspDto =  riskCheckClient.batchAccumulation(genericDTO);
			if(JudgeUtils.isNotSuccess(rspDto.getMsgCd())){
				logger.error("订单{}实时风控累计失败:{}",orderDO.getOrderNo(),  rspDto.getMsgCd());
			}
		}catch (Exception e){
			logger.error("订单{}风控累计失败。",orderDO.getOrderNo(),e);
		}

	}

	/**
	 * 支付短款撤单
	 * @param orderNo
	 */
	public void shortErrCancel(String orderNo){
		logger.debug("订单"+orderNo+"短款撤单");
		try{
			locker.lock("CSH_LOCK"+orderNo,18,22,()->{
				OrderDO orderDO = this.orderTransactionalService.queryOrderNotNone(orderNo);
				RefundOrderDTO refundOrderDTO=new RefundOrderDTO();
				refundOrderDTO.setBusRdfOrdNo("LR"+orderNo);
				refundOrderDTO.setBusType("0601");
				refundOrderDTO.setMercId(orderDO.getPayeeId());
				refundOrderDTO.setOrderCcy(orderDO.getCcy());
				refundOrderDTO.setOrginOrderNo(orderDO.getOrderNo());
				refundOrderDTO.setMercName(orderDO.getMercName());
				refundOrderDTO.setRefundUserId(orderDO.getPayerId());
				refundOrderDTO.setRfdAmt(orderDO.getTotalAmt());
				refundOrderDTO.setRfdReason("对账长款退款");
				refundOrderDTO.setTxType("06");

				GenericDTO<RefundOrderDTO> genericDTO=new GenericDTO<RefundOrderDTO>();
				genericDTO.setBody(refundOrderDTO);
				GenericRspDTO<NoBody> rspDTO=this.refundOrderService.rechargeRfund(genericDTO);
                if(JudgeUtils.isNotSuccess(rspDTO.getMsgCd())){
                	LemonException.throwBusinessException(rspDTO.getMsgCd());
                }
				return null;
			});
		}catch (Exception e){
			LemonException.throwBusinessException("CSH20085");
		}
	}


	/**
	 * 修改订单状态
	 */
	@Override
	public void updateOrder(OrderDO orderDO) {
		//更新收银订单状态
		orderTransactionalService.updateOrder(orderDO);
		//更新账单状态
		UpdateUserBillDTO updUserReq = new UpdateUserBillDTO();
		updUserReq.setOrderNo(orderDO.getOrderNo());
		updUserReq.setOrderStatus(orderDO.getOrderStatus());
		billSyncHandler.updateBill(updUserReq);
	}

	public PaymentResultDTO directPay(GenericDTO<DirectPaymentDTO> directGenDTO){
		DirectPaymentDTO directPaymentDTO=directGenDTO.getBody();
		if(!(StringUtils.equals(directPaymentDTO.getBusType(),"0301") && JudgeUtils.isBlank(directPaymentDTO.getPayPassword()))){
			//验证支付密码
			checkPayPassword(directGenDTO.getBody().getPayerId(),directPaymentDTO.getPayPassword(),true,directPaymentDTO.getValidateRandom(), directPaymentDTO.getSeaRandom());
		}
		OrderDO orderDO=orderTransactionalService.queryOrderByBusNo(directPaymentDTO.getExtOrderNo());
		if(JudgeUtils.isNotNull(orderDO)){
			LemonException.throwBusinessException("CSH20080");
		}
		orderDO=new OrderDO();
		BeanUtils.copyProperties(orderDO, directPaymentDTO);
		String ymd= DateTimeUtils.getCurrentDateStr();
		String orderNo= IdGenUtils.generateId(CshConstants.ORD_GEN_PRE + ymd, 15);
		orderDO.setOrderNo(ymd+orderNo);
		orderDO.setOrderTm(DateTimeUtils.getCurrentLocalDateTime());
		orderDO.setOrderStatus(OrderStatus.PRE_PAY.getValue());

		if(JudgeUtils.isNotNull(directPaymentDTO.getEffTm())){
			orderDO.setOrderExpTm(directPaymentDTO.getEffTm());
		}else{
			orderDO.setOrderExpTm(DateTimeUtils.getCurrentLocalDateTime().plusDays(2));
		}
		orderDO.setBusOrderNo(directPaymentDTO.getExtOrderNo());
		//设置无补款类型
		orderDO.setCrdPayType(CshConstants.CRD_TYPE_NONE);
		orderDO.setPayerId(directPaymentDTO.getPayerId());
		orderDO.setFndOrderNo("");
		orderDO.setPayeeId(directPaymentDTO.getPayeeId());
		orderDO.setMercName(directPaymentDTO.getPayeeName());
		orderDO.setOrderChannel(directPaymentDTO.getSysChannel());
		orderDO.setBusPayType(directPaymentDTO.getBusPaytype());

		orderDO.setInvAmt(new BigDecimal(0));
		orderDO.setLeftCardAmt(new BigDecimal(0));
		orderDO.setLeftInvAmt(new BigDecimal(0));

		orderDO.setCcy(directPaymentDTO.getOrderCcy());
		orderDO.setGoodsInfo(directPaymentDTO.getGoodsDesc());
		orderDO.setRemark("");
		orderDO.setPayJrnNo("");
		orderDO.setAppCnl(directPaymentDTO.getAppCnl());
		orderDO.setAcTm(DateTimeUtils.getCurrentLocalDate());
		orderDO.setBusType(directPaymentDTO.getBusType());
		String paytype=orderCommonComponent.getPayType(orderDO.getAppCnl(),orderDO.getBusType(),
				orderDO.getPayeeId(), orderDO.getBusPayType());
		//没有找到合适的支付方式
		if(paytype.indexOf(CshConstants.YES)<0){
			throw  new LemonException("CSH20016");
		}
		orderDO.setPayType(paytype);
		caculateFee(orderDO);
		orderDO.setCouponNo("");
		orderDO.setCouponAmt(BigDecimal.valueOf(directPaymentDTO.gethCouponAmt()));
		orderDO.setLeftCouponAmt(BigDecimal.valueOf(directPaymentDTO.gethCouponAmt()));

		Integer couponCount = directPaymentDTO.gethCouponAmt();
		//若是海币数量大于0，设置优惠类型
		if(JudgeUtils.isNotNull(couponCount) && couponCount.compareTo(0) > 0){
			orderDO.setCouponType(CouponType.H_COUPON.getType());
			//订单金额校验
			BigDecimal couponAmt = new BigDecimal(couponCount).divide(BigDecimal.valueOf(CshConstants.M_USD_RATE));
			if(directPaymentDTO.getCashAmt().add(couponAmt).compareTo(directPaymentDTO.getOrderAmt()) != 0){
				LemonException.throwBusinessException("CSH20003");
			}
		}
		//不使用海币
/*		if(JudgeUtils.isNull(couponCount) || couponCount.compareTo(0) == 0){
			if(directPaymentDTO.getCashAmt().compareTo(directPaymentDTO.getOrderAmt()) != 0){
				LemonException.throwBusinessException("CSH20003");
			}
		}*/
		orderDO.setBalAmt(directPaymentDTO.getCashAmt());
		orderDO.setCrdPayAmt(new BigDecimal(0));
		orderDO.setLeftBalAmt(directPaymentDTO.getCashAmt());

		checkBeforeInitOrder(orderDO);

		orderTransactionalService.initOrder(orderDO);
		boolean payerIdExist = true;
		if(JudgeUtils.isBlank(orderDO.getPayerId())){
			payerIdExist = false;
		}
		//付款方手机后四位
		String extInfo = null;
		if(JudgeUtils.isNotBlank(directPaymentDTO.getMblNo())){
			extInfo = directPaymentDTO.getMblNo()+"|";
		}
		//创建账单
		orderCommonComponent.synchronizeBil(orderDO,OrderCommonComponent.CREATE_BIL,payerIdExist, extInfo, null);

		BigDecimal balAmt=orderDO.getBalAmt();
		String couponType=orderDO.getCouponType();

		PayJrnDO payJrnDO=saveJrn(orderDO, balAmt, Integer.valueOf(CshConstants.CRD_TYPE_NONE), BigDecimal.valueOf(0),
				couponType, orderDO.getCouponAmt(),orderDO.getCouponNo(),directPaymentDTO.getOrderCcy());

		//实时风控
		riskControl(orderDO);
		//账务处理
		handleAccAndMkm(orderDO,payJrnDO);

		//更新订单和流水
		updateJrnAndOrder(payJrnDO);


		//风控累计
		riskAmount(orderDO);

        //设置操作员
        Map extMap = null;
        if(JudgeUtils.equals(directPaymentDTO.getTxType(), TradeType.CONSUME.getType())
                && JudgeUtils.isNotNull(directPaymentDTO.getLoginId())){
            extMap = new HashMap();
            Map map = new HashMap();
            map.put("loginId", directPaymentDTO.getLoginId());
            extMap.put(TradeType.CONSUME.getType(), map);
        }

		//同步账单
		orderDO.setOrderStatus(OrderStatus.SUCC.getValue());
		orderCommonComponent.synchronizeBil(orderDO,OrderCommonComponent.UPD_BIL,payerIdExist, extInfo, extMap);
		return createPaymentResultDTO(orderDO.getOrderNo(),orderDO.getOrderAmt(),orderDO.getBusOrderNo(),orderDO.getGoodsInfo(),orderDO.getPayeeId(),orderDO.getTxType());
	}

	public PaymentResultDTO newDirectPay(GenericDTO<DirectPaymentDTO> directGenDTO){
		DirectPaymentDTO directPaymentDTO=directGenDTO.getBody();
		if(!(StringUtils.equals(directPaymentDTO.getBusType(),"0301") && JudgeUtils.isBlank(directPaymentDTO.getPayPassword()))){
			//验证支付密码
			checkPayPassword(directGenDTO.getBody().getPayerId(),directPaymentDTO.getPayPassword(),true,directPaymentDTO.getValidateRandom(), directPaymentDTO.getSeaRandom());
		}
		OrderDO orderDO=orderTransactionalService.queryOrderByBusNo(directPaymentDTO.getExtOrderNo());
		if(JudgeUtils.isNotNull(orderDO)){
			LemonException.throwBusinessException("CSH20080");
		}
		orderDO = new OrderDO();
		BeanUtils.copyProperties(orderDO, directPaymentDTO);
		String ymd= DateTimeUtils.getCurrentDateStr();
		String orderNo= IdGenUtils.generateId(CshConstants.ORD_GEN_PRE + ymd, 15);
		orderDO.setOrderNo(ymd+orderNo);
		orderDO.setOrderTm(DateTimeUtils.getCurrentLocalDateTime());
		orderDO.setOrderStatus(OrderStatus.PRE_PAY.getValue());

		if(JudgeUtils.isNotNull(directPaymentDTO.getEffTm())){
			orderDO.setOrderExpTm(directPaymentDTO.getEffTm());
		}else{
			orderDO.setOrderExpTm(DateTimeUtils.getCurrentLocalDateTime().plusDays(2));
		}
		orderDO.setBusOrderNo(directPaymentDTO.getExtOrderNo());
		//设置无补款类型
		orderDO.setCrdPayType(CshConstants.CRD_TYPE_NONE);
		orderDO.setPayerId(directPaymentDTO.getPayerId());
		orderDO.setFndOrderNo("");
		orderDO.setPayeeId(directPaymentDTO.getPayeeId());
		orderDO.setMercName(directPaymentDTO.getPayeeName());
		orderDO.setOrderChannel(directPaymentDTO.getSysChannel());
		orderDO.setBusPayType(directPaymentDTO.getBusPaytype());

		orderDO.setInvAmt(new BigDecimal(0));
		orderDO.setLeftCardAmt(new BigDecimal(0));
		orderDO.setLeftInvAmt(new BigDecimal(0));

		orderDO.setCcy(directPaymentDTO.getOrderCcy());
		orderDO.setGoodsInfo(directPaymentDTO.getGoodsDesc());
		orderDO.setRemark("");
		orderDO.setPayJrnNo("");
		orderDO.setAppCnl(directPaymentDTO.getAppCnl());
		orderDO.setAcTm(DateTimeUtils.getCurrentLocalDate());
		orderDO.setBusType(directPaymentDTO.getBusType());
//		String paytype=orderCommonComponent.getPayType(orderDO.getAppCnl(),orderDO.getBusType(),
//				orderDO.getPayeeId(), orderDO.getBusPayType());
//		//没有找到合适的支付方式
//		if(paytype.indexOf(CshConstants.YES)<0){
//			throw  new LemonException("CSH20016");
//		}
		orderDO.setPayType("平台内转账");
		caculateFee(orderDO);
		orderDO.setCouponNo("");
		orderDO.setCouponAmt(BigDecimal.valueOf(directPaymentDTO.gethCouponAmt()));
		orderDO.setLeftCouponAmt(BigDecimal.valueOf(directPaymentDTO.gethCouponAmt()));
		orderDO.setBalAmt(directPaymentDTO.getCashAmt());
		orderDO.setCrdPayAmt(new BigDecimal(0));
		orderDO.setLeftBalAmt(directPaymentDTO.getCashAmt());

		checkBeforeInitOrder(orderDO);

		orderTransactionalService.initOrder(orderDO);
		boolean payerIdExist = true;
		if(JudgeUtils.isBlank(orderDO.getPayerId())){
			payerIdExist = false;
		}
		//付款方手机后四位
		String extInfo = null;
		if(JudgeUtils.isNotBlank(directPaymentDTO.getMblNo())){
			extInfo = directPaymentDTO.getMblNo()+"|";
		}
		//创建账单
		orderCommonComponent.synchronizeBil(orderDO,OrderCommonComponent.CREATE_BIL,payerIdExist, extInfo, null);

		BigDecimal balAmt=orderDO.getBalAmt();
		String couponType=orderDO.getCouponType();

		PayJrnDO payJrnDO = saveJrn(orderDO, balAmt, Integer.valueOf(CshConstants.CRD_TYPE_NONE), BigDecimal.valueOf(0),
				couponType, orderDO.getCouponAmt(),orderDO.getCouponNo(),directPaymentDTO.getOrderCcy());

		//实时风控
		riskControl(orderDO);
		//账务处理
		handleAccAndMkm(orderDO,payJrnDO);
		return createPaymentResultDTO(orderDO.getOrderNo(),orderDO.getOrderAmt(),orderDO.getBusOrderNo(),orderDO.getGoodsInfo(),orderDO.getPayeeId(),orderDO.getTxType());
	}


	/**
	 * 商户转账用户，待结算账户支付
	 * @param genericTransferPaymentDTO
	 * @return
	 */
	public PaymentResultDTO merchantTransferPay(GenericDTO<TransferPaymentDTO> genericTransferPaymentDTO){
		TransferPaymentDTO transferPaymentDTO=genericTransferPaymentDTO.getBody();
		OrderDO orderDO=orderTransactionalService.queryOrderByBusNo(transferPaymentDTO.getBusOrderNo());
		if(JudgeUtils.isNotNull(orderDO)){
			LemonException.throwBusinessException("CSH20080");
		}
		orderDO=new OrderDO();
		BeanUtils.copyProperties(orderDO, transferPaymentDTO);
		String ymd= DateTimeUtils.getCurrentDateStr();
		String orderNo= IdGenUtils.generateId(CshConstants.ORD_GEN_PRE + ymd, 15);
		orderDO.setOrderNo(ymd+orderNo);
		orderDO.setOrderTm(DateTimeUtils.getCurrentLocalDateTime());
		orderDO.setOrderStatus(OrderStatus.WAIT_PAY.getValue());

		if(JudgeUtils.isNotNull(transferPaymentDTO.getEffTm())){
			orderDO.setOrderExpTm(transferPaymentDTO.getEffTm());
		}else{
			orderDO.setOrderExpTm(DateTimeUtils.getCurrentLocalDateTime().plusDays(2));
		}
		orderDO.setBusOrderNo(transferPaymentDTO.getBusOrderNo());
		orderDO.setCrdPayType(CshConstants.CRD_TYPE_NONE);
		orderDO.setPayerId(transferPaymentDTO.getPayerId());
		orderDO.setFndOrderNo("");
		//收款方为用户
		orderDO.setPayeeId(transferPaymentDTO.getPayeeId());
		//商户名为付款方名字
		orderDO.setMercName(transferPaymentDTO.getPayerName());
		orderDO.setOrderChannel(transferPaymentDTO.getSysChannel());
		orderDO.setBusPayType(transferPaymentDTO.getBusPaytype());

		orderDO.setInvAmt(new BigDecimal(0));
		orderDO.setLeftCardAmt(new BigDecimal(0));
		orderDO.setLeftInvAmt(new BigDecimal(0));

		orderDO.setCcy(transferPaymentDTO.getOrderCcy());
		orderDO.setGoodsInfo(transferPaymentDTO.getGoodsDesc());
		orderDO.setRemark(transferPaymentDTO.getRemark());
		orderDO.setPayJrnNo(LemonUtils.getRequestId());
		orderDO.setAppCnl(transferPaymentDTO.getAppCnl());
		orderDO.setAcTm(DateTimeUtils.getCurrentLocalDate());
		orderDO.setBusType(transferPaymentDTO.getBusType());
		String paytype=orderCommonComponent.getPayType(orderDO.getAppCnl(),orderDO.getBusType(),
				orderDO.getPayeeId(), orderDO.getBusPayType());
		//没有找到合适的支付方式
		if(paytype.indexOf(CshConstants.YES)<0){
			throw  new LemonException("CSH20016");
		}
		orderDO.setPayType(paytype);
		caculateFee(orderDO);
		orderDO.setCouponNo("");
		orderDO.setCouponAmt(new BigDecimal(0));
		orderDO.setCouponType(CouponType.NONE.getType());
		orderDO.setLeftCouponAmt(new BigDecimal(0));

		orderDO.setBalAmt(transferPaymentDTO.getCashAmt());
		orderDO.setCrdPayAmt(new BigDecimal(0));
		orderDO.setLeftBalAmt(transferPaymentDTO.getCashAmt());

		checkBeforeInitOrder(orderDO);

		orderTransactionalService.initOrder(orderDO);

		//创建账单
		orderCommonComponent.synchronizeBil(orderDO,OrderCommonComponent.CREATE_BIL,true, null,null);

		BigDecimal balAmt=orderDO.getBalAmt();

		PayJrnDO payJrnDO=saveJrn(orderDO, balAmt, Integer.valueOf(CshConstants.CRD_TYPE_NONE), BigDecimal.valueOf(0),
				CouponType.NONE.getType(), orderDO.getCouponAmt(),orderDO.getCouponNo(),transferPaymentDTO.getOrderCcy());

		//实时风控
		riskControl(orderDO);

		//商户结算账户账务处理
		List<AccountingReqDTO> accList = tamOrderServiceImpl.handleSettleAccount(orderDO,payJrnDO);

		try{
			//更新订单和流水
			updateJrnAndOrder(payJrnDO);
		}catch (Exception e){
			//账务冲正
			logger.error("更新数据库失败，账务冲正处理...");
			if(JudgeUtils.isNotEmpty(accList)){
				for(AccountingReqDTO ar : accList){
					ar.setTxSts(ACMConstants.ACCOUNTING_CANCEL);
				}
				acmComponent.requestAc(accList);
			}
			//更新订单
			OrderDO updateOrder = new OrderDO();
			updateOrder.setOrderNo(orderDO.getOrderNo());
			updateOrder.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
			updateOrder.setOrderStatus(OrderStatus.FAIL.getValue());
			updateOrder(updateOrder);
			LemonException.throwBusinessException("SYS00001");
		}
		//登记一笔不清分的商户服务费
		if(JudgeUtils.equals(orderDO.getBusType(),BussinessType.TRANSFER_M_BAL.getValue())){
			merchantTransferHandler.registMerChantTransferFee(orderDO);
			logger.debug("登记不清分商户转账订单号：" + orderDO.getOrderNo());
		}

		//风控累计
		riskAmount(orderDO);

		//付款方手机后四位
		String extInfo = null;
		//如果是商户转账到用户的情况
		if(JudgeUtils.isNotBlank(transferPaymentDTO.getMblNo())){
			//MblNo传的是收款方手机后4位数
			extInfo = "|" + transferPaymentDTO.getMblNo();
		}

		//同步账单
		orderDO.setOrderStatus(OrderStatus.SUCC.getValue());
		orderCommonComponent.synchronizeBil(orderDO,OrderCommonComponent.UPD_BIL,true, extInfo, null);
		return createPaymentResultDTO(orderDO.getOrderNo(),orderDO.getOrderAmt(),orderDO.getBusOrderNo(),orderDO.getGoodsInfo(),orderDO.getPayeeId(),orderDO.getTxType());
	}


	/**
	 * 准备通知业务的最新数据
	 * @param dbOrder
	 * @param updateOrder
	 * @return
	 */
	protected OrderDO prepareNotifyOrder(OrderDO dbOrder,OrderDO updateOrder){
		logger.debug("========================  业务通知准备数据  ========================");
		updateOrder.setTxType(dbOrder.getTxType());
		logger.debug("交易类型："+updateOrder.getTxType());
		updateOrder.setBusType(dbOrder.getBusType());
		logger.debug("业务类型："+updateOrder.getBusType());
		updateOrder.setTotalAmt(dbOrder.getTotalAmt());
		logger.debug("支付总金额："+updateOrder.getTotalAmt());
		updateOrder.setOrderAmt(dbOrder.getOrderAmt());
		logger.debug("订单金额："+updateOrder.getOrderAmt());
		updateOrder.setCcy(dbOrder.getCcy());
		logger.debug("订单币种："+updateOrder.getCcy());

		String payerId = dbOrder.getPayerId();
		/*if(JudgeUtils.isBlank(payerId) && (JudgeUtils.equals(updateOrder.getTxType(),"02"))){
			payerId = LemonUtils.getUserId();
		}*/
		updateOrder.setPayerId(payerId);
		logger.debug("付款方ID："+updateOrder.getPayerId());
		updateOrder.setPayType(dbOrder.getPayType());
		logger.debug("支付方式："+updateOrder.getPayType());
		updateOrder.setBusOrderNo(dbOrder.getBusOrderNo());
		logger.debug("业务订单号："+updateOrder.getBusOrderNo());
		if(JudgeUtils.isNull(updateOrder.getFee())){
			updateOrder.setFee(dbOrder.getFee());
		}
		logger.debug("手续费：" +updateOrder.getFee());
        updateOrder.setOrderTm(dbOrder.getOrderTm());
        logger.debug("下单时间：" +updateOrder.getOrderTm());

        if(JudgeUtils.isNull(updateOrder.getBalAmt())){
            updateOrder.setBalAmt(dbOrder.getBalAmt());
        }
        logger.debug("余额账户支付金额："+updateOrder.getBalAmt());

		String couponType = updateOrder.getCouponType();
		if(JudgeUtils.isBlank(couponType)){
			updateOrder.setCouponType(dbOrder.getCouponType());
		}
		logger.debug("优惠类型："+updateOrder.getCouponType());

		BigDecimal couponAmt = updateOrder.getCouponAmt();
		if(JudgeUtils.isNull(couponAmt)){
			updateOrder.setCouponAmt(dbOrder.getCouponAmt());
		}
		logger.debug("优惠金额："+updateOrder.getCouponAmt());
		if(JudgeUtils.isNull(updateOrder.getCrdPayType())){
            updateOrder.setCrdPayType(dbOrder.getCrdPayType());
        }
        logger.debug("补款类型："+updateOrder.getCrdPayType());
		if(JudgeUtils.isNull(updateOrder.getCrdPayAmt())){
            updateOrder.setCrdPayAmt(dbOrder.getCrdPayAmt());
        }
        logger.debug("补款金额："+updateOrder.getCrdPayAmt());

        if(JudgeUtils.isNull(updateOrder.getCapCorg())){
            updateOrder.setCapCorg(dbOrder.getCapCorg());
        }
        logger.debug("资金机构："+updateOrder.getCapCorg());

		if(JudgeUtils.isNull(updateOrder.getCapCorgNo())){
			updateOrder.setCapCorgNo(dbOrder.getCapCorgNo());
		}
		logger.debug("网银资金合作机构："+updateOrder.getCapCorgNo());

        if(JudgeUtils.isNull(updateOrder.getLast4CardNo())){
            updateOrder.setLast4CardNo(dbOrder.getLast4CardNo());
        }
        logger.debug("卡后四位："+updateOrder.getLast4CardNo());

        if(JudgeUtils.isNull(updateOrder.getPayMod())){
            updateOrder.setPayMod(dbOrder.getPayMod());
        }
        logger.debug("支付方式payMod："+updateOrder.getPayMod());

        if(JudgeUtils.isNull(updateOrder.getMercName())){
            updateOrder.setMercName(dbOrder.getMercName());
        }

        if(JudgeUtils.isNull(updateOrder.getPayeeId())){
            updateOrder.setPayeeId(dbOrder.getPayeeId());
        }
        logger.debug("商户名称："+updateOrder.getMercName());

		if(JudgeUtils.isNull(updateOrder.getRemark())){
			updateOrder.setRemark(dbOrder.getRemark());
		}
		logger.debug("订单备注："+updateOrder.getRemark());

		logger.debug("========================业务通知准备数据 end========================");
		return updateOrder;
	}

	public void handleSuccess(String fndOrderNo,BigDecimal crdAmt,OrderDO orderDO){
		String payerId = orderDO.getPayerId();
		boolean payerIdExist = true;
		if(JudgeUtils.isBlank(payerId)){
			payerIdExist = false;
		}
		if(crdAmt.compareTo(orderDO.getCrdPayAmt()) != 0){
			throw new LemonException("CSH10014");
		}

		PayJrnDO payJrnDO = this.orderTransactionalService.queryJrnNotNone(orderDO.getPayJrnNo());

		payJrnDO.setFndOrderNo(fndOrderNo);

		//账务处理
		handleAccAndMkm(orderDO,payJrnDO);

		//更新订单和流水
		OrderDO orderDO2=updateJrnAndOrder(payJrnDO);

		prepareNotifyOrder(orderDO,orderDO2);

		//风控累计
		riskAmount(orderDO2);

		//通知业务模块,使用更新后的订单
		notifyBussiness(orderDO2,payJrnDO);
		//同步账单
		orderCommonComponent.synchronizeBil(orderDO2,OrderCommonComponent.UPD_BIL,payerIdExist, null,null);
	}

	protected void giveDiscountCoupon(OrderDO orderDO){
		if(StringUtils.isBlank(orderDO.getPayerId())){
			logger.debug("没有付款方ID，不发放营销权："+orderDO.getOrderNo());
			return;
		}

		GenericRspDTO<AutoRealeaseRspDTO> rspDTO=acmComponent.giveDiscountCoupon(orderDO);
		if(JudgeUtils.isNotSuccess(rspDTO.getMsgCd())){
			logger.debug("调用营销发券失败："+rspDTO.getMsgCd());
		}else{
			AutoRealeaseRspDTO autoRealeaseRspDTO=rspDTO.getBody();
			if(JudgeUtils.isNotNull(autoRealeaseRspDTO)){
				if(StringUtils.equals(autoRealeaseRspDTO.getResult(),"1")){
					orderDO.setCouponNo(autoRealeaseRspDTO.getConponNo());
					orderDO.setCouponAmt(autoRealeaseRspDTO.getAmt());
					orderDO.setCouponType(CouponType.D_COUPON.getType());
				}
			}
		}
	}

	public CashierViewDTO cashierQuto(OrderDO orderDO){
		riskUserStatus(orderDO.getTxType(),LemonUtils.getUserId(),"01");
		try{
			return locker.lock("CSH_PAY_LOCK"+orderDO.getOrderNo(),18,16,
					()->{
						if(!StringUtils.equals(orderDO.getOrderStatus(),OrderStatus.WAIT_PAY.getValue())){
							if(StringUtils.equals(orderDO.getOrderStatus(),OrderStatus.PRE_PAY.getValue())){
								if(!StringUtils.equals(LemonUtils.getUserId(),orderDO.getPayerId())){
									LemonException.throwBusinessException("CSH20089");
								}
							}else{
								LemonException.throwBusinessException("CSH20002");
							}
						}

						//付款方id为空，没有发过营销券
						if(StringUtils.isBlank(orderDO.getPayerId()) && StringUtils.isBlank(orderDO.getCouponNo())){
							orderDO.setPayerId(LemonUtils.getUserId());
							giveDiscountCoupon(orderDO);
						}
						CashierViewDTO cashierViewDTO=orderCommonComponent.createCashierViewDTO(orderDO);
						orderDO.setOrderStatus(OrderStatus.PRE_PAY.getValue());
						this.orderTransactionalService.updateOrder(orderDO);
						//商户下单让用户扫订单二维码情况，补下用户账单
						if(JudgeUtils.equals(orderDO.getBusType(), BussinessType.CONSUME_SCAN.getValue())){
							orderCommonComponent.synchronizeBil(orderDO,OrderCommonComponent.UPD_BIL,false, null,null);
						}
						return cashierViewDTO;
					});
		}catch (LemonException e){
			throw e;
		}catch (Exception e){
			throw LemonException.create(e);
		}
	}

	protected void riskUserStatus(String txType,String userId,String idType){
		if(StringUtils.isBlank(userId)){
			return;
		}
		RiskCheckUserStatusReqDTO riskCheckUserStatusReqDTO=new RiskCheckUserStatusReqDTO();
		riskCheckUserStatusReqDTO.setTxTyp(txType);
		riskCheckUserStatusReqDTO.setId(userId);
		riskCheckUserStatusReqDTO.setIdTyp(idType);
		GenericRspDTO rspDTO=riskCheckClient.checkUserStatus(riskCheckUserStatusReqDTO);
		if(JudgeUtils.isNotSuccess(rspDTO.getMsgCd())){
			logger.error("风控用户或商户 {} 状态检查失败:{}",userId,rspDTO.getMsgCd());
			LemonException.throwBusinessException(rspDTO.getMsgCd());
		}
	}

	@Override
	public String preCheckExchangeOrder(GenericDTO<ExchangeCoinDTO> req) {
		//风控检查
		ExchangeCoinDTO exchangeCoinDTO = req.getBody();
		if(JudgeUtils.isNull(exchangeCoinDTO)) {
			LemonException.throwBusinessException("CSH20063");
		}
		OrderDO orderDO = new OrderDO();
		BeanUtils.copyProperties(orderDO,exchangeCoinDTO);
		checkBeforeInitOrder(orderDO);
		//冻结金额
		AccountingHoldReqDTO holdReqDTO = new AccountingHoldReqDTO();
		holdReqDTO.setAcNo(exchangeCoinDTO.getAcNo());
		holdReqDTO.setCapTyp(CapTypEnum.CAP_TYP_CASH.getCapTyp());
		holdReqDTO.setCcy(exchangeCoinDTO.getCcy());
		holdReqDTO.setHldCd("TR");
		holdReqDTO.setHldBal(exchangeCoinDTO.getOrderAmt());
		holdReqDTO.setExpDt(DateTimeUtils.getCurrentLocalDate());
		holdReqDTO.setExpTm(DateTimeUtils.getCurrentLocalTime());
		holdReqDTO.setDueProcMod("1");
		holdReqDTO.setOrdTyp(exchangeCoinDTO.getTxType());
		holdReqDTO.setOrdNo(exchangeCoinDTO.getOrderNo());
		GenericDTO<AccountingHoldReqDTO> holdReq = GenericDTO.newInstance(holdReqDTO);
		String holdNo = accountingTreatmentClient.holdUserAccountBalance(holdReq).getBody();
		//创建订单 bil_user_order
		CreateUserBillDTO billDTO = new CreateUserBillDTO();
		BeanUtils.copyProperties(billDTO,exchangeCoinDTO);
		billDTO.setTxTm(DateTimeUtils.getCurrentLocalDateTime());
		billDTO.setOrderStatus(OrderStatus.WAIT_AUDIT.getValue());
		billSyncHandler.createBill(billDTO);
		return holdNo;
	}

	@Override
	public void completeExchangeOrder(GenericDTO<ExchangeCoinDTO> req) {
		ExchangeCoinDTO exchangeCoinDTO = req.getBody();
		OrderDO orderDO = new OrderDO();
		UpdateUserBillDTO billDTO = new UpdateUserBillDTO();
		billDTO.setOrderNo(exchangeCoinDTO.getOrderNo());
		accountingTreatmentClient.unHoldUserAccountBalance(exchangeCoinDTO.getHoldNo());
		//订单失败
		if(JudgeUtils.equals(exchangeCoinDTO.getOrderSts(), CshConstants.EXCHANGE_REJECTED)) {
			billDTO.setOrderStatus(OrderStatus.FAIL.getValue());
			billSyncHandler.updateBill(billDTO);
			return;
		}
		BeanUtils.copyProperties(orderDO,exchangeCoinDTO);
		//生成科目 完成资金转入转出
		createAccQueue(orderDO, null);
		//更新bil订单记录
		billDTO.setOrderStatus(OrderStatus.SUCC.getValue());
		billSyncHandler.updateBill(billDTO);
	}

}
