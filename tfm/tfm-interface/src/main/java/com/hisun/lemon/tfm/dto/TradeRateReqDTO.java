package com.hisun.lemon.tfm.dto;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 交易费率查询请求传输对象
 * <AUTHOR>
 * @date 2017年7月8日 下午3:46:03
 * @version V1.0
 */
public class TradeRateReqDTO {
    /** 业务类型 */
    @ApiModelProperty(name = "busType", value = "业务类型", required = true)
    @NotNull(message = "TFM10002")
    @Length(max = 4)
    private String busType;
    /** 币种 */
    @ApiModelProperty(name = "ccy", value = "币种", required = true)
    @Pattern(regexp = "KHR|USD|CNY", message = "TFM10005")
    private String ccy;

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }
}
