package com.hisun.lemon.tfm.dto;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 交易手续费响应传输对象
 * <AUTHOR>
 * @date 2017年7月8日 下午3:46:03
 * @version V1.0
 */
public class TradeFeeRspDTO {
    /** 计费订单号 */
    @ApiModelProperty(name = "feeOrderNo", value = "计费订单号")
    private String feeOrderNo;
    /** 交易总金额 */
    @ApiModelProperty(name = "tradeTotalAmt", value = "交易总金额")
    private BigDecimal tradeTotalAmt;
    /** 交易金额 */
    @ApiModelProperty(name = "tradeAmt", value = "交易金额")
    private BigDecimal tradeAmt;
    /** 交易手续费 */
    @ApiModelProperty(name = "tradeFee", value = "交易手续费")
    private BigDecimal tradeFee;
    /** 计费模式 */
    @ApiModelProperty(name = "calculateMode", value = "计费模式(internal:内扣 external:外扣)")
    private String calculateMode;
    /** 计费方式 */
    @ApiModelProperty(name = "calculateType", value = "计费方式(percent:百分比 fixed:固定手续费)")
    private String calculateType;
    /** 交易费率 */
    @ApiModelProperty(name = "rate", value = "交易费率")
    private BigDecimal rate;
    /** 固定手续费 */
    @ApiModelProperty(name = "fixFee", value = "固定手续费")
    private BigDecimal fixFee;

    public String getFeeOrderNo() {
        return feeOrderNo;
    }

    public void setFeeOrderNo(String feeOrderNo) {
        this.feeOrderNo = feeOrderNo;
    }

    public BigDecimal getTradeTotalAmt() {
        return tradeTotalAmt;
    }

    public void setTradeTotalAmt(BigDecimal tradeTotalAmt) {
        this.tradeTotalAmt = tradeTotalAmt;
    }

    public BigDecimal getTradeAmt() {
        return tradeAmt;
    }

    public void setTradeAmt(BigDecimal tradeAmt) {
        this.tradeAmt = tradeAmt;
    }

    public BigDecimal getTradeFee() {
        return tradeFee;
    }

    public void setTradeFee(BigDecimal tradeFee) {
        this.tradeFee = tradeFee;
    }

    public String getCalculateMode() {
        return calculateMode;
    }

    public void setCalculateMode(String calculateMode) {
        this.calculateMode = calculateMode;
    }

    public String getCalculateType() {
        return calculateType;
    }

    public void setCalculateType(String calculateType) {
        this.calculateType = calculateType;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public BigDecimal getFixFee() {
        return fixFee;
    }

    public void setFixFee(BigDecimal fixFee) {
        this.fixFee = fixFee;
    }
}
