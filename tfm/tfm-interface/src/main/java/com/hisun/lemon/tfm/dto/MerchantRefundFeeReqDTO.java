package com.hisun.lemon.tfm.dto;

import java.math.BigDecimal;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 商户手续费退款处理请求传输对象
 * <AUTHOR>
 * @date 2017年7月8日 下午3:46:03
 * @version V1.0
 */
public class MerchantRefundFeeReqDTO {
    /** 业务订单号 */
    @ApiModelProperty(name = "busOrderNo", value = "业务订单号", required = true)
    @NotNull(message = "TFM10004")
    @Length(max = 32)
    private String busOrderNo;
    /** 退款金额 */
    @ApiModelProperty(name = "refundAmt", value = "退款金额", required = true)
    @NotNull(message = "TFM10003")
    private BigDecimal refundAmt;

    public String getBusOrderNo() {
        return busOrderNo;
    }

    public void setBusOrderNo(String busOrderNo) {
        this.busOrderNo = busOrderNo;
    }

    public BigDecimal getRefundAmt() {
        return refundAmt;
    }

    public void setRefundAmt(BigDecimal refundAmt) {
        this.refundAmt = refundAmt;
    }

}
