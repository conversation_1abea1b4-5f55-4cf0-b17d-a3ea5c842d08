package com.hisun.lemon.tfm.dto;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 商户手续费退款冲正处理请求传输对象
 * <AUTHOR>
 * @date 2017年7月8日 下午3:46:03
 * @version V1.0
 */
public class MerchantRefundFeeReversalReqDTO {
    /** 计费退款订单号 */
    @ApiModelProperty(name = "orderNo", value = "计费退款订单号", required = true)
    @NotNull(message = "TFM10004")
    @Length(max = 32)
    private String orderNo;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

}
