package com.hisun.lemon.tfm.dto;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;

/**
 * @Description 商户清分结果请求传输对象
 * <AUTHOR>
 * @date 2017年7月8日 下午3:46:03
 * @version V1.0
 */
public class MerchantCleanReqDTO {
    /** 用户号 **/
    @ApiModelProperty(name = "userId", value = "用户号", required = true)
    private String userId;

    @ApiModelProperty(name = "orderNo", value = "收银订单号", required = true)
    @NotNull(message = "TFM10004")
    private String orderNo;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}
