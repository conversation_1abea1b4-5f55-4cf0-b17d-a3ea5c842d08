package com.hisun.lemon.tfm.dto;

import java.math.BigDecimal;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 商户手续费预算请求传输对象
 * <AUTHOR>
 * @date 2017年7月8日 下午3:46:03
 * @version V1.0
 */
public class MerchantFeeCalculateReqDTO {
    /** 用户号 **/
    @ApiModelProperty(name = "userId", value = "用户号", required = true)
    @NotNull(message = "TFM10001")
    @Length(max = 20)
    private String userId;
    /** 业务类型 */
    @ApiModelProperty(name = "busType", value = "业务类型", required = true)
    @NotNull(message = "TFM10002")
    @Length(max = 4)
    private String busType;
    /** 币种 */
    @ApiModelProperty(name = "ccy", value = "币种", required = true)
    @Pattern(regexp = "KHR|USD|CNY", message = "TFM10005")
    private String ccy;
    /** 交易金额 */
    @ApiModelProperty(name = "tradeAmt", value = "交易金额", required = true)
    @NotNull(message = "TFM10003")
    private BigDecimal tradeAmt;
    /** 渠道 */
    @ApiModelProperty(name = "channel", value = "渠道")
    private String channel;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public BigDecimal getTradeAmt() {
        return tradeAmt;
    }

    public void setTradeAmt(BigDecimal tradeAmt) {
        this.tradeAmt = tradeAmt;
    }

    public String getChannel() { return channel; }

    public void setChannel(String channel) { this.channel = channel; }

}
