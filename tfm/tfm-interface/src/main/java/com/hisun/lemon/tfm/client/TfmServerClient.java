package com.hisun.lemon.tfm.client;

import com.hisun.lemon.tfm.dto.*;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;

/**
 * @Description  计费服务接口
 * <AUTHOR>
 * @date 2017年7月7日 下午3:01:38 
 * @version V1.0
 */
@FeignClient("TFM")
public interface TfmServerClient {
    
    /**
     * @Description 交易费率查询
     * <AUTHOR>
     * @param reqDTO
     * @return
     */
    @PostMapping("/tfm/rate")
    public GenericRspDTO<TradeRateRspDTO> tradeRate(@Validated @RequestBody GenericDTO<TradeRateReqDTO> reqDTO);
    
    /**
     * @Description 商户费率查询
     * <AUTHOR>
     * @param reqDTO
     * @return
     */
    @PostMapping("/tfm/merchant/rate")
    public GenericRspDTO<MerchantRateRspDTO> merchantRate(@Validated @RequestBody GenericDTO<MerchantRateReqDTO> reqDTO);
    
    /**
     * @Description 交易手续费预算
     * <AUTHOR>
     * @param reqDTO
     * @return
     */
    @PostMapping("/tfm/fee/calculate")
    public GenericRspDTO<TradeFeeCaculateRspDTO> tradeFeeCaculate(@Validated @RequestBody GenericDTO<TradeFeeCaculateReqDTO> reqDTO);
    
    /**
     * @Description 商户手续费预算
     * <AUTHOR>
     * @param reqDTO
     * @return
     */
    @PostMapping("/tfm/merchant/fee/calculate")
    public GenericRspDTO<MerchantFeeCalculateRspDTO> merchanFeeCalculate(@Validated @RequestBody GenericDTO<MerchantFeeCalculateReqDTO> reqDTO);
    
    /**
     * @Description 交易手续费
     * <AUTHOR>
     * @param reqDTO
     * @return
     */
    @PostMapping("/tfm/fee")
    public GenericRspDTO<TradeFeeRspDTO> tradeFee(@Validated @RequestBody GenericDTO<TradeFeeReqDTO> reqDTO);
    
    /**
     * @Description 商户手续费
     * <AUTHOR>
     * @param reqDTO
     * @return
     */
    @PostMapping("/tfm/merchant/fee")
    public GenericRspDTO<TradeFeeRspDTO> merchantTradeFee(@Validated @RequestBody GenericDTO<TradeFeeReqDTO> reqDTO);
    
    /**
     * @Description 商户手续费退款处理
     * <AUTHOR>
     * @param reqDTO
     * @return
     */
    @PostMapping("/tfm/merchant/fee/refund")
    public GenericRspDTO<MerchantRefundFeeRspDTO> merchantRefundFee(@Validated @RequestBody GenericDTO<MerchantRefundFeeReqDTO> reqDTO);
    
    /**
     * @Description 商户手续费退款处理撤销
     * <AUTHOR>
     * @param reqDTO
     * @return
     */
    @PostMapping("/tfm/merchant/fee/refund/reversal")
    public GenericRspDTO<NoBody> merchantRefundFeeReversal(@Validated @RequestBody GenericDTO<MerchantRefundFeeReversalReqDTO> reqDTO);


    @PostMapping("/tfm/merchant/clean/detail")
    public GenericRspDTO<MerchantCleanRspDTO> merchantCleanQuery(@Validated @RequestBody GenericDTO<MerchantCleanReqDTO> reqDTO) ;
}
