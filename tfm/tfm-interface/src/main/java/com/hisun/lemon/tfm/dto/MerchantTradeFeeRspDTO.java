package com.hisun.lemon.tfm.dto;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 商户手续费响应传输对象
 * <AUTHOR>
 * @date 2017年7月8日 下午3:46:03
 * @version V1.0
 */
public class MerchantTradeFeeRspDTO {
    /** 计费流水号 */
    @ApiModelProperty(name = "feeJrnNo", value = "计费流水号")
    private String feeJrnNo;
    /** 计费方式 */
    @ApiModelProperty(name = "calculateType", value = "计费方式")
    private String calculateType;
    /** 交易金额 */
    @ApiModelProperty(name = "tradeAmt", value = "交易金额")
    private BigDecimal tradeAmt;
    /** 交易费率 */
    @ApiModelProperty(name = "tradeRate", value = "交易费率")
    private BigDecimal tradeRate;
    /** 交易手续费 */
    @ApiModelProperty(name = "tradeFee", value = "交易手续费")
    private BigDecimal tradeFee;

    public String getFeeJrnNo() {
        return feeJrnNo;
    }

    public void setFeeJrnNo(String feeJrnNo) {
        this.feeJrnNo = feeJrnNo;
    }

    public String getCalculateType() {
        return calculateType;
    }

    public void setCalculateType(String calculateType) {
        this.calculateType = calculateType;
    }

    public BigDecimal getTradeAmt() {
        return tradeAmt;
    }

    public void setTradeAmt(BigDecimal tradeAmt) {
        this.tradeAmt = tradeAmt;
    }

    public BigDecimal getTradeRate() {
        return tradeRate;
    }

    public void setTradeRate(BigDecimal tradeRate) {
        this.tradeRate = tradeRate;
    }

    public BigDecimal getTradeFee() {
        return tradeFee;
    }

    public void setTradeFee(BigDecimal tradeFee) {
        this.tradeFee = tradeFee;
    }

}
