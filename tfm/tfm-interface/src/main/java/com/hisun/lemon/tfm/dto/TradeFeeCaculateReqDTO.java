package com.hisun.lemon.tfm.dto;

import java.math.BigDecimal;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;

/**
 * @Description 交易手续费预算请求传输对象
 * <AUTHOR>
 * @date 2017年7月8日 下午3:46:03
 * @version V1.0
 */
public class TradeFeeCaculateReqDTO {
    /** 业务类型 */
    @ApiModelProperty(name = "busType", value = "业务类型", required = true)
    @NotNull(message = "TFM10002")
    @Length(max = 4)
    private String busType;
    /** 币种 */
    @ApiModelProperty(name = "ccy", value = "币种", required = true)
    private String ccy;
    /** 交易金额 */
    @ApiModelProperty(name = "tradeAmt", value = "交易金额", required = true)
    @NotNull(message = "TFM10003")
    private BigDecimal tradeAmt;

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public BigDecimal getTradeAmt() {
        return tradeAmt;
    }

    public void setTradeAmt(BigDecimal tradeAmt) {
        this.tradeAmt = tradeAmt;
    }

}
