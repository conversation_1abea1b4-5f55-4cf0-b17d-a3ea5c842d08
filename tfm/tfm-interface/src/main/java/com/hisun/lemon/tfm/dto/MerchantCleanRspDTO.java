package com.hisun.lemon.tfm.dto;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * @Description 商户清分结果请求传输对象
 * <AUTHOR>
 * @date 2017年7月8日 下午3:46:03
 * @version V1.0
 */
public class MerchantCleanRspDTO {

    /**
     * @Fields orderNo 订单号
     */
    private String orderNo;
    /**
     * @Fields userId 内部用户号
     */
    private String userId;
    /**
     * @Fields userName 用户名称
     */
    private String userName;
    /**
     * @Fields busType 业务类型
     */
    private String busType;
    /**
     * @Fields busTypeDesc 业务类型描述
     */
    private String busTypeDesc;
    /**
     * @Fields ccy 币种
     */
    private String ccy;
    /**
     * @Fields tradeTotalAmt 交易总金额
     */
    private BigDecimal tradeTotalAmt;
    /**
     * @Fields tradeAmt 交易金额
     */
    private BigDecimal tradeAmt;
    /**
     * @Fields tradeDate 交易日期
     */
    private LocalDate tradeDate;
    /**
     * @Fields tradeTime 交易时间
     */
    private LocalTime tradeTime;
    /**
     * @Fields calculateMod 计费模式 internal:内扣 external:外扣
     */
    private String calculateMod;
    /**
     * @Fields calculateType 计费方式 percent:百分比 fixed:固定手续费
     */
    private String calculateType;
    /**
     * @Fields rate 费率
     */
    private BigDecimal rate;
    /**
     * @Fields fee 交易手续费
     */
    private BigDecimal fee;
    /**
     * @Fields clearStats 清分状态 0:待清分 1:已清分 9:不需清分
     */
    private String clearStats;
    /**
     * @Fields busOrderNo 业务订单号
     */
    private String busOrderNo;
    /**
     * @Fields busOrderTime 业务订单时间
     */
    private LocalDateTime busOrderTime;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getBusTypeDesc() {
        return busTypeDesc;
    }

    public void setBusTypeDesc(String busTypeDesc) {
        this.busTypeDesc = busTypeDesc;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public BigDecimal getTradeTotalAmt() {
        return tradeTotalAmt;
    }

    public void setTradeTotalAmt(BigDecimal tradeTotalAmt) {
        this.tradeTotalAmt = tradeTotalAmt;
    }

    public BigDecimal getTradeAmt() {
        return tradeAmt;
    }

    public void setTradeAmt(BigDecimal tradeAmt) {
        this.tradeAmt = tradeAmt;
    }

    public LocalDate getTradeDate() {
        return tradeDate;
    }

    public void setTradeDate(LocalDate tradeDate) {
        this.tradeDate = tradeDate;
    }

    public LocalTime getTradeTime() {
        return tradeTime;
    }

    public void setTradeTime(LocalTime tradeTime) {
        this.tradeTime = tradeTime;
    }

    public String getCalculateMod() {
        return calculateMod;
    }

    public void setCalculateMod(String calculateMod) {
        this.calculateMod = calculateMod;
    }

    public String getCalculateType() {
        return calculateType;
    }

    public void setCalculateType(String calculateType) {
        this.calculateType = calculateType;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public String getClearStats() {
        return clearStats;
    }

    public void setClearStats(String clearStats) {
        this.clearStats = clearStats;
    }

    public String getBusOrderNo() {
        return busOrderNo;
    }

    public void setBusOrderNo(String busOrderNo) {
        this.busOrderNo = busOrderNo;
    }

    public LocalDateTime getBusOrderTime() {
        return busOrderTime;
    }

    public void setBusOrderTime(LocalDateTime busOrderTime) {
        this.busOrderTime = busOrderTime;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}
