<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.tfm.dao.IRateRuleDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.tfm.entity.RateRuleDO" >
        <id column="bus_type" property="busType" jdbcType="VARCHAR" />
        <result column="bus_type_desc" property="busTypeDesc" jdbcType="VARCHAR" />
        <result column="ccy" property="ccy" jdbcType="VARCHAR" />
        <result column="calculate_mod" property="calculateMod" jdbcType="VARCHAR" />
        <result column="calculate_type" property="calculateType" jdbcType="VARCHAR" />
        <result column="rate" property="rate" jdbcType="DECIMAL" />
        <result column="fix_fee" property="fixFee" jdbcType="DECIMAL" />
        <result column="charge_type" property="chargeType" jdbcType="VARCHAR" />
        <result column="calculate_min_amt" property="calculateMinAmt" jdbcType="DECIMAL" />
        <result column="min_fee" property="minFee" jdbcType="DECIMAL" />
        <result column="max_fee" property="maxFee" jdbcType="DECIMAL" />
        <result column="stats" property="stats" jdbcType="CHAR" />
        <result column="eff_date" property="effDate" jdbcType="DATE" />
        <result column="exp_date" property="expDate" jdbcType="DATE" />
        <result column="opr_id" property="oprId" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        bus_type, bus_type_desc, ccy, calculate_mod, calculate_type, rate, fix_fee, charge_type, calculate_min_amt,
        min_fee, max_fee, stats, eff_date, exp_date, opr_id, create_time, modify_time, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from tfm_rate_rule
        where bus_type = #{busType,jdbcType=VARCHAR}
    </select>
    
    <select id="getByCondition" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from tfm_rate_rule
        <where>
            <if test="busType != null" >
                bus_type = #{busType,jdbcType=VARCHAR}
            </if>
            <if test="busTypeDesc != null" >
                and bus_type_desc = #{busTypeDesc,jdbcType=VARCHAR}
            </if>
            <if test="ccy != null" >
                and ccy = #{ccy,jdbcType=VARCHAR}
            </if>
            <if test="calculateMod != null" >
                and calculate_mod = #{calculateMod,jdbcType=VARCHAR}
            </if>
            <if test="calculateType != null" >
                and calculate_type = #{calculateType,jdbcType=VARCHAR}
            </if>
            <if test="rate != null" >
                and rate = #{rate,jdbcType=DECIMAL}
            </if>
            <if test="fixFee != null" >
                and fix_fee = #{fixFee,jdbcType=DECIMAL}
            </if>
            <if test="chargeType != null" >
                and charge_type = #{chargeType,jdbcType=VARCHAR}
            </if>
            <if test="calculateMinAmt != null" >
                and calculate_min_amt = #{calculateMinAmt,jdbcType=DECIMAL}
            </if>
            <if test="minFee != null" >
                and min_fee = #{minFee,jdbcType=DECIMAL}
            </if>
            <if test="maxFee != null" >
                and max_fee = #{maxFee,jdbcType=DECIMAL}
            </if>
            <if test="stats != null" >
                and stats = #{stats,jdbcType=CHAR}
            </if>
            <if test="effDate != null" >
                and eff_date = #{effDate,jdbcType=DATE}
            </if>
            <if test="expDate != null" >
                and exp_date = #{expDate,jdbcType=DATE}
            </if>
            <if test="oprId != null" >
                and opr_id = #{oprId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from tfm_rate_rule
        where bus_type = #{busType,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.tfm.entity.RateRuleDO" >
        insert into tfm_rate_rule
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="busType != null" >
                bus_type,
            </if>
            <if test="busTypeDesc != null" >
                bus_type_desc,
            </if>
            <if test="ccy != null" >
                ccy,
            </if>
            <if test="calculateMod != null" >
                calculate_mod,
            </if>
            <if test="calculateType != null" >
                calculate_type,
            </if>
            <if test="rate != null" >
                rate,
            </if>
            <if test="fixFee != null" >
                fix_fee,
            </if>
            <if test="chargeType != null" >
                charge_type,
            </if>
            <if test="calculateMinAmt != null" >
                calculate_min_amt,
            </if>
            <if test="minFee != null" >
                min_fee,
            </if>
            <if test="maxFee != null" >
                max_fee,
            </if>
            <if test="stats != null" >
                stats,
            </if>
            <if test="effDate != null" >
                eff_date,
            </if>
            <if test="expDate != null" >
                exp_date,
            </if>
            <if test="oprId != null" >
                opr_id,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="busType != null" >
                #{busType,jdbcType=VARCHAR},
            </if>
            <if test="busTypeDesc != null" >
                #{busTypeDesc,jdbcType=VARCHAR},
            </if>
            <if test="ccy != null" >
                #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="calculateMod != null" >
                #{calculateMod,jdbcType=VARCHAR},
            </if>
            <if test="calculateType != null" >
                #{calculateType,jdbcType=VARCHAR},
            </if>
            <if test="rate != null" >
                #{rate,jdbcType=DECIMAL},
            </if>
            <if test="fixFee != null" >
                #{fixFee,jdbcType=DECIMAL},
            </if>
            <if test="chargeType != null" >
                #{chargeType,jdbcType=VARCHAR},
            </if>
            <if test="calculateMinAmt != null" >
                #{calculateMinAmt,jdbcType=DECIMAL},
            </if>
            <if test="minFee != null" >
                #{minFee,jdbcType=DECIMAL},
            </if>
            <if test="maxFee != null" >
                #{maxFee,jdbcType=DECIMAL},
            </if>
            <if test="stats != null" >
                #{stats,jdbcType=CHAR},
            </if>
            <if test="effDate != null" >
                #{effDate,jdbcType=DATE},
            </if>
            <if test="expDate != null" >
                #{expDate,jdbcType=DATE},
            </if>
            <if test="oprId != null" >
                #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.tfm.entity.RateRuleDO" >
        update tfm_rate_rule
        <set >
            <if test="busTypeDesc != null" >
                bus_type_desc = #{busTypeDesc,jdbcType=VARCHAR},
            </if>
            <if test="ccy != null" >
                ccy = #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="calculateMod != null" >
                calculate_mod = #{calculateMod,jdbcType=VARCHAR},
            </if>
            <if test="calculateType != null" >
                calculate_type = #{calculateType,jdbcType=VARCHAR},
            </if>
            <if test="rate != null" >
                rate = #{rate,jdbcType=DECIMAL},
            </if>
            <if test="fixFee != null" >
                fix_fee = #{fixFee,jdbcType=DECIMAL},
            </if>
            <if test="chargeType != null" >
                charge_type = #{chargeType,jdbcType=VARCHAR},
            </if>
            <if test="calculateMinAmt != null" >
                calculate_min_amt = #{calculateMinAmt,jdbcType=DECIMAL},
            </if>
            <if test="minFee != null" >
                min_fee = #{minFee,jdbcType=DECIMAL},
            </if>
            <if test="maxFee != null" >
                max_fee = #{maxFee,jdbcType=DECIMAL},
            </if>
            <if test="stats != null" >
                stats = #{stats,jdbcType=CHAR},
            </if>
            <if test="effDate != null" >
                eff_date = #{effDate,jdbcType=DATE},
            </if>
            <if test="expDate != null" >
                exp_date = #{expDate,jdbcType=DATE},
            </if>
            <if test="oprId != null" >
                opr_id = #{oprId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where bus_type = #{busType,jdbcType=VARCHAR}
    </update>
</mapper>