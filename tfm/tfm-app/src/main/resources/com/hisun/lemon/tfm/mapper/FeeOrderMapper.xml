<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.tfm.dao.IFeeOrderDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.tfm.entity.FeeOrderDO" >
        <id column="order_no" property="orderNo" jdbcType="VARCHAR" />
        <result column="user_id" property="userId" jdbcType="VARCHAR" />
        <result column="user_name" property="userName" jdbcType="VARCHAR" />
        <result column="bus_type" property="busType" jdbcType="VARCHAR" />
        <result column="bus_type_desc" property="busTypeDesc" jdbcType="VARCHAR" />
        <result column="ccy" property="ccy" jdbcType="VARCHAR" />
        <result column="trade_total_amt" property="tradeTotalAmt" jdbcType="DECIMAL" />
        <result column="trade_amt" property="tradeAmt" jdbcType="DECIMAL" />
        <result column="trade_date" property="tradeDate" jdbcType="DATE" />
        <result column="trade_time" property="tradeTime" jdbcType="TIME" />
        <result column="calculate_mod" property="calculateMod" jdbcType="VARCHAR" />
        <result column="calculate_type" property="calculateType" jdbcType="VARCHAR" />
        <result column="rate" property="rate" jdbcType="DECIMAL" />
        <result column="fee" property="fee" jdbcType="DECIMAL" />
        <result column="clear_stats" property="clearStats" jdbcType="CHAR" />
        <result column="bus_order_no" property="busOrderNo" jdbcType="VARCHAR" />
        <result column="bus_order_time" property="busOrderTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="tm_smp" property="tmSmp" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        order_no, user_id, user_name, bus_type, bus_type_desc, ccy, trade_total_amt, trade_amt, 
        trade_date, trade_time, calculate_mod, calculate_type, rate, fee, clear_stats, bus_order_no, 
        bus_order_time, create_time, modify_time, tm_smp
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from tfm_fee_order
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </select>

    <!--查询该业务订单号最早录入的一笔交易计费订单-->
    <select id="getFirstBusOrder" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from tfm_fee_order
        where bus_order_no = #{busOrderNo,jdbcType=VARCHAR}
        order by order_no asc
        limit 0, 1
    </select>
    
    <select id="getRecentNeedHandle" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from tfm_fee_order
        where bus_order_no = #{busOrderNo,jdbcType=VARCHAR}
          and clear_stats in ('0', '1')
        order by order_no desc
        limit 0, 1
    </select>
    
    <select id="getRecentRefund" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from tfm_fee_order
        where bus_order_no = #{busOrderNo,jdbcType=VARCHAR}
          and clear_stats = '2'
        order by order_no desc
        limit 0, 1
    </select>
    
    <select id="getListByCondition" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from tfm_fee_order
        <where>
            <if test="orderNo != null" >
                order_no = #{orderNo,jdbcType=VARCHAR}
            </if>
            <if test="userId != null" >
                and user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="userName != null" >
                and user_name = #{userName,jdbcType=VARCHAR}
            </if>
            <if test="busType != null" >
                and bus_type = #{busType,jdbcType=VARCHAR}
            </if>
            <if test="busTypeDesc != null" >
                and bus_type_desc = #{busTypeDesc,jdbcType=VARCHAR}
            </if>
            <if test="ccy != null" >
                and ccy = #{ccy,jdbcType=VARCHAR}
            </if>
            <if test="tradeTotalAmt != null" >
                and trade_total_amt = #{tradeTotalAmt,jdbcType=DECIMAL}
            </if>
            <if test="tradeAmt != null" >
                and trade_amt = #{tradeAmt,jdbcType=DECIMAL}
            </if>
            <if test="tradeDate != null" >
                and trade_date = #{tradeDate,jdbcType=DATE}
            </if>
            <if test="tradeTime != null" >
                and trade_time = #{tradeTime,jdbcType=TIME}
            </if>
            <if test="calculateMod != null" >
                and calculate_mod = #{calculateMod,jdbcType=VARCHAR}
            </if>
            <if test="calculateType != null" >
                and calculate_type = #{calculateType,jdbcType=VARCHAR}
            </if>
            <if test="rate != null" >
                and rate = #{rate,jdbcType=DECIMAL}
            </if>
            <if test="fee != null" >
                and fee = #{fee,jdbcType=DECIMAL}
            </if>
            <if test="clearStats != null" >
                and clear_stats = #{clearStats,jdbcType=CHAR}
            </if>
            <if test="busOrderNo != null" >
                and bus_order_no = #{busOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="busOrderTime != null" >
                and bus_order_time = #{busOrderTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        order by order_no
    </select>
    
    <select id="getUnfinishListByCondition" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from tfm_fee_order
        <where>
            <if test="orderNo != null" >
                order_no = #{orderNo,jdbcType=VARCHAR}
            </if>
            <if test="userId != null" >
                and user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="userName != null" >
                and user_name = #{userName,jdbcType=VARCHAR}
            </if>
            <if test="busType != null" >
                and bus_type = #{busType,jdbcType=VARCHAR}
            </if>
            <if test="busTypeDesc != null" >
                and bus_type_desc = #{busTypeDesc,jdbcType=VARCHAR}
            </if>
            <if test="ccy != null" >
                and ccy = #{ccy,jdbcType=VARCHAR}
            </if>
            <if test="tradeTotalAmt != null" >
                and trade_total_amt = #{tradeTotalAmt,jdbcType=DECIMAL}
            </if>
            <if test="tradeAmt != null" >
                and trade_amt = #{tradeAmt,jdbcType=DECIMAL}
            </if>
            <if test="tradeDate != null" >
                and trade_date &gt;= #{tradeDate,jdbcType=DATE}
            </if>
            <if test="tradeTime != null" >
                and trade_time = #{tradeTime,jdbcType=TIME}
            </if>
            <if test="calculateMod != null" >
                and calculate_mod = #{calculateMod,jdbcType=VARCHAR}
            </if>
            <if test="calculateType != null" >
                and calculate_type = #{calculateType,jdbcType=VARCHAR}
            </if>
            <if test="rate != null" >
                and rate = #{rate,jdbcType=DECIMAL}
            </if>
            <if test="fee != null" >
                and fee = #{fee,jdbcType=DECIMAL}
            </if>
            <if test="clearStats != null" >
                and clear_stats = #{clearStats,jdbcType=CHAR}
            </if>
            <if test="busOrderNo != null" >
                and bus_order_no = #{busOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="busOrderTime != null" >
                and bus_order_time = #{busOrderTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        order by order_no
    </select>
    
    <select id="getByCondition" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from tfm_fee_order
        <where>
            <if test="orderNo != null" >
                order_no = #{orderNo,jdbcType=VARCHAR}
            </if>
            <if test="userId != null" >
                and user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="userName != null" >
                and user_name = #{userName,jdbcType=VARCHAR}
            </if>
            <if test="busType != null" >
                and bus_type = #{busType,jdbcType=VARCHAR}
            </if>
            <if test="busTypeDesc != null" >
                and bus_type_desc = #{busTypeDesc,jdbcType=VARCHAR}
            </if>
            <if test="ccy != null" >
                and ccy = #{ccy,jdbcType=VARCHAR}
            </if>
            <if test="tradeTotalAmt != null" >
                and trade_total_amt = #{tradeTotalAmt,jdbcType=DECIMAL}
            </if>
            <if test="tradeAmt != null" >
                and trade_amt = #{tradeAmt,jdbcType=DECIMAL}
            </if>
            <if test="tradeDate != null" >
                and trade_date = #{tradeDate,jdbcType=DATE}
            </if>
            <if test="tradeTime != null" >
                and trade_time = #{tradeTime,jdbcType=TIME}
            </if>
            <if test="calculateMod != null" >
                and calculate_mod = #{calculateMod,jdbcType=VARCHAR}
            </if>
            <if test="calculateType != null" >
                and calculate_type = #{calculateType,jdbcType=VARCHAR}
            </if>
            <if test="rate != null" >
                and rate = #{rate,jdbcType=DECIMAL}
            </if>
            <if test="fee != null" >
                and fee = #{fee,jdbcType=DECIMAL}
            </if>
            <if test="clearStats != null" >
                and clear_stats = #{clearStats,jdbcType=CHAR}
            </if>
            <if test="busOrderNo != null" >
                and bus_order_no = #{busOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="busOrderTime != null" >
                and bus_order_time = #{busOrderTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from tfm_fee_order
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.tfm.entity.FeeOrderDO" >
        insert into tfm_fee_order
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="orderNo != null" >
                order_no,
            </if>
            <if test="userId != null" >
                user_id,
            </if>
            <if test="userName != null" >
                user_name,
            </if>
            <if test="busType != null" >
                bus_type,
            </if>
            <if test="busTypeDesc != null" >
                bus_type_desc,
            </if>
            <if test="ccy != null" >
                ccy,
            </if>
            <if test="tradeTotalAmt != null" >
                trade_total_amt,
            </if>
            <if test="tradeAmt != null" >
                trade_amt,
            </if>
            <if test="tradeDate != null" >
                trade_date,
            </if>
            <if test="tradeTime != null" >
                trade_time,
            </if>
            <if test="calculateMod != null" >
                calculate_mod,
            </if>
            <if test="calculateType != null" >
                calculate_type,
            </if>
            <if test="rate != null" >
                rate,
            </if>
            <if test="fee != null" >
                fee,
            </if>
            <if test="clearStats != null" >
                clear_stats,
            </if>
            <if test="busOrderNo != null" >
                bus_order_no,
            </if>
            <if test="busOrderTime != null" >
                bus_order_time,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="orderNo != null" >
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null" >
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="busType != null" >
                #{busType,jdbcType=VARCHAR},
            </if>
            <if test="busTypeDesc != null" >
                #{busTypeDesc,jdbcType=VARCHAR},
            </if>
            <if test="ccy != null" >
                #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="tradeTotalAmt != null" >
                #{tradeTotalAmt,jdbcType=DECIMAL},
            </if>
            <if test="tradeAmt != null" >
                #{tradeAmt,jdbcType=DECIMAL},
            </if>
            <if test="tradeDate != null" >
                #{tradeDate,jdbcType=DATE},
            </if>
            <if test="tradeTime != null" >
                #{tradeTime,jdbcType=TIME},
            </if>
            <if test="calculateMod != null" >
                #{calculateMod,jdbcType=VARCHAR},
            </if>
            <if test="calculateType != null" >
                #{calculateType,jdbcType=VARCHAR},
            </if>
            <if test="rate != null" >
                #{rate,jdbcType=DECIMAL},
            </if>
            <if test="fee != null" >
                #{fee,jdbcType=DECIMAL},
            </if>
            <if test="clearStats != null" >
                #{clearStats,jdbcType=CHAR},
            </if>
            <if test="busOrderNo != null" >
                #{busOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="busOrderTime != null" >
                #{busOrderTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.tfm.entity.FeeOrderDO" >
        update tfm_fee_order
        <set >
            <if test="userId != null" >
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null" >
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="busType != null" >
                bus_type = #{busType,jdbcType=VARCHAR},
            </if>
            <if test="busTypeDesc != null" >
                bus_type_desc = #{busTypeDesc,jdbcType=VARCHAR},
            </if>
            <if test="ccy != null" >
                ccy = #{ccy,jdbcType=VARCHAR},
            </if>
            <if test="tradeTotalAmt != null" >
                trade_total_amt = #{tradeTotalAmt,jdbcType=DECIMAL},
            </if>
            <if test="tradeAmt != null" >
                trade_amt = #{tradeAmt,jdbcType=DECIMAL},
            </if>
            <if test="tradeDate != null" >
                trade_date = #{tradeDate,jdbcType=DATE},
            </if>
            <if test="tradeTime != null" >
                trade_time = #{tradeTime,jdbcType=TIME},
            </if>
            <if test="calculateMod != null" >
                calculate_mod = #{calculateMod,jdbcType=VARCHAR},
            </if>
            <if test="calculateType != null" >
                calculate_type = #{calculateType,jdbcType=VARCHAR},
            </if>
            <if test="rate != null" >
                rate = #{rate,jdbcType=DECIMAL},
            </if>
            <if test="fee != null" >
                fee = #{fee,jdbcType=DECIMAL},
            </if>
            <if test="clearStats != null" >
                clear_stats = #{clearStats,jdbcType=CHAR},
            </if>
            <if test="busOrderNo != null" >
                bus_order_no = #{busOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="busOrderTime != null" >
                bus_order_time = #{busOrderTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </update>
    
    
    <update id="updateByCondition" parameterType="com.hisun.lemon.tfm.entity.FeeOrderDO" >
        update tfm_fee_order
        <set >
            <if test="valueDO.userId != null" >
                user_id = #{valueDO.userId,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.userName != null" >
                user_name = #{valueDO.userName,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.busType != null" >
                bus_type = #{valueDO.busType,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.busTypeDesc != null" >
                bus_type_desc = #{valueDO.busTypeDesc,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.ccy != null" >
                ccy = #{valueDO.ccy,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.tradeTotalAmt != null" >
                trade_total_amt = #{valueDO.tradeTotalAmt,jdbcType=DECIMAL},
            </if>
            <if test="valueDO.tradeAmt != null" >
                trade_amt = #{valueDO.tradeAmt,jdbcType=DECIMAL},
            </if>
            <if test="valueDO.tradeDate != null" >
                trade_date = #{valueDO.tradeDate,jdbcType=DATE},
            </if>
            <if test="valueDO.tradeTime != null" >
                trade_time = #{valueDO.tradeTime,jdbcType=TIME},
            </if>
            <if test="valueDO.calculateMod != null" >
                calculate_mod = #{valueDO.calculateMod,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.calculateType != null" >
                calculate_type = #{valueDO.calculateType,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.rate != null" >
                rate = #{valueDO.rate,jdbcType=DECIMAL},
            </if>
            <if test="valueDO.fee != null" >
                fee = #{valueDO.fee,jdbcType=DECIMAL},
            </if>
            <if test="valueDO.clearStats != null" >
                clear_stats = #{valueDO.clearStats,jdbcType=CHAR},
            </if>
            <if test="valueDO.busOrderNo != null" >
                bus_order_no = #{valueDO.busOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="valueDO.busOrderTime != null" >
                bus_order_time = #{valueDO.busOrderTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valueDO.createTime != null" >
                create_time = #{valueDO.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valueDO.modifyTime != null" >
                modify_time = #{valueDO.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="valueDO.tmSmp != null" >
                tm_smp = #{valueDO.tmSmp,jdbcType=TIMESTAMP},
            </if>
        </set>
        <where>
            <if test="conditionDO.orderNo != null" >
                order_no = #{conditionDO.orderNo,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.userId != null" >
                and user_id = #{conditionDO.userId,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.userName != null" >
                and user_name = #{conditionDO.userName,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.busType != null" >
                and bus_type = #{conditionDO.busType,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.busTypeDesc != null" >
                and bus_type_desc = #{conditionDO.busTypeDesc,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.ccy != null" >
                and ccy = #{conditionDO.ccy,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.tradeTotalAmt != null" >
                and trade_total_amt = #{conditionDO.tradeTotalAmt,jdbcType=DECIMAL}
            </if>
            <if test="conditionDO.tradeAmt != null" >
                and trade_amt = #{conditionDO.tradeAmt,jdbcType=DECIMAL}
            </if>
            <if test="conditionDO.tradeDate != null" >
                and trade_date = #{conditionDO.tradeDate,jdbcType=DATE}
            </if>
            <if test="conditionDO.tradeTime != null" >
                and trade_time = #{conditionDO.tradeTime,jdbcType=TIME}
            </if>
            <if test="conditionDO.calculateMod != null" >
                and calculate_mod = #{conditionDO.calculateMod,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.calculateType != null" >
                and calculate_type = #{conditionDO.calculateType,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.rate != null" >
                and rate = #{conditionDO.rate,jdbcType=DECIMAL}
            </if>
            <if test="conditionDO.fee != null" >
                and fee = #{conditionDO.fee,jdbcType=DECIMAL}
            </if>
            <if test="conditionDO.clearStats != null" >
                and clear_stats = #{conditionDO.clearStats,jdbcType=CHAR}
            </if>
            <if test="conditionDO.busOrderNo != null" >
                and bus_order_no = #{conditionDO.busOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="conditionDO.busOrderTime != null" >
                and bus_order_time = #{conditionDO.busOrderTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </update>
</mapper>