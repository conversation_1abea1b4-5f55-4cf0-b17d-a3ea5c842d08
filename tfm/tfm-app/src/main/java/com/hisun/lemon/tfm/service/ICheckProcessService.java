package com.hisun.lemon.tfm.service;

import java.time.LocalDate;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.tfm.entity.CheckControlDO;
import com.hisun.lemon.tfm.entity.CheckParamDO;

/**
 * @Description 对账处理服务接口
 * <AUTHOR>
 * @date 2017年7月7日 上午11:07:48
 * @version V1.0
 */
public interface ICheckProcessService {

    /**
     * @Description 获取对账文件
     * <AUTHOR>
     * @param checkParamDO
     * @param checkControlDO
     * @return
     * @throws LemonException
     */
    String checkFileGet(CheckParamDO checkParamDO, CheckControlDO checkControlDO) throws LemonException;
    
    /**
     * @Description 文件入库
     * <AUTHOR>
     * @param checkParamDO
     * @param checkControlDO
     * @return
     * @throws LemonException
     */
    String checkFileDatebase(CheckParamDO checkParamDO, CheckControlDO checkControlDO) throws LemonException;
    
    /**
     * @Description 对账处理
     * <AUTHOR>
     * @param checkParamDO
     * @param checkControlDO
     * @return
     * @throws LemonException
     */
    String checkProcess(LocalDate checkDate, CheckParamDO checkParamDO, CheckControlDO checkControlDO) throws LemonException;
    
    /**
     * @Description 差错处理
     * <AUTHOR>
     * @param checkParamDO
     * @param checkControlDO
     * @return
     * @throws LemonException
     */
    String checkErrorProcess(CheckParamDO checkParamDO, CheckControlDO checkControlDO) throws LemonException;
}
