/*
 * @ClassName IMerchantRateRuleDao
 * @Description 
 * @version 1.0
 * @Date 2017-08-05 14:33:00
 */
package com.hisun.lemon.tfm.dao;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.tfm.entity.MerchantRateRuleDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface IMerchantRateRuleDao extends BaseDao<MerchantRateRuleDO> {
    
    MerchantRateRuleDO getByCondition(MerchantRateRuleDO merchantRateRuleDO);

    MerchantRateRuleDO getValidByCondition(MerchantRateRuleDO qryInMerchantRateRuleDO);
}