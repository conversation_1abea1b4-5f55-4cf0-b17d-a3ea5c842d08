package com.hisun.lemon.tfm.mq;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.bil.constants.BilConstants;
import com.hisun.lemon.bil.dto.UpdateMercBillDTO;
import com.hisun.lemon.framework.stream.MultiOutput;
import com.hisun.lemon.framework.stream.producer.Producer;
import com.hisun.lemon.framework.stream.producer.Producers;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;
import com.hisun.lemon.tfm.entity.FeeOrderDO;

/**
 * @Description 账单同步
 * <AUTHOR>
 * @date 2017年10月20日 上午10:14:06 
 * @version V1.0
 */
@Component
public class BillSynchronizeProduce {
    
    private static final Logger logger = LoggerFactory.getLogger(BillSynchronizeProduce.class);
    
    @Resource
    private ObjectMapper objectMapper;
    
    /**
     * 清分账单信息同步
     */
    @Producers({
            @Producer(beanName=BilConstants.SYNC_UPDATE_MERC_BEAN, channelName= MultiOutput.OUTPUT_TWO)
    })
    public UpdateMercBillDTO billSynchronizeNotify(FeeOrderDO feeOrderDO) {
        //通知消息
        UpdateMercBillDTO updateMercBillDTO = new UpdateMercBillDTO();
        updateMercBillDTO.setOrderNo(feeOrderDO.getBusOrderNo());
        updateMercBillDTO.setCheckDate(feeOrderDO.getTradeDate());
        updateMercBillDTO.setServeFee(feeOrderDO.getFee());
        String rspData = ObjectMapperHelper.writeValueAsString(objectMapper, updateMercBillDTO, true);
        logger.info("BillSynchronizeProduce.billSynchronizeNotify()清分模块，清分账单异步通知：" + rspData);
        return updateMercBillDTO;
    }


}
