package com.hisun.lemon.tfm.service;

import java.time.LocalDate;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.tfm.dto.*;
import com.hisun.lemon.tfm.entity.CheckControlDO;
import com.hisun.lemon.tfm.entity.FeeOrderDO;

/**
 * @Description 计费服务接口
 * <AUTHOR>
 * @date 2017年7月7日 上午11:07:48
 * @version V1.0
 */
public interface ITfmService {

    /**
     * @Description 交易费率查询
     * <AUTHOR>
     * @param tradeFeeReqDTO
     * @return
     * @throws LemonException
     */
    TradeRateRspDTO tradeRate(TradeRateReqDTO tradeRateReqDTO) throws LemonException;

    /**
     * @Description 商户费率查询
     * <AUTHOR>
     * @param tradeFeeReqDTO
     * @return
     * @throws LemonException
     */
    MerchantRateRspDTO merchantRate(MerchantRateReqDTO merchantRateReqDTO) throws LemonException;

    /**
     * @Description 交易手续费预算
     * <AUTHOR>
     * @param tradeFeeReqDTO
     * @return
     * @throws LemonException
     */
    TradeFeeCaculateRspDTO tradeFeeCalculate(TradeFeeCaculateReqDTO tradeFeeReqDTO) throws LemonException;

    /**
     * @Description 商户手续费预算
     * <AUTHOR>
     * @param tradeFeeReqDTO
     * @return
     * @throws LemonException
     */
    MerchantFeeCalculateRspDTO merchantFeeCalculate(MerchantFeeCalculateReqDTO merchantFeeCalculateReqDTO)
            throws LemonException;

    /**
     * @Description 交易手续费
     * <AUTHOR>
     * @param tradeFeeReqDTO
     * @return
     * @throws LemonException
     */
    TradeFeeRspDTO tradeFee(TradeFeeReqDTO tradeFeeReqDTO) throws LemonException;

    /**
     * @Description 商户手续费
     * <AUTHOR>
     * @param tradeFeeReqDTO
     * @return
     * @throws LemonException
     */
    TradeFeeRspDTO merchantTradeFee(TradeFeeReqDTO tradeFeeReqDTO) throws LemonException;

    /**
     * @Description 商户结算、转账业务手续费登记，不需要清分
     * <AUTHOR>
     * @param tradeFeeReqDTO
     * @return
     * @throws LemonException
     */
    TradeFeeRspDTO merchantTransferTradeFee(TradeFeeReqDTO tradeFeeReqDTO) throws LemonException;


    /**
     * @Description 商户手续费退款处理，按照rate计算需退的手续费金额
     * <AUTHOR>
     * @param refundFeeReqDTO
     * @throws LemonException
     */
    MerchantRefundFeeRspDTO merchantRefundFee(MerchantRefundFeeReqDTO refundFeeReqDTO) throws LemonException;

    /**
     * @Description 商户手续费退款处理，按照退款金额占原订单金额比例，计算需退的手续费金额
     * <AUTHOR>
     * @param refundFeeReqDTO
     * @throws LemonException
     */
    MerchantRefundFeeRspDTO merchantRefundFeeNew(MerchantRefundFeeReqDTO refundFeeReqDTO) throws LemonException;

    /**
     * @Description 商户手续费退款冲正处理
     * <AUTHOR>
     * @param refundFeeReversalReqDTO
     * @throws LemonException
     */
    void merchantRefundFeeReversal(MerchantRefundFeeReversalReqDTO refundFeeReversalReqDTO) throws LemonException;
    
    
    /**
     * @Description 订单清分处理
     * <AUTHOR>
     * @param checkDate
     * @param checkControlDO
     * @throws LemonException
     */
    void clearHandler(LocalDate checkDate, CheckControlDO checkControlDO) throws LemonException;

    /**
     * @Description 查询订单清分结果
     * <AUTHOR>
     * @param merchantCleanReqDTO
     * @throws LemonException
     */
    FeeOrderDO merchantCleanQuery(MerchantCleanReqDTO merchantCleanReqDTO) throws LemonException;

}
