/*
 * @ClassName ICheckParamDao
 * @Description 
 * @version 1.0
 * @Date 2017-08-07 10:47:20
 */
package com.hisun.lemon.tfm.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.tfm.entity.CheckParamDO;

@Mapper
public interface ICheckParamDao extends BaseDao<CheckParamDO> {
    
    CheckParamDO getByCondition(CheckParamDO checkParamDO);
    
    List<CheckParamDO> getListByCondition(CheckParamDO checkParamDO);
}