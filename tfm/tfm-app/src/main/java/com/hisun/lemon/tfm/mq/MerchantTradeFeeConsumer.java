package com.hisun.lemon.tfm.mq;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.framework.data.GenericCmdDTO;
import com.hisun.lemon.framework.stream.MessageHandler;
import com.hisun.lemon.framework.utils.ObjectMapperHelper;
import com.hisun.lemon.tfm.dto.TradeFeeReqDTO;
import com.hisun.lemon.tfm.service.ITfmService;

/**
 * @Description 商户交易手续费异步登记
 * <AUTHOR>
 * @date 2017年8月29日 上午10:54:34 
 * @version V1.0
 */
@Component("merchantTradeFeeConsumer")
public class MerchantTradeFeeConsumer implements MessageHandler<TradeFeeReqDTO> {
    
    private static final Logger logger = LoggerFactory.getLogger(MerchantTradeFeeConsumer.class);
    @Resource
    private ITfmService tfmService;
    @Resource
    private ObjectMapper objectMapper;

    @Override
    public void onMessageReceive(GenericCmdDTO<TradeFeeReqDTO> genericCmdDTO) {
        logger.info("Receive msg hand {}", ObjectMapperHelper.writeValueAsString(objectMapper, genericCmdDTO.getBody(), true));
        try {
            tfmService.merchantTradeFee(genericCmdDTO.getBody());
        } catch (LemonException e) {
            logger.info("Receive msg hand error {}", e.getMsgCd());
        }
    }

}
