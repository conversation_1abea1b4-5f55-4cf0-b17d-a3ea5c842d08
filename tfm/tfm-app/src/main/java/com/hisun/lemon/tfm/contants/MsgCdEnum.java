package com.hisun.lemon.tfm.contants;

/**
 * @Description 计费模块错误码枚举类
 * <AUTHOR>
 * @date 2017年7月7日 下午3:27:58
 * @version V1.0
 */
public enum MsgCdEnum {

    /** 交易成功 */
    SUCCESS("TFM00000", "transaction successfull"),
    /** 用户号不能为空 */
    USERID_CANTNULL("TFM10001", "user id cannot be empty"),
    /** 业务类型不能为空 */
    BUSTYPE_CANTNULL("TFM10002", "business cannot be empty"),
    /** 交易金额不能为空 */
    TXAMT_CANTNULL("TFM10003", "trade amt cannot be empty"),
    /** 交易订单号不能为空 */
    BUS_ORDER_NO_CANTNULL("TFM10004", "business order no cannot be empty"),
    /** 币种不能为空 */
    TRADE_CCY_CANTNULL("TFM10005", "trade ccy cannot be empty"),
    /** 订单时间不能为空 */
    BUS_ORDER_TIME_CANTNULL("TFM10008", "business order time cannot be empty"),
    /** 交易费率信息不存在 */
    TRADE_FEE_RATE_NOT_EXISTS("TFM30001", "trade fee not exists"),
    /** 手续费登记失败 */
    TRADE_FEE_REG_FAIL("TFM30002", "trade fee register failed"), 
    /** 退款金额大于原订单金额 */
    REFUND_AMT_ERROR("TFM30003", "trade amt grater then trade order amt"), 
    /** 交易订单信息不存在 */
    BUS_ORDER_NOT_EXSITS("TFM30004", "businerss order not exists"), 
    /** 手续费退款处理失败 */
    FEE_REFUND_FAIL("TFM30005", "fee refund failure"), 
    /** 手续费退款撤销处理失败 */
    FEE_REVERSAL_FAIL("TFM30006", "fee refund reversal failure"), 
    /** 对账组件配置有误 */
    CHECK_COMPONENT_NOT_EXISTS("TFM39998", "check component not exists"),
    /** 对账组件生成失败 */
    CHECK_COMPONENT_GEN_FAILURE("TFM39999", "check component generate failure"),
    LAST("CMM39999", "last");

    private String msgCd;
    private String msgInfo;

    private MsgCdEnum(String msgCd, String msgInfo) {
        this.msgCd = msgCd;
        this.msgInfo = msgInfo;
    }

    public String getMsgCd() {
        return msgCd;
    }

    public String getMsgInfo() {
        return msgInfo;
    }

}
