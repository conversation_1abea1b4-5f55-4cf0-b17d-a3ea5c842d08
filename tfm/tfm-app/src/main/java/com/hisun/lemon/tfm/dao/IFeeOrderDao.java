/*
 * @ClassName IFeeOrderDao
 * @Description 
 * @version 1.0
 * @Date 2017-08-07 16:27:02
 */
package com.hisun.lemon.tfm.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hisun.lemon.framework.dao.BaseDao;
import com.hisun.lemon.tfm.entity.FeeOrderDO;

@Mapper
public interface IFeeOrderDao extends BaseDao<FeeOrderDO> {
    
    FeeOrderDO getByCondition(FeeOrderDO feeOrderDO);
    
    List<FeeOrderDO> getListByCondition(FeeOrderDO feeOrderDO);
    
    List<FeeOrderDO> getUnfinishListByCondition(FeeOrderDO feeOrderDO);

    /**
     * 查询该业务订单号最早录入的一笔交易计费订单
     */
    FeeOrderDO getFirstBusOrder(@Param("busOrderNo")String busOrderNo);
    
    FeeOrderDO getRecentNeedHandle(@Param("busOrderNo")String busOrderNo);
    
    FeeOrderDO getRecentRefund(@Param("busOrderNo")String busOrderNo);
    
    int updateByCondition(@Param("valueDO")FeeOrderDO updateValueFeeOrderDO, @Param("conditionDO")FeeOrderDO updateConditionFeeOrderDO);
}