/*
 * @ClassName CheckParamDO
 * @Description 
 * @version 1.0
 * @Date 2017-08-07 10:47:20
 */
package com.hisun.lemon.tfm.entity;

import java.time.LocalDate;
import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.BaseDO;

public class CheckParamDO extends BaseDO {
    /**
     * @Fields checkTypeId 对账类型
     */
    private String checkTypeId;
    /**
     * @Fields importTable 导入数据库表名
     */
    private String importTable;
    /**
     * @Fields importFormat 导入数据格式
     */
    private String importFormat;
    /**
     * @Fields importFilePath 导入数据文件路径
     */
    private String importFilePath;
    /**
     * @Fields exportTable 导出数据库表名
     */
    private String exportTable;
    /**
     * @Fields exportFormat 导出数据格式
     */
    private String exportFormat;
    /**
     * @Fields exportFilePath 导出数据文件路径
     */
    private String exportFilePath;
    /**
     * @Fields autoFlag 自动处理标识 U:人工 S:系统
     */
    private String autoFlag;
    /**
     * @Fields encryptFlag 加密标识 0:否 1:是
     */
    private String encryptFlag;
    /**
     * @Fields encryptComponent 加密组件名称
     */
    private String encryptComponent;
    /**
     * @Fields descryptFlag 解密标识 0:否 1:是
     */
    private String descryptFlag;
    /**
     * @Fields descryptComponent 解密组件名称
     */
    private String descryptComponent;
    /**
     * @Fields checkProcessComponent 对账处理组件名称
     */
    private String checkProcessComponent;
    /**
     * @Fields checkProcessClazz 对账处理组件类对象
     */
    private String checkProcessClazz;
    /**
     * @Fields errorProcessComponent 差错处理组件名称
     */
    private String errorProcessComponent;
    /**
     * @Fields errorProcessClazz 差错处理组件名称
     */
    private String errorProcessClazz;
    /**
     * @Fields sftpSysChannel 文件服务器系统渠道
     */
    private String sftpSysChannel;
    /**
     * @Fields sftpBusChannel 文件服务器业务渠道
     */
    private String sftpBusChannel;
    /**
     * @Fields multipleCheckFlg 每日多次对账标识 Y:是 N:否
     */
    private String multipleCheckFlg;
    /**
     * @Fields stats 状态0:无效 1:生效
     */
    private String stats;
    /**
     * @Fields effDate 生效日期
     */
    private LocalDate effDate;
    /**
     * @Fields expDate 失效日期
     */
    private LocalDate expDate;
    /**
     * @Fields oprId 操作员ID
     */
    private String oprId;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getCheckTypeId() {
        return checkTypeId;
    }

    public void setCheckTypeId(String checkTypeId) {
        this.checkTypeId = checkTypeId;
    }

    public String getImportTable() {
        return importTable;
    }

    public void setImportTable(String importTable) {
        this.importTable = importTable;
    }

    public String getImportFormat() {
        return importFormat;
    }

    public void setImportFormat(String importFormat) {
        this.importFormat = importFormat;
    }

    public String getImportFilePath() {
        return importFilePath;
    }

    public void setImportFilePath(String importFilePath) {
        this.importFilePath = importFilePath;
    }

    public String getExportTable() {
        return exportTable;
    }

    public void setExportTable(String exportTable) {
        this.exportTable = exportTable;
    }

    public String getExportFormat() {
        return exportFormat;
    }

    public void setExportFormat(String exportFormat) {
        this.exportFormat = exportFormat;
    }

    public String getExportFilePath() {
        return exportFilePath;
    }

    public void setExportFilePath(String exportFilePath) {
        this.exportFilePath = exportFilePath;
    }

    public String getAutoFlag() {
        return autoFlag;
    }

    public void setAutoFlag(String autoFlag) {
        this.autoFlag = autoFlag;
    }

    public String getEncryptFlag() {
        return encryptFlag;
    }

    public void setEncryptFlag(String encryptFlag) {
        this.encryptFlag = encryptFlag;
    }

    public String getEncryptComponent() {
        return encryptComponent;
    }

    public void setEncryptComponent(String encryptComponent) {
        this.encryptComponent = encryptComponent;
    }

    public String getDescryptFlag() {
        return descryptFlag;
    }

    public void setDescryptFlag(String descryptFlag) {
        this.descryptFlag = descryptFlag;
    }

    public String getDescryptComponent() {
        return descryptComponent;
    }

    public void setDescryptComponent(String descryptComponent) {
        this.descryptComponent = descryptComponent;
    }

    public String getCheckProcessComponent() {
        return checkProcessComponent;
    }

    public void setCheckProcessComponent(String checkProcessComponent) {
        this.checkProcessComponent = checkProcessComponent;
    }

    public String getErrorProcessComponent() {
        return errorProcessComponent;
    }

    public void setErrorProcessComponent(String errorProcessComponent) {
        this.errorProcessComponent = errorProcessComponent;
    }

    public String getCheckProcessClazz() {
        return checkProcessClazz;
    }

    public void setCheckProcessClazz(String checkProcessClazz) {
        this.checkProcessClazz = checkProcessClazz;
    }

    public String getErrorProcessClazz() {
        return errorProcessClazz;
    }

    public void setErrorProcessClazz(String errorProcessClazz) {
        this.errorProcessClazz = errorProcessClazz;
    }

    public String getSftpSysChannel() {
        return sftpSysChannel;
    }

    public void setSftpSysChannel(String sftpSysChannel) {
        this.sftpSysChannel = sftpSysChannel;
    }

    public String getSftpBusChannel() {
        return sftpBusChannel;
    }

    public void setSftpBusChannel(String sftpBusChannel) {
        this.sftpBusChannel = sftpBusChannel;
    }

    public String getMultipleCheckFlg() {
        return multipleCheckFlg;
    }

    public void setMultipleCheckFlg(String multipleCheckFlg) {
        this.multipleCheckFlg = multipleCheckFlg;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDate getEffDate() {
        return effDate;
    }

    public void setEffDate(LocalDate effDate) {
        this.effDate = effDate;
    }

    public LocalDate getExpDate() {
        return expDate;
    }

    public void setExpDate(LocalDate expDate) {
        this.expDate = expDate;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}