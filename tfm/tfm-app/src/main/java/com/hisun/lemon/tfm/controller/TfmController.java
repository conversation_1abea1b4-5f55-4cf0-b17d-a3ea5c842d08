package com.hisun.lemon.tfm.controller;

import javax.annotation.Resource;

import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.tfm.dto.*;
import com.hisun.lemon.tfm.entity.FeeOrderDO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.tfm.contants.MsgCdEnum;
import com.hisun.lemon.tfm.service.ITfmService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;

/**
 * 计费管理
 * 
 * @GetMapping @PostMapping @PutMapping @DeleteMapping @PatchMapping
 * <AUTHOR>
 * @date 2017年7月5日
 * @time 上午10:46:30
 *
 */
@RestController
@RequestMapping("/tfm")
@Api(tags = "TfmController", description = "计费服务")
public class TfmController extends BaseController{

    /**
     * 计费服务
     */
    @Resource
    private ITfmService tfmService;

    @ApiOperation(value = "交易费率查询", notes = "交易费率查询")
    @ApiResponse(code = 200, message = "查询交易费率结果")
    @PostMapping("/rate")
    public GenericRspDTO<TradeRateRspDTO> tradeRate(@Validated @RequestBody GenericDTO<TradeRateReqDTO> reqDTO) {
        GenericRspDTO<TradeRateRspDTO> rspDTO = new GenericRspDTO<>();
        TradeRateReqDTO tradeRateReqDTO = reqDTO.getBody();
        TradeRateRspDTO tradeRateRspDTO = null;
        try {
            tradeRateRspDTO = tfmService.tradeRate(tradeRateReqDTO);
            rspDTO.setBody(tradeRateRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }

    @ApiOperation(value = "商户费率查询", notes = "商户费率查询")
    @ApiResponse(code = 200, message = "查询商户费率结果")
    @PostMapping("/merchant/rate")
    public GenericRspDTO<MerchantRateRspDTO> merchantRate(
            @Validated @RequestBody GenericDTO<MerchantRateReqDTO> reqDTO) {
        GenericRspDTO<MerchantRateRspDTO> rspDTO = new GenericRspDTO<>();
        MerchantRateReqDTO merchantRateReqDTO = reqDTO.getBody();
        MerchantRateRspDTO merchantRateRspDTO = null;
        try {
            merchantRateRspDTO = tfmService.merchantRate(merchantRateReqDTO);
            rspDTO.setBody(merchantRateRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }

    @ApiOperation(value = "交易手续费预算", notes = "交易手续费预算")
    @ApiResponse(code = 200, message = "交易手续费预算结果")
    @PostMapping("/fee/calculate")
    public GenericRspDTO<TradeFeeCaculateRspDTO> tradeFeeCaculate(
            @Validated @RequestBody GenericDTO<TradeFeeCaculateReqDTO> reqDTO) {
        GenericRspDTO<TradeFeeCaculateRspDTO> rspDTO = new GenericRspDTO<>();
        TradeFeeCaculateReqDTO tradeFeeCaculateReqDTO = reqDTO.getBody();
        TradeFeeCaculateRspDTO tradeFeeCaculateRspDTO = null;
        try {
            tradeFeeCaculateRspDTO = tfmService.tradeFeeCalculate(tradeFeeCaculateReqDTO);
            rspDTO.setBody(tradeFeeCaculateRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }

    @ApiOperation(value = "商户手续费预算", notes = "商户手续费预算")
    @ApiResponse(code = 200, message = "商户手续费预算结果")
    @PostMapping("/merchant/fee/calculate")
    public GenericRspDTO<MerchantFeeCalculateRspDTO> merchanFeeCalculate(
            @Validated @RequestBody GenericDTO<MerchantFeeCalculateReqDTO> reqDTO) {
        GenericRspDTO<MerchantFeeCalculateRspDTO> rspDTO = new GenericRspDTO<>();
        MerchantFeeCalculateReqDTO merchantFeeCalculateReqDTO = reqDTO.getBody();
        MerchantFeeCalculateRspDTO merchantFeeCalculateRspDTO = null;
        try {
            merchantFeeCalculateRspDTO = tfmService.merchantFeeCalculate(merchantFeeCalculateReqDTO);
            rspDTO.setBody(merchantFeeCalculateRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }

    @ApiOperation(value = "交易手续费", notes = "交易手续费")
    @ApiResponse(code = 200, message = "交易手续费结果")
    @PostMapping("/fee")
    public GenericRspDTO<TradeFeeRspDTO> tradeFee(@Validated @RequestBody GenericDTO<TradeFeeReqDTO> reqDTO) {
        GenericRspDTO<TradeFeeRspDTO> rspDTO = new GenericRspDTO<>();
        TradeFeeReqDTO tradeFeeReqDTO = reqDTO.getBody();
        TradeFeeRspDTO tradeFeeRspDTO = null;
        try {
            tradeFeeRspDTO = tfmService.tradeFee(tradeFeeReqDTO);
            rspDTO.setBody(tradeFeeRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }

    @ApiOperation(value = "商户手续费", notes = "商户手续费")
    @ApiResponse(code = 200, message = "商户手续费结果")
    @PostMapping("/merchant/fee")
    public GenericRspDTO<TradeFeeRspDTO> merchantTradeFee(@Validated @RequestBody GenericDTO<TradeFeeReqDTO> reqDTO) {
        GenericRspDTO<TradeFeeRspDTO> rspDTO = new GenericRspDTO<>();
        TradeFeeReqDTO tradeFeeReqDTO = reqDTO.getBody();
        TradeFeeRspDTO tradeFeeRspDTO = null;
        try {
            tradeFeeRspDTO = tfmService.merchantTradeFee(tradeFeeReqDTO);
            rspDTO.setBody(tradeFeeRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }

    @ApiOperation(value = "商户结算、转账业务手续费登记，不需要清分", notes = "商户结算、转账业务手续费登记，不需要清分")
    @ApiResponse(code = 200, message = "商户结算、转账业务手续费登记，不需要清分")
    @PostMapping("/merchant/transfer/fee")
    public GenericRspDTO<TradeFeeRspDTO> merchantTransferTradeFee(@Validated @RequestBody GenericDTO<TradeFeeReqDTO> reqDTO) {
        GenericRspDTO<TradeFeeRspDTO> rspDTO = new GenericRspDTO<>();
        TradeFeeReqDTO tradeFeeReqDTO = reqDTO.getBody();
        TradeFeeRspDTO tradeFeeRspDTO = null;
        try {
            tradeFeeRspDTO = tfmService.merchantTransferTradeFee(tradeFeeReqDTO);
            rspDTO.setBody(tradeFeeRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }
    
    @ApiOperation(value = "商户手续费退款处理", notes = "商户手续费退款处理")
    @ApiResponse(code = 200, message = "处理商户退款手续费结果")
    @PostMapping("/merchant/fee/refund")
    public GenericRspDTO<MerchantRefundFeeRspDTO> merchantRefundFee(@Validated @RequestBody GenericDTO<MerchantRefundFeeReqDTO> reqDTO) {
        GenericRspDTO<MerchantRefundFeeRspDTO> rspDTO = new GenericRspDTO<>();
        MerchantRefundFeeReqDTO refundFeeReqDTO = reqDTO.getBody();
        MerchantRefundFeeRspDTO refundFeeRspDTO = null;
        try {
            //refundFeeRspDTO = tfmService.merchantRefundFee(refundFeeReqDTO);
            refundFeeRspDTO = tfmService.merchantRefundFeeNew(refundFeeReqDTO);
            rspDTO.setBody(refundFeeRspDTO);
            rspDTO.setMsgCd(MsgCdEnum.SUCCESS.getMsgCd());
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }
    
    @ApiOperation(value = "商户手续费退款处理冲正", notes = "商户手续费退款冲正处理")
    @ApiResponse(code = 200, message = "处理商户退款手续费结果")
    @PostMapping("/merchant/fee/refund/reversal")
    public GenericRspDTO<NoBody> merchantRefundFeeReversal(@Validated @RequestBody GenericDTO<MerchantRefundFeeReversalReqDTO> reqDTO) {
        GenericRspDTO<NoBody> rspDTO = GenericRspDTO.newSuccessInstance();
        MerchantRefundFeeReversalReqDTO refundFeeReversalReqDTO = reqDTO.getBody();
        try {
            tfmService.merchantRefundFeeReversal(refundFeeReversalReqDTO);
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }

    @ApiOperation(value = "查询订单清分结果", notes = "查询订单清分结果")
    @ApiResponse(code = 200, message = "查询订单清分结果")
    @PostMapping("/merchant/clean/detail")
    public GenericRspDTO<MerchantCleanRspDTO> merchantCleanQuery(@Validated @RequestBody GenericDTO<MerchantCleanReqDTO> reqDTO) {
        GenericRspDTO<MerchantCleanRspDTO> rspDTO = null;
        MerchantCleanReqDTO merchantCleanReqDTO = reqDTO.getBody();
        MerchantCleanRspDTO merchantCleanRspDTO = new MerchantCleanRspDTO();
        try {
            FeeOrderDO feeOrderDO = tfmService.merchantCleanQuery(merchantCleanReqDTO);
            if(feeOrderDO != null){
                BeanUtils.copyProperties(merchantCleanRspDTO, feeOrderDO);
            }
            rspDTO = GenericRspDTO.newSuccessInstance(merchantCleanRspDTO);
        } catch (LemonException e) {
            rspDTO.setMsgCd(e.getMsgCd());
        }
        return rspDTO;
    }

}
