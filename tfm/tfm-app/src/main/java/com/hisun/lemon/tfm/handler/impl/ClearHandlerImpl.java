package com.hisun.lemon.tfm.handler.impl;

import java.time.LocalDate;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.framework.lock.Locked;
import com.hisun.lemon.tfm.entity.CheckControlDO;
import com.hisun.lemon.tfm.handler.ICheckProcessHandler;
import com.hisun.lemon.tfm.service.ITfmService;

@Component
public class ClearHandlerImpl implements ICheckProcessHandler {
    
    @Resource
    private ITfmService tfmService;
    
    @Locked(lockName = "ClearHandlerLock", leaseTime=60, waitTime=30)
    public void checkFileProcess(LocalDate checkDate, CheckControlDO checkControlDO) throws LemonException {
        tfmService.clearHandler(checkDate, checkControlDO);
    }

}
