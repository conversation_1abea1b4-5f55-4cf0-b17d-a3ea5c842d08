/*
 * @ClassName RateRuleDO
 * @Description 
 * @version 1.0
 * @Date 2017-08-05 14:20:27
 */
package com.hisun.lemon.tfm.entity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.hisun.lemon.framework.data.BaseDO;

public class RateRuleDO extends BaseDO {
    /**
     * @Fields busType 业务类型
     */
    private String busType;
    /**
     * @Fields busTypeDesc 业务类型描述
     */
    private String busTypeDesc;
    /**
     * @Fields ccy 币种
     */
    private String ccy;
    /**
     * @Fields calculateMod 计费模式 internal:内扣 external:外扣
     */
    private String calculateMod;
    /**
     * @Fields calculateType 计费方式 percent:百分比 fixed:固定手续费
     */
    private String calculateType;
    /**
     * @Fields rate 费率
     */
    private BigDecimal rate;
    /**
     * @Fields fixFee 固定手续费
     */
    private BigDecimal fixFee;
    /**
     * @Fields chargeType 收费方式 single:单笔 cycle:固定周期
     */
    private String chargeType;
    /**
     * @Fields calculateMinAmt 计费起始金额
     */
    private BigDecimal calculateMinAmt;
    /**
     * @Fields minFee 最低收取费用
     */
    private BigDecimal minFee;
    /**
     * @Fields maxFee 最高收取费用
     */
    private BigDecimal maxFee;
    /**
     * @Fields stats 状态0:无效 1:生效
     */
    private String stats;
    /**
     * @Fields effDate 生效日期
     */
    private LocalDate effDate;
    /**
     * @Fields expDate 失效日期
     */
    private LocalDate expDate;
    /**
     * @Fields oprId 操作员ID
     */
    private String oprId;
    /**
     * @Fields tmSmp 时间戳
     */
    private LocalDateTime tmSmp;

    public String getBusType() {
        return busType;
    }

    public void setBusType(String busType) {
        this.busType = busType;
    }

    public String getBusTypeDesc() {
        return busTypeDesc;
    }

    public void setBusTypeDesc(String busTypeDesc) {
        this.busTypeDesc = busTypeDesc;
    }

    public String getCcy() {
        return ccy;
    }

    public void setCcy(String ccy) {
        this.ccy = ccy;
    }

    public String getCalculateMod() {
        return calculateMod;
    }

    public void setCalculateMod(String calculateMod) {
        this.calculateMod = calculateMod;
    }

    public String getCalculateType() {
        return calculateType;
    }

    public void setCalculateType(String calculateType) {
        this.calculateType = calculateType;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public BigDecimal getFixFee() {
        return fixFee;
    }

    public void setFixFee(BigDecimal fixFee) {
        this.fixFee = fixFee;
    }

    public String getChargeType() {
        return chargeType;
    }

    public void setChargeType(String chargeType) {
        this.chargeType = chargeType;
    }

    public BigDecimal getCalculateMinAmt() {
        return calculateMinAmt;
    }

    public void setCalculateMinAmt(BigDecimal calculateMinAmt) {
        this.calculateMinAmt = calculateMinAmt;
    }

    public BigDecimal getMinFee() {
        return minFee;
    }

    public void setMinFee(BigDecimal minFee) {
        this.minFee = minFee;
    }

    public BigDecimal getMaxFee() {
        return maxFee;
    }

    public void setMaxFee(BigDecimal maxFee) {
        this.maxFee = maxFee;
    }

    public String getStats() {
        return stats;
    }

    public void setStats(String stats) {
        this.stats = stats;
    }

    public LocalDate getEffDate() {
        return effDate;
    }

    public void setEffDate(LocalDate effDate) {
        this.effDate = effDate;
    }

    public LocalDate getExpDate() {
        return expDate;
    }

    public void setExpDate(LocalDate expDate) {
        this.expDate = expDate;
    }

    public String getOprId() {
        return oprId;
    }

    public void setOprId(String oprId) {
        this.oprId = oprId;
    }

    public LocalDateTime getTmSmp() {
        return tmSmp;
    }

    public void setTmSmp(LocalDateTime tmSmp) {
        this.tmSmp = tmSmp;
    }
}