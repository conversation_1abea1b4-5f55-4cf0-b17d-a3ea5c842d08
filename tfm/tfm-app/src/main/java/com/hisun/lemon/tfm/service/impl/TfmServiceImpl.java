package com.hisun.lemon.tfm.service.impl;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import com.hisun.lemon.tfm.dto.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.hisun.lemon.acm.client.AccountManagementClient;
import com.hisun.lemon.acm.client.AccountingTreatmentClient;
import com.hisun.lemon.acm.dto.AccountingReqDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.data.NoBody;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.tfm.contants.Constants;
import com.hisun.lemon.tfm.contants.MsgCdEnum;
import com.hisun.lemon.tfm.dao.ICheckControlDao;
import com.hisun.lemon.tfm.dao.IFeeOrderDao;
import com.hisun.lemon.tfm.dao.IMerchantRateRuleDao;
import com.hisun.lemon.tfm.dao.IRateRuleDao;
import com.hisun.lemon.tfm.entity.CheckControlDO;
import com.hisun.lemon.tfm.entity.FeeOrderDO;
import com.hisun.lemon.tfm.entity.MerchantRateRuleDO;
import com.hisun.lemon.tfm.entity.RateRuleDO;
import com.hisun.lemon.tfm.mq.BillSynchronizeProduce;
import com.hisun.lemon.tfm.service.ITfmService;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.lemon.urm.dto.UserBasicInfDTO;

@Transactional
@Service
public class TfmServiceImpl extends BaseService implements ITfmService {

    private static final Logger logger = LoggerFactory.getLogger(TfmServiceImpl.class);

    @Resource
    private IRateRuleDao rateRuleDao;
    @Resource
    private IMerchantRateRuleDao merchantRateRuleDao;
    @Resource
    private IFeeOrderDao feeOrderDao;
    @Resource
    private UserBasicInfClient userBasicInfClient;
    @Resource
    private AccountManagementClient accountManagementClient;
    @Resource
    private AccountingTreatmentClient accountingTreatmentClient;
    @Resource
    private ICheckControlDao checkControlDao;
    @Resource
    private BillSynchronizeProduce billSynchronizeProduce;

    @Override
    public TradeRateRspDTO tradeRate(TradeRateReqDTO tradeRateReqDTO) throws LemonException {
        TradeRateRspDTO tradeRateRspDTO = new TradeRateRspDTO();
        String busType = tradeRateReqDTO.getBusType();
        String ccy = tradeRateReqDTO.getCcy();
        // 查询交易费率
        RateRuleDO qryInRateRuleDO = new RateRuleDO();
        qryInRateRuleDO.setCcy("USD");
        if (JudgeUtils.isNotBlank(ccy)) {
            qryInRateRuleDO.setCcy(ccy);
        }
        qryInRateRuleDO.setBusType(busType);
        qryInRateRuleDO.setStats(Constants.STATUS_AVAILABLE);
        RateRuleDO qryOutRateRuleDO = rateRuleDao.getByCondition(qryInRateRuleDO);
        if (JudgeUtils.isNull(qryOutRateRuleDO)) {
            LemonException.throwBusinessException(MsgCdEnum.TRADE_FEE_RATE_NOT_EXISTS.getMsgCd());
        }
        tradeRateRspDTO.setCaculateType(qryOutRateRuleDO.getCalculateType());
        tradeRateRspDTO.setCalculateMinAmt(qryOutRateRuleDO.getCalculateMinAmt());
        tradeRateRspDTO.setMinFee(qryOutRateRuleDO.getMinFee());
        tradeRateRspDTO.setMaxFee(qryOutRateRuleDO.getMaxFee());
        tradeRateRspDTO.setRate(qryOutRateRuleDO.getRate());
        tradeRateRspDTO.setFixFee(qryOutRateRuleDO.getFixFee());
        return tradeRateRspDTO;
    }

    @Override
    public MerchantRateRspDTO merchantRate(MerchantRateReqDTO merchantRateReqDTO) throws LemonException {
        MerchantRateRspDTO merchantRateRspDTO = new MerchantRateRspDTO();
        String userId = merchantRateReqDTO.getUserId();
        String busType = merchantRateReqDTO.getBusType();
        String channel = merchantRateReqDTO.getChannel();
        if(JudgeUtils.isEmpty(channel)){
            channel = "Seatelpay";
        }
        // 查询商户费率
        MerchantRateRuleDO qryInMerchantRateRuleDO = new MerchantRateRuleDO();
        qryInMerchantRateRuleDO.setUserId(userId);
        qryInMerchantRateRuleDO.setBusType(busType);
        qryInMerchantRateRuleDO.setChannel(channel);
        qryInMerchantRateRuleDO.setStats(Constants.STATUS_AVAILABLE);
        MerchantRateRuleDO qryOutMerchantRateRuleDO = merchantRateRuleDao.getValidByCondition(qryInMerchantRateRuleDO);
        if (JudgeUtils.isNull(qryOutMerchantRateRuleDO)) {
            // 查默认费率信息
            qryInMerchantRateRuleDO.setUserId(Constants.USER_ID_DEFAULT);
            qryOutMerchantRateRuleDO = merchantRateRuleDao.getByCondition(qryInMerchantRateRuleDO);
            if (JudgeUtils.isNull(qryOutMerchantRateRuleDO)) {
                LemonException.throwBusinessException(MsgCdEnum.TRADE_FEE_RATE_NOT_EXISTS.getMsgCd());
            }
        }
        merchantRateRspDTO.setCaculateType(qryOutMerchantRateRuleDO.getCalculateType());
        merchantRateRspDTO.setCalculateMinAmt(qryOutMerchantRateRuleDO.getCalculateMinAmt());
        merchantRateRspDTO.setMinFee(qryOutMerchantRateRuleDO.getMinFee());
        merchantRateRspDTO.setMaxFee(qryOutMerchantRateRuleDO.getMaxFee());
        merchantRateRspDTO.setRate(qryOutMerchantRateRuleDO.getRate());
        merchantRateRspDTO.setFixFee(qryOutMerchantRateRuleDO.getFixFee());
        return merchantRateRspDTO;
    }

    @Override
    public TradeFeeCaculateRspDTO tradeFeeCalculate(TradeFeeCaculateReqDTO tradeFeeReqDTO) throws LemonException {
        TradeFeeCaculateRspDTO tradeFeeCaculateRspDTO = new TradeFeeCaculateRspDTO();
        String busType = tradeFeeReqDTO.getBusType();
        String ccy = tradeFeeReqDTO.getCcy();
        BigDecimal tradeAmt = tradeFeeReqDTO.getTradeAmt();
        // 交易费率信息查询
        RateRuleDO qryInRateRuleDO = new RateRuleDO();
        qryInRateRuleDO.setCcy("USD");
        if (JudgeUtils.isNotBlank(ccy)) {
            qryInRateRuleDO.setCcy(ccy);
        }
        qryInRateRuleDO.setBusType(busType);
        qryInRateRuleDO.setStats(Constants.STATUS_AVAILABLE);
        RateRuleDO qryOutRateRuleDO = rateRuleDao.getByCondition(qryInRateRuleDO);
        if (JudgeUtils.isNull(qryOutRateRuleDO)) {
            // 未查询到费率信息返回手续费为0
            tradeFeeCaculateRspDTO.setTradeToalAmt(tradeAmt);
            tradeFeeCaculateRspDTO.setTradeAmt(tradeAmt);
            tradeFeeCaculateRspDTO.setTradeFee(BigDecimal.ZERO);
            return tradeFeeCaculateRspDTO;
        }
        DecimalFormat format = new DecimalFormat("#.00");
        // 计费模式
        String calculateMode = qryOutRateRuleDO.getCalculateMod().trim();
        // 计费方式
        String calculateType = qryOutRateRuleDO.getCalculateType().trim();
        // 计费起始金额
        BigDecimal calculateMinAmt = qryOutRateRuleDO.getCalculateMinAmt();
        // 最低收取费用
        BigDecimal minFee = qryOutRateRuleDO.getMinFee();
        // 最高收取费用
        BigDecimal maxFee = qryOutRateRuleDO.getMaxFee();
        // 费率
        BigDecimal rate = qryOutRateRuleDO.getRate();
        // 固定手续费
        BigDecimal fixFee = qryOutRateRuleDO.getFixFee();
        // 交易总金额
        BigDecimal tradeTotalAmt = tradeAmt;
        BigDecimal tradeFee = fixFee;
        // 判断计费方式
        if (calculateType.equals(Constants.FEE_CALCULATE_PERCENT)) {
            // 百分比
            tradeFee = tradeAmt.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
            // 判断最低收取费用和最高收取费用
            if (tradeFee.compareTo(minFee) < 0) {
                tradeFee = minFee;
            }
            if (tradeFee.compareTo(maxFee) > 0) {
                tradeFee = maxFee;
            }
        }
        // 判断手续费是否大于交易金额
        if (tradeFee.compareTo(tradeAmt) > 0) {
            tradeFee = tradeAmt;
        }
        // 判断计费起始金额
        if (tradeAmt.compareTo(calculateMinAmt) < 0) {
            tradeFee = BigDecimal.ZERO;
        }
        tradeFee = BigDecimal.valueOf(Double.parseDouble(format.format(tradeFee)));
        // 判断计费模式
        if (calculateMode.equals(Constants.FEE_MODE_EXTERNAL)) {
            // 手续费外扣
            tradeTotalAmt = tradeAmt.add(tradeFee);
        }
        // 交易金额
        tradeAmt = tradeTotalAmt.subtract(tradeFee);
        tradeFeeCaculateRspDTO.setCalculateMode(calculateMode);
        tradeFeeCaculateRspDTO.setCalculateType(calculateType);
        tradeFeeCaculateRspDTO.setRate(rate);
        tradeFeeCaculateRspDTO.setFixFee(fixFee);
        tradeFeeCaculateRspDTO.setTradeToalAmt(tradeTotalAmt);
        tradeFeeCaculateRspDTO.setTradeAmt(tradeAmt);
        tradeFeeCaculateRspDTO.setTradeFee(tradeFee);
        return tradeFeeCaculateRspDTO;
    }

    @Override
    public MerchantFeeCalculateRspDTO merchantFeeCalculate(MerchantFeeCalculateReqDTO merchantFeeCalculateReqDTO)
            throws LemonException {
        MerchantFeeCalculateRspDTO merchantFeeCalculateRspDTO = new MerchantFeeCalculateRspDTO();
        String userId = merchantFeeCalculateReqDTO.getUserId();
        String busType = merchantFeeCalculateReqDTO.getBusType();
        BigDecimal tradeAmt = merchantFeeCalculateReqDTO.getTradeAmt();
        String channel = merchantFeeCalculateReqDTO.getChannel();
        if(JudgeUtils.isEmpty(channel)){
            channel = "Seatelpay";
        }
        // 查询商户费率
        MerchantRateRuleDO qryInMerchantRateRuleDO = new MerchantRateRuleDO();
        qryInMerchantRateRuleDO.setUserId(userId);
        qryInMerchantRateRuleDO.setBusType(busType);
        qryInMerchantRateRuleDO.setStats(Constants.STATUS_AVAILABLE);
        qryInMerchantRateRuleDO.setChannel(channel);
        MerchantRateRuleDO qryOutMerchantRateRuleDO = merchantRateRuleDao.getValidByCondition(qryInMerchantRateRuleDO);
        if (JudgeUtils.isNull(qryOutMerchantRateRuleDO)) {
            // 查默认费率信息
            qryInMerchantRateRuleDO.setUserId(Constants.USER_ID_DEFAULT);
            qryOutMerchantRateRuleDO = merchantRateRuleDao.getByCondition(qryInMerchantRateRuleDO);
            if (JudgeUtils.isNull(qryOutMerchantRateRuleDO)) {
                LemonException.throwBusinessException(MsgCdEnum.TRADE_FEE_RATE_NOT_EXISTS.getMsgCd());
            }
        }
        DecimalFormat format = new DecimalFormat("#.00");
        // 计费模式
        String calculateMode = qryOutMerchantRateRuleDO.getCalculateMod().trim();
        // 计费方式
        String calculateType = qryOutMerchantRateRuleDO.getCalculateType().trim();
        // 计费起始金额
        BigDecimal calculateMinAmt = qryOutMerchantRateRuleDO.getCalculateMinAmt();
        // 最低收取费用
        BigDecimal minFee = qryOutMerchantRateRuleDO.getMinFee();
        // 最高收取费用
        BigDecimal maxFee = qryOutMerchantRateRuleDO.getMaxFee();
        // 费率
        BigDecimal rate = qryOutMerchantRateRuleDO.getRate();
        // 固定手续费
        BigDecimal fixFee = qryOutMerchantRateRuleDO.getFixFee();
        // 交易总金额
        BigDecimal tradeTotalAmt = tradeAmt;
        BigDecimal tradeFee = fixFee;
        // 判断计费方式
        if (calculateType.equals(Constants.FEE_CALCULATE_PERCENT)) {
            // 百分比
            tradeFee = tradeAmt.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
            // 判断最低收取费用和最高收取费用
            if (tradeFee.compareTo(minFee) < 0) {
                tradeFee = minFee;
            }
            if (tradeFee.compareTo(maxFee) > 0) {
                tradeFee = maxFee;
            }
        }
        // 判断手续费是否大于交易金额
        if (tradeFee.compareTo(tradeAmt) > 0) {
            tradeFee = tradeAmt;
        }
        // 判断计费起始金额
        if (tradeAmt.compareTo(calculateMinAmt) < 0) {
            tradeFee = BigDecimal.ZERO;
        }
        // 交易续费
        tradeFee = BigDecimal.valueOf(Double.parseDouble(format.format(tradeFee)));
        // 判断计费模式
        if (calculateMode.equals(Constants.FEE_MODE_EXTERNAL)) {
            // 手续费外扣
            tradeTotalAmt = tradeAmt.add(tradeFee);
        }
        // 交易金额
        tradeAmt = tradeTotalAmt.subtract(tradeFee);
        merchantFeeCalculateRspDTO.setCalculateMode(calculateMode);
        merchantFeeCalculateRspDTO.setCalculateType(calculateType);
        merchantFeeCalculateRspDTO.setRate(rate);
        merchantFeeCalculateRspDTO.setFixFee(fixFee);
        merchantFeeCalculateRspDTO.setTradeToalAmt(tradeTotalAmt);
        merchantFeeCalculateRspDTO.setTradeAmt(tradeAmt);
        merchantFeeCalculateRspDTO.setTradeFee(tradeFee);
        return merchantFeeCalculateRspDTO;
    }

    @Override
    public TradeFeeRspDTO tradeFee(TradeFeeReqDTO tradeFeeReqDTO) throws LemonException {
        TradeFeeRspDTO tradeFeeRspDTO = new TradeFeeRspDTO();
        String userId = tradeFeeReqDTO.getUserId();
        String busType = tradeFeeReqDTO.getBusType();
        BigDecimal tradeAmt = tradeFeeReqDTO.getTradeAmt();
        String ccy = tradeFeeReqDTO.getCcy();
        String busOrderNo = tradeFeeReqDTO.getBusOrderNo();
        LocalDateTime busOrderTime = tradeFeeReqDTO.getBusOrderTime();
        LocalDate tradeDate = DateTimeUtils.getCurrentLocalDate();
        LocalTime tradeTime = DateTimeUtils.getCurrentLocalTime();
        // 查询用户信息信息
        UserBasicInfDTO userBasicInfDTO = getUserBasicInfo(userId);
        String userName = userBasicInfDTO.getUsrNm();
        // 查询交易费率信息
        RateRuleDO qryInRateRuleDO = new RateRuleDO();
        qryInRateRuleDO.setBusType(busType);
        qryInRateRuleDO.setStats(Constants.STATUS_AVAILABLE);
        RateRuleDO qryOutRateRuleDO = rateRuleDao.getByCondition(qryInRateRuleDO);
        if (JudgeUtils.isNull(qryOutRateRuleDO)) {
            LemonException.throwBusinessException(MsgCdEnum.TRADE_FEE_RATE_NOT_EXISTS.getMsgCd());
        }
        DecimalFormat format = new DecimalFormat("#.00");
        // 业务类型名称
        String busTypeDesc = qryOutRateRuleDO.getBusTypeDesc().trim();
        // 计费模式
        String calculateMode = qryOutRateRuleDO.getCalculateMod().trim();
        // 计费方式
        String calculateType = qryOutRateRuleDO.getCalculateType().trim();
        // 计费起始金额
        BigDecimal calculateMinAmt = qryOutRateRuleDO.getCalculateMinAmt();
        // 最低收取费用
        BigDecimal minFee = qryOutRateRuleDO.getMinFee();
        // 最高收取费用
        BigDecimal maxFee = qryOutRateRuleDO.getMaxFee();
        // 费率
        BigDecimal rate = qryOutRateRuleDO.getRate();
        // 固定手续费
        BigDecimal fixFee = qryOutRateRuleDO.getFixFee();
        // 交易总金额
        BigDecimal tradeTotalAmt = tradeAmt;
        BigDecimal tradeFee = fixFee;
        // 判断计费方式
        if (calculateType.equals(Constants.FEE_CALCULATE_PERCENT)) {
            // 百分比
            tradeFee = tradeAmt.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
            // 判断最低收取费用和最高收取费用
            if (tradeFee.compareTo(minFee) < 0) {
                tradeFee = minFee;
            }
            if (tradeFee.compareTo(maxFee) > 0) {
                tradeFee = maxFee;
            }
        }
        // 判断手续费是否大于交易金额
        if (tradeFee.compareTo(tradeAmt) > 0) {
            tradeFee = tradeAmt;
        }
        // 判断计费起始金额
        if (tradeAmt.compareTo(calculateMinAmt) < 0) {
            tradeFee = BigDecimal.ZERO;
        }
        tradeFee = BigDecimal.valueOf(Double.parseDouble(format.format(tradeFee)));
        // 判断计费模式
        if (calculateMode.equals(Constants.FEE_MODE_EXTERNAL)) {
            // 手续费外扣
            tradeTotalAmt = tradeAmt.add(tradeFee);
        }
        // 交易金额
        tradeAmt = tradeTotalAmt.subtract(tradeFee);
        // 登记交易费流水
        FeeOrderDO feeOrderDO = new FeeOrderDO();
        String feeOrderNo = generateOrderNo();
        feeOrderDO.setOrderNo(feeOrderNo);
        feeOrderDO.setUserId(userId);
        feeOrderDO.setUserName(userName);
        feeOrderDO.setBusType(busType);
        feeOrderDO.setBusTypeDesc(busTypeDesc);
        feeOrderDO.setCcy(ccy);
        feeOrderDO.setTradeTotalAmt(tradeTotalAmt);
        feeOrderDO.setTradeAmt(tradeAmt);
        feeOrderDO.setTradeDate(tradeDate);
        feeOrderDO.setTradeTime(tradeTime);
        feeOrderDO.setCalculateMod(calculateMode);
        feeOrderDO.setCalculateType(calculateType);
        feeOrderDO.setRate(rate);
        feeOrderDO.setFee(tradeFee);
        feeOrderDO.setClearStats(Constants.CLEAR_STATUS_NO);
        feeOrderDO.setBusOrderNo(busOrderNo);
        feeOrderDO.setBusOrderTime(busOrderTime);
        int result = feeOrderDao.insert(feeOrderDO);
        if (result <= 0) {
            LemonException.throwBusinessException(MsgCdEnum.TRADE_FEE_REG_FAIL.getMsgCd());
        }
        tradeFeeRspDTO.setFeeOrderNo(feeOrderNo);
        tradeFeeRspDTO.setCalculateMode(calculateMode);
        tradeFeeRspDTO.setCalculateType(calculateType);
        tradeFeeRspDTO.setRate(rate);
        tradeFeeRspDTO.setFixFee(fixFee);
        tradeFeeRspDTO.setTradeTotalAmt(tradeTotalAmt);
        tradeFeeRspDTO.setTradeAmt(tradeAmt);
        tradeFeeRspDTO.setTradeFee(tradeFee);
        return tradeFeeRspDTO;
    }

    @Override
    public TradeFeeRspDTO merchantTradeFee(TradeFeeReqDTO tradeFeeReqDTO) throws LemonException {
        TradeFeeRspDTO tradeFeeRspDTO = new TradeFeeRspDTO();
        String userId = tradeFeeReqDTO.getUserId();
        String busType = tradeFeeReqDTO.getBusType();
        BigDecimal tradeAmt = tradeFeeReqDTO.getTradeAmt();
        String ccy = tradeFeeReqDTO.getCcy();
        String channel = tradeFeeReqDTO.getChannel();
        if(JudgeUtils.isEmpty(channel)){
            channel = "Seatelpay";
        }
        String busOrderNo = tradeFeeReqDTO.getBusOrderNo();
        LocalDateTime busOrderTime = tradeFeeReqDTO.getBusOrderTime();
        LocalDate tradeDate = DateTimeUtils.getCurrentLocalDate();
        LocalTime tradeTime = DateTimeUtils.getCurrentLocalTime();
        // 查询用户信息信息
        UserBasicInfDTO userBasicInfDTO = getUserBasicInfo(userId);
        String userName = userBasicInfDTO.getMercName();
        // 查询商户费率
        MerchantRateRuleDO qryInMerchantRateRuleDO = new MerchantRateRuleDO();
        qryInMerchantRateRuleDO.setUserId(userId);
        qryInMerchantRateRuleDO.setBusType(busType);
        qryInMerchantRateRuleDO.setStats(Constants.STATUS_AVAILABLE);
        qryInMerchantRateRuleDO.setCcy(ccy);
        qryInMerchantRateRuleDO.setChannel(channel);

        MerchantRateRuleDO qryOutMerchantRateRuleDO = merchantRateRuleDao.getValidByCondition(qryInMerchantRateRuleDO);
        if (JudgeUtils.isNull(qryOutMerchantRateRuleDO)) {
            // 查默认费率信息
            qryInMerchantRateRuleDO.setUserId(Constants.USER_ID_DEFAULT);
            qryOutMerchantRateRuleDO = merchantRateRuleDao.getByCondition(qryInMerchantRateRuleDO);
            if (JudgeUtils.isNull(qryOutMerchantRateRuleDO)) {
                LemonException.throwBusinessException(MsgCdEnum.TRADE_FEE_RATE_NOT_EXISTS.getMsgCd());
            }
        }
        DecimalFormat format = new DecimalFormat("#.00");
        // 业务类型名称
        String busTypeDesc = qryOutMerchantRateRuleDO.getBusTypeDesc().trim();
        // 计费模式
        String calculateMode = qryOutMerchantRateRuleDO.getCalculateMod().trim();
        // 计费方式
        String calculateType = qryOutMerchantRateRuleDO.getCalculateType().trim();
        // 计费起始金额
        BigDecimal calculateMinAmt = qryOutMerchantRateRuleDO.getCalculateMinAmt();
        // 最低收取费用
        BigDecimal minFee = qryOutMerchantRateRuleDO.getMinFee();
        // 最高收取费用
        BigDecimal maxFee = qryOutMerchantRateRuleDO.getMaxFee();
        // 费率
        BigDecimal rate = qryOutMerchantRateRuleDO.getRate();
        // 固定手续费
        BigDecimal fixFee = qryOutMerchantRateRuleDO.getFixFee();
        // 交易总金额
        BigDecimal tradeTotalAmt = tradeAmt;
        BigDecimal tradeFee = fixFee;
        // 判断计费方式
        if (calculateType.equals(Constants.FEE_CALCULATE_PERCENT)) {
            // 百分比
            tradeFee = tradeAmt.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
            // 判断最低收取费用和最高收取费用
            if (tradeFee.compareTo(minFee) < 0) {
                tradeFee = minFee;
            }
            if (tradeFee.compareTo(maxFee) > 0) {
                tradeFee = maxFee;
            }
        }
        // 判断手续费是否大于交易金额
        if (tradeFee.compareTo(tradeAmt) > 0) {
            tradeFee = tradeAmt;
        }
        // 判断计费起始金额
        if (tradeAmt.compareTo(calculateMinAmt) < 0) {
            tradeFee = BigDecimal.ZERO;
        }
        tradeFee = BigDecimal.valueOf(Double.parseDouble(format.format(tradeFee)));
        // 判断计费模式
        if (calculateMode.equals(Constants.FEE_MODE_EXTERNAL)) {
            // 手续费外扣
            tradeTotalAmt = tradeAmt.add(tradeFee);
        }
        // 交易金额
        tradeAmt = tradeTotalAmt.subtract(tradeFee);
        // 登记交易费流水
        FeeOrderDO feeOrderDO = new FeeOrderDO();
        String feeOrderNo = generateOrderNo();
        feeOrderDO.setOrderNo(feeOrderNo);
        feeOrderDO.setUserId(userId);
        feeOrderDO.setUserName(userName);
        feeOrderDO.setBusType(busType);
        feeOrderDO.setBusTypeDesc(busTypeDesc);
        feeOrderDO.setCcy(ccy);
        feeOrderDO.setTradeTotalAmt(tradeTotalAmt);
        feeOrderDO.setTradeAmt(tradeAmt);
        feeOrderDO.setTradeDate(tradeDate);
        feeOrderDO.setTradeTime(tradeTime);
        feeOrderDO.setCalculateMod(calculateMode);
        feeOrderDO.setCalculateType(calculateType);
        feeOrderDO.setRate(rate);
        feeOrderDO.setFee(tradeFee);
        feeOrderDO.setClearStats(Constants.CLEAR_STATUS_INIT);
        feeOrderDO.setBusOrderNo(busOrderNo);
        feeOrderDO.setBusOrderTime(busOrderTime);
        int result = feeOrderDao.insert(feeOrderDO);
        if (result <= 0) {
            LemonException.throwBusinessException(MsgCdEnum.TRADE_FEE_REG_FAIL.getMsgCd());
        }
        tradeFeeRspDTO.setFeeOrderNo(feeOrderNo);
        tradeFeeRspDTO.setCalculateMode(calculateMode);
        tradeFeeRspDTO.setCalculateType(calculateType);
        tradeFeeRspDTO.setRate(rate);
        tradeFeeRspDTO.setFixFee(fixFee);
        tradeFeeRspDTO.setTradeTotalAmt(tradeTotalAmt);
        tradeFeeRspDTO.setTradeAmt(tradeAmt);
        tradeFeeRspDTO.setTradeFee(tradeFee);
        return tradeFeeRspDTO;
    }

    /**
     * @Description 商户结算、转账业务手续费登记，不需要清分
     * <AUTHOR>
     * @param tradeFeeReqDTO
     * @return
     * @throws LemonException
     */
    @Override
    public TradeFeeRspDTO merchantTransferTradeFee(TradeFeeReqDTO tradeFeeReqDTO) throws LemonException {
        TradeFeeRspDTO tradeFeeRspDTO = new TradeFeeRspDTO();
        String userId = tradeFeeReqDTO.getUserId();
        String busType = tradeFeeReqDTO.getBusType();
        BigDecimal tradeAmt = tradeFeeReqDTO.getTradeAmt();
        String ccy = tradeFeeReqDTO.getCcy();
        String channel = tradeFeeReqDTO.getChannel();
        if(JudgeUtils.isEmpty(channel)){
            channel = "Seatelpay";
        }
        String busOrderNo = tradeFeeReqDTO.getBusOrderNo();
        LocalDateTime busOrderTime = tradeFeeReqDTO.getBusOrderTime();
        LocalDate tradeDate = DateTimeUtils.getCurrentLocalDate();
        LocalTime tradeTime = DateTimeUtils.getCurrentLocalTime();

        // 查询商户信息
        UserBasicInfDTO userBasicInfDTO = getUserBasicInfo(userId);
        String userName = userBasicInfDTO.getMercName();

        // 查询商户费率
        MerchantRateRuleDO qryInMerchantRateRuleDO = new MerchantRateRuleDO();
        qryInMerchantRateRuleDO.setUserId(userId);
        qryInMerchantRateRuleDO.setBusType(busType);
        qryInMerchantRateRuleDO.setStats(Constants.STATUS_AVAILABLE);
        qryInMerchantRateRuleDO.setCcy(ccy);
        qryInMerchantRateRuleDO.setChannel(channel);
        MerchantRateRuleDO qryOutMerchantRateRuleDO = merchantRateRuleDao.getValidByCondition(qryInMerchantRateRuleDO);
        if (JudgeUtils.isNull(qryOutMerchantRateRuleDO)) {
            // 查默认费率信息
            qryInMerchantRateRuleDO.setUserId(Constants.USER_ID_DEFAULT);
            qryOutMerchantRateRuleDO = merchantRateRuleDao.getByCondition(qryInMerchantRateRuleDO);
            if (JudgeUtils.isNull(qryOutMerchantRateRuleDO)) {
                LemonException.throwBusinessException(MsgCdEnum.TRADE_FEE_RATE_NOT_EXISTS.getMsgCd());
            }
        }
        DecimalFormat format = new DecimalFormat("#.00");
        // 业务类型名称
        String busTypeDesc = qryOutMerchantRateRuleDO.getBusTypeDesc().trim();
        // 计费模式
        String calculateMode = qryOutMerchantRateRuleDO.getCalculateMod().trim();
        // 计费方式
        String calculateType = qryOutMerchantRateRuleDO.getCalculateType().trim();
        // 计费起始金额
        BigDecimal calculateMinAmt = qryOutMerchantRateRuleDO.getCalculateMinAmt();
        // 最低收取费用
        BigDecimal minFee = qryOutMerchantRateRuleDO.getMinFee();
        // 最高收取费用
        BigDecimal maxFee = qryOutMerchantRateRuleDO.getMaxFee();
        // 费率
        BigDecimal rate = qryOutMerchantRateRuleDO.getRate();
        // 固定手续费
        BigDecimal fixFee = qryOutMerchantRateRuleDO.getFixFee();
        // 交易总金额
        BigDecimal tradeTotalAmt = tradeAmt;
        BigDecimal tradeFee = fixFee;
        // 判断计费方式
        if (calculateType.equals(Constants.FEE_CALCULATE_PERCENT)) {
            // 百分比
            tradeFee = tradeAmt.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
            // 判断最低收取费用和最高收取费用
            if (tradeFee.compareTo(minFee) < 0) {
                tradeFee = minFee;
            }
            if (tradeFee.compareTo(maxFee) > 0) {
                tradeFee = maxFee;
            }
        }
        // 判断手续费是否大于交易金额
        if (tradeFee.compareTo(tradeAmt) > 0) {
            tradeFee = tradeAmt;
        }
        // 判断计费起始金额
        if (tradeAmt.compareTo(calculateMinAmt) < 0) {
            tradeFee = BigDecimal.ZERO;
        }
        tradeFee = BigDecimal.valueOf(Double.parseDouble(format.format(tradeFee)));
        // 判断计费模式
        if (calculateMode.equals(Constants.FEE_MODE_EXTERNAL)) {
            // 手续费外扣
            tradeTotalAmt = tradeAmt.add(tradeFee);
        }
        // 交易金额
        tradeAmt = tradeTotalAmt.subtract(tradeFee);
        // 登记交易费流水
        FeeOrderDO feeOrderDO = new FeeOrderDO();
        String feeOrderNo = generateOrderNo();
        feeOrderDO.setOrderNo(feeOrderNo);
        feeOrderDO.setUserId(userId);
        feeOrderDO.setUserName(userName);
        feeOrderDO.setBusType(busType);
        feeOrderDO.setBusTypeDesc(busTypeDesc);
        feeOrderDO.setCcy(ccy);
        feeOrderDO.setTradeTotalAmt(tradeTotalAmt);
        feeOrderDO.setTradeAmt(tradeAmt);
        feeOrderDO.setTradeDate(tradeDate);
        feeOrderDO.setTradeTime(tradeTime);
        feeOrderDO.setCalculateMod(calculateMode);
        feeOrderDO.setCalculateType(calculateType);
        feeOrderDO.setRate(rate);
        feeOrderDO.setFee(tradeFee);
        feeOrderDO.setClearStats(Constants.CLEAR_STATUS_NO);
        feeOrderDO.setBusOrderNo(busOrderNo);
        feeOrderDO.setBusOrderTime(busOrderTime);
        int result = feeOrderDao.insert(feeOrderDO);
        if (result <= 0) {
            LemonException.throwBusinessException(MsgCdEnum.TRADE_FEE_REG_FAIL.getMsgCd());
        }
        tradeFeeRspDTO.setFeeOrderNo(feeOrderNo);
        tradeFeeRspDTO.setCalculateMode(calculateMode);
        tradeFeeRspDTO.setCalculateType(calculateType);
        tradeFeeRspDTO.setRate(rate);
        tradeFeeRspDTO.setFixFee(fixFee);
        tradeFeeRspDTO.setTradeTotalAmt(tradeTotalAmt);
        tradeFeeRspDTO.setTradeAmt(tradeAmt);
        tradeFeeRspDTO.setTradeFee(tradeFee);
        return tradeFeeRspDTO;
    }

    @Override
    public MerchantRefundFeeRspDTO merchantRefundFee(MerchantRefundFeeReqDTO refundFeeReqDTO) throws LemonException {
        MerchantRefundFeeRspDTO merchantRefundFeeRspDTO = new MerchantRefundFeeRspDTO();
        LocalDate tradeDate = DateTimeUtils.getCurrentLocalDate();
        LocalTime tradeTime = DateTimeUtils.getCurrentLocalTime();
        String busOrderNo = refundFeeReqDTO.getBusOrderNo();
        // 查询原交易计费订单信息
        FeeOrderDO oldFeeOrderDO = feeOrderDao.getRecentNeedHandle(busOrderNo);
        if (JudgeUtils.isNull(oldFeeOrderDO)) {
            LemonException.throwBusinessException(MsgCdEnum.BUS_ORDER_NOT_EXSITS.getMsgCd());
        }
        String userId = oldFeeOrderDO.getUserId();
        // 交易费率
        BigDecimal rate = oldFeeOrderDO.getRate();
        // 计费方式
        String calculateType = oldFeeOrderDO.getCalculateType().trim();
        // 计费模式
        String calculateMode = oldFeeOrderDO.getCalculateMod().trim();
        // 订单状态
        String oldClearStats = oldFeeOrderDO.getClearStats();
        // 原订单交易金额
        BigDecimal oldTradeAmt = oldFeeOrderDO.getTradeTotalAmt();
        if (calculateMode.equals(Constants.FEE_MODE_EXTERNAL)) {
            // 计费模式外扣
            oldTradeAmt = oldFeeOrderDO.getTradeAmt();
        }
        // 退款交易金额
        BigDecimal refundAmt = refundFeeReqDTO.getRefundAmt();
        // 待清分交易金额
        BigDecimal clearTradeAmt = oldTradeAmt.subtract(refundAmt);
        // 原订单手续费
        BigDecimal oldTradeFee = oldFeeOrderDO.getFee();
        // 退款手续费
        BigDecimal refundFee = refundAmt.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
        if (calculateType.equals(Constants.FEE_CALCULATE_FIXED)) {
            // 退款金额费率比
            BigDecimal rundAmtRate = refundAmt.divide(oldTradeAmt, BigDecimal.ROUND_HALF_UP);
            // 退款手续费
            refundFee = oldTradeFee.multiply(rundAmtRate).setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        // 原交易手续费为0
        if (oldTradeFee.compareTo(BigDecimal.ZERO) == 0) {
            // 退款手续费
            refundFee = BigDecimal.ZERO;
        }
        // 退款手续费大于原订单手续费
        if (refundFee.compareTo(oldTradeFee) > 0) {
            refundFee = oldTradeFee;
        }
        // 待清分手续费
        BigDecimal clearFee = oldTradeFee.subtract(refundFee);
        // 原订单结算金额
        BigDecimal oldSettleAmt = oldFeeOrderDO.getTradeAmt();
        // 退款结算金额
        BigDecimal refundSettleAmt = refundAmt.subtract(refundFee);
        // 待清分结算金额
        BigDecimal clearSettleAmt = clearTradeAmt.subtract(clearFee);
        if (calculateMode.equals(Constants.FEE_MODE_EXTERNAL)) {
            // 退款结算金额
            refundSettleAmt = refundAmt;
            // 退款手续费
            refundFee = refundSettleAmt.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
            // 待清分结算金额
            clearSettleAmt = oldSettleAmt.subtract(refundSettleAmt);
            if (calculateType.equals(Constants.FEE_CALCULATE_FIXED)) {
                // 退款金额费率比
                BigDecimal rundAmtRate = refundAmt.divide(oldTradeAmt, BigDecimal.ROUND_HALF_UP);
                // 退款手续费
                refundFee = oldTradeFee.multiply(rundAmtRate).setScale(2, BigDecimal.ROUND_HALF_UP);
            }
            // 原交易手续费为0
            if (oldTradeFee.compareTo(BigDecimal.ZERO) == 0) {
                // 退款手续费
                refundFee = BigDecimal.ZERO;
            }
            // 退款手续费大于原订单手续费
            if (refundFee.compareTo(oldTradeFee) > 0) {
                refundFee = oldTradeFee;
            }
            // 待清分手续费
            clearFee = oldTradeFee.subtract(refundFee);
            // 退款交易金额
            refundAmt = refundSettleAmt.add(refundFee);
            // 待清分交易金额
            clearTradeAmt = clearSettleAmt.add(clearFee);
        }
        // 生成退款订单
        FeeOrderDO refundFeeOrderDO = new FeeOrderDO();
        String refundFeeOrderNo = generateOrderNo();
        refundFeeOrderDO.setOrderNo(refundFeeOrderNo);
        refundFeeOrderDO.setUserId(oldFeeOrderDO.getUserId());
        refundFeeOrderDO.setUserName(oldFeeOrderDO.getUserName());
        refundFeeOrderDO.setBusType(oldFeeOrderDO.getBusType());
        refundFeeOrderDO.setBusTypeDesc(oldFeeOrderDO.getBusTypeDesc());
        refundFeeOrderDO.setCcy(oldFeeOrderDO.getCcy());
        refundFeeOrderDO.setTradeTotalAmt(refundAmt);
        refundFeeOrderDO.setTradeAmt(refundSettleAmt);
        refundFeeOrderDO.setTradeDate(tradeDate);
        refundFeeOrderDO.setTradeTime(tradeTime);
        refundFeeOrderDO.setCalculateMod(oldFeeOrderDO.getCalculateMod());
        refundFeeOrderDO.setCalculateType(oldFeeOrderDO.getCalculateType());
        refundFeeOrderDO.setRate(rate);
        refundFeeOrderDO.setFee(refundFee);
        refundFeeOrderDO.setClearStats(Constants.CLEAR_STATUS_REFUND);
        refundFeeOrderDO.setBusOrderNo(busOrderNo);
        refundFeeOrderDO.setBusOrderTime(oldFeeOrderDO.getBusOrderTime());
        registerFeeOrder(refundFeeOrderDO);
        // 判断原交易订单是否清分
        if (oldClearStats.equals(Constants.CLEAR_STATUS_CHECK)) {
            // 退款账务处理
            feeRefundAccountTreat(userId, Constants.AC_TXSTS_N, refundFeeOrderDO);
        } else {
            if (clearSettleAmt.compareTo(BigDecimal.ZERO) > 0) {
                // 登记待清分订单
                FeeOrderDO needHandFeeOrderDO = new FeeOrderDO();
                String needHandFeeOrderNo = generateOrderNo();
                needHandFeeOrderDO.setOrderNo(needHandFeeOrderNo);
                needHandFeeOrderDO.setUserId(oldFeeOrderDO.getUserId());
                needHandFeeOrderDO.setUserName(oldFeeOrderDO.getUserName());
                needHandFeeOrderDO.setBusType(oldFeeOrderDO.getBusType());
                needHandFeeOrderDO.setBusTypeDesc(oldFeeOrderDO.getBusTypeDesc());
                needHandFeeOrderDO.setCcy(oldFeeOrderDO.getCcy());
                needHandFeeOrderDO.setTradeTotalAmt(clearTradeAmt);
                needHandFeeOrderDO.setTradeAmt(clearSettleAmt);
                needHandFeeOrderDO.setTradeDate(tradeDate);
                needHandFeeOrderDO.setTradeTime(tradeTime);
                needHandFeeOrderDO.setCalculateMod(oldFeeOrderDO.getCalculateMod());
                needHandFeeOrderDO.setCalculateType(oldFeeOrderDO.getCalculateType());
                needHandFeeOrderDO.setRate(rate);
                needHandFeeOrderDO.setFee(clearFee);
                needHandFeeOrderDO.setClearStats(Constants.CLEAR_STATUS_INIT);
                needHandFeeOrderDO.setBusOrderNo(busOrderNo);
                needHandFeeOrderDO.setBusOrderTime(oldFeeOrderDO.getBusOrderTime());
                registerFeeOrder(needHandFeeOrderDO);
            }
            // 更新原订单状态
            FeeOrderDO updateValueFeeOrderDO = new FeeOrderDO();
            updateValueFeeOrderDO.setClearStats(Constants.CLEAR_STATUS_REFUND);
            FeeOrderDO updateConditionFeeOrderDO = new FeeOrderDO();
            updateConditionFeeOrderDO.setOrderNo(oldFeeOrderDO.getOrderNo());
            updateConditionFeeOrderDO.setClearStats(Constants.CLEAR_STATUS_INIT);
            updateFeeOrder(updateValueFeeOrderDO, updateConditionFeeOrderDO);
        }
        merchantRefundFeeRspDTO.setOrderNo(refundFeeOrderNo);
        return merchantRefundFeeRspDTO;
    }

    /**
     * @Description 商户手续费退款处理，按照退款金额占原订单金额比例，计算需退的手续费金额
     * @param refundFeeReqDTO
     * @throws LemonException
     * <AUTHOR>
     */
    @Override
    public MerchantRefundFeeRspDTO merchantRefundFeeNew(MerchantRefundFeeReqDTO refundFeeReqDTO) throws LemonException {
        MerchantRefundFeeRspDTO merchantRefundFeeRspDTO = new MerchantRefundFeeRspDTO();
        LocalDate tradeDate = DateTimeUtils.getCurrentLocalDate();
        LocalTime tradeTime = DateTimeUtils.getCurrentLocalTime();
        String busOrderNo = refundFeeReqDTO.getBusOrderNo();

        /**
         * 查询该业务订单号最早录入的一笔交易计费订单，获取该订单总金额
         * <AUTHOR>
         */
        FeeOrderDO firstFeeOrderDO = feeOrderDao.getFirstBusOrder(busOrderNo);
        if (JudgeUtils.isNull(firstFeeOrderDO)) {
            LemonException.throwBusinessException(MsgCdEnum.BUS_ORDER_NOT_EXSITS.getMsgCd());
        }
        // 最早录入的订单总金额
        BigDecimal totalTradeAmt = firstFeeOrderDO.getTradeTotalAmt();
        // 最早录入的订单总手续费
        BigDecimal totalFeeAmt = firstFeeOrderDO.getFee();

        // 查询原交易计费订单信息
        FeeOrderDO oldFeeOrderDO = feeOrderDao.getRecentNeedHandle(busOrderNo);
        if (JudgeUtils.isNull(oldFeeOrderDO)) {
            LemonException.throwBusinessException(MsgCdEnum.BUS_ORDER_NOT_EXSITS.getMsgCd());
        }
        String userId = oldFeeOrderDO.getUserId();

        // 交易费率 = 退款交易金额 / 最早录入的订单总金额
        BigDecimal rate = oldFeeOrderDO.getRate();
        // 计费方式
        String calculateType = oldFeeOrderDO.getCalculateType().trim();
        // 计费模式
        String calculateMode = oldFeeOrderDO.getCalculateMod().trim();
        // 订单状态
        String oldClearStats = oldFeeOrderDO.getClearStats();
        // 原订单交易金额，原订单待清分交易金额
        BigDecimal oldTradeAmt = oldFeeOrderDO.getTradeTotalAmt();
        // 计费模式枚举值 外扣
        if (calculateMode.equals(Constants.FEE_MODE_EXTERNAL)) {
            // 计费模式外扣，原订单交易金额 = 原商户待结算金额
            oldTradeAmt = oldFeeOrderDO.getTradeAmt();
        }
        // 退款交易金额
        BigDecimal refundAmt = refundFeeReqDTO.getRefundAmt();
        // 待清分交易金额 = 原订单待清分金额 - 退款交易金额
        BigDecimal clearTradeAmt = oldTradeAmt.subtract(refundAmt);
        // 原订单手续费
        BigDecimal oldTradeFee = oldFeeOrderDO.getFee();
        // 退款手续费 = 最早录入的订单总手续费 * (退款金额 / 最早录入的订单总金额) modify by gl
        //BigDecimal refundFee = refundAmt.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
        BigDecimal refundFee = totalFeeAmt.multiply(refundAmt).divide(totalTradeAmt, 2, BigDecimal.ROUND_HALF_UP);
        // 计费方式枚举值 固定金额
        if (calculateType.equals(Constants.FEE_CALCULATE_FIXED)) {
            // 退款金额费率比
            //BigDecimal rundAmtRate = refundAmt.divide(oldTradeAmt, BigDecimal.ROUND_HALF_UP);
            // 退款手续费
            //refundFee = oldTradeFee.multiply(rundAmtRate).setScale(2, BigDecimal.ROUND_HALF_UP);
            refundFee = totalFeeAmt.multiply(refundAmt).divide(totalTradeAmt, 2, BigDecimal.ROUND_HALF_UP);
        }
        // 原交易手续费为0
        if (oldTradeFee.compareTo(BigDecimal.ZERO) == 0) {
            // 退款手续费
            refundFee = BigDecimal.ZERO;
        }
        // 退款手续费大于原订单手续费
        if (refundFee.compareTo(oldTradeFee) > 0) {
            refundFee = oldTradeFee;
        }
        // 退款金额等于原订单交易金额，则为全额退款 add by gl
        if (refundAmt.compareTo(oldTradeAmt) == 0) {
            refundFee = oldTradeFee;
        }
        // 退款金额大于原订单交易金额 add by gl
        if (refundAmt.compareTo(oldTradeAmt) > 0) {
            LemonException.throwBusinessException(MsgCdEnum.REFUND_AMT_ERROR.getMsgCd());
        }
        // 待清分手续费
        BigDecimal clearFee = oldTradeFee.subtract(refundFee);
        // 原订单结算金额
        BigDecimal oldSettleAmt = oldFeeOrderDO.getTradeAmt();
        // 退款结算金额
        BigDecimal refundSettleAmt = refundAmt.subtract(refundFee);
        // 待清分结算金额
        BigDecimal clearSettleAmt = clearTradeAmt.subtract(clearFee);
        // 计费模式枚举值 外扣
        if (calculateMode.equals(Constants.FEE_MODE_EXTERNAL)) {
            // 退款结算金额
            refundSettleAmt = refundAmt;
            // 退款手续费 modify by gl
            //refundFee = refundSettleAmt.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
            refundFee = totalFeeAmt.multiply(refundAmt).divide(totalTradeAmt, 2, BigDecimal.ROUND_HALF_UP);
            // 待清分结算金额
            clearSettleAmt = oldSettleAmt.subtract(refundSettleAmt);
            // 计费方式枚举值 固定金额
            if (calculateType.equals(Constants.FEE_CALCULATE_FIXED)) {
                // 退款金额费率比
                //BigDecimal rundAmtRate = refundAmt.divide(oldTradeAmt, BigDecimal.ROUND_HALF_UP);
                // 退款手续费
                //refundFee = oldTradeFee.multiply(rundAmtRate).setScale(2, BigDecimal.ROUND_HALF_UP);
            }
            // 原交易手续费为0
            if (oldTradeFee.compareTo(BigDecimal.ZERO) == 0) {
                // 退款手续费
                refundFee = BigDecimal.ZERO;
            }
            // 退款手续费大于原订单手续费
            if (refundFee.compareTo(oldTradeFee) > 0) {
                refundFee = oldTradeFee;
            }
            // 退款金额等于原订单交易金额，则为全额退款 add by gl
            if (refundAmt.compareTo(oldTradeAmt) == 0) {
                refundFee = oldTradeFee;
            }
            // 待清分手续费
            clearFee = oldTradeFee.subtract(refundFee);
            // 退款交易金额
            refundAmt = refundSettleAmt.add(refundFee);
            // 待清分交易金额
            clearTradeAmt = clearSettleAmt.add(clearFee);
        }
        // 生成退款订单
        FeeOrderDO refundFeeOrderDO = new FeeOrderDO();
        String refundFeeOrderNo = generateOrderNo();
        refundFeeOrderDO.setOrderNo(refundFeeOrderNo);
        refundFeeOrderDO.setUserId(oldFeeOrderDO.getUserId());
        refundFeeOrderDO.setUserName(oldFeeOrderDO.getUserName());
        refundFeeOrderDO.setBusType(oldFeeOrderDO.getBusType());
        refundFeeOrderDO.setBusTypeDesc(oldFeeOrderDO.getBusTypeDesc());
        refundFeeOrderDO.setCcy(oldFeeOrderDO.getCcy());
        refundFeeOrderDO.setTradeTotalAmt(refundAmt);
        refundFeeOrderDO.setTradeAmt(refundSettleAmt);
        refundFeeOrderDO.setTradeDate(tradeDate);
        refundFeeOrderDO.setTradeTime(tradeTime);
        refundFeeOrderDO.setCalculateMod(oldFeeOrderDO.getCalculateMod());
        refundFeeOrderDO.setCalculateType(oldFeeOrderDO.getCalculateType());
        refundFeeOrderDO.setRate(rate);
        refundFeeOrderDO.setFee(refundFee);
        refundFeeOrderDO.setClearStats(Constants.CLEAR_STATUS_REFUND);
        refundFeeOrderDO.setBusOrderNo(busOrderNo);
        refundFeeOrderDO.setBusOrderTime(oldFeeOrderDO.getBusOrderTime());
        registerFeeOrder(refundFeeOrderDO);
        // 判断原交易订单是否清分
        if (oldClearStats.equals(Constants.CLEAR_STATUS_CHECK)) {
            // 退款账务处理
            feeRefundAccountTreat(userId, Constants.AC_TXSTS_N, refundFeeOrderDO);
        } else {
            // 待清分结算金额 modify by gonglei
            //if (clearSettleAmt.compareTo(BigDecimal.ZERO) > 0) {
            // 待清分订单金额
            if (clearTradeAmt.compareTo(BigDecimal.ZERO) > 0) {
                // 登记待清分订单
                FeeOrderDO needHandFeeOrderDO = new FeeOrderDO();
                String needHandFeeOrderNo = generateOrderNo();
                needHandFeeOrderDO.setOrderNo(needHandFeeOrderNo);
                needHandFeeOrderDO.setUserId(oldFeeOrderDO.getUserId());
                needHandFeeOrderDO.setUserName(oldFeeOrderDO.getUserName());
                needHandFeeOrderDO.setBusType(oldFeeOrderDO.getBusType());
                needHandFeeOrderDO.setBusTypeDesc(oldFeeOrderDO.getBusTypeDesc());
                needHandFeeOrderDO.setCcy(oldFeeOrderDO.getCcy());
                needHandFeeOrderDO.setTradeTotalAmt(clearTradeAmt);
                needHandFeeOrderDO.setTradeAmt(clearSettleAmt);
                needHandFeeOrderDO.setTradeDate(tradeDate);
                needHandFeeOrderDO.setTradeTime(tradeTime);
                needHandFeeOrderDO.setCalculateMod(oldFeeOrderDO.getCalculateMod());
                needHandFeeOrderDO.setCalculateType(oldFeeOrderDO.getCalculateType());
                needHandFeeOrderDO.setRate(rate);
                needHandFeeOrderDO.setFee(clearFee);
                needHandFeeOrderDO.setClearStats(Constants.CLEAR_STATUS_INIT);
                needHandFeeOrderDO.setBusOrderNo(busOrderNo);
                needHandFeeOrderDO.setBusOrderTime(oldFeeOrderDO.getBusOrderTime());
                registerFeeOrder(needHandFeeOrderDO);
            }
            // 更新原订单状态
            FeeOrderDO updateValueFeeOrderDO = new FeeOrderDO();
            updateValueFeeOrderDO.setClearStats(Constants.CLEAR_STATUS_REFUND);
            FeeOrderDO updateConditionFeeOrderDO = new FeeOrderDO();
            updateConditionFeeOrderDO.setOrderNo(oldFeeOrderDO.getOrderNo());
            updateConditionFeeOrderDO.setClearStats(Constants.CLEAR_STATUS_INIT);
            updateFeeOrder(updateValueFeeOrderDO, updateConditionFeeOrderDO);
        }
        merchantRefundFeeRspDTO.setOrderNo(refundFeeOrderNo);
        return merchantRefundFeeRspDTO;
    }

    @Override
    public void merchantRefundFeeReversal(MerchantRefundFeeReversalReqDTO refundFeeReversalReqDTO)
            throws LemonException {
        LocalDate tradeDate = DateTimeUtils.getCurrentLocalDate();
        LocalTime tradeTime = DateTimeUtils.getCurrentLocalTime();
        // 查询退款订单信息
        String orderNo = refundFeeReversalReqDTO.getOrderNo();
        FeeOrderDO refundOrderDO = feeOrderDao.get(orderNo);
        if (JudgeUtils.isNull(refundOrderDO)) {
            LemonException.throwBusinessException(MsgCdEnum.BUS_ORDER_NOT_EXSITS.getMsgCd());
        }
        String busOrderNo = refundOrderDO.getBusOrderNo();
        // 查询原交易计费订单信息
        FeeOrderDO oldFeeOrderDO = feeOrderDao.getRecentNeedHandle(busOrderNo);
        int result;
        FeeOrderDO updateValueDO;
        FeeOrderDO updateConditionDO;
        if (JudgeUtils.isNull(oldFeeOrderDO)) {
            // 登记待清分订单
            FeeOrderDO needHandFeeOrderDO = new FeeOrderDO();
            String needHandFeeOrderNo = generateOrderNo();
            BeanUtils.copyProperties(needHandFeeOrderDO, refundOrderDO);
            needHandFeeOrderDO.setOrderNo(needHandFeeOrderNo);
            needHandFeeOrderDO.setTradeDate(tradeDate);
            needHandFeeOrderDO.setTradeTime(tradeTime);
            needHandFeeOrderDO.setClearStats(Constants.CLEAR_STATUS_INIT);
            registerFeeOrder(needHandFeeOrderDO);

            // 更新退款订单为撤销
            updateValueDO = new FeeOrderDO();
            updateValueDO.setClearStats(Constants.CLEAR_STATUS_REVERSAL);
            updateConditionDO = new FeeOrderDO();
            updateConditionDO.setOrderNo(refundOrderDO.getOrderNo());
            updateConditionDO.setClearStats(Constants.CLEAR_STATUS_REFUND);
            result = feeOrderDao.updateByCondition(updateValueDO, updateConditionDO);
            if (result <= 0) {
                LemonException.throwBusinessException(MsgCdEnum.FEE_REVERSAL_FAIL.getMsgCd());
            }
        } else {
            // 判断原交易计费订单清分状态
            String clearStats = oldFeeOrderDO.getClearStats();
            if (clearStats.equals(Constants.CLEAR_STATUS_CHECK)) {
                // 做账务冲正处理
                String userId = refundOrderDO.getUserId();
                feeRefundAccountTreat(userId, Constants.AC_TXSTS_C, refundOrderDO);
            } else {
                // 登记待清分订单
                BigDecimal tradeTotalAmt = refundOrderDO.getTradeTotalAmt().add(oldFeeOrderDO.getTradeTotalAmt());
                BigDecimal tradeAmt = refundOrderDO.getTradeAmt().add(oldFeeOrderDO.getTradeAmt());
                BigDecimal tradeFee = refundOrderDO.getFee().add(oldFeeOrderDO.getFee());
                FeeOrderDO needHandFeeOrderDO = new FeeOrderDO();
                String needHandFeeOrderNo = generateOrderNo();
                needHandFeeOrderDO.setOrderNo(needHandFeeOrderNo);
                needHandFeeOrderDO.setUserId(oldFeeOrderDO.getUserId());
                needHandFeeOrderDO.setUserName(oldFeeOrderDO.getUserName());
                needHandFeeOrderDO.setBusType(oldFeeOrderDO.getBusType());
                needHandFeeOrderDO.setBusTypeDesc(oldFeeOrderDO.getBusTypeDesc());
                needHandFeeOrderDO.setCcy(oldFeeOrderDO.getCcy());
                needHandFeeOrderDO.setTradeTotalAmt(tradeTotalAmt);
                needHandFeeOrderDO.setTradeAmt(tradeAmt);
                needHandFeeOrderDO.setTradeDate(tradeDate);
                needHandFeeOrderDO.setTradeTime(tradeTime);
                needHandFeeOrderDO.setCalculateMod(oldFeeOrderDO.getCalculateMod());
                needHandFeeOrderDO.setCalculateType(oldFeeOrderDO.getCalculateType());
                needHandFeeOrderDO.setRate(oldFeeOrderDO.getRate());
                needHandFeeOrderDO.setFee(tradeFee);
                needHandFeeOrderDO.setClearStats(Constants.CLEAR_STATUS_INIT);
                needHandFeeOrderDO.setBusOrderNo(busOrderNo);
                needHandFeeOrderDO.setBusOrderTime(oldFeeOrderDO.getBusOrderTime());
                registerFeeOrder(needHandFeeOrderDO);

                // 更新原交易计费订单信息为撤销
                updateValueDO = new FeeOrderDO();
                updateValueDO.setClearStats(Constants.CLEAR_STATUS_REVERSAL);
                updateConditionDO = new FeeOrderDO();
                updateConditionDO.setOrderNo(oldFeeOrderDO.getOrderNo());
                updateConditionDO.setClearStats(Constants.CLEAR_STATUS_INIT);
                result = feeOrderDao.updateByCondition(updateValueDO, updateConditionDO);
                if (result <= 0) {
                    LemonException.throwBusinessException(MsgCdEnum.FEE_REVERSAL_FAIL.getMsgCd());
                }
            }
            // 更新退款订单为撤销
            updateValueDO = new FeeOrderDO();
            updateValueDO.setClearStats(Constants.CLEAR_STATUS_REVERSAL);
            updateConditionDO = new FeeOrderDO();
            updateConditionDO.setOrderNo(orderNo);
            updateConditionDO.setClearStats(Constants.CLEAR_STATUS_REFUND);
            result = feeOrderDao.updateByCondition(updateValueDO, updateConditionDO);
            if (result <= 0) {
                LemonException.throwBusinessException(MsgCdEnum.FEE_REVERSAL_FAIL.getMsgCd());
            }
        }
    }

    // 生成订单号
    private String generateOrderNo() {
        String tradeDateTimeStr = DateTimeUtils.getCurrentDateTimeStr();
        String orderJrn = IdGenUtils.generateId("TFM_ORDER_NO", 8);
        return LemonUtils.getApplicationName() + tradeDateTimeStr + orderJrn;
    }

    // 登记订单
    private void registerFeeOrder(FeeOrderDO feeOrderDO) throws LemonException {
        int result = feeOrderDao.insert(feeOrderDO);
        if (result <= 0) {
            LemonException.throwBusinessException(MsgCdEnum.FEE_REFUND_FAIL.getMsgCd());
        }
    }

    // 更新订单
    private void updateFeeOrder(FeeOrderDO updateValueFeeOrderDO, FeeOrderDO updateConditionFeeOrderDO)
            throws LemonException {
        int result = feeOrderDao.updateByCondition(updateValueFeeOrderDO, updateConditionFeeOrderDO);
        if (result <= 0) {
            LemonException.throwBusinessException(MsgCdEnum.FEE_REFUND_FAIL.getMsgCd());
        }
    }

    // 用户基本信息查询
    private UserBasicInfDTO getUserBasicInfo(String userId) throws LemonException {
        GenericRspDTO<UserBasicInfDTO> rspDTO = userBasicInfClient.queryUser(userId);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            if (logger.isDebugEnabled()) {
                logger.debug("user:" + userId + " get basic info failure");
            }
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
        return rspDTO.getBody();
    }

    // 查询账户账号
    private String getAccountNo(String userId) {
        GenericRspDTO<String> rspDTO = accountManagementClient.queryAcNo(userId);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            if (logger.isDebugEnabled()) {
                logger.debug("user:" + userId + " get account no failure");
            }
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
        return rspDTO.getBody();
    }

    // 商户退款账务处理
    private void feeRefundAccountTreat(String userId, String txType, FeeOrderDO feeOrderDO) throws LemonException {
        // 退款订单号
        String refundFeeOrderNo = feeOrderDO.getOrderNo();
        // 退款订单日期
        LocalDate refundOrderDate = feeOrderDO.getTradeDate();
        // 退款订单时间
        LocalTime refundOrderTime = feeOrderDO.getTradeTime();
        // 退款交易金额
        BigDecimal refundTradeAmt = feeOrderDO.getTradeTotalAmt();
        // 退款结算金额
        BigDecimal refundSettleAmt = feeOrderDO.getTradeAmt();
        // 退款手续费
        BigDecimal refundFee = feeOrderDO.getFee();
        // 查询商户账户账号
        String accountNo = getAccountNo(userId);
        GenericDTO<List<AccountingReqDTO>> reqDTO = new GenericDTO<>();
        List<AccountingReqDTO> accountingReqDTOs = new ArrayList<>();
        // 借：其他应付款-支付账户-商户结算账户
        // 退款金额大于0才记账
        if (refundSettleAmt.compareTo(BigDecimal.ZERO) > 0) {
            AccountingReqDTO accountingReqDTO1 = new AccountingReqDTO();
            accountingReqDTO1.setTxSts(txType); // 交易状态 N:正常 R:撤销 C:冲正
            accountingReqDTO1.setAcTyp("U");// 账户类型 U:用户 I：科目
            accountingReqDTO1.setAcNo(accountNo);// 交易账号
            accountingReqDTO1.setCapTyp(Constants.ACM_TYPE_SETTLE);// 资金属性
            accountingReqDTO1.setTxTyp("06");// 交易类型 06:退款
            accountingReqDTO1.setTxAmt(refundSettleAmt); // 交易金额
            accountingReqDTO1.setDcFlg("D"); // 借贷标志 D:借 C:贷
            accountingReqDTO1.setTxJrnNo(refundFeeOrderNo);
            accountingReqDTO1.setTxOrdNo(refundFeeOrderNo); // 交易订单号
            accountingReqDTO1.setTxOrdDt(refundOrderDate); // 交易订单日期
            accountingReqDTO1.setTxOrdTm(refundOrderTime); // 交易订单时间
            accountingReqDTO1.setRmk("商户退款");
            accountingReqDTOs.add(accountingReqDTO1);
        }
        // 手续费大于0才记账
        if (refundFee.compareTo(BigDecimal.ZERO) > 0) {
            // 借：手续费收入-支付账户-消费
            AccountingReqDTO accountingReqDTO2 = new AccountingReqDTO();
            accountingReqDTO2.setTxSts(txType); // 交易状态 N:正常 R:撤销 C:冲正
            accountingReqDTO2.setAcTyp("I");// 账户类型 U:用户 I：科目
            accountingReqDTO2.setItmNo(Constants.AC_ITEMNO_FEE); // 科目账号
            accountingReqDTO2.setCapTyp(Constants.ACM_TYPE_CASH);// 资金属性
            accountingReqDTO2.setTxTyp("06");// 交易类型 06:退款
            accountingReqDTO2.setTxAmt(refundFee); // 交易金额
            accountingReqDTO2.setDcFlg("D"); // 借贷标志 D:借 C:贷
            accountingReqDTO2.setTxJrnNo(refundFeeOrderNo);
            accountingReqDTO2.setTxOrdNo(refundFeeOrderNo); // 交易订单号
            accountingReqDTO2.setTxOrdDt(refundOrderDate); // 交易订单日期
            accountingReqDTO2.setTxOrdTm(refundOrderTime); // 交易订单时间
            accountingReqDTO2.setRmk("商户退款");
            accountingReqDTOs.add(accountingReqDTO2);
        }
        // 贷：其他应付款-暂收-收银台
        AccountingReqDTO accountingReqDTO3 = new AccountingReqDTO();
        accountingReqDTO3.setTxSts(txType); // 交易状态 N:正常 R:撤销 C:冲正 撤销暂不考虑
        accountingReqDTO3.setAcTyp("I");// 账户类型 U:用户 I：科目
        accountingReqDTO3.setItmNo(Constants.AC_ITEMNO_CASH);// 科目账号
        accountingReqDTO3.setCapTyp(Constants.ACM_TYPE_CASH);// 资金属性
        accountingReqDTO3.setTxTyp("06");// 交易类型 06:退款
        accountingReqDTO3.setTxAmt(refundTradeAmt); // 交易金额
        accountingReqDTO3.setDcFlg("C"); // 借贷标志 D:借 C:贷
        accountingReqDTO3.setTxJrnNo(refundFeeOrderNo);
        accountingReqDTO3.setTxOrdNo(refundFeeOrderNo); // 交易订单号
        accountingReqDTO3.setTxOrdDt(refundOrderDate); // 交易订单日期
        accountingReqDTO3.setTxOrdTm(refundOrderTime); // 交易订单时间
        accountingReqDTO3.setRmk("商户退款");
        accountingReqDTOs.add(accountingReqDTO3);
        reqDTO.setBody(accountingReqDTOs);
        GenericRspDTO<NoBody> rspDTO = accountingTreatmentClient.accountingTreatment(reqDTO);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
    }

    // 订单清分账务处理
    private void clearAccountTreat(String userId, String txType, FeeOrderDO feeOrderDO) throws LemonException {
        // 查询商户账户账号
        String accountNo = getAccountNo(userId);
        String tradeOrderNo = feeOrderDO.getOrderNo();
        BigDecimal tradeTotalAmt = feeOrderDO.getTradeTotalAmt();
        BigDecimal fee = feeOrderDO.getFee();
        LocalDate tradeDate = feeOrderDO.getTradeDate();
        LocalTime tradeTime = feeOrderDO.getTradeTime();
        GenericDTO<List<AccountingReqDTO>> reqDTO = new GenericDTO<>();
        List<AccountingReqDTO> accountingReqDTOs = new ArrayList<>();
        // 借：其他应付款-暂收-收银台
        AccountingReqDTO accountingReqDTO1 = new AccountingReqDTO();
        accountingReqDTO1.setTxSts(txType); // 交易状态 N:正常 R:撤销 C:冲正
        accountingReqDTO1.setAcTyp("I");// 账户类型 U:用户 I：科目
        accountingReqDTO1.setItmNo(Constants.AC_ITEMNO_CASH);// 科目号
        accountingReqDTO1.setCapTyp(Constants.ACM_TYPE_CASH);// 资金属性
        accountingReqDTO1.setTxTyp("02");// 交易类型 02:消费
        accountingReqDTO1.setTxAmt(tradeTotalAmt); // 交易金额
        accountingReqDTO1.setDcFlg("D"); // 借贷标志 D:借 C:贷
        accountingReqDTO1.setTxJrnNo(tradeOrderNo);
        accountingReqDTO1.setTxOrdNo(tradeOrderNo); // 交易订单号
        accountingReqDTO1.setTxOrdDt(tradeDate); // 交易订单日期
        accountingReqDTO1.setTxOrdTm(tradeTime); // 交易订单时间
        accountingReqDTO1.setRmk("商户清分");
        accountingReqDTOs.add(accountingReqDTO1);
        // 贷: 其他应付款-支付账户-商户结算账户
        AccountingReqDTO accountingReqDTO2 = new AccountingReqDTO();
        accountingReqDTO2.setTxSts(txType); // 交易状态 N:正常 R:撤销 C:冲正 撤销暂不考虑
        accountingReqDTO2.setAcTyp("U");// 账户类型 U:用户 I：科目
        accountingReqDTO2.setAcNo(accountNo);// 交易账号
        accountingReqDTO2.setCapTyp(Constants.ACM_TYPE_SETTLE);// 资金属性
        accountingReqDTO2.setTxTyp("02");// 交易类型 02:消费
        accountingReqDTO2.setTxAmt(tradeTotalAmt); // 交易金额
        accountingReqDTO2.setDcFlg("C"); // 借贷标志 D:借 C:贷
        accountingReqDTO2.setTxJrnNo(tradeOrderNo);
        accountingReqDTO2.setTxOrdNo(tradeOrderNo); // 交易订单号
        accountingReqDTO2.setTxOrdDt(tradeDate); // 交易订单日期
        accountingReqDTO2.setTxOrdTm(tradeTime); // 交易订单时间
        accountingReqDTO2.setRmk("商户清分");
        accountingReqDTOs.add(accountingReqDTO2);
        // 手续费大于0才记账
        if (fee.compareTo(BigDecimal.ZERO) > 0) {
            // 借：其他应付款-支付账户-商户结算账户
            AccountingReqDTO accountingReqDTO3 = new AccountingReqDTO();
            accountingReqDTO3.setTxSts(txType); // 交易状态 N:正常 R:撤销 C:冲正
            accountingReqDTO3.setAcTyp("U");// 账户类型 U:用户 I：科目
            accountingReqDTO3.setAcNo(accountNo);// 交易账号
            accountingReqDTO3.setCapTyp(Constants.ACM_TYPE_SETTLE);// 资金属性
            accountingReqDTO3.setTxTyp("02");// 交易类型 02:消费
            accountingReqDTO3.setTxAmt(fee); // 交易金额
            accountingReqDTO3.setDcFlg("D"); // 借贷标志 D:借 C:贷
            accountingReqDTO3.setTxJrnNo(tradeOrderNo);
            accountingReqDTO3.setTxOrdNo(tradeOrderNo); // 交易订单号
            accountingReqDTO3.setTxOrdDt(tradeDate); // 交易订单日期
            accountingReqDTO3.setTxOrdTm(tradeTime); // 交易订单时间
            accountingReqDTO3.setRmk("商户清分");
            accountingReqDTOs.add(accountingReqDTO3);
            // 贷：手续费收入-支付账户-消费
            AccountingReqDTO accountingReqDTO4 = new AccountingReqDTO();
            accountingReqDTO4.setTxSts(txType); // 交易状态 N:正常 R:撤销 C:冲正
            accountingReqDTO4.setAcTyp("I");// 账户类型 U:用户 I：科目
            accountingReqDTO4.setItmNo(Constants.AC_ITEMNO_FEE);// 科目号
            accountingReqDTO4.setCapTyp(Constants.ACM_TYPE_CASH);// 资金属性
            accountingReqDTO4.setTxTyp("02");// 交易类型 02:消费
            accountingReqDTO4.setTxAmt(fee); // 交易金额
            accountingReqDTO4.setDcFlg("C"); // 借贷标志 D:借 C:贷
            accountingReqDTO4.setTxJrnNo(tradeOrderNo);
            accountingReqDTO4.setTxOrdNo(tradeOrderNo); // 交易订单号
            accountingReqDTO4.setTxOrdDt(tradeDate); // 交易订单日期
            accountingReqDTO4.setTxOrdTm(tradeTime); // 交易订单时间
            accountingReqDTO4.setRmk("商户清分");
            accountingReqDTOs.add(accountingReqDTO4);
        }
        reqDTO.setBody(accountingReqDTOs);
        GenericRspDTO<NoBody> rspDTO = accountingTreatmentClient.accountingTreatment(reqDTO);
        if (JudgeUtils.isNotSuccess(rspDTO.getMsgCd())) {
            if (logger.isDebugEnabled()) {
                logger.debug("user:" + userId + " clear account trade failure");
            }
            LemonException.throwBusinessException(rspDTO.getMsgCd());
        }
    }

    @Override
    public void clearHandler(LocalDate checkDate, CheckControlDO checkControlDO) throws LemonException {
        String checkBatchNo = checkControlDO.getCheckBatchNo();
        LocalDateTime tradeTime = DateTimeUtils.getCurrentLocalDateTime();
        // 查询待清分的手续费订单
        FeeOrderDO qryInFeeOrderDO = new FeeOrderDO();
        qryInFeeOrderDO.setTradeDate(checkDate);
        qryInFeeOrderDO.setClearStats(Constants.CLEAR_STATUS_INIT);
        List<FeeOrderDO> feeOrderDOs = feeOrderDao.getUnfinishListByCondition(qryInFeeOrderDO);
        Integer totalCount = Integer.valueOf(0);
        BigDecimal totalAmt = BigDecimal.valueOf(0.00);
        if (JudgeUtils.isNotEmpty(feeOrderDOs)) {
            for (FeeOrderDO feeOrderDO : feeOrderDOs) {
                String userId = feeOrderDO.getUserId();
                String orderNo = feeOrderDO.getOrderNo();
                BigDecimal tradeAmt = feeOrderDO.getTradeTotalAmt();
                try {
                    // 账务处理
                    clearAccountTreat(userId, Constants.AC_TXSTS_N, feeOrderDO);
                    logger.info("=========clearHandler() 批次号 = " + checkBatchNo + "; 清分订单号 = " + orderNo + "=========");
                    // 更新手续费订单清分状态
                    FeeOrderDO updateValueFeeOrderDO = new FeeOrderDO();
                    updateValueFeeOrderDO.setClearStats(Constants.CLEAR_STATUS_CHECK);
                    FeeOrderDO updateConditionFeeOrderDO = new FeeOrderDO();
                    updateConditionFeeOrderDO.setOrderNo(orderNo);
                    updateConditionFeeOrderDO.setClearStats(Constants.CLEAR_STATUS_INIT);
                    int result = feeOrderDao.updateByCondition(updateValueFeeOrderDO, updateConditionFeeOrderDO);
                    if (result <= 0) {
                        // 清分状态更新失败做账务冲正处理
                        clearAccountTreat(userId, Constants.AC_TXSTS_C, feeOrderDO);
                        // 继续处理后面的订单
                        continue;
                    }
                    // 账单同步
                    billSynchronizeProduce.billSynchronizeNotify(feeOrderDO);
                    totalCount++;
                    totalAmt = totalAmt.add(tradeAmt);
                } catch (LemonException e) {
                    continue;
                }
            }
        }
        // 更新手续费清分批次信息
        CheckControlDO updateValueCheckControlDO = new CheckControlDO();
        updateValueCheckControlDO.setTotalCount(totalCount);
        updateValueCheckControlDO.setTotalAmt(totalAmt);
        updateValueCheckControlDO.setModifyTime(tradeTime);
        CheckControlDO updateConditionCheckControlDO = new CheckControlDO();
        updateConditionCheckControlDO.setCheckBatchNo(checkBatchNo);
        checkControlDao.updateByCondition(updateValueCheckControlDO, updateConditionCheckControlDO);
    }

    /**
     * @Description 查询订单清分结果
     * <AUTHOR>
     * @param merchantCleanReqDTO
     * @throws LemonException
     */
    @Override
    public FeeOrderDO merchantCleanQuery(MerchantCleanReqDTO merchantCleanReqDTO) throws LemonException{
        return feeOrderDao.getFirstBusOrder(merchantCleanReqDTO.getOrderNo());
    }
}
