package com.hisun.lemon.tfm.contants;

/**
 * @Description 计费模块常量类
 * <AUTHOR>
 * @date 2017年7月14日 下午5:49:21 
 * @version V1.0
 */
public class Constants {
    
    private Constants() {
        throw new IllegalStateException("Utility class");
    }
    
    /** 校验枚举值 初始状态 */
    public static final String CHECK_INIT = "0";
    /** 校验枚举值 验证状态 */
    public static final String CHECK_VERIFIED = "1";
    /** 校验枚举值 失效状态 */
    public static final String CHECK_EXPIRE = "2";
    /** 状态枚举值 无效状态 */
    public static final String STATUS_INVALID = "0";
    /** 状态枚举值 有效状态 */
    public static final String STATUS_AVAILABLE = "1";
    /** 清分状态枚举值 待清分状态 */
    public static final String CLEAR_STATUS_INIT = "0";
    /** 清分状态枚举值 已清分状态 */
    public static final String CLEAR_STATUS_CHECK = "1";
    /** 清分状态枚举值 已退款 */
    public static final String CLEAR_STATUS_REFUND = "2";
    /** 清分状态枚举值 已撤销 */
    public static final String CLEAR_STATUS_REVERSAL = "3";
    /** 清分状态枚举值 无需清分状态 */
    public static final String CLEAR_STATUS_NO = "9";
    /** 计费模式枚举值 内扣 */
    public static final String FEE_MODE_INTERNAL = "internal";
    /** 计费模式枚举值 外扣 */
    public static final String FEE_MODE_EXTERNAL = "external";
    /** 计费方式枚举值 百分比 */
    public static final String FEE_CALCULATE_PERCENT = "percent";
    /** 计费方式枚举值 固定金额 */
    public static final String FEE_CALCULATE_FIXED = "fixed";
    /** 收费方式枚举值 实时单笔 */
    public static final String FEE_CHARGE_SINGLE = "single";
    /** 默认userId */
    public static final String USER_ID_DEFAULT = "default";
    /** 收费方式枚举值 固定周期 */
    public static final String FEE_CHARGE_CYCLE = "cycle";
    /** 账务交易状态 正常*/
    public static final String AC_TXSTS_N = "N";
    /** 账务交易状态 冲正*/
    public static final String AC_TXSTS_C = "C";
    /** 账务交易状态 撤销*/
    public static final String AC_TXSTS_R = "R";
    /** 其他应付款-暂收-收银台  科目号 */
    public static final String AC_ITEMNO_CASH = "2241030001";
    /** 手续费收入-支付账户-消费  科目号 */
    public static final String AC_ITEMNO_FEE = "6021010001";
    /** 账户类型枚举值 现金账户 */ 
    public static final String ACM_TYPE_CASH = "1";
    /** 账户类型枚举值 待结算账户 */ 
    public static final String ACM_TYPE_SETTLE = "8";
    /** 对账状态举值 初始状态 */
    public static final String CHECK_FILE_INIT = "0";
    /** 对账状态举值 获取文件 */
    public static final String CHECK_FILE_GET = "1";
    /** 对账状态举值 获取文件 */
    public static final String CHECK_FILE_DATABASE = "2";
    /** 对账状态举值 对账处理 */
    public static final String CHECK_FILE_PROCESS = "3";
    /** 对账状态举值 差错处理 */
    public static final String CHECK_ERROR_PROCESS = "4";
    /** 对账状态举值 对账结束 */
    public static final String CHECK_FILE_OVER = "9";
    /** 竖线分隔符 */
    public static final String VERTICALLINE = "\\|";
}
