package com.hisun.lemon.tfm.service;

import java.time.LocalDate;

import com.hisun.lemon.common.exception.LemonException;

/**
 * @Description 对账批次服务接口
 * <AUTHOR>
 * @date 2017年7月7日 上午11:07:48
 * @version V1.0
 */
public interface ICheckControlService {

    /**
     * @Description 登记对账批次
     * <AUTHOR>
     * @param checkDate
     * @throws LemonException
     */
    void checkBatchRegister(LocalDate checkDate) throws LemonException;
    
    /**
     * @Description 处理对账批次
     * <AUTHOR>
     * @param checkDate
     * @throws LemonException
     */
    void checkBatchProcess(LocalDate checkDate) throws LemonException;
}
