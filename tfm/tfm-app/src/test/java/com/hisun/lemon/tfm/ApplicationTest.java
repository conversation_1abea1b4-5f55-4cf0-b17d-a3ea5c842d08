package com.hisun.lemon.tfm;

import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.tfm.service.ICheckControlService;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDate;

@RunWith(SpringRunner.class)
@SpringBootTest
@Ignore
public class ApplicationTest {
    @Resource
    private ICheckControlService checkControlService;

    /**
     * 创建对账批次
     */
    @Test
    public void registerChkBatNo() {
        LocalDate checkDate = DateTimeUtils.getCurrentLocalDate();
        checkControlService.checkBatchRegister(checkDate);
        System.out.println("=================end=================");
    }
    
}
