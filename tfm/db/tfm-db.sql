insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TFM00000','zh','交易成功!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TFM10001','zh','用户号不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TFM10002','zh','业务类型不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TFM10003','zh','交易金额不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TFM10004','zh','交易订单不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TFM10005','zh','币种不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TFM10008','zh','订单时间不能为空!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TFM30001','zh','交易费率信息不存在或已失效!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TFM30002','zh','手续费登记失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TFM30003','zh','退款金额不能大于原订单金额!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TFM30004','zh','交易订单不存在!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TFM30005','zh','手续费退款处理失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TFM30006','zh','手续费退款撤销处理失败!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TFM39998','zh','对账组件配置有误!',now(),now());
insert into lemon_msg_info(msg_cd,language,msg_info,create_time,modifyTime) 
values ('TFM39999','zh','对账组件生成失败!',now(),now());

--drop table tfm_check_control;
--计费对账主控表
CREATE TABLE IF NOT EXISTS tfm_check_control (
    check_batch_no VARCHAR(25) NOT NULL COMMENT '对账批次号',
    check_date DATE NOT NULL COMMENT '对账清算日期',
    check_seq INTEGER NOT NULL COMMENT '对账批次序号',
    check_type_id VARCHAR(8) NOT NULL COMMENT '对账类型',
    check_file_nm VARCHAR(64) COMMENT '对账文件名称',
    check_begin_time DATETIME NOT NULL COMMENT '对账开始时间',
    check_end_time DATETIME COMMENT '对账结束时间',
    check_stats CHAR(1) DEFAULT '0' COMMENT '对账状态 0:待对账 1:已获取文件 2:已入库 3:已对账 4:已差错处理 9:对账完成',
    file_receive_count INTEGER(9) DEFAULT 0 COMMENT '文件总笔数',
    file_receive_amt DECIMAL(15,2) DEFAULT 0.00 COMMENT '文件总金额',
    total_count INTEGER(9) DEFAULT 0 COMMENT '总笔数',
    total_amt DECIMAL(15,2) DEFAULT 0.00 COMMENT '总金额',
    short_count INTEGER(9) DEFAULT 0 COMMENT '我方有对方无笔数',
    short_amt DECIMAL(15,2) DEFAULT 0.00 COMMENT '我方有对方无金额',
    long_count INTEGER(9) DEFAULT 0 COMMENT '对方有我方无笔数',
    long_amt DECIMAL(15,2) DEFAULT 0.00 COMMENT '对方有我方无金额',
    error_count INTEGER(9) DEFAULT 0 COMMENT '差错笔数',
    error_amt DECIMAL(15,2) DEFAULT 0.00 COMMENT '差错金额',
    doubt_count INTEGER(9) DEFAULT 0 COMMENT '存疑笔数',
    doubt_amt DECIMAL(15,2) DEFAULT 0.00 COMMENT '存疑金额',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE tfm_check_control ADD CONSTRAINT pk_tfm_check_control PRIMARY KEY(check_batch_no);
--索引
ALTER TABLE tfm_check_control ADD UNIQUE idx_tfm_check_control_1(check_batch_no);
--索引
ALTER TABLE tfm_check_control ADD UNIQUE idx_tfm_check_control_2(check_type_id, check_date, check_seq);
--索引
ALTER TABLE tfm_check_control ADD INDEX idx_tfm_check_control_3(check_date);

--drop table tfm_check_param;
--计费对账参数表
CREATE TABLE IF NOT EXISTS tfm_check_param (
    check_type_id VARCHAR(16) NOT NULL COMMENT '对账类型',
    sftp_sys_channel VARCHAR(16) COMMENT '文件服务器系统渠道',
    sftp_bus_channel VARCHAR(16) COMMENT '文件服务器业务渠道',
    import_table VARCHAR(16) COMMENT '导入数据库表名',
    import_format VARCHAR(32) COMMENT '导入数据格式',
    import_file_path VARCHAR(128) COMMENT '导入数据文件路径',
    export_table VARCHAR(16) COMMENT '导出数据库表名',
    export_format VARCHAR(32) COMMENT '导出数据格式',
    export_file_path VARCHAR(128) COMMENT '导出数据文件路径',
    auto_flag CHAR(1) DEFAULT 'S' COMMENT '自动处理标识 U:人工 S:系统',
    encrypt_flag CHAR(1) DEFAULT '0' COMMENT '加密标识 0:否 1:是',
    encrypt_component VARCHAR(32) COMMENT '加密组件名称',
    descrypt_flag CHAR(1) DEFAULT '0' COMMENT '解密标识 0:否 1:是',
    descrypt_component VARCHAR(32) COMMENT '解密组件名称',
    check_process_component VARCHAR(32) COMMENT '对账处理组件名称',
    check_process_clazz VARCHAR(64) COMMENT '对账处理组件类对象',
    error_process_component VARCHAR(32) COMMENT '差错处理组件名称',
    error_process_clazz VARCHAR(64) COMMENT '差错处理组件类对象',
    multiple_check_flg CHAR(1) DEFAULT 'Y' COMMENT '每日多次对账标识 Y:是 N:否', 
    stats CHAR(1) DEFAULT '1' COMMENT '状态0:无效 1:生效',
    eff_date DATE NOT NULL COMMENT '生效日期',
    exp_date DATE NOT NULL COMMENT '失效日期',
    opr_id VARCHAR(8) NOT NULL COMMENT '操作员ID',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE tfm_check_param ADD CONSTRAINT pk_tfm_check_param PRIMARY KEY(check_type_id);
--索引
ALTER TABLE tfm_check_param ADD UNIQUE idx_tfm_check_param_1(check_type_id);

--drop table tfm_fee_order;
--交易手续费订单表
CREATE TABLE IF NOT EXISTS tfm_fee_order (
    order_no VARCHAR(25) NOT NULL COMMENT '订单号',
    user_id VARCHAR(20) NOT NULL COMMENT '内部用户号',
    user_name VARCHAR(32) NOT NULL COMMENT '用户名称',
    bus_type VARCHAR(8) NOT NULL COMMENT  '业务类型',
    bus_type_desc VARCHAR(16) NOT NULL COMMENT  '业务类型描述',
    ccy VARCHAR(8) NOT NULL COMMENT '币种',
    trade_total_amt DECIMAL(15,2) NOT NULL COMMENT '交易总金额',
    trade_amt DECIMAL(15,2) NOT NULL COMMENT '交易金额',
    trade_date DATE NOT NULL COMMENT '交易日期',
    trade_time TIME NOT NULL COMMENT '交易时间',
    calculate_mod VARCHAR(8) COMMENT '计费模式 internal:内扣 external:外扣',
    calculate_type VARCHAR(8) NOT NULL COMMENT '计费方式 percent:百分比 fixed:固定手续费',
    rate DECIMAL(15,4) NOT NULL COMMENT '费率',
    fee DECIMAL(15,2) NOT NULL COMMENT '交易手续费',
    clear_stats char(1) DEFAULT '0' COMMENT '清分状态 0:待清分 1:已清分 2:退款 3:撤销 9:不需清分',
    bus_order_no VARCHAR(32) COMMENT '业务订单号',
    bus_order_time DATETIME COMMENT '业务订单时间',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE tfm_fee_order ADD CONSTRAINT pk_tfm_fee_order PRIMARY KEY(order_no);
--索引
ALTER TABLE tfm_fee_order ADD UNIQUE idx_tfm_fee_order_1(order_no);
--索引
ALTER TABLE tfm_fee_order ADD INDEX idx_tfm_fee_order_2(user_id);
--索引
ALTER TABLE tfm_fee_order ADD INDEX idx_tfm_fee_order_3(bus_type);
--索引
ALTER TABLE tfm_fee_order ADD INDEX idx_tfm_fee_order_4(trade_date);
--索引
ALTER TABLE tfm_fee_order ADD INDEX idx_tfm_fee_order_5(bus_order_no);

--drop table tfm_file_config;
--文件服务器参数表
CREATE TABLE IF NOT EXISTS tfm_file_config (
    id VARCHAR(8) NOT NULL COMMENT 'ID',
    sftp_sys_channel VARCHAR(16) COMMENT '文件服务器系统渠道',
    sftp_bus_channel VARCHAR(16) COMMENT '文件服务器业务渠道',
    remote_name VARCHAR(16) COMMENT '文件服务器用户名',
    remote_password VARCHAR(16) COMMENT '文件服务器密码',
    remote_ip VARCHAR(32) COMMENT '文件服务器IP地址',
    remote_port VARCHAR(16) COMMENT '文件服务器端口',
    connect_timeout INT(8) COMMENT '连接超时时间 单位:毫秒',
    remote_file_path VARCHAR(32) COMMENT '文件上传路径',
    remote_file_name VARCHAR(128) COMMENT '文件上传名称',
    opr_id VARCHAR(8) NOT NULL COMMENT '操作员ID',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE tfm_file_config ADD CONSTRAINT pk_tfm_file_config PRIMARY KEY(id);
--索引
ALTER TABLE tfm_file_config ADD UNIQUE idx_tfm_file_config_1(id);
--索引
ALTER TABLE tfm_file_config ADD UNIQUE idx_tfm_file_config_2(sftp_sys_channel, sftp_bus_channel);

--DROP TABLE tfm_merchant_rate_rule;
--商户交易费率规则表
CREATE TABLE IF NOT EXISTS tfm_merchant_rate_rule (
    bus_type VARCHAR(8) NOT NULL COMMENT '业务类型',
    bus_type_desc VARCHAR(16) NOT NULL COMMENT '业务类型描述',
    user_id VARCHAR(20) NOT NULL COMMENT '内部用户号',
    user_name VARCHAR(32) NOT NULL COMMENT '用户名称',
    ccy VARCHAR(8) NOT NULL COMMENT '币种',
    calculate_mod VARCHAR(8) NOT NULL COMMENT '计费模式 internal:内扣 external:外扣',
    calculate_type VARCHAR(8) NOT NULL COMMENT '计费方式 percent:百分比 fixed:固定手续费',
    rate DECIMAL(15,4) default 0.0000 COMMENT '费率',
    fix_fee DECIMAL(15,2) default 0 COMMENT '固定手续费',
    charge_type VARCHAR(16) NOT NULL COMMENT '收费方式 single:单笔 cycle:固定周期',
    min_fee DECIMAL(15,2) DEFAULT 0 COMMENT '最低收取费用',
    max_fee DECIMAL(15,2) DEFAULT 9999999999999.99 COMMENT '最高收取费用',
    stats CHAR(1) DEFAULT '1' COMMENT '状态0:无效 1:生效',
    eff_date DATE NOT NULL COMMENT '生效日期',
    exp_date DATE NOT NULL COMMENT '失效日期',
    opr_id VARCHAR(8) NOT NULL COMMENT '操作员ID',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE tfm_merchant_rate_rule ADD CONSTRAINT pk_tfm_merchant_rate_rule PRIMARY KEY(bus_type, user_id);
--唯一索引
ALTER TABLE tfm_merchant_rate_rule ADD UNIQUE idx_tfm_merchant_rate_rule_1(bus_type, user_id);
--索引
ALTER TABLE tfm_merchant_rate_rule ADD INDEX idx_tfm_merchant_rate_rule_2(eff_date, exp_date);

--drop table tfm_rate_rule;
--个人交易费率规则表
CREATE TABLE IF NOT EXISTS tfm_rate_rule (
    bus_type VARCHAR(8) NOT NULL COMMENT '业务类型',
    bus_type_desc VARCHAR(16) NOT NULL COMMENT '业务类型描述',
    ccy VARCHAR(8) NOT NULL COMMENT '币种',
    calculate_mod VARCHAR(8) NOT NULL COMMENT '计费模式 internal:内扣 external:外扣',
    calculate_type VARCHAR(8) NOT NULL COMMENT '计费方式 percent:百分比 fixed:固定手续费',
    rate DECIMAL(15,4) default 0.0000 COMMENT '费率',
    fix_fee DECIMAL(15,2) default 0 COMMENT '固定手续费',
    charge_type VARCHAR(16) NOT NULL COMMENT '收费方式 single:单笔 cycle:固定周期',
    calculate_min_amt DECIMAL(15,2) DEFAULT 0 COMMENT '计费起始金额',
    min_fee DECIMAL(15,2) DEFAULT 0 COMMENT '最低收取费用',
    max_fee DECIMAL(15,2) DEFAULT 9999999999999.99 COMMENT '最高收取费用',
    stats CHAR(1) DEFAULT '1' COMMENT '状态0:无效 1:生效',
    eff_date DATE NOT NULL COMMENT '生效日期',
    exp_date DATE NOT NULL COMMENT '失效日期',
    opr_id VARCHAR(8) NOT NULL COMMENT '操作员ID',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    modify_time DATETIME NOT NULL COMMENT '修改时间',
    tm_smp TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳'
)

--主键
ALTER TABLE tfm_rate_rule ADD CONSTRAINT pk_tfm_rate_rule PRIMARY KEY(bus_type);
--索引
ALTER TABLE tfm_rate_rule ADD UNIQUE idx_tfm_rate_rule_1(bus_type);
--索引
ALTER TABLE tfm_rate_rule ADD INDEX idx_tfm_rate_rule_2(eff_date, exp_date);
