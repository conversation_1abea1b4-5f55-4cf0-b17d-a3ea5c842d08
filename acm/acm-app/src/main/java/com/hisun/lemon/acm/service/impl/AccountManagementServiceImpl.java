package com.hisun.lemon.acm.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.hisun.lemon.acm.common.utils.BalanceTagUtil;
import com.hisun.lemon.acm.component.AcmComponent;
import com.hisun.lemon.acm.constants.*;
import com.hisun.lemon.acm.dao.*;
import com.hisun.lemon.acm.dto.*;
import com.hisun.lemon.acm.entity.*;
import com.hisun.lemon.acm.service.IAccountManagementService;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.DateTimeUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.cpi.client.CregisClient;
import com.hisun.lemon.cpi.dto.CregisAccDetailReqDTO;
import com.hisun.lemon.cpi.dto.CregisAccDetailRspDTO;
import com.hisun.lemon.cpi.dto.CregisReqDTO;
import com.hisun.lemon.framework.data.GenericDTO;
import com.hisun.lemon.framework.data.GenericRspDTO;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.tam.client.ExchangeOrderClient;
import com.hisun.lemon.urm.client.UserAuthenticationClient;
import com.hisun.lemon.urm.client.UserBasicInfClient;
import com.hisun.lemon.urm.constants.URMConstants;
import com.hisun.lemon.urm.constants.URMMessageCode;
import com.hisun.lemon.urm.dto.CheckPayPwdDTO;
import com.hisun.lemon.urm.dto.UserBasicInfDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @function AccountManagementServiceImpl
 * @description 账户管理服务实现
 * @date 7/13/2017 Thu
 * @time 7:12 PM
 */
@Service
@Transactional
public class AccountManagementServiceImpl extends BaseService implements IAccountManagementService {
    private static final Logger logger = LoggerFactory.getLogger(AccountManagementServiceImpl.class);

    @Resource
    private IAcmAcInfDao iAcmAcInfDao;

    @Resource
    private IAcmAcBalDao iAcmAcBalDao;

    @Resource
    private IAcmItmInfDao iAcmItmInfDao;

    @Resource
    private IAcmItmBalDao iAcmItmBalDao;

    @Resource
    private IAcmItmDetailDao iAcmItmDetailDao;

    @Resource
    private IDmCcyListDao iDmCcyListDao;

    @Resource
    private IFmAccountInfoDao iFmAccountInfoDao;

    @Resource
    private AcmComponent acmComponent;


    @Resource
    private UserBasicInfClient userBasicInfClient;

    @Autowired
    @Qualifier("redisTemplateString")
    RedisTemplate<String, String> redisTemplate;

    @Resource
    private IDmAccountAddressDao iDmAccountAddressDao;

    @Resource
    private IDmTransferCallbackDao dmTransferCallbackDao;

    @Resource
    private UserAuthenticationClient userAuthenticationClient;

    @Resource
    private ExchangeOrderClient exchangeOrderClient;

    @Resource
    private CregisClient cregisClient;

    @Resource
    private IDmAccountAddressDao dmAccountAddressDao;

    /**
     * 开通支付账户
     *
     * @param userId
     * @return
     */
    @Override
    public String openUserAccount(String userId) {
        AcmAcInfDO acmAcInfDO = new AcmAcInfDO();
        String country = LemonUtils.getLocale().getCountry().toUpperCase();
        logger.info("AC_NO:" + country);
        if (JudgeUtils.isBlank(country)) {
            country = ACMConstants.UNKNOWN_COUNTRY_CODE;
        }
        StringBuffer countryCode = new StringBuffer();
        //获取国家缩写的ASC码的十进制值，作为账号的地区编码与序号组成用户账号
        for (int i = 0; i < country.length(); i++) {
            char c = country.charAt(i);
            countryCode.append(Integer.parseInt(Integer.toHexString(c), 16));
        }
        String acNo = IdGenUtils.generateId("AC_NO", countryCode.toString(), ACMConstants.AC_SEQ_LEN);
        acNo += generateCheckFlag(acNo);
        acmAcInfDO.setAcNo(acNo);
        acmAcInfDO.setAcSts(ACMConstants.AC_OPEN_STS);
        acmAcInfDO.setCcy(ACMConstants.SYS_DEFAULT_CCY);
        acmAcInfDO.setUserId(userId);
        acmAcInfDO.setAcCreDt(DateTimeUtils.getCurrentLocalDate());
        acmAcInfDO.setAcCreTm(DateTimeUtils.getCurrentLocalTime());
        acmAcInfDO.setCcyType(CcyTypeEnum.FM.getCcyType());
        iAcmAcInfDao.insert(acmAcInfDO);

        //创建用户余额记录
        createUserAccountBalance(acNo, ACMConstants.SYS_DEFAULT_CCY, null);

        /*交易结束返回结果*/
        return acNo;
    }

    /**
     * 生成账号校验位
     *
     * @param acNo
     * @return
     */
    public static String generateCheckFlag(String acNo) {
        int LEN = acNo.length();
        int SUM_POS = 0;
        for (int i = 0; i < LEN; i++) {
            int TMP = Integer.parseInt(acNo.substring(LEN - i - 1, LEN - i));
            /*数字的排序从右到左,校验位为第一位，奇数位*1 偶数位*2，如果结果为2位数，则把个位和十位相加*/
            if (i % 2 == 0) {
                SUM_POS += (TMP * 2 / 10) + (TMP * 2 % 10);
            } else {
                SUM_POS += TMP;
            }
        }
        int CHK_POS = 0;
        int modRes = SUM_POS % 10;
        if (modRes != 0) {
            CHK_POS = 10 - modRes;
        }
        logger.info("CHK_POS: " + Integer.toString(CHK_POS));
        return Integer.toString(CHK_POS);
    }

    /**
     * 开通科目账户
     *
     * @param acmItmInfDO
     * @return
     */

    @Override
    public void openItemAccount(AcmItmInfDO acmItmInfDO) {
        iAcmItmInfDao.insert(acmItmInfDO);

        //设置科目余额记录
        createItemAccountBalance(acmItmInfDO.getItmNo());
    }

    /**
     * 账户销户
     *
     * @param userId
     * @return
     */
    @Override
    public void closeUserAccount(String userId) {
        //检查账户余额是否为0
        AcmAcBalDO acmAcBalDO = new AcmAcBalDO();
        acmAcBalDO.setUserId(userId);
        List<AcmAcBalDO> acmAcBalDOS = iAcmAcBalDao.find(acmAcBalDO);
        for (AcmAcBalDO acmAcBal : acmAcBalDOS) {
            if (acmAcBal.getAcCurBal().compareTo(BigDecimal.ZERO) > 0) {
                LemonException.throwBusinessException(ACMMessageCode.AC_BAL_NOT_ZERO);
            }
        }

        //实例化账户信息实体对象，并执行销户
        AcmAcInfDO acmAcInfDO = new AcmAcInfDO();
        acmAcInfDO.setUserId(userId);
        acmAcInfDO.setAcSts(ACMConstants.AC_CANCEL_STS);
        acmAcInfDO.setAcClsDt(DateTimeUtils.getCurrentLocalDate());
        acmAcInfDO.setAcClsTm(DateTimeUtils.getCurrentLocalTime());
        iAcmAcInfDao.update(acmAcInfDO);
    }

    /**
     * 科目销户
     *
     * @param itmNo
     * @return
     */
    @Override
    public void closeItemAccount(String itmNo) {
        //检查科目余额是否为0
        AcmItmBalDO acmItmBalDO = iAcmItmBalDao.get(itmNo);
        if (acmItmBalDO.getTdCrBal().compareTo(acmItmBalDO.getTdDrBal()) != 0) {
            LemonException.throwBusinessException(ACMMessageCode.ITM_BAL_NOT_ZERO);
        }

        //检查是否存在挂销账记录
        //TODO 检查挂销账记录

        //如果余额批量更新，检查当日是否有发生额
        if (JudgeUtils.equals(acmItmBalDO.getUpdBalFlg(), ACMConstants.BAL_BAT_UPD)) {
            AcmItmDetailDO acmItmDetailDO = new AcmItmDetailDO();
            acmItmDetailDO.setItmNo(itmNo);
            acmItmDetailDO.setAcDt(DateTimeUtils.getCurrentLocalDate());
            List<AcmItmDetailDO> acmItmDetailDOS = iAcmItmDetailDao.find(acmItmDetailDO);
            if (JudgeUtils.isNotNull(acmItmDetailDOS)) {
                LemonException.throwBusinessException(ACMMessageCode.ITM_BAL_NOT_ZERO);
            }
        }

        //修改科目状态
        AcmItmInfDO acmItmInfDO = new AcmItmInfDO();
        acmItmInfDO.setItmNo(itmNo);
        acmItmInfDO.setItmSts(ACMConstants.ITM_CANCEL_STS);
        iAcmItmInfDao.update(acmItmInfDO);
    }

    /**
     * 建立用户账户余额
     *
     * @param acNo
     * @param ccy
     * @param capTyp
     * @return
     */
    @Override
    public void createUserAccountBalance(String acNo, String ccy, String capTyp) {
        AcmAcInfDO acmAcInf = new AcmAcInfDO();
        acmAcInf.setAcNo(acNo);
        acmAcInf.setCcy(ccy);
        List<AcmAcInfDO> acmAcInfDOList = iAcmAcInfDao.find(acmAcInf);
        if (JudgeUtils.isEmpty(acmAcInfDOList)) {
            LemonException.throwBusinessException(ACMMessageCode.AC_NOT_EXIST);
        }
        for (AcmAcInfDO acmAcInfDO : acmAcInfDOList) {
            logger.info(acmAcInfDO.toString());
            AcmAcBalDO acmAcBalDO = new AcmAcBalDO();
            acmAcBalDO.setAcNo(acmAcInfDO.getAcNo());
            acmAcBalDO.setCcy(acmAcInfDO.getCcy());
            acmAcBalDO.setUserId(acmAcInfDO.getUserId());
            acmAcBalDO.setAcCurBal(BigDecimal.valueOf(0, 2));
            acmAcBalDO.setAcUavaBal(BigDecimal.valueOf(0, 2));
            acmAcBalDO.setAcLastBal(BigDecimal.valueOf(0, 2));
            acmAcBalDO.setAcLastUavaBal(BigDecimal.valueOf(0, 2));
            acmAcBalDO.setAcUpdDt(DateTimeUtils.getCurrentLocalDate());
            acmAcBalDO.setAcUpdTm(DateTimeUtils.getCurrentLocalTime());
            if (capTyp == null || capTyp.isEmpty()) {
                for (CapTypEnum capTypEnum : CapTypEnum.values()) {
                    acmAcBalDO.setCapTyp(capTypEnum.getCapTyp());
                    String balTag = BalanceTagUtil.createBalanceTag(acmAcInfDO.getAcNo(),
                            capTypEnum.getCapTyp(), BigDecimal.valueOf(0, 2));
                    acmAcBalDO.setAcBalTag(balTag);
                    iAcmAcBalDao.insert(acmAcBalDO);
                }
            } else {
                String balTag = BalanceTagUtil.createBalanceTag(acmAcInfDO.getAcNo(), capTyp, BigDecimal.valueOf(0, 2));
                acmAcBalDO.setAcBalTag(balTag);
                acmAcBalDO.setCapTyp(capTyp);
                iAcmAcBalDao.insert(acmAcBalDO);
            }
        }
    }

    /**
     * 建立科目账户余额
     *
     * @param itmNo
     * @return
     */
    @Override
    public void createItemAccountBalance(String itmNo) {
        AcmItmInfDO acmItmInfDO = iAcmItmInfDao.get(itmNo);
        AcmItmBalDO acmItmBalDO = new AcmItmBalDO();
        if (JudgeUtils.isNull(acmItmInfDO)) {
            LemonException.throwLemonException(ACMMessageCode.ITM_NOT_EXIST);
        }
        BeanUtils.copyProperties(acmItmBalDO, acmItmInfDO);
        acmItmBalDO.setCcy(ACMConstants.SYS_DEFAULT_CCY);
        acmItmBalDO.setUppDt(DateTimeUtils.getCurrentLocalDate());
        acmItmBalDO.setUpdBalFlg(acmItmInfDO.getUpdBalFlg());
        acmItmBalDO.setLastDrBal(BigDecimal.valueOf(0, 2));
        acmItmBalDO.setLastCrBal(BigDecimal.valueOf(0, 2));
        acmItmBalDO.setTdCrAmt(BigDecimal.valueOf(0, 2));
        acmItmBalDO.setTdDrAmt(BigDecimal.valueOf(0, 2));
        acmItmBalDO.setTdCrNum(0);
        acmItmBalDO.setTdDrNum(0);
        acmItmBalDO.setMonCrAmt(BigDecimal.valueOf(0, 2));
        acmItmBalDO.setMonDrAmt(BigDecimal.valueOf(0, 2));
        acmItmBalDO.setMonDrAmt(BigDecimal.valueOf(0, 2));
        acmItmBalDO.setMonCrAmt(BigDecimal.valueOf(0, 2));
        acmItmBalDO.setTdDrBal(BigDecimal.valueOf(0, 2));
        acmItmBalDO.setTdCrBal(BigDecimal.valueOf(0, 2));
        acmItmBalDO.setTdDrBal(BigDecimal.valueOf(0, 2));
        acmItmBalDO.setTdCrBal(BigDecimal.valueOf(0, 2));
        acmItmBalDO.setGlSts(ACMConstants.GL_STS_INIT);
        iAcmItmBalDao.insert(acmItmBalDO);
    }

    /**
     * 根据账号或用户号或资金属性查询账户余额信息
     *
     * @param acNo
     * @param userId
     * @param capTyp
     * @return
     */
    @Override
    public List<AcmAcBalDO> queryAcBal(String acNo, String userId, String capTyp) {
        AcmAcInfDO acmAcInfDO = new AcmAcInfDO();
        acmAcInfDO.setAcNo(acNo);
        acmAcInfDO.setUserId(userId);
        List<AcmAcInfDO> acmAcInfDOS = iAcmAcInfDao.find(acmAcInfDO);
        if (JudgeUtils.isNotEmpty(acmAcInfDOS)) {
            List<AcmAcBalDO> acmAcBalDOS = new ArrayList<AcmAcBalDO>();
            List<AcmAcBalDO> result = new ArrayList<AcmAcBalDO>();
            for (AcmAcInfDO acmInf : acmAcInfDOS) {
                AcmAcBalDO acmAcBalDO = new AcmAcBalDO();
                acmAcBalDO.setAcNo(acmInf.getAcNo());
                acmAcBalDO.setCapTyp(capTyp);
                acmAcBalDOS = iAcmAcBalDao.find(acmAcBalDO);
                if (JudgeUtils.isNotEmpty(acmAcBalDOS)) {
                    acmAcBalDOS.stream().forEach(acBalDO -> acBalDO.setAcCurBal(acBalDO.getAcCurBal().subtract(acBalDO.getAcUavaBal())));
                    if (JudgeUtils.equals(acmInf.getCcyType(), CcyTypeEnum.DM.getCcyType())) {
                        DmAccountDetailRspDTO userDmAcc = this.queryDmAccountDetail(acmInf.getAcNo());
                        if(JudgeUtils.isNotNull(userDmAcc)) {
                            acmAcBalDOS.stream().forEach(acBalDO -> acBalDO.setNetwork(userDmAcc.getNetwork()));
                        }
                    }
                    if (JudgeUtils.isBlank(capTyp)) {
                        if (acmAcBalDOS.size() != CapTypEnum.values().length) {
                            for (CapTypEnum capTypEnum1 : CapTypEnum.values()) {
                                int i = 0;
                                for (AcmAcBalDO acBalDO : acmAcBalDOS) {
                                    if (JudgeUtils.equals(capTypEnum1.getCapTyp(), acBalDO.getCapTyp())) {
                                        i++;
                                        break;
                                    }
                                }
                                if (i == 0) {
                                    acmAcBalDOS.add(initBalRecord(acmInf, capTypEnum1.getCapTyp()));
                                }
                            }
                        }
                    }
                } else {
                    if (JudgeUtils.isBlank(capTyp)) {
                        for (CapTypEnum capTypEnum : CapTypEnum.values()) {
                            acmAcBalDOS.add(initBalRecord(acmInf, capTypEnum.getCapTyp()));
                        }
                    } else {
                        acmAcBalDOS.add(initBalRecord(acmInf, capTyp));
                    }
                }
                result.addAll(acmAcBalDOS);
            }
            return result;
        }
        return null;
    }


    public List<AcmAcBalDO> queryAcBal(String acNo, String userId, String capTyp, String ccy) {
        AcmAcInfDO acmAcInfDO = new AcmAcInfDO();
        acmAcInfDO.setAcNo(acNo);
        acmAcInfDO.setUserId(userId);
        acmAcInfDO.setCcy(ccy);
        List<AcmAcInfDO> acmAcInfDOS = iAcmAcInfDao.find(acmAcInfDO);
        if (JudgeUtils.isNotEmpty(acmAcInfDOS)) {
            List<AcmAcBalDO> acmAcBalDOS = new ArrayList<AcmAcBalDO>();
            for (AcmAcInfDO acmInf : acmAcInfDOS) {
                AcmAcBalDO acmAcBalDO = new AcmAcBalDO();
                acmAcBalDO.setAcNo(acmInf.getAcNo());
                acmAcBalDO.setCapTyp(capTyp);
                acmAcBalDOS = iAcmAcBalDao.find(acmAcBalDO);
                if (JudgeUtils.isNotEmpty(acmAcBalDOS)) {
                    acmAcBalDOS.stream().forEach(acBalDO -> acBalDO.setAcCurBal(acBalDO.getAcCurBal().subtract(acBalDO.getAcUavaBal())));
                    if (JudgeUtils.isBlank(capTyp)) {
                        if (acmAcBalDOS.size() != CapTypEnum.values().length) {
                            for (CapTypEnum capTypEnum1 : CapTypEnum.values()) {
                                int i = 0;
                                for (AcmAcBalDO acBalDO : acmAcBalDOS) {
                                    if (JudgeUtils.equals(capTypEnum1.getCapTyp(), acBalDO.getCapTyp())) {
                                        i++;
                                        break;
                                    }
                                }
                                if (i == 0) {
                                    acmAcBalDOS.add(initBalRecord(acmInf, capTypEnum1.getCapTyp()));
                                }
                            }
                        }
                    }
                } else {
                    if (JudgeUtils.isBlank(capTyp)) {
                        for (CapTypEnum capTypEnum : CapTypEnum.values()) {
                            acmAcBalDOS.add(initBalRecord(acmInf, capTypEnum.getCapTyp()));
                        }
                    } else {
                        acmAcBalDOS.add(initBalRecord(acmInf, capTyp));
                    }
                }
            }
            return acmAcBalDOS;
        }

        return null;

    }


    /**
     * 根据账号查询、更新冻结账户结算余额
     *
     * @param acNo
     * @param freezeAmt
     * @return
     */
    @Override
    public void handleSettleAcBal(String acNo, BigDecimal freezeAmt) {

        //List<AcmAcDetailDO> AcmAcDetailDOs =  iAcmAcDetailDao.getTranList(acNo, acDt);
        //BigDecimal settleBal = BigDecimal.ZERO;
        /*if(AcmAcDetailDOs != null && AcmAcDetailDOs.size() >0){
            AcmAcDetailDO acmAcDetailDO = AcmAcDetailDOs.get(0);
            String dcflag = acmAcDetailDO.getDcFlg();
            BigDecimal currbal = acmAcDetailDO.getCurBal();
            BigDecimal txAmt = acmAcDetailDO.getTxAmt();
            if("D".equals(dcflag)){
                settleBal = currbal.subtract(txAmt);
            }else{
                settleBal = currbal.add(txAmt);
            }
            if(acBal.compareTo(settleBal) < 0){
                settleBal = acBal;
                logger.info("settleAmt exception : " + settleBal);
            }
            //更新账户冻结金额

            int res = iAcmAcBalDao.updateFreezeAmt(settleBal, acNo, capType);
            if(res != 1){
                logger.info("settleAmtUpdate exception : " + settleBal + "  ,  " + acNo);
            }
        }*/
        int res = iAcmAcBalDao.updateFreezeAmt(acNo, freezeAmt);
        if (res != 1) {
            logger.info("settleAmtUpdate exception : " + acNo + "  ,  " + freezeAmt.toString());
        }

    }

    public void unfreezeSettleAcBal(String userId, String capTyp) {
        int res = iAcmAcBalDao.delFreezeAmt(userId, capTyp);
        if (res != 1) {
            logger.info("settleAmtDel exception : " + userId);
        }
    }


    /**
     * 根据用户ID查询用户账号
     *
     * @param userId
     * @return
     */
    @Override
    public String queryAcNo(String userId) {
        AcmAcInfDO acmAcInfDO = new AcmAcInfDO();
        acmAcInfDO.setUserId(userId);
        List<AcmAcInfDO> acmAcInfDOS = iAcmAcInfDao.find(acmAcInfDO);
        return acmAcInfDOS.get(0).getAcNo();
    }

    /**
     * 根据用户ID和币种查询用户账号
     *
     * @param userId
     * @return
     */
    @Override
    public String queryAcNo(String userId, String ccy) {
        AcmAcInfDO acmAcInfDO = new AcmAcInfDO();
        acmAcInfDO.setUserId(userId);
        acmAcInfDO.setCcy(ccy);
        List<AcmAcInfDO> acmAcInfDOS = iAcmAcInfDao.find(acmAcInfDO);
        return acmAcInfDOS.get(0).getAcNo();
    }

    /**
     * 获取用户币种账户列表
     *
     * @param userCcyAccountListDTO
     * @return
     */
    @Override
    public List<UserCcyAccountRspDTO> getUserCcyAcountList(UserCcyAccountListDTO userCcyAccountListDTO) {
        // 查询账户基本信息
        AcmAcInfDO acmAcInfDO = new AcmAcInfDO();
        acmAcInfDO.setUserId(userCcyAccountListDTO.getUserId());
        acmAcInfDO.setAcSts(ACMConstants.AC_OPEN_STS);
        List<AcmAcInfDO> acmAcInfDOList = iAcmAcInfDao.find(acmAcInfDO);
        if (JudgeUtils.isEmpty(acmAcInfDOList)) {
            return Collections.emptyList();
        }

        for(AcmAcInfDO info : acmAcInfDOList) {
            if(JudgeUtils.equals(info.getCcyType(), CcyTypeEnum.DM.getCcyType())) {
                this.updateBalByCregis(info.getAcNo());
            }
        }

        // 按币种分组
        Map<String, List<AcmAcInfDO>> ccyGroupMap = acmAcInfDOList.stream()
                .collect(Collectors.groupingBy(AcmAcInfDO::getCcy));

        // 准备响应列表
        List<UserCcyAccountRspDTO> result = new ArrayList<>();
        for (Map.Entry<String, List<AcmAcInfDO>> entry : ccyGroupMap.entrySet()) {
            String ccy = entry.getKey();
            List<AcmAcInfDO> accounts = entry.getValue();

            // 提取该币种下所有账户的开户行或网络
            List<String> banksOrNetworks = accounts.stream()
                    .map(AcmAcInfDO::getBank)
                    .filter(Objects::nonNull) // 过滤空值
                    .distinct() // 去重
                    .collect(Collectors.toList());

            // 查询该币种的所有余额记录
            AcmAcBalDO acmAcBalDO = new AcmAcBalDO();
            acmAcBalDO.setCcy(ccy);
            acmAcBalDO.setUserId(userCcyAccountListDTO.getUserId());
            List<AcmAcBalDO> acmAcBalDOList = iAcmAcBalDao.find(acmAcBalDO);

            // todo 计算总额？、可用余额、冻结金额和待入账金额
            BigDecimal totalBalance = BigDecimal.ZERO;
            BigDecimal availableBalance = BigDecimal.ZERO;
            BigDecimal frozenBalance = BigDecimal.ZERO;
            BigDecimal pendingBalance = BigDecimal.ZERO;

            for (AcmAcBalDO bal : acmAcBalDOList) {
//                totalBalance = ;
                availableBalance = availableBalance.add(bal.getAcCurBal() != null ? bal.getAcCurBal().subtract(bal.getAcUavaBal()) : BigDecimal.ZERO);
                frozenBalance = frozenBalance.add(bal.getAcCurFreezeBal() != null ? bal.getAcCurFreezeBal() : BigDecimal.ZERO);
//                pendingBalance = ;
            }

            // 创建响应 DTO
            UserCcyAccountRspDTO rspDTO = new UserCcyAccountRspDTO();
            rspDTO.setCcy(ccy);
            //获取币种logo
            DmCcyListDO dmCcyListDO = iDmCcyListDao.get(ccy);
            rspDTO.setCcyLogo(dmCcyListDO != null && dmCcyListDO.getCoinIconUrl() != null ? dmCcyListDO.getCoinIconUrl() : "");
            rspDTO.setAccountCount(accounts.size());
            rspDTO.setBanksOrNetworks(banksOrNetworks);
            rspDTO.setTotalBalance(availableBalance);
            rspDTO.setAvailableBalance(availableBalance);
            rspDTO.setFrozenBalance(frozenBalance);
            rspDTO.setPendingBalance(pendingBalance);

            result.add(rspDTO);
        }

        // 按币种和最早开户日期排序
        result.sort(Comparator.comparing(UserCcyAccountRspDTO::getCcy)
                .thenComparing(dto -> ccyGroupMap.get(dto.getCcy()).stream()
                        .map(AcmAcInfDO::getAcCreDt)
                        .filter(Objects::nonNull)
                        .min(LocalDate::compareTo)
                        .orElse(LocalDate.now())));

        return result;
    }

    /**
     * 获取用户数币账户列表
     *
     * @param userDmAccountListDTO
     * @return
     */
    @Override
    public List<UserDmAccountRspDTO> getUserDmAcountList(UserDmAccountListDTO userDmAccountListDTO) {
        // 查询数币账户基本信息
        AcmAcInfDO acmAcInfDO = new AcmAcInfDO();
        acmAcInfDO.setUserId(userDmAccountListDTO.getUserId());
        acmAcInfDO.setAcSts(ACMConstants.AC_OPEN_STS);
        acmAcInfDO.setCcyType(CcyTypeEnum.DM.getCcyType());
        List<AcmAcInfDO> acmAcInfDOList = iAcmAcInfDao.find(acmAcInfDO);
        if (JudgeUtils.isEmpty(acmAcInfDOList)) {
            return Collections.emptyList();
        }

        for(AcmAcInfDO info : acmAcInfDOList) {
            if(JudgeUtils.equals(info.getCcyType(), CcyTypeEnum.DM.getCcyType())) {
                this.updateBalByCregis(info.getAcNo());
            }
        }

        // 准备响应列表
        List<UserDmAccountRspDTO> result = new ArrayList<>();
        for (AcmAcInfDO acInf : acmAcInfDOList) {
            // 查询该账户的余额记录
            AcmAcBalDO acmAcBalDO = new AcmAcBalDO();
            acmAcBalDO.setCcy(acInf.getCcy());
            acmAcBalDO.setUserId(userDmAccountListDTO.getUserId());
            acmAcBalDO.setAcNo(acInf.getAcNo());
            acmAcBalDO.setCapTyp(CapTypEnum.CAP_TYP_CASH.getCapTyp());
            AcmAcBalDO acmAcBal = iAcmAcBalDao.find(acmAcBalDO).get(0);

            // 计算账户余额和账户总资产
            BigDecimal accountBalance = BigDecimal.ZERO;
            BigDecimal totalBalance = BigDecimal.ZERO;

            // 账户余额
            accountBalance = accountBalance.add(acmAcBal.getAcCurBal() != null ? acmAcBal.getAcCurBal().subtract(acmAcBal.getAcUavaBal()) : BigDecimal.ZERO);
            // 账户总资产
//                totalBalance = ;

            //查询数币详情获取网络
            DmAccountDetailRspDTO dmAccountDetailRspDTO = this.queryDmAccountDetail(acInf.getAcNo());
            if (JudgeUtils.isNull(dmAccountDetailRspDTO)){
                LemonException.throwBusinessException(ACMMessageCode.AC_NOT_EXIST);
            }
            // 创建响应 DTO
            UserDmAccountRspDTO rspDTO = new UserDmAccountRspDTO();
            rspDTO.setAcNo(acInf.getAcNo());
            rspDTO.setCcy(acInf.getCcy());
            rspDTO.setNetwork(dmAccountDetailRspDTO.getNetwork());
            // 获取币种logo
            DmCcyListDO dmCcyListDO = iDmCcyListDao.get(acInf.getCcy());
            rspDTO.setCcyLogo(dmCcyListDO != null && dmCcyListDO.getCoinIconUrl() != null ? dmCcyListDO.getCoinIconUrl() : "");
            rspDTO.setAccountBalance(accountBalance);
            rspDTO.setTotalBalance(accountBalance);
            rspDTO.setAcCreDt(acInf.getAcCreDt());
            rspDTO.setAcCreTm(acInf.getAcCreTm());

            result.add(rspDTO);
        }

        // 先按最早开户日期排序，再按开户时间排序
        result.sort(Comparator.comparing(UserDmAccountRspDTO::getAcCreDt)
                .thenComparing(UserDmAccountRspDTO::getAcCreTm));

        return result;
    }


    @Override
    public List<UserFmAccountRspDTO> getUserFmAccountList(UserDmAccountListDTO userDmAccountListDTO) {
        // 查询法币账户基本信息
        AcmAcInfDO acmAcInfDO = new AcmAcInfDO();
        acmAcInfDO.setUserId(userDmAccountListDTO.getUserId());
        acmAcInfDO.setAcSts(ACMConstants.AC_OPEN_STS);
        acmAcInfDO.setCcyType(CcyTypeEnum.FM.getCcyType());
        List<AcmAcInfDO> acmAcInfDOList = iAcmAcInfDao.find(acmAcInfDO);
        if (JudgeUtils.isEmpty(acmAcInfDOList)) {
            return Collections.emptyList();
        }

        List<UserFmAccountRspDTO> result = new ArrayList<>();

        for(AcmAcInfDO infDO : acmAcInfDOList) {
            AcmAcBalDO balDO = iAcmAcBalDao.get(infDO.getAcNo(), CapTypEnum.CAP_TYP_CASH.getCapTyp());
            if(JudgeUtils.isNull(balDO)) {
                return null;
            }
            // 创建响应 DTO
            UserFmAccountRspDTO rspDTO = new UserFmAccountRspDTO();
            rspDTO.setAccountNo(balDO.getAcNo());
            rspDTO.setCcy(balDO.getCcy());
            rspDTO.setAccountBalance(balDO.getAcCurBal().subtract(balDO.getAcUavaBal()));
            rspDTO.setAccountName(iFmAccountInfoDao.queryUserAccountName(userDmAccountListDTO.getUserId()));
            result.add(rspDTO);
        }
        return result;
    }

    @Override
    public UserFmAccountDetailRspDTO getFmAccountDetail(String acNo) {
        //查询账户信息
        UserFmAccountDetailRspDTO result = new UserFmAccountDetailRspDTO();
        AcmAcInfDO acmAcInfDO = iAcmAcInfDao.queryAcc(null, null, null, acNo);
        if (JudgeUtils.isNull(acmAcInfDO)) {
            return result;
        }
        BeanUtils.copyProperties(result, acmAcInfDO);

        //查询账户余额
        AcmAcBalDO acmAcBalDO = iAcmAcBalDao.get(acNo, CapTypEnum.CAP_TYP_CASH.getCapTyp());
        if(JudgeUtils.isNull(acmAcBalDO)) {
            return result;
        }
        result.setAccountBalance(acmAcBalDO.getAcCurBal().subtract(acmAcBalDO.getAcUavaBal()));

        //查询账户名称
        result.setAccountName(iFmAccountInfoDao.queryUserAccountName(acmAcInfDO.getUserId()));

        //TODO logo 账户总资产 账户类型 绑定时间字段
        return result;
    }

    /**
     * 开通数币账户
     *
     * @param req
     */
    @Transactional
    @Override
    public void openDmAccount(GenericDTO<OpenDmAccountDTO> req) {
        String userId = JudgeUtils.isBlank(req.getUserId()) ? LemonUtils.getUserId() : req.getUserId();
        logger.info("开通数币账户: userId={}", userId);
        // 1. 判断是否完成账户认证
        GenericRspDTO<UserBasicInfDTO> userBasicInfDTO = userBasicInfClient.queryUser(userId);
        UserBasicInfDTO userBasicInfDTO1 = userBasicInfDTO.getBody();
        if (JudgeUtils.isNull(userBasicInfDTO1)) {
            logger.error("用户不存在: userId={}", userId);
            LemonException.throwLemonException(URMMessageCode.USR_NOT_EXIST);
        }
        if (JudgeUtils.equals(userBasicInfDTO1.getUsrSts(), URMConstants.USR_CANCEL)) {
            logger.error("用户已注销: userId={}", userId);
            LemonException.throwLemonException(URMMessageCode.USER_ALREADY_CANCEL);
        }
        if (!JudgeUtils.equals(userBasicInfDTO1.getKybCert(), "Y")) {
            logger.error("用户未完成身份认证: userId={}", userId);
            LemonException.throwLemonException(URMMessageCode.USER_NOT_AUTH);
        }

        OpenDmAccountDTO openDmAccountDTO = req.getBody();

        // 2. 交易密码校验
        GenericDTO<CheckPayPwdDTO> payPwdDTOGenericDTO = new GenericDTO<>();
        CheckPayPwdDTO payPwdReq = new CheckPayPwdDTO();
//        payPwdReq.setUserId(userId);
        payPwdReq.setPayPwd(openDmAccountDTO.getPayPwd());
        payPwdDTOGenericDTO.setBody(payPwdReq);
        userAuthenticationClient.checkPayPwd(payPwdDTOGenericDTO);

        // 账户校验——1个币种1个网络仅限1个账户
        List<DmAccountAddressDO> dmList = iDmAccountAddressDao.getByUserIdAndNet(userId, openDmAccountDTO.getNetwork());
        if(JudgeUtils.isNotEmpty(dmList)) {
            for(DmAccountAddressDO dm : dmList) {
                AcmAcInfDO search = new AcmAcInfDO();
                search.setAcNo(dm.getAcmAcNo());
                List<AcmAcInfDO> acInf = iAcmAcInfDao.find(search);
                if(JudgeUtils.isNotEmpty(acInf) && JudgeUtils.equals(acInf.get(0).getCcy(), openDmAccountDTO.getCcy())) {
                    LemonException.throwLemonException("ACM30027");
                }
            }
        }

        // 3. 生成数币账号编码
        String acNo = IdGenUtils.generateId("AC_NO", ACMConstants.AC_SEQ_LEN);
        acNo += generateCheckFlag(acNo);
        logger.info("生成数币账号: acNo={}, ccy={}", acNo, openDmAccountDTO.getCcy());

        // 4. 查询ID最小的未启用地址记录，加锁
        DmAccountAddressDO dmAccountAddress = iDmAccountAddressDao.getMinDisabledAddress(openDmAccountDTO.getNetwork());
        if (JudgeUtils.isNull(dmAccountAddress)) {
            logger.error("无可用地址: network={}", openDmAccountDTO.getNetwork());
            LemonException.throwBusinessException(ACMMessageCode.ADDRESS_NOT_AVAILABLE);
        }

        // 5. 更新地址记录状态为已启用，设置acm_ac_no和user_id
        dmAccountAddress.setStatus(AddressStatusEnum.ENABLED.getCode());
        dmAccountAddress.setAcmAcNo(acNo);
        dmAccountAddress.setUserId(userId);
        dmAccountAddress.setModifyTime(LocalDateTime.now());
        // 根据地址设置二维码,二维码内容
        String qrContent = String.format("address:%s", dmAccountAddress.getAddress());
        dmAccountAddress.setQrCodeBase64(generateQRCodeBase64(qrContent));
        iDmAccountAddressDao.update(dmAccountAddress);
        logger.info("更新地址记录: address={}, status=ENABLED, acmAcNo={}, userId={}",
                dmAccountAddress.getAddress(), acNo, userId);

        // 6. 插入账户基本信息(acm_ac_inf)
        insertAccountInfo(acNo, openDmAccountDTO, userId);

        // 7. 插入账户余额信息(acm_ac_bal)，初始化现金和待结算资金
        insertAccountBalances(acNo, openDmAccountDTO, userId);

//        // 3. 为 DS 和 DC 分别生成数币账号编码
//        String[] useTypes = {"DS", "DC"}; // 收款和充值
//        Map<String, String> acNoMap = new HashMap<>();
//        for (String useType : useTypes) {
//            String acNo = IdGenUtils.generateId("AC_NO", ACMConstants.AC_SEQ_LEN);
//            acNo += generateCheckFlag(acNo);
//            acNoMap.put(useType, acNo);
//            logger.info("生成数币账号: useType={}, acNo={}, ccy={}", useType, acNo, openDmAccountDTO.getCcy());
//        }
//
//        // 4. 查询两个最小 ID 的未启用地址记录，加锁
//        List<DmAccountAddressDO> addressList =iDmAccountAddressDao.getTwoMinDisabledAddress(openDmAccountDTO.getNetwork());
//        if (JudgeUtils.isNull(addressList) || addressList.size() < 2) {
//            logger.error("无可用地址: network={}", openDmAccountDTO.getNetwork());
//            LemonException.throwBusinessException(ACMMessageCode.ADDRESS_NOT_AVAILABLE);
//        }
//
//        // 5. 更新两条地址记录状态为已启用，设置 acm_ac_no 和 user_id
//        for (int i = 0; i < useTypes.length; i++) {
//            DmAccountAddressDO dmAccountAddress = addressList.get(i);
//            String useType = useTypes[i];
//            String acNo = acNoMap.get(useType);
//
//            dmAccountAddress.setStatus(AddressStatusEnum.ENABLED.getCode());
//            dmAccountAddress.setAcmAcNo(acNo);
//            dmAccountAddress.setUserId(userId);
//            dmAccountAddress.setUseType(useType); // 设置使用类型
//            dmAccountAddress.setModifyTime(LocalDateTime.now());
//
//            // 根据地址设置二维码内容
//            String qrContent = String.format("address:%s", dmAccountAddress.getAddress());
//            dmAccountAddress.setQrCodeBase64(generateQRCodeBase64(qrContent));
//
//            iDmAccountAddressDao.update(dmAccountAddress);
//            logger.info("更新地址记录: useType={}, address={}, status=ENABLED, acmAcNo={}, userId={}",
//                    useType, dmAccountAddress.getAddress(), acNo, userId);
//        }
//
//
//        for (String useType : useTypes) {
//            // 6. 为每种使用类型插入账户基本信息 (acm_ac_inf)
//            insertAccountInfo(acNoMap.get(useType), openDmAccountDTO, userId);
//
//            // 7. 插入账户余额信息(acm_ac_bal)，初始化现金和待结算资金
//            insertAccountBalances(acNoMap.get(useType), openDmAccountDTO, userId);
//        }
    }

    /**
     * 插入账户余额信息(acm_ac_bal)
     *
     * @param acNo             账号编码
     * @param openDmAccountDTO 开户请求参数
     * @param userId           用户ID
     */
    private void insertAccountBalances(String acNo, OpenDmAccountDTO openDmAccountDTO, String userId) {
        logger.info("插入账户余额信息: acNo={}", acNo);
        // 插入账户余额信息（acm_ac_bal），初始化现金和待结算资金
        for (CapTypEnum capTypEnum : CapTypEnum.values()) {
            AcmAcBalDO balDO = new AcmAcBalDO();
            balDO.setAcNo(acNo);
            balDO.setCapTyp(capTypEnum.getCapTyp());
            balDO.setCcy(openDmAccountDTO.getCcy());
            balDO.setUserId(userId);
            balDO.setAcCurBal(BigDecimal.valueOf(0, 2));
            balDO.setAcUavaBal(BigDecimal.valueOf(0, 2));
            balDO.setAcLastBal(BigDecimal.valueOf(0, 2));
            balDO.setAcLastUavaBal(BigDecimal.valueOf(0, 2));
            balDO.setAcCurFreezeBal(BigDecimal.valueOf(0, 2));
            balDO.setAcUpdDt(DateTimeUtils.getCurrentLocalDate());
            balDO.setAcUpdTm(DateTimeUtils.getCurrentLocalTime());
            balDO.setCreateTime(DateTimeUtils.getCurrentLocalDateTime());
            balDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());

            // 生成余额标签
            String balTag = BalanceTagUtil.createBalanceTag(acNo, capTypEnum.getCapTyp(), BigDecimal.ZERO);
            balDO.setAcBalTag(balTag);

            // 插入余额记录
            iAcmAcBalDao.insert(balDO);
            logger.info("插入现金余额记录: acNo={}, capTyp={}, ccy={}", acNo, capTypEnum.getCapTyp(), openDmAccountDTO.getCcy());
        }
    }

    /**
     * 插入账户基本信息(acm_ac_inf)
     *
     * @param acNo             账号编码
     * @param openDmAccountDTO 开户请求参数
     * @param userId           用户ID
     */
    private void insertAccountInfo(String acNo, OpenDmAccountDTO openDmAccountDTO, String userId) {
        logger.info("插入账户基本信息: acNo={}", acNo);
        AcmAcInfDO acmAcInfDO = new AcmAcInfDO();
        acmAcInfDO.setAcNo(acNo);
        acmAcInfDO.setCcy(openDmAccountDTO.getCcy());
        acmAcInfDO.setCcyType(CcyTypeEnum.DM.getCcyType()); // 数币账户
        acmAcInfDO.setBank(openDmAccountDTO.getNetwork());
        acmAcInfDO.setUserId(userId);
        acmAcInfDO.setAcSts(ACMConstants.AC_OPEN_STS); // 开户状态
        acmAcInfDO.setAcCreDt(LocalDate.now());
        acmAcInfDO.setAcCreTm(DateTimeUtils.getCurrentLocalTime());
        acmAcInfDO.setCreateTime(LocalDateTime.now());
        acmAcInfDO.setModifyTime(LocalDateTime.now());
        iAcmAcInfDao.insert(acmAcInfDO);
        logger.info("插入账户基本信息: acNo={}, ccy={}, userId={}", acNo, openDmAccountDTO.getCcy(), userId);
    }

    @Override
    public String openFmAccount(OpenFmAccountDTO openFmAccountDTO) {
        logger.info("法币账户开户流程");
        // kyb认证校验
        GenericRspDTO<UserBasicInfDTO> userBasicInfDTO = userBasicInfClient.queryUser(openFmAccountDTO.getUserId());
        UserBasicInfDTO userBasicInfDTO1 = userBasicInfDTO.getBody();
        if (JudgeUtils.isNull(userBasicInfDTO1)) {
            logger.error("用户不存在: userId={}", openFmAccountDTO.getUserId());
            LemonException.throwLemonException(URMMessageCode.USR_NOT_EXIST);
        }
        if (JudgeUtils.equals(userBasicInfDTO1.getUsrSts(), URMConstants.USR_CANCEL)) {
            logger.error("用户已注销: userId={}", openFmAccountDTO.getUserId());
            LemonException.throwLemonException(URMMessageCode.USER_ALREADY_CANCEL);
        }
        if (JudgeUtils.equals(userBasicInfDTO1.getKybCert(), URMConstants.NO_KYB_CERT)) {
            logger.error("用户未完成kyb认证: userId={}", openFmAccountDTO.getUserId());
            LemonException.throwLemonException(URMMessageCode.USER_NOT_AUTH);
        }

        //校验相同币种法币账户最多只能有一个
        AcmAcInfDO exist = iAcmAcInfDao.queryAccByCcy(openFmAccountDTO.getUserId(), openFmAccountDTO.getCcy());
        if(JudgeUtils.isNotNull(exist)) {
            LemonException.throwLemonException("ACM30029");
        }

        AcmAcInfDO req = iAcmAcInfDao.queryAcc(openFmAccountDTO.getUserId(), ACMConstants.AC_PENDING_STS, null, null);
        AcmAcInfDO fmAcc = new AcmAcInfDO();
        if (JudgeUtils.isNotNull(req)) {
            //最多只能有一个处理中的申请
            logger.error("已有一个处理中的法币开户申请");
            LemonException.throwBusinessException(ACMMessageCode.EXIST_OPENING_REQUEST);
        }
        req = iAcmAcInfDao.queryAcc(openFmAccountDTO.getUserId(), ACMConstants.AC_OPEN_STS, openFmAccountDTO.getBank(), null);
        if (JudgeUtils.isNotNull(req)) {
            //已持有该开户行同名账户，无需审核，通过校验直接成功
            //校验交易密码，开户名是否一致，acNo不能重复
//            GenericDTO<CheckPayPwdDTO> payPwdDTOGenericDTO = new GenericDTO<>();
//            CheckPayPwdDTO payPwdReq = new CheckPayPwdDTO();
//            payPwdReq.setPayPwd(openFmAccountDTO.getPayPwd());
//            payPwdDTOGenericDTO.setBody(payPwdReq);
//            if(JudgeUtils.isNotSuccess(userAuthenticationClient.checkPayPwd(payPwdDTOGenericDTO).getMsgCd())) {
//                logger.error("交易密码校验失败");
//                LemonException.throwBusinessException(URMMessageCode.PAY_PWD_CHECK_FAIL);
//            }
            String acName = iFmAccountInfoDao.queryUserAccountName(openFmAccountDTO.getUserId());
            if (!JudgeUtils.equals(acName, openFmAccountDTO.getAccountName())) {
                logger.error("开户名与KYB企业主体名称校验失败");
                LemonException.throwBusinessException(ACMMessageCode.ACNAME_KYB_NOT_SAME);
            }

            //通过校验直接成功
            String acNo = genAcNo();
            fmAcc.setAcNo(acNo);
            fmAcc.setAcSts(ACMConstants.AC_OPEN_STS);
            fmAcc.setCcy(openFmAccountDTO.getCcy());
            fmAcc.setUserId(openFmAccountDTO.getUserId());
            fmAcc.setAcCreDt(DateTimeUtils.getCurrentLocalDate());
            fmAcc.setAcCreTm(DateTimeUtils.getCurrentLocalTime());
            fmAcc.setCcyType(CcyTypeEnum.FM.getCcyType());
            fmAcc.setBank(openFmAccountDTO.getBank());
            iAcmAcInfDao.insert(fmAcc);
            //创建用户余额记录
            createUserAccountBalance(acNo, ACMConstants.SYS_DEFAULT_CCY, null);
            return acNo;
        }

        //组装申请数据
        fmAcc.setAcNo(genAcNo());
        fmAcc.setAcSts(ACMConstants.AC_PENDING_STS);
        fmAcc.setCcy(openFmAccountDTO.getCcy());
        fmAcc.setUserId(openFmAccountDTO.getUserId());
        fmAcc.setAcCreDt(DateTimeUtils.getCurrentLocalDate());
        fmAcc.setAcCreTm(DateTimeUtils.getCurrentLocalTime());
        fmAcc.setCcyType(CcyTypeEnum.FM.getCcyType());
        fmAcc.setBank(openFmAccountDTO.getBank());
        iAcmAcInfDao.insert(fmAcc);

        return fmAcc.getAcNo();
    }

    /**
     * 获取用户所有数币账户信息
     *
     * @param req
     * @return
     */
    @Override
    public List<DmAccountInfoRspDTO> getDmAcountInfoList(DmAccountInfoDTO req) {
        List<DmAccountInfoRspDTO> result = new ArrayList<>();
        // 构建账户基本信息查询条件
        AcmAcInfDO acInfQuery = new AcmAcInfDO();
        acInfQuery.setUserId(req.getUserId());
        acInfQuery.setAcSts(ACMConstants.AC_OPEN_STS);
        acInfQuery.setCcyType(CcyTypeEnum.DM.getCcyType());
        if (JudgeUtils.isNotBlank(req.getCcy())) {
            acInfQuery.setCcy(req.getCcy());
        }

        // 查询账户基本信息
        List<AcmAcInfDO> acInfList = iAcmAcInfDao.find(acInfQuery);
        if (acInfList == null || acInfList.isEmpty()) {
            logger.info("用户userId:{}未开通账户！", req.getUserId());
            return result;
        }

        // 收集所有账号
        List<String> acNoList = new ArrayList<>();
        acInfList.forEach(acInf -> acNoList.add(acInf.getAcNo()));

        // 构建余额查询条件
        AcmAcBalDO acBalQuery = new AcmAcBalDO();
        acBalQuery.setUserId(req.getUserId());
        acBalQuery.setCapTyp(CapTypEnum.CAP_TYP_CASH.getCapTyp()); // 仅查询现金余额

        // 查询账户余额
        List<AcmAcBalDO> acBalList = iAcmAcBalDao.find(acBalQuery);

        // 构建地址查询条件
        DmAccountAddressDO addressQuery = new DmAccountAddressDO();
        addressQuery.setUserId(req.getUserId());
        addressQuery.setStatus(AddressStatusEnum.ENABLED.getCode()); // 仅查询启用状态的地址
        if (JudgeUtils.isNotBlank(req.getNetwork())) {
            addressQuery.setNetwork(req.getNetwork());
        }
        if (JudgeUtils.isNotBlank(req.getAddress())) {
            addressQuery.setAddress(req.getAddress());
        }

        // 查询链上地址信息
        List<DmAccountAddressDO> addressList = iDmAccountAddressDao.find(addressQuery);

        // 组装结果
        for (AcmAcInfDO acInf : acInfList) {
            DmAccountInfoRspDTO rspDTO = new DmAccountInfoRspDTO();
            rspDTO.setAcNo(acInf.getAcNo());
            rspDTO.setCcy(acInf.getCcy());
            //获取币种logo
            DmCcyListDO dmCcyListDO = iDmCcyListDao.get(rspDTO.getCcy());
            rspDTO.setCcyLogo(dmCcyListDO != null && dmCcyListDO.getCoinIconUrl() != null ? dmCcyListDO.getCoinIconUrl() : "");

            // 查找对应余额
            for (AcmAcBalDO bal : acBalList) {
                if (bal.getAcNo().equals(acInf.getAcNo()) && bal.getCcy().equals(acInf.getCcy())) {
                    rspDTO.setAccountBalance(bal.getAcCurBal());
                    break;
                }
            }

            // 查找对应地址信息
            for (DmAccountAddressDO address : addressList) {
                if (address.getAcmAcNo() != null && address.getAcmAcNo().equals(acInf.getAcNo())) {
                    rspDTO.setNetwork(address.getNetwork());
                    rspDTO.setAddress(address.getAddress());
                    rspDTO.setQrCodeBase64(address.getQrCodeBase64());
                    break;
                }
            }

            // 只有当找到有效地址信息时才添加到结果
            if (JudgeUtils.isNotBlank(rspDTO.getAddress())) {
                result.add(rspDTO);
            }
        }

        return result;
    }

    /**
     * 通过订单集合获取数币账户收款/充值分页记录列表
     *
     * @param req
     * @return
     */
    @Override
    public List<ReceiptRecord> getDmAccountReceiptsList(ReceiptRecordReqDTO req) {

        // 计算分页偏移量
        int offset = ((req.getPageNo() - 1) * req.getPageSize());

        // 查询分页数据
        List<DmTransferCallbackDO> doList = dmTransferCallbackDao.findReceipts(
                req.getOrderNos(), req.getPageSize(), offset);

        // 转换 DO 到 DTO
        List<ReceiptRecord> records = new ArrayList<>();
        for (DmTransferCallbackDO doRecord : doList) {
            ReceiptRecord record = new ReceiptRecord();
            record.setTxTime(doRecord.getTxBaseCreateTime());
            record.setCoinId(doRecord.getCoinId());
            record.setAmount(doRecord.getAmount());
            record.setFee(doRecord.getTxBaseFee());
            record.setReceiveAddress(doRecord.getAddress());
            record.setPayAddress(doRecord.getCpAddress());
            record.setOrderId(doRecord.getOrderId());
            record.setTxHash(doRecord.getTxBaseTxId());
            record.setStatus(doRecord.getTxBaseStatus());
            record.setChannel(doRecord.getChannel());
            record.setMemo(JudgeUtils.isBlank(doRecord.getTxBaseMemo()) ? "--" : doRecord.getTxBaseMemo());
            records.add(record);
        }

        return records;
    }

    /**
     * 添加回调信息
     *
     * @param req
     * @return
     */
    @Override
    public Long addCallback(AddCallbackReqDTO req) {
        DmTransferCallbackDO dmTransferCallbackDo = new DmTransferCallbackDO();
        BeanUtils.copyProperties(dmTransferCallbackDo, req);
        dmTransferCallbackDao.insert(dmTransferCallbackDo);
        return dmTransferCallbackDo.getId();
    }

    /**
     * 通过地址获取数币账户信息
     *
     * @param address
     * @return
     */
    @Override
    public DmAccountAddressRspDTO getByAddress(String address) {
        DmAccountAddressDO dmAccountAddress = iDmAccountAddressDao.getByAddress(address);
        if (JudgeUtils.isNull(dmAccountAddress)) {
            logger.info("未找到地址: {} 对应的数币账户信息！", address);
            return null;
        }
        DmAccountAddressRspDTO dmAccountAddressRspDTO = new DmAccountAddressRspDTO();
        BeanUtils.copyProperties(dmAccountAddressRspDTO, dmAccountAddress);
        return dmAccountAddressRspDTO;
    }

    /**
     * 更新回调信息
     *
     * @param req
     */
    @Override
    public void updateCallback(UpdateCallbackReqDTO req) {
        DmTransferCallbackDO dmTransferCallbackDo = new DmTransferCallbackDO();
        BeanUtils.copyProperties(dmTransferCallbackDo, req);
        dmTransferCallbackDao.update(dmTransferCallbackDo);
    }

    /**
     * 更新账户余额
     *
     * @param req
     */
    @Override
    public void updateAccBal(UpdateAccBalReqDTO req) {
        AcmAcBalDO acmAcBalDO = new AcmAcBalDO();
        BeanUtils.copyProperties(acmAcBalDO, req);
        iAcmAcBalDao.update(acmAcBalDO);
    }

    /**
     * 获取当前用户收款/充值数币账户列表
     *
     * @param req
     * @return
     */
    @Override
    public List<DmReceiptAccRspDTO> getDmReceiptAcc(DmReceiptAccReqDTO req) {
        //1. 在账户信息表中查询当前用户所有数币账户
        AcmAcInfDO acmAcInfDO = new AcmAcInfDO();
        BeanUtils.copyProperties(acmAcInfDO, req);
        acmAcInfDO.setAcSts(ACMConstants.AC_OPEN_STS);
        acmAcInfDO.setCcyType(CcyTypeEnum.DM.getCcyType());
        List<AcmAcInfDO> acmAcInfDOList = iAcmAcInfDao.find(acmAcInfDO);
        if (JudgeUtils.isEmpty(acmAcInfDOList)) {
            return Collections.emptyList();
        }

        //2. 获取账户编码集合
        List<String> acNoList = acmAcInfDOList.stream().map(AcmAcInfDO::getAcNo).collect(Collectors.toList());

        //3. 在账户地址表中根据 acNoList 和 useType 查询账户列表
        List<DmAccountAddressDO> dmAccountAddressDOList = iDmAccountAddressDao.findByAcNoListAndUseType(acNoList, req.getUseType());

        //4. 获取当前用户收款/充值数币账户列表
        List<DmReceiptAccRspDTO> list = new ArrayList<>();
        dmAccountAddressDOList.forEach(dmAccountAddressDO -> {
            DmReceiptAccRspDTO rspDTO = new DmReceiptAccRspDTO();
            rspDTO.setAcNo(dmAccountAddressDO.getAcmAcNo());
            list.add(rspDTO);
        });
        return list;
    }

    @Override
    public String calNetWorth(String userId) {
        List<AcmAcBalDO> acmAcBalDOList = this.queryAcBal(null,userId,CapTypEnum.CAP_TYP_CASH.getCapTyp());
        BigDecimal netWorth = BigDecimal.ZERO;
        BigDecimal rate = BigDecimal.ZERO;
        if(JudgeUtils.isEmpty(acmAcBalDOList)) {
            return "0";
        }
        for(AcmAcBalDO bal : acmAcBalDOList) {
            if(JudgeUtils.equals(bal.getCcy(), ACMConstants.SYS_DEFAULT_CCY)) {
                netWorth = netWorth.add(bal.getAcCurBal());
            } else {
                if(JudgeUtils.isNotNull(exchangeOrderClient.queryExchangeRate(bal.getCcy(), ACMConstants.SYS_DEFAULT_CCY).getBody()))
                rate = exchangeOrderClient.queryExchangeRate(bal.getCcy(), ACMConstants.SYS_DEFAULT_CCY).getBody().getExchangeRate();
                BigDecimal usdCurBal = bal.getAcCurBal().multiply(rate);
                //BigDecimal usdUavaBal = bal.getAcUavaBal().multiply(rate);
                netWorth = netWorth.add(usdCurBal);
            }
        }
        return netWorth.toString();
    }

    /**
     * 校验地址是否存在
     * @param address
     * @return
     */
    @Override
    public boolean checkAddressExists(String address) {
        DmAccountAddressDO dmAccountAddress = iDmAccountAddressDao.getByAddress(address);
        return !JudgeUtils.isNull(dmAccountAddress);
    }

    /**
     * 查询数币账户详情
     * @param acNo
     * @return
     */
    @Override
    public DmAccountDetailRspDTO queryDmAccountDetail(String acNo) {

        //获取账户信息
        AcmAcInfDO acmAcInfDO = new AcmAcInfDO();
        acmAcInfDO.setAcNo(acNo);
        acmAcInfDO.setCcyType(CcyTypeEnum.DM.getCcyType());
        acmAcInfDO.setAcSts(ACMConstants.AC_OPEN_STS);
        AcmAcInfDO acInfDO = iAcmAcInfDao.find(acmAcInfDO).get(0);
        if (JudgeUtils.isNull(acInfDO)) {
            return null;
        }
        //获取账户地址信息
        DmAccountAddressDO dmAccountAddressDO = dmAccountAddressDao.getByAcNo(acNo);
        if (JudgeUtils.isNull(dmAccountAddressDO)) {
            return null;
        }
        //账户所属金库编码
        String vaultCode = dmAccountAddressDO.getVaultCode();
        //账户所在组编码
        String groupCode = dmAccountAddressDO.getGroupCode();
        //账号 id
        String accountId = String.valueOf(dmAccountAddressDO.getAccountId());
        //账户地址
        String address = dmAccountAddressDO.getAddress();

        CregisAccDetailReqDTO req = new CregisAccDetailReqDTO();
        List<CregisAccDetailReqDTO.QueryCondition> queryList = new ArrayList<>();
        CregisAccDetailReqDTO.QueryCondition condition = new CregisAccDetailReqDTO.QueryCondition();
        condition.setKey("address");
        condition.setValue(address);
        queryList.add(condition);
        req.setQueryList(queryList);

        CregisReqDTO cregisReqDTO = new CregisReqDTO();
        cregisReqDTO.setCregisAccDetailReqDTO(req);
        cregisReqDTO.setVaultCode(vaultCode);
        cregisReqDTO.setGroupCode(groupCode);
        cregisReqDTO.setAccountId(accountId);

        cregisReqDTO.setCcy(acInfDO.getCcy());

        CregisAccDetailRspDTO detail = null;
        try {
            detail = cregisClient.getAccountDetail(cregisReqDTO);
        } catch (Exception e) {
            logger.info("CregisClient.getAccountDetail error:", e);
            detail = null;
        }
        AcmAcBalDO acmAcBalDO = iAcmAcBalDao.get(acNo, CapTypEnum.CAP_TYP_CASH.getCapTyp());
        if(JudgeUtils.isNotNull(detail)){
            acmAcBalDO.setAcCurBal(detail.getBalance());
            iAcmAcBalDao.update(acmAcBalDO);
        }
        if (JudgeUtils.isNull(detail)) {
            logger.error("Cregis 账户详情查询失败，地址address:{}", address);
            return null;
        }
        DmAccountDetailRspDTO dmAccountDetailRspDTO = new DmAccountDetailRspDTO();
        BeanUtils.copyProperties(dmAccountDetailRspDTO, detail);
        acmAcBalDO = iAcmAcBalDao.get(acNo, CapTypEnum.CAP_TYP_CASH.getCapTyp());
        if (JudgeUtils.isNull(acmAcBalDO)) {
            return null;
        }

//        DmCcyListDO dmCcyListDO = iDmCcyListDao.get(acmAcBalDO.getCcy());
//        BigDecimal realBal = convertDmAccBal(detail.getBalance(), dmCcyListDO.getPrec());
        dmAccountDetailRspDTO.setUserId(acInfDO.getUserId());
        dmAccountDetailRspDTO.setCoinId((JudgeUtils.isNull(detail) || JudgeUtils.isBlank(detail.getCoinId())) ?
                acInfDO.getCcy() : detail.getCoinId());
        dmAccountDetailRspDTO.setAcNo(acInfDO.getAcNo());
        dmAccountDetailRspDTO.setAddress((JudgeUtils.isNull(detail) || JudgeUtils.isBlank(detail.getAddress())) ?
                address : detail.getAddress());
        dmAccountDetailRspDTO.setRealBal(acmAcBalDO.getAcCurBal().subtract(acmAcBalDO.getAcUavaBal()));
        dmAccountDetailRspDTO.setNetwork((JudgeUtils.isNull(detail) || JudgeUtils.isBlank(detail.getNetwork())) ?
                dmAccountAddressDO.getNetwork() : detail.getNetwork());
        dmAccountDetailRspDTO.setCreateTime(acmAcBalDO.getCreateTime());
        dmAccountDetailRspDTO.setUpdateTime(acmAcBalDO.getModifyTime());
        dmAccountDetailRspDTO.setStatus((JudgeUtils.isNull(detail) || JudgeUtils.isNull(detail.getStatus())) ?
                Integer.valueOf(acInfDO.getAcSts()) : detail.getStatus());
        dmAccountDetailRspDTO.setAccountId((JudgeUtils.isNull(detail) || JudgeUtils.isNull(detail.getAccountId())) ?
                Long.valueOf(accountId) : detail.getAccountId());

        return dmAccountDetailRspDTO;
    }

    /**
     * 条件查询数币账户地址信息
     * @param findDmAddressReqDTO
     * @return
     */
    @Override
    public List<DmAccountAddressRspDTO> findDmAddress(FindDmAddressReqDTO findDmAddressReqDTO) {

        DmAccountAddressDO dmAccountAddressDO = new DmAccountAddressDO();
        BeanUtils.copyProperties(dmAccountAddressDO,findDmAddressReqDTO);
        dmAccountAddressDO.setUserId(LemonUtils.getUserId());
        List<DmAccountAddressDO> dmAccountAddressDOList = iDmAccountAddressDao.find(dmAccountAddressDO);
        List<DmAccountAddressRspDTO> dmAccountAddressRspDTOList = new ArrayList<>();
        if(JudgeUtils.isNotEmpty(dmAccountAddressDOList)) {
            for(DmAccountAddressDO dmAccountAddressDO1 : dmAccountAddressDOList) {
                DmAccountAddressRspDTO dmAccountAddressRspDTO = new DmAccountAddressRspDTO();
                BeanUtils.copyProperties(dmAccountAddressRspDTO, dmAccountAddressDO1);
                dmAccountAddressRspDTOList.add(dmAccountAddressRspDTO);
            }
        }

        return dmAccountAddressRspDTOList;
    }

    /**
     * 生成收款二维码
     *
     * @param dmPaymentQrcodeDTO
     * @return
     */
    @Override
    public DmPaymentQrCodeRspDTO getPaymentQrcode(DmPaymentQrcodeReqDTO dmPaymentQrcodeDTO) {
        logger.info("生成收款二维码，请求参数: {}", dmPaymentQrcodeDTO);

        // 二维码内容
        String qrContent;

        DmPaymentQrCodeRspDTO response = new DmPaymentQrCodeRspDTO();
        LocalDateTime expiryDateTime = null;

        DmAccountAddressDO dmAccountAddress = iDmAccountAddressDao.getByAcNo(dmPaymentQrcodeDTO.getAcNo());

        // 判断是否设置了金额
        if (dmPaymentQrcodeDTO.getMoney() == null || dmPaymentQrcodeDTO.getMoney().compareTo(BigDecimal.ZERO) <= 0) {
            // 无金额，从表中查询是否存在二维码
            if (JudgeUtils.isNotBlank(dmAccountAddress.getQrCodeBase64())) {
                response.setQrcodeBase64(dmAccountAddress.getQrCodeBase64());
                response.setExpiryDateTime(null);
                return response;
            }
            // 生成长期二维码
            qrContent = String.format("address:%s", dmAccountAddress.getAddress());

        } else {
            /// 有金额：生成带金额和有效期的二维码,不存储
            qrContent = String.format("address:%s?money=%s",
                    dmPaymentQrcodeDTO.getAddress(),
                    dmPaymentQrcodeDTO.getMoney().toString());

            // 设置60分钟有效期
            expiryDateTime = LocalDateTime.now().plusMinutes(60);
        }

        try {
            // 生成二维码
            String qrCodeBase64 = generateQRCodeBase64(qrContent);
            response.setQrcodeBase64(qrCodeBase64);
            response.setExpiryDateTime(expiryDateTime);

            // 仅无金额二维码存数据库
            if (dmPaymentQrcodeDTO.getMoney() == null || dmPaymentQrcodeDTO.getMoney().compareTo(BigDecimal.ZERO) <= 0) {
                dmAccountAddress.setQrCodeBase64(qrCodeBase64);
                dmAccountAddress.setModifyTime(LocalDateTime.now());
                iDmAccountAddressDao.update(dmAccountAddress);
            }
        } catch (Exception e) {
            logger.error("生成二维码失败", e);
            throw new RuntimeException("生成二维码失败: " + e.getMessage());
        }

        return response;
    }

    private String generateQRCodeBase64(String content) {
        // 配置二维码参数
        QrConfig config = new QrConfig(300, 300);
        config.setMargin(2);
        config.setCharset(StandardCharsets.UTF_8);

        // 生成二维码图片
        BufferedImage qrImage = QrCodeUtil.generate(content, config);

        // 将图片转换为 Base64 编码
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ImgUtil.write(qrImage, ImgUtil.IMAGE_TYPE_PNG, outputStream);
        return Base64.encode(outputStream.toByteArray());
    }

    private AcmAcBalDO initBalRecord(AcmAcInfDO acmAcInfDO, String capTyp) {
        AcmAcBalDO acmAcBalDO = new AcmAcBalDO();
        acmAcBalDO.setAcNo(acmAcInfDO.getAcNo());
        acmAcBalDO.setCcy(acmAcInfDO.getCcy());
        acmAcBalDO.setUserId(acmAcInfDO.getUserId());
        acmAcBalDO.setAcCurBal(BigDecimal.valueOf(0, 2));
        acmAcBalDO.setAcUavaBal(BigDecimal.valueOf(0, 2));
        acmAcBalDO.setCapTyp(capTyp);
        return acmAcBalDO;
    }

    private String genAcNo() {
        String country = LemonUtils.getLocale().getCountry().toUpperCase();
        logger.info("AC_NO:" + country);
        if (JudgeUtils.isBlank(country)) {
            country = ACMConstants.UNKNOWN_COUNTRY_CODE;
        }
        StringBuffer countryCode = new StringBuffer();
        //获取国家缩写的ASC码的十进制值，作为账号的地区编码与序号组成用户账号
        for (int i = 0; i < country.length(); i++) {
            char c = country.charAt(i);
            countryCode.append(Integer.parseInt(Integer.toHexString(c), 16));
        }
        String acNo = IdGenUtils.generateId("AC_NO", countryCode.toString(), ACMConstants.AC_SEQ_LEN);
        acNo += generateCheckFlag(acNo);
        return acNo;
    }

    @Override
    public void updateBalByCregis(String acNo) {
        CregisAccDetailRspDTO detail = getDmAccDetailFromCregis(acNo);
        AcmAcBalDO bal = iAcmAcBalDao.get(acNo,CapTypEnum.CAP_TYP_CASH.getCapTyp());
//        DmCcyListDO dmCcyListDO = iDmCcyListDao.get(bal.getCcy());
        if(JudgeUtils.isNull(detail) && JudgeUtils.isNull(bal)) {
            logger.info("Cregis账户详情查询失败或账号不存在:{}",acNo);
            return;
        }
//        BigDecimal realBal = convertDmAccBal(detail.getBalance(), dmCcyListDO.getPrec());
        bal.setAcCurBal(detail.getBalance());
        iAcmAcBalDao.update(bal);
    }

    @Override
    public void rechargeAndReceiptAct(AddOrderReqDTO order) {
        // 用户账户 +100
        AccountingReqDTO userAccountReqDTO = null; // 用户现金账户账务对象
        //账号资金属性：1 现金 8 待结算
        String balCapType= CapTypEnum.CAP_TYP_CASH.getCapTyp();
        //现金账户
        String balAcNo= order.getAcNo();
        //总金额
        BigDecimal userAmt = order.getOrderAmt();
        List<AccountingReqDTO> acUserList = new ArrayList();
        String acmJrnNo = "";
        String rmk = "";
        if ("DC".equals(order.getTxType())) {
            acmJrnNo = LemonUtils.getApplicationName() + "DC01" + IdGenUtils.generateIdWithDate("pwmRechangeOrdNo",10);
            rmk = "数币充值$";
        }else {
            acmJrnNo = LemonUtils.getApplicationName() + "DS01" + IdGenUtils.generateIdWithDate("pwmRechangeOrdNo",10);
            rmk = "数币收款$";
        }
        // 贷：其他应付款-支付账户-xx用户现金账户
        userAccountReqDTO=acmComponent.createAccountingReqDTO(order.getOrderNo(), acmJrnNo, order.getTxType(),
                ACMConstants.ACCOUNTING_NOMARL, userAmt, balAcNo, ACMConstants.USER_AC_TYP, balCapType, ACMConstants.AC_C_FLG,
                null, null, null, null, null, rmk + order.getOrderAmt());
        acUserList.add(userAccountReqDTO);
        acmComponent.requestAc(acUserList);

    }

    private CregisAccDetailRspDTO getDmAccDetailFromCregis(String acNo) {
        //获取账户信息
        DmAccountAddressDO dmAccountAddressDO = dmAccountAddressDao.getByAcNo(acNo);
        if (JudgeUtils.isNull(dmAccountAddressDO)) {
            return null;
        }
        //账户所属金库编码
        String vaultCode = dmAccountAddressDO.getVaultCode();
        //账户所在组编码
        String groupCode = dmAccountAddressDO.getGroupCode();
        //账号 id
        String accountId = String.valueOf(dmAccountAddressDO.getAccountId());
        //账户地址
        String address = dmAccountAddressDO.getAddress();

        CregisAccDetailReqDTO req = new CregisAccDetailReqDTO();
        List<CregisAccDetailReqDTO.QueryCondition> queryList = new ArrayList<>();
        CregisAccDetailReqDTO.QueryCondition condition = new CregisAccDetailReqDTO.QueryCondition();
        condition.setKey("address");
        condition.setValue(address);
        queryList.add(condition);
        req.setQueryList(queryList);

        //获取账户信息
        AcmAcInfDO acmAcInfDO = new AcmAcInfDO();
        acmAcInfDO.setAcNo(acNo);
        acmAcInfDO.setCcyType(CcyTypeEnum.DM.getCcyType());
        acmAcInfDO.setAcSts(ACMConstants.AC_OPEN_STS);
        AcmAcInfDO acInfDO = iAcmAcInfDao.find(acmAcInfDO).get(0);
        if (JudgeUtils.isNull(acInfDO)) {
            return null;
        }

        CregisReqDTO cregisReqDTO = new CregisReqDTO();
        cregisReqDTO.setCregisAccDetailReqDTO(req);
        cregisReqDTO.setVaultCode(vaultCode);
        cregisReqDTO.setGroupCode(groupCode);
        cregisReqDTO.setAccountId(accountId);

        CregisAccDetailRspDTO detail = cregisClient.getAccountDetail(cregisReqDTO);

        return detail;
    }

    private BigDecimal convertDmAccBal(Integer balNum, Integer prec) {
        if (JudgeUtils.isNull(balNum) || JudgeUtils.isNull(prec)) {
            return BigDecimal.ZERO;
        }
        BigDecimal bal = new BigDecimal(balNum);
        BigDecimal divisor = BigDecimal.TEN.pow(prec);
        return bal.divide(divisor);
    }
}
