<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">

<head>
    <title data-i18n="index.title">TMS | Dashboard</title>
    <div th:replace="head"></div>
</head>

<body>
<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg dashbard-1">
        <div th:replace="top"></div>
        <div class="row wrapper border-bottom white-bg page-heading">

        </div>
        <div class="row">
            <div class="col-lg-12">
                <div class="wrapper wrapper-content">
                    <!--<div class="row" style="height: 600px;">-->
                        <!--&lt;!&ndash;<div sec:authorize="hasRole('ROLE_ADMIN')">你好</div>&ndash;&gt;-->
                        <!--&lt;!&ndash;<div sec:authorize="hasRole('USER')">hello</div>&ndash;&gt;-->
                        <!--&lt;!&ndash;<div sec:authorize="hasPermission('','/system')">首页页面（经过权限检查）</div>&ndash;&gt;-->
                        <!--&lt;!&ndash;<div sec:authorize="hasPermission('','/index1')">没出来</div>&ndash;&gt;-->
                        <!--<div>首页暂未定制</div>-->
                    <!--</div>-->
                    <div class="middle-box text-center animated fadeInRightBig" style="height: 500px;">
                        <h3 class="font-bold">This is page content</h3>
                        <h3 sec:authorize="hasPermission('','/index')">
                            hello wjs
                        </h3>
                        <div class="error-desc">
                            You can create here any grid layout you want. And any variation layout you imagine:) Check out
                            main dashboard and other site. It use many different layout.
                            <br/><a href="index.html" class="btn btn-primary m-t">Dashboard</a>
                        </div>
                    </div>
                </div>

                <div th:replace="footer"></div>
            </div>
        </div>

    </div>
</div>

<div th:replace="script"></div>

</body>
</html>
