<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title>Cregis回调记录查询</title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>
        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>
            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2>Cregis回调记录查询</h2>
                    <ol class="breadcrumb">
                        <li>
                            <a>资金出入</a>
                        </li>
                        <li>
                            <a>数币订单管理</a>
                        </li>
                        <li>
                            <strong>Cregis回调记录查询</strong>
                        </li>
                    </ol>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!--回调记录详细信息弹窗-->
                <div th:replace="cpt/dcorder/callback/callbackModal"></div>

                <!-- 查询表单 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-title">
                                <h5>查询条件</h5>
                                <div class="ibox-tools">
                                    <a class="collapse-link">
                                        <i class="fa fa-chevron-up"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12">
                                        <!-- 订单ID -->
                                        <label class="col-sm-2 control-label" for="searchOrderId"
                                            data-i18n="cregis.callback.orderId">订单ID</label>
                                        <div class="col-sm-4">
                                            <input name="searchOrderId" id="searchOrderId" class="form-control"
                                                value="" />
                                        </div>
                                        <!-- 账单编号 -->
                                        <label class="col-sm-2 control-label" for="searchBilOrderNo"
                                            data-i18n="cregis.callback.bilOrderNo">账单编号</label>
                                        <div class="col-sm-4">
                                            <input name="searchBilOrderNo" id="searchBilOrderNo" class="form-control"
                                                value="" />
                                        </div>
                                    </div>
                                    <div class="form-group col-sm-12">
                                        <!-- 处理状态 -->
                                        <label class="col-sm-2 control-label" for="searchIsProcessed"
                                            data-i18n="cregis.callback.isProcessed">处理状态</label>
                                        <div class="col-sm-4">
                                            <select name="searchIsProcessed" id="searchIsProcessed"
                                                class="form-control">
                                                <option value="">全部</option>
                                                <option value="0">未处理</option>
                                                <option value="1">已处理</option>
                                            </select>
                                        </div>
                                        <!-- 交易方向 -->
                                        <label class="col-sm-2 control-label" for="searchDirection"
                                            data-i18n="cregis.callback.direction">交易方向</label>
                                        <div class="col-sm-4">
                                            <select name="searchDirection" id="searchDirection" class="form-control">
                                                <option value="">全部</option>
                                                <option value="IN">入账</option>
                                                <option value="OUT">出账</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-sm-4 col-sm-offset-1">
                                            <button type="button" id="searchBtn" class="btn btn-primary"
                                                onclick="search()" data-i18n="common.search">查询</button>
                                            <button type="button" id="resetBtn" class="btn btn-white"
                                                onclick="resetForm()" data-i18n="common.reset">重置</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!--数据表格-->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-title">
                                <h5>回调记录</h5>
                                <div class="ibox-tools">
                                    <a class="collapse-link">
                                        <i class="fa fa-chevron-up"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="callbackTable" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>交易哈希</th>
                                                <th>交易状态</th>
                                                <th>订单ID</th>
                                                <th>账单编号</th>
                                                <th>币种</th>
                                                <th>金额</th>
                                                <th>方向</th>
                                                <th>回调时间</th>
                                                <th>处理状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div th:replace="script"></div>

    <script>
        var table;

        $(document).ready(function () {
            // 初始化国际化
            $("[data-i18n]").i18n();

            // 初始化数据表格
            initTable();
        });

        // 初始化表格
        function initTable() {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/locales/datatables/zh.json';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
                default:
                    languageUrl = '/locales/datatables/zh.json';
            }

            // 等待国际化加载完成
            i18nLoad.then(function () {

                table = $('#callbackTable').DataTable({
                    dom: 'Blfrtip',
                    buttons: [
                        { extend: 'copyHtml5', text: '复制' },
                        { extend: 'csvHtml5', text: 'CSV' },
                        { extend: 'excelHtml5', title: 'Cregis回调记录查询', text: 'Excel' }
                    ],
                    language: {
                        url: languageUrl
                    },
                    responsive: true,
                    serverSide: true,
                    searchDelay: 1000,
                    searching: false,
                    processing: true,
                    ordering: false,
                    paging: true,
                    pageLength: 10,
                    lengthMenu: [10, 25, 50, 100],
                    pagingType: "simple_numbers",
                    ajax: {
                        contentType: 'application/json',
                        url: '/cpt/dcorder/callback/findAll',
                        type: 'post',
                        data: function (d) {
                            var queryData = {
                                draw: d.draw,
                                start: d.start,
                                length: d.length,
                                orderId: $('#searchOrderId').val(),
                                bilOrderNo: $('#searchBilOrderNo').val(),
                                isProcessed: $('#searchIsProcessed').val(),
                                direction: $('#searchDirection').val()
                            };
                            return JSON.stringify(queryData);
                        },
                        dataSrc: function (json) {
                            if (json && json.data) {
                                return json.data;
                            } else {
                                console.error('Invalid response format:', json);
                                return [];
                            }
                        },
                        error: function (xhr, error, thrown) {
                            console.error('Ajax error:', error);
                            console.error('Response:', xhr.responseText);
                        }
                    },
                    columns: [
                        {
                            data: 'id',
                            width: '60px'
                        },
                        {
                            data: 'txBaseTxId',
                            render: function (data, type, row) {
                                if (data && data.length > 20) {
                                    return '<span title="' + data + '">' + data.substring(0, 20) + '...</span>';
                                }
                                return data || '';
                            }
                        },
                        {
                            data: 'txBaseStatus',
                            width: '80px'
                        },
                        {
                            data: 'orderId',
                            render: function (data, type, row) {
                                if (data && data.length > 15) {
                                    return '<span title="' + data + '">' + data.substring(0, 15) + '...</span>';
                                }
                                return data || '';
                            }
                        },
                        {
                            data: 'bilOrderNo',
                            render: function (data, type, row) {
                                if (data && data.length > 15) {
                                    return '<span title="' + data + '">' + data.substring(0, 15) + '...</span>';
                                }
                                return data || '';
                            }
                        },
                        {
                            data: 'coinId',
                            width: '80px'
                        },
                        {
                            data: 'amount',
                            width: '100px'
                        },
                        {
                            data: 'direction',
                            width: '80px',
                            render: function (data, type, row) {
                                if (data === 'IN') {
                                    return '<span class="label label-success">入账</span>';
                                } else if (data === 'OUT') {
                                    return '<span class="label label-warning">出账</span>';
                                }
                                return data || '';
                            }
                        },
                        {
                            data: 'callbackTime',
                            width: '140px',
                            render: function (data, type, row) {
                                if (data) {
                                    return new Date(data).toLocaleString();
                                }
                                return '';
                            }
                        },
                        {
                            data: 'isProcessed',
                            width: '80px',
                            render: function (data, type, row) {
                                if (data === 1) {
                                    return '<span class="label label-primary">已处理</span>';
                                } else if (data === 0) {
                                    return '<span class="label label-danger">未处理</span>';
                                }
                                return data || '';
                            }
                        },
                        {
                            data: null,
                            width: '80px',
                            orderable: false,
                            render: function (data, type, row) {
                                var detailText = (typeof $.i18n !== 'undefined') ? $.i18n.t('common.detail') : '详情';
                                return '<button type="button" class="btn btn-info btn-xs" onclick="showDetail(' + row.id + ')"><i class="fa fa-search"></i> ' + detailText + '</button>';
                            }
                        }
                    ]
                });

                // 更新表头文本
                updateTableHeaders();
            });
        }

        // 更新表头文本
        function updateTableHeaders() {
            if (typeof $.i18n !== 'undefined') {
                $('#callbackTable thead th').eq(0).text($.i18n.t('cregis.callback.id'));
                $('#callbackTable thead th').eq(1).text($.i18n.t('cregis.callback.txBaseTxId'));
                $('#callbackTable thead th').eq(2).text($.i18n.t('cregis.callback.txBaseStatus'));
                $('#callbackTable thead th').eq(3).text($.i18n.t('cregis.callback.orderId'));
                $('#callbackTable thead th').eq(4).text($.i18n.t('cregis.callback.bilOrderNo'));
                $('#callbackTable thead th').eq(5).text($.i18n.t('cregis.callback.coinId'));
                $('#callbackTable thead th').eq(6).text($.i18n.t('cregis.callback.amount'));
                $('#callbackTable thead th').eq(7).text($.i18n.t('cregis.callback.direction'));
                $('#callbackTable thead th').eq(8).text($.i18n.t('cregis.callback.callbackTime'));
                $('#callbackTable thead th').eq(9).text($.i18n.t('cregis.callback.isProcessed'));
                $('#callbackTable thead th').eq(10).text($.i18n.t('common.operations'));
            }
        }

        // 查询
        function search() {
            table.ajax.reload();
        }

        // 重置表单
        function resetForm() {
            $('#queryForm')[0].reset();
            table.ajax.reload();
        }

        // 显示详情
        function showDetail(id) {
            $.ajax({
                type: "POST",
                url: "/cpt/dcorder/callback/findDetail",
                data: { "id": id },
                dataType: "json",
                success: function (data) {
                    if (data) {
                        // 填充详情数据
                        $('#detail_id').text(data.id || '');
                        $('#detail_txBaseNetwork').text(data.txBaseNetwork || '');
                        $('#detail_txBaseBlockId').text(data.txBaseBlockId || '');
                        $('#detail_txBaseTxId').text(data.txBaseTxId || '');
                        $('#detail_txBaseEcode').text(data.txBaseEcode || '');
                        $('#detail_txBaseGroupId').text(data.txBaseGroupId || '');
                        $('#detail_txBaseFee').text(data.txBaseFee || '');
                        $('#detail_txBaseStatus').text(data.txBaseStatus || '');
                        $('#detail_txBaseCreateTime').text(data.txBaseCreateTime ? new Date(data.txBaseCreateTime).toLocaleString() : '');
                        $('#detail_txBaseBlockHash').text(data.txBaseBlockHash || '');
                        $('#detail_txBaseProgramId').text(data.txBaseProgramId || '');
                        $('#detail_txBaseComputeUnitsConsumed').text(data.txBaseComputeUnitsConsumed || '');
                        $('#detail_txBaseMemo').text(data.txBaseMemo || '');
                        $('#detail_vaultCode').text(data.vaultCode || '');
                        $('#detail_accountId').text(data.accountId || '');
                        $('#detail_accountType').text(data.accountType || '');
                        $('#detail_orderId').text(data.orderId || '');
                        $('#detail_bilOrderNo').text(data.bilOrderNo || '');
                        $('#detail_coinId').text(data.coinId || '');
                        $('#detail_network').text(data.network || '');
                        $('#detail_address').text(data.address || '');
                        $('#detail_cpAddress').text(data.cpAddress || '');
                        $('#detail_amount').text(data.amount || '');
                        $('#detail_direction').text(data.direction === 'IN' ? '入账' : (data.direction === 'OUT' ? '出账' : data.direction));
                        $('#detail_channel').text(data.channel || '');
                        $('#detail_callbackTime').text(data.callbackTime ? new Date(data.callbackTime).toLocaleString() : '');
                        $('#detail_isProcessed').text(data.isProcessed === 1 ? '已处理' : (data.isProcessed === 0 ? '未处理' : data.isProcessed));

                        // 显示模态框
                        $('#callbackDetailModal').modal('show');
                    } else {
                        alert('获取详情失败');
                    }
                },
                error: function () {
                    alert('获取详情失败');
                }
            });
        }
    </script>
</body>

</html>