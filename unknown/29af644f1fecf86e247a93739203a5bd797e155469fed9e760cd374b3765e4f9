<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="nav.acmmgr.accountaddress.title">数币地址管理</title>
    <div th:replace="head"></div>
    <!-- 添加必要的JavaScript依赖 -->
    <script src="/js/plugins/validate/jquery.validate.min.js"></script>
    <script src="/js/plugins/validate/messages_zh.min.js"></script>
    <script src="/js/plugins/toastr/toastr.min.js"></script>
    <link href="/css/plugins/toastr/toastr.min.css" rel="stylesheet">
    <style>
        /* 状态标签样式增强 */
        .label {
            font-size: 11px;
            padding: 4px 8px;
            border-radius: 3px;
            font-weight: 500;
        }

        .label i {
            margin-right: 3px;
        }

        .label-success {
            background-color: #5cb85c;
            border: 1px solid #4cae4c;
        }

        .label-warning {
            background-color: #f0ad4e;
            border: 1px solid #eea236;
        }

        .label-default {
            background-color: #777;
            border: 1px solid #666;
        }

        .label-info {
            background-color: #5bc0de;
            border: 1px solid #46b8da;
        }

        .label-primary {
            background-color: #337ab7;
            border: 1px solid #2e6da4;
        }

        /* 时间戳样式增强 */
        .timestamp-display {
            color: #666;
            font-size: 12px;
        }

        .timestamp-display i {
            margin-right: 4px;
            color: #999;
        }

        /* 详情模态框中的状态和时间显示 */
        .form-control-static .label {
            font-size: 12px;
            padding: 5px 10px;
        }

        .form-control-static .timestamp-display {
            font-size: 13px;
        }

        /* 表格中的状态列居中对齐 */
        #accountAddressTable tbody td:nth-child(7) {
            text-align: center;
        }

        /* Tooltip样式优化 */
        .tooltip-inner {
            max-width: 300px;
            text-align: left;
        }
    </style>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>

            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="nav.acmmgr.accountaddress.content">数币地址管理</h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="nav.acmmgr">账户管理</a>
                        </li>
                        <li class="active">
                            <strong data-i18n="nav.acmmgr.accountaddress.content">数币地址管理</strong>
                        </li>
                    </ol>
                </div>
                <div class="col-lg-2">
                    <div class="btn-group pull-right" style="margin-top: 30px;">
                        <button type="button" class="btn btn-primary btn-sm" id="btnAdd">
                            <i class="fa fa-plus"></i> <span data-i18n="acm.accountaddress.add">新增</span>
                        </button>
                    </div>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!-- 查询表单 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12">
                                        <!-- 金库编码 -->
                                        <label class="col-sm-2 control-label" for="searchVaultCode"
                                            data-i18n="acm.accountaddress.vaultCode">金库编码</label>
                                        <div class="col-sm-4">
                                            <input name="vaultCode" id="searchVaultCode" class="form-control"
                                                value="" />
                                        </div>
                                        <!-- 地址 -->
                                        <label class="col-sm-2 control-label" for="searchAddress"
                                            data-i18n="acm.accountaddress.address">地址</label>
                                        <div class="col-sm-4">
                                            <input name="address" id="searchAddress" class="form-control" value="" />
                                        </div>
                                    </div>
                                    <div class="form-group col-sm-12">
                                        <!-- 网络 -->
                                        <label class="col-sm-2 control-label" for="searchNetwork"
                                            data-i18n="acm.accountaddress.network">网络</label>
                                        <div class="col-sm-4">
                                            <input name="network" id="searchNetwork" class="form-control" value="" />
                                        </div>
                                        <!-- 状态 -->
                                        <label class="col-sm-2 control-label" for="searchStatus"
                                            data-i18n="acm.accountaddress.status">状态</label>
                                        <div class="col-sm-4">
                                            <select name="status" id="searchStatus" class="form-control">
                                                <option value="" data-i18n="acm.accountaddress.allStatus">全部状态</option>
                                                <option value="ENABLED" data-i18n="acm.accountaddress.enabled">已启用
                                                </option>
                                                <option value="DISABLED" data-i18n="acm.accountaddress.disabled">未启用
                                                </option>
                                                <option value="FROZEN" data-i18n="acm.accountaddress.frozen">冻结</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-sm-4 col-sm-offset-1">
                                            <button type="button" id="searchBtn" class="btn btn-primary"
                                                data-i18n="acm.accountaddress.search">查询</button>
                                            <button type="button" id="resetBtn" class="btn btn-default"
                                                data-i18n="acm.accountaddress.reset">重置</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="accountAddressTable"
                                        class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="acm.accountaddress.id">ID</th>
                                                <th data-i18n="acm.accountaddress.vaultCode">金库编码</th>
                                                <th data-i18n="acm.accountaddress.groupCode">账户组编码</th>
                                                <th data-i18n="acm.accountaddress.accountId">账户ID</th>
                                                <th data-i18n="acm.accountaddress.address">地址</th>
                                                <th data-i18n="acm.accountaddress.network">网络</th>
                                                <th data-i18n="acm.accountaddress.status">状态</th>
                                                <th data-i18n="acm.accountaddress.createTime">创建时间</th>
                                                <th data-i18n="acm.accountaddress.operations">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <!-- 新增模态框 -->
    <div class="modal fade" id="addModal" tabindex="-1" role="dialog" aria-labelledby="addModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="addModalLabel" data-i18n="acm.accountaddress.addTitle">新增账户地址</h4>
                </div>
                <div class="modal-body">
                    <form id="addForm" class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="vaultCode"
                                data-i18n="acm.accountaddress.vaultCode">金库编码</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="vaultCode" name="vaultCode" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="groupCode"
                                data-i18n="acm.accountaddress.groupCode">账户组编码</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="groupCode" name="groupCode" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="accountId"
                                data-i18n="acm.accountaddress.accountId">账户ID</label>
                            <div class="col-sm-9">
                                <input type="number" class="form-control" id="accountId" name="accountId" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="address"
                                data-i18n="acm.accountaddress.address">地址</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="address" name="address" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="network"
                                data-i18n="acm.accountaddress.network">网络</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="network" name="network" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="status"
                                data-i18n="acm.accountaddress.status">状态</label>
                            <div class="col-sm-9">
                                <select class="form-control" id="status" name="status" required>
                                    <option value="ENABLED" data-i18n="acm.accountaddress.enabled">已启用</option>
                                    <option value="DISABLED" selected data-i18n="acm.accountaddress.disabled">未启用
                                    </option>
                                    <option value="FROZEN" data-i18n="acm.accountaddress.frozen">冻结</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="acmAcNo"
                                data-i18n="acm.accountaddress.acmAcNo">对应账号</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="acmAcNo" name="acmAcNo" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="userId"
                                data-i18n="acm.accountaddress.userId">客户ID</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="userId" name="userId" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="useType"
                                data-i18n="acm.accountaddress.useType">用途类型</label>
                            <div class="col-sm-9">
                                <select class="form-control" id="useType" name="useType">
                                    <option value="" data-i18n="acm.accountaddress.selectUseType">请选择用途类型</option>
                                    <option value="DS" data-i18n="acm.accountaddress.useTypeDS">收款</option>
                                    <option value="DC" data-i18n="acm.accountaddress.useTypeDC">充值</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="qrcodeBase64"
                                data-i18n="acm.accountaddress.qrcodeBase64">二维码Base64</label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="qrcodeBase64" name="qrcodeBase64"
                                    rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="acm.accountaddress.cancel">取消</button>
                    <button type="button" class="btn btn-primary" id="btnSave"
                        data-i18n="acm.accountaddress.save">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 查看详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="detailModalLabel" data-i18n="acm.accountaddress.detailTitle">账户地址详情</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="acm.accountaddress.id">ID</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="acm.accountaddress.vaultCode">金库编码</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailVaultCode"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="acm.accountaddress.groupCode">账户组编码</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailGroupCode"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="acm.accountaddress.accountId">账户ID</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailAccountId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="acm.accountaddress.address">地址</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailAddress"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="acm.accountaddress.network">网络</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailNetwork"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="acm.accountaddress.status">状态</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailStatus"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="acm.accountaddress.acmAcNo">对应账号</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailAcmAcNo"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="acm.accountaddress.userId">客户ID</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUserId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="acm.accountaddress.useType">用途类型</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUseType"></p>
                            </div>
                        </div>
                        <div class="form-group" id="qrcodeGroup" style="display: none;">
                            <label class="col-sm-3 control-label" data-i18n="acm.accountaddress.qrcode">二维码</label>
                            <div class="col-sm-9">
                                <img id="detailQrcode" class="img-responsive" style="max-width: 200px;" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="acm.accountaddress.createTime">创建时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailCreateTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="acm.accountaddress.updateTime">更新时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUpdateTime"></p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="acm.accountaddress.close">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var table;
        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/js/Chinese.json';
                    break;
                case 'en':
                    languageUrl = '/js/English.json';
                    break;
                default:
                    languageUrl = '/js/Chinese.json';
            }

            // 初始化DataTables
            table = $('#accountAddressTable').DataTable({
                dom: 'Blfrtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/acm/accountaddress/findAll',
                    type: 'post',
                    data: function (d) {
                        d.extraSearch = {
                            "vaultCode": $("#searchVaultCode").val() || "",
                            "address": $("#searchAddress").val() || "",
                            "network": $("#searchNetwork").val() || "",
                            "status": $("#searchStatus").val() || ""
                        };
                        return JSON.stringify(d);
                    },
                    dataSrc: function (json) {
                        return json.data || [];
                    },
                    error: function (xhr, error, thrown) {
                        console.error('DataTables AJAX error:', error, thrown);
                        toastr.error('加载数据失败，请刷新页面重试');
                    }
                },
                serverSide: true,
                searching: false,
                searchDelay: 1000,
                responsive: true,
                processing: true,
                language: {
                    url: languageUrl
                },
                columns: [
                    { data: 'id' },
                    { data: 'vaultCode' },
                    { data: 'groupCode' },
                    { data: 'accountId' },
                    { data: 'address' },
                    { data: 'network' },
                    {
                        data: 'status',
                        render: function (data) {
                            var label = '';
                            switch (data) {
                                case 'ENABLED':
                                    label = '<span class="label label-success">已启用</span>';
                                    break;
                                case 'DISABLED':
                                    label = '<span class="label label-default">未启用</span>';
                                    break;
                                case 'FROZEN':
                                    label = '<span class="label label-warning">冻结</span>';
                                    break;
                                default:
                                    label = '<span class="label label-default">' + data + '</span>';
                            }
                            return label;
                        }
                    },
                    {
                        data: 'createTime',
                        render: function (data) {
                            return data ? new Date(data).toLocaleString() : '';
                        }
                    },
                    {
                        data: null,
                        orderable: false,
                        render: function (data, type, row) {
                            return '<button type="button" class="btn btn-xs btn-info view-detail" data-id="' + row.id + '">详情</button> ';
                            buttons += '<button type="button" class="btn btn-xs btn-primary btn-edit" data-id="' + row.id + '">编辑</button> ';
                            buttons += '<button type="button" class="btn btn-xs btn-danger btn-delete" data-id="' + row.id + '">删除</button>';
                            return buttons;
                        }
                    }
                ],
                buttons: [
                    { extend: 'copyHtml5' },
                    { extend: 'csvHtml5' },
                    { extend: 'excelHtml5', title: '账户地址列表' }
                ]
            });

            // 新增按钮
            $("#btnAdd").click(function () {
                $("#addModalLabel").text("新增账户地址");
                $("#addForm")[0].reset();
                $("#addModal").modal("show");
            });

            // 保存按钮
            $("#btnSave").click(function () {
                if (!$("#addForm").valid()) {
                    toastr.error('请填写必填项');
                    return;
                }

                var formData = $("#addForm").serialize();
                $.ajax({
                    url: "/acm/accountaddress/add",
                    type: "POST",
                    data: formData,
                    success: function (res) {
                        if (res.result === "ACM00000") {
                            toastr.success('操作成功');
                            $("#addModal").modal("hide");
                            table.ajax.reload();
                        } else {
                            var errorMsg = getErrorMessage(response && response.result ? response.result : 'ACM10011');
                            showErrorMessage(errorMsg);
                        }
                    },
                    error: function (xhr, status, error) {
                        toastr.error('操作失败：' + error);
                    }
                });
            });

            // 查看详情
            $(document).on("click", ".view-detail", function () {
                var id = $(this).data("id");
                $.ajax({
                    url: "/acm/accountaddress/getDetail",
                    type: "POST",
                    data: { id: id },
                    success: function (data) {
                        $("#detailId").text(data.id || '');
                        $("#detailVaultCode").text(data.vaultCode || '');
                        $("#detailGroupCode").text(data.groupCode || '');
                        $("#detailAccountId").text(data.accountId || '');
                        $("#detailAddress").text(data.address || '');
                        $("#detailNetwork").text(data.network || '');
                        $("#detailStatus").text(data.status || '');
                        $("#detailAcmAcNo").text(data.acmAcNo || '');
                        $("#detailUserId").text(data.userId || '');
                        $("#detailUseType").text(data.useType || '');
                        $("#detailModal").modal("show");
                    },
                    error: function (xhr, status, error) {
                        toastr.error('获取详情失败：' + error);
                    }
                });
            });

            // 表单验证
            $("#addForm").validate({
                rules: {
                    vaultCode: {
                        required: true,
                        maxlength: 50
                    },
                    groupCode: {
                        required: true,
                        maxlength: 50
                    },
                    accountId: {
                        required: true,
                        number: true
                    },
                    address: {
                        required: true,
                        maxlength: 100
                    },
                    network: {
                        maxlength: 50
                    },
                    status: {
                        required: true
                    }
                },
                messages: {
                    vaultCode: {
                        required: "请输入金库编码",
                        maxlength: "金库编码不能超过50个字符"
                    },
                    groupCode: {
                        required: "请输入账户组编码",
                        maxlength: "账户组编码不能超过50个字符"
                    },
                    accountId: {
                        required: "请输入账户ID",
                        number: "请输入有效的数字"
                    },
                    address: {
                        required: "请输入地址",
                        maxlength: "地址不能超过100个字符"
                    },
                    network: {
                        maxlength: "网络不能超过50个字符"
                    },
                    status: {
                        required: "请选择状态"
                    }
                }
            });

            // 地址验证
            $("#address").blur(function () {
                var address = $(this).val();
                if (address) {
                    $.ajax({
                        url: "/acm/accountaddress/validateAddress",
                        type: "POST",
                        data: { address: address },
                        success: function (res) {
                            if (!res.valid) {
                                toastr.warning('该地址已存在');
                            }
                        }
                    });
                }
            });
        });

        // 搜索方法
        function search() {
            table.ajax.reload();
        }

        // 重置表单
        function resetForm() {
            $("#queryForm")[0].reset();
            search();
        }

        // 绑定搜索和重置按钮事件
        $("#searchBtn").click(function () {
            search();
        });

        $("#resetBtn").click(function () {
            resetForm();
        });
    </script>
</body>

</html>