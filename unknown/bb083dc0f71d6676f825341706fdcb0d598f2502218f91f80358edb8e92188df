<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="cpi.cardHead.title"></title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>
        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>
            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="cpi.cardHead.content"></h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="cpi.firstLevel"></a>
                        </li>
                        <li>
                            <a data-i18n="cpi.secondLevelCardMgr"></a>
                        </li>
                        <li>
                            <strong data-i18n="cpi.cardHead.content"></strong>
                        </li>
                    </ol>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!--查询条件-->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-4">
                                        <!--手机号-->
                                        <label class="col-sm-4 control-label" for="mblNo" data-i18n="cpi.mblNo"></label>
                                        <div class="col-sm-8">
                                            <input name="mblNo" id="mblNo" class="form-control" value=""/>
                                        </div>
                                        <!--协议生效标志-->
                                        <label class="col-sm-4 control-label" for="agrEffFlg" data-i18n="cpi.agrEffFlg"></label>
                                        <div class="col-sm-8">
                                            <select name="agrEffFlg" id="agrEffFlg" class="form-control" value="">
                                                <option data-i18n="cpi.selectByNull" selected value=""></option>
                                                <option data-i18n="cpi.agrEffFlg-W" value="W"></option>
                                                <option data-i18n="cpi.agrEffFlg-Y" value="Y"></option>
                                                <option data-i18n="cpi.agrEffFlg-N" value="N"></option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group col-sm-4">
                                        <!--用户号/商户号-->
                                        <label class="col-sm-4 control-label" for="userId" data-i18n="cpi.userId"></label>
                                        <div class="col-sm-8">
                                            <input name="userId" id="userId" class="form-control" value=""/>
                                        </div>
                                        <!--对公/对私标志-->
                                        <label class="col-sm-4 control-label" for="bnkPsnFlg" data-i18n="cpi.bnkPsnFlg"></label>
                                        <div class="col-sm-8">
                                            <select name="bnkPsnFlg" id="bnkPsnFlg" class="form-control" value="">
                                                <option data-i18n="cpi.selectByNull" selected value=""></option>
                                                <option data-i18n="cpi.bnkPsnFlg-B" value="B"></option>
                                                <option data-i18n="cpi.bnkPsnFlg-C" value="C"></option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group col-sm-4">
                                        <!--卡类型-->
                                        <label class="col-sm-4 control-label" for="crdAcTyp" data-i18n="cpi.crdAcTyp"></label>
                                        <div class="col-sm-8">
                                            <select name="crdAcTyp" id="crdAcTyp" class="form-control" value="">
                                                <option data-i18n="cpi.selectByNull" selected value=""></option>
                                                <option data-i18n="cpi.crdAcTyp-D" value="D"></option>
                                                <option data-i18n="cpi.crdAcTyp-C" value="C"></option>
                                            </select>
                                        </div>
                                        <!--签约日期-->
                                        <label class="col-sm-4 control-label" for="beginDate" data-i18n="cpi.signDt"></label>
                                        <div class="col-sm-4">
                                            <input name="beginDate" id="beginDate" class="form-control" value=""/>
                                        </div>
                                        <div class="col-sm-4">
                                            <input name="endDate" id="endDate" class="form-control" value=""/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-sm-4 col-sm-offset-1">
                                            <button type="button" id="searchBtn" onclick="search()" class="btn btn-primary" data-i18n="cpi.search"></button>
                                            <button type="button" id="viewDetailBtn" onclick="viewDetail()" class="btn btn-primary" data-toggle="modal" >查看明细</button>
                                            <button type="button" id="cardUnbindBtn" onclick="cardUnbind()" class="btn btn-primary" data-toggle="modal" >绑卡解除</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!--查看用户绑卡详细信息，以及解除绑定-->
                <div th:replace="cpt/param/card/cardModal"></div>

                <!--数据表格-->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="orderInf" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="cpi.agrNo"></th>
                                                <th data-i18n="cpi.signAgrno"></th>
                                                <th data-i18n="cpi.agrDirect"></th>
                                                <th data-i18n="cpi.crdAcTyp"></th>
                                                <th data-i18n="cpi.agrEffFlg"></th>
                                                <th data-i18n="cpi.bnkPsnFlg"></th>
                                                <th data-i18n="cpi.signDt"></th>
                                                <th data-i18n="cpi.signTm"></th>
                                                <th data-i18n="cpi.unsignDt"></th>
                                                <th data-i18n="cpi.unsignTm"></th>
                                                <th data-i18n="cpi.userId"></th>
                                                <th data-i18n="cpi.mblNo"></th>
                                                <th data-i18n="cpi.crdCorpOrg"></th>
                                                <th data-i18n="cpi.rutCorpOrg"></th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var editor;
        var table;
        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }

            i18nLoad.then(function () {
                editor = new $.fn.dataTable.Editor({
                    table: "#orderInf",
                    idSrc: 'agrNo',
                    fields: [
                        {name: "id", type: "hidden"},
                        {label: $.i18n.t("cpi.agrNo"), name: "agrNo"},
                        {label: $.i18n.t("cpi.signAgrno"), name: "signAgrno"},
                        {label: $.i18n.t("cpi.agrDirect"), name: "agrDirect"},
                        {label: $.i18n.t("cpi.crdAcTyp"), name: "crdAcTyp"},
                        {label: $.i18n.t("cpi.agrEffFlg"), name: "agrEffFlg"},
                        {label: $.i18n.t("cpi.bnkPsnFlg"), name: "bnkPsnFlg"},
                        {label: $.i18n.t("cpi.signDt"), name: "signDt"},
                        {label: $.i18n.t("cpi.signTm"), name: "signTm"},
                        {label: $.i18n.t("cpi.unsignDt"), name: "unsignDt"},
                        {label: $.i18n.t("cpi.unsignTm"), name: "unsignTm"},
                        {label: $.i18n.t("cpi.userId"), name: "userId"},
                        {label: $.i18n.t("cpi.mblNo"), name: "mblNo"},
                        {label: $.i18n.t("cpi.crdCorpOrg"), name: "crdCorpOrg"},
                        {label: $.i18n.t("cpi.rutCorpOrg"), name: "rutCorpOrg"},
                    ]
                });

                table = $('#orderInf').DataTable({
                    dom: 'Blfrtip',
                    ajax: {
                        contentType: 'application/json',
                        url: '/cpt/param/card/findCardProtList',
                        type: 'post',
                        data: function (d) {
                            d.extra_search = {
                                "mblNo" : $("#mblNo").val(),
                                "userId" : $("#userId").val(),
                                "crdAcTyp" : $("#crdAcTyp").val(),
                                "agrEffFlg" : $("#agrEffFlg").val(),
                                "bnkPsnFlg" : $("#bnkPsnFlg").val(),
                                "beginDate" : $("#beginDate").val(),
                                "endDate" : $("#endDate").val()
                            };
                            return JSON.stringify(d);
                        }
                    },
                    serverSide: true,
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    select: true,
                    buttons: [
//                        {extend: "create", editor: editor},
//                        {extend: "edit", editor: editor},
//                        {extend: "remove", editor: editor},
//                        {extend: 'copyHtml5'},
//                        {extend: 'csvHtml5'},
//                        {extend: 'excelHtml5', title: '文件名'},
                        'copy',
                        'csv',
                        'excel',
//                        'pdf',
//                        'print'
                    ],
                    columns: [{
                        data: 'agrNo'
                    },{
                        data: 'signAgrno'
                    },{
                        data: 'agrDirect',
                        render: function (data, type, row) {
                            switch (data) {
                                case "0":
                                    return $.i18n.t("cpi.agrDirect-0");
                                case "1":
                                    return $.i18n.t("cpi.agrDirect-1");
                                default:
                                    return data;
                            }
                        }
                    },{
                        data: 'crdAcTyp',
                        render: function (data, type, row) {
                            switch (data) {
                                case "D":
                                    return $.i18n.t("cpi.crdAcTyp-D");
                                case "C":
                                    return $.i18n.t("cpi.crdAcTyp-C");
                                default:
                                    return data;
                            }
                        }
                    },{
                        data: 'agrEffFlg',
                        render: function (data, type, row) {
                            switch (data) {
                                case "W":
                                    return $.i18n.t("cpi.agrEffFlg-W");
                                case "Y":
                                    return $.i18n.t("cpi.agrEffFlg-Y");
                                case "N":
                                    return $.i18n.t("cpi.agrEffFlg-N");
                                default:
                                    return data;
                            }
                        }
                    },{
                        data: 'bnkPsnFlg',
                        render: function (data, type, row) {
                            switch (data) {
                                case "B":
                                    return $.i18n.t("cpi.bnkPsnFlg-B");
                                case "C":
                                    return $.i18n.t("cpi.bnkPsnFlg-C");
                                default:
                                    return data;
                            }
                        }
                    },{
                        data: 'signDt'
                    },{
                        data: 'signTm'
                    },{
                        data: 'unsignDt'
                    },{
                        data: 'unsignTm'
                    },{
                        data: 'userId'
                    },{
                        data: 'mblNo'
                    },{
                        data: 'crdCorpOrg',
                        render: function (data, type, row) {
                            switch (data) {
                                case "ICBC":
                                    return $.i18n.t("corg.ICBC");
                                case "CMBC":
                                    return $.i18n.t("corg.CMBC");
                                case "CMB":
                                    return $.i18n.t("corg.CMB");
                                case "BEA":
                                    return $.i18n.t("corg.BEA");
                                default:
                                    return data;
                            }
                        }
                    },{
                        data: 'rutCorpOrg',
                        render: function (data, type, row) {
                            switch (data) {
                                case "ICBC":
                                    return $.i18n.t("corg.ICBC");
                                case "CMBC":
                                    return $.i18n.t("corg.CMBC");
                                case "CMB":
                                    return $.i18n.t("corg.CMB");
                                case "BEA":
                                    return $.i18n.t("corg.BEA");
                                default:
                                    return data;
                            }
                        }
                    }]
                });
            });
        });

        <!--初始化日期控件-->
        var beginTimePick = $('#beginDate').datetimepicker({
            lang:'en',
            timepicker:false,
            validateOnBlur: false,
            format:'Y-m-d ',
            formatDate:'Y-m-d',
            onChangeDate: function(dateText, inst) {
                endTimePick.datetimepicker('minDate',new Date(dateText.replace("-", "-")));
            }
        });
        var endTimePick = $('#endDate').datetimepicker({
            lang:'en',
            timepicker:false,
            validateOnBlur: false,
            format:'Y-m-d ',
            formatDate:'Y-m-d'
            // maxDate:'+1970/01/01',
        });

        //查询按钮
        function search() {
            table.ajax.reload();
        }

        //清空modal中的内容
        function clearModalValues() {
            $("#agr_eff_flg").val("");
            $("#sign_dt").val("");
            $("#sign_tm").val("");
            $("#mbl_no").val("");
            $("#crd_corp_org").val("");
            $("#agr_direct").val("");
            $("#crd_ac_typ").val("");
            $("#id_typ").val("");
            $("#id_no").val("");
            $("#crd_no").val("");
            $("#crd_usr_nm").val("");
            $("#crd_cvv2").val("");
            $("#crd_exp_dt").val("");
            $("#agr_no").val("");
        }

        //查看用户绑卡详细信息
        function viewDetail() {
            clearModalValues();
            $("#platUnbindBtn").attr("disabled", "disabled");
            $("#bothUnbindBtn").attr("disabled", "disabled");

            //获取表格中选中的单行
            var row = table.row('.selected');
            if(row.length == 0) {
                swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.shouldSelectOne"), "error");
                return;
            }

            //获取选中行的内容
            var rowData = table.row(row).data();
            var agrNo = rowData["agrNo"];
            $.ajax({
                url:"/cpt/param/card/findCardProtDetail",
                data:{
                    "agrNo": agrNo
                },
                dataType: "json",
                type: "post",
                success: function(data) {
                    if (data != null) {
                        $("#cardModal").modal("show");
                        $("#agr_eff_flg").val(convertAgrEffFlg(data.agrEffFlg));
                        $("#sign_dt").val(data.signDt);
                        $("#sign_tm").val(data.signTm);
                        $("#mbl_no").val(data.mblNo);
                        $("#crd_corp_org").val(convertCorpOrg(data.crdCorpOrg));
                        $("#agr_direct").val(data.agrDirect);
                        $("#crd_ac_typ").val(convertCrdAcTyp(data.crdAcTyp));
                        $("#crd_no").val(data.crdNo);
                        $("#crd_usr_nm").val(data.crdUsrNm);
                        $("#crd_cvv2").val(data.crdCvv2);
                        $("#crd_exp_dt").val(data.crdExpDt);
                        $("#agr_no").val(data.agr_no);
                    } else {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.detailNull"), "error");
                    }
                },
                error: function() {
                    swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.systemException"), "error");
                }
            });
        }

        //用户银行卡解绑
        function cardUnbind() {
            clearModalValues();
            $("#platUnbindBtn").removeAttr("disabled", "disabled");
            $("#bothUnbindBtn").removeAttr("disabled", "disabled");

            //获取表格中选中的单行
            var row = table.row('.selected');
            if(row.length == 0) {
                swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.shouldSelectOne"), "error");
                return;
            }

            //获取选中行的内容
            var rowData = table.row(row).data();
            var agrNo = rowData["agrNo"];
            var agrEffFlg = rowData["agrEffFlg"];
            if(agrEffFlg != "Y") {
                swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.agrEffFlgError"), "error");
                return;
            }

            //异步调起后台服务
            $.ajax({
                url:"/cpt/param/card/findCardProtDetail",
                data:{
                    "agrNo": agrNo
                },
                dataType: "json",
                type: "post",
                success: function(data) {
                    if (data != null) {
                        $("#cardModal").modal("show");
                        $("#agr_eff_flg").val(convertAgrEffFlg(data.agrEffFlg));
                        $("#sign_dt").val(data.signDt);
                        $("#sign_tm").val(data.signTm);
                        $("#mbl_no").val(data.mblNo);
                        $("#crd_corp_org").val(convertCorpOrg(data.crdCorpOrg));
                        $("#agr_direct").val(data.agrDirect);
                        $("#crd_ac_typ").val(convertCrdAcTyp(data.crdAcTyp));
                        $("#crd_no").val(data.crdNo);
                        $("#crd_usr_nm").val(data.crdUsrNm);
                        $("#crd_cvv2").val(data.crdCvv2);
                        $("#crd_exp_dt").val(data.crdExpDt);
                        $("#agr_no").val(data.agrNo);
                    } else {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.detailNull"), "error");
                    }
                },
                error: function() {
                    swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.systemException"), "error");
                }
            });
        }

        //银行卡解绑
        function unbindCard() {
            swal({
                title: $.i18n.t("cpi.unbindConfirmTitle"),
                text: $.i18n.t("cpi.unbindConfirmText"),
                type: "warning",
                showCancelButton: true,
                cancelButtonText: $.i18n.t("cpi.swal-cancel"),
                confirmButtonColor: "#DD6B55",
                confirmButtonText: $.i18n.t("cpi.swal-confirm"),
                closeOnConfirm: true
            }, function () {
                $("#cardModal").modal("hide");
                var agrNo = $("#agr_no").val();
                var agrDirect = $("#agr_direct").val();
                //异步调起后台服务
                $.ajax({
                    url:"/cpt/param/card/unbindCard",
                    data:{
                        "agrNo": agrNo,
                        "agrDirect": agrDirect
                    },
                    dataType: "json",
                    type: "post",
                    success: function(data) {
                        if (data != null) {
                            var msgCd = data.msgCd;
                            if("CPI00000" == msgCd){
                                swal($.i18n.t("cpi.swal-success"), $.i18n.t("cpi.unbindConfirmSuss"), "success");
                                search();
                            }
                        } else {
                            swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.unbindConfirmFail"), "error");
                        }
                    },
                    error: function() {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.systemException"), "error");
                    }
                });
            });
        }

        //银行名称转换
        function convertCorpOrg(data) {
            switch (data) {
                case "ICBC":
                    return $.i18n.t("corg.ICBC");
                case "CMBC":
                    return $.i18n.t("corg.CMBC");
                default:
                    return data;
            }
        }

        //生效标志转换
        function convertAgrEffFlg(data) {
            switch (data) {
                case "W":
                    return $.i18n.t("cpi.agrEffFlg-W");
                case "Y":
                    return $.i18n.t("cpi.agrEffFlg-Y");
                case "N":
                    return $.i18n.t("cpi.agrEffFlg-N");
                default:
                    return data;
            }
        }

        //银行卡类型转换
        function convertCrdAcTyp(data) {
            switch (data) {
                case "D":
                    return $.i18n.t("cpi.crdAcTyp-D");
                case "C":
                    return $.i18n.t("cpi.crdAcTyp-C");
                default:
                    return data;
            }
        }

    </script>
</body>

</html>