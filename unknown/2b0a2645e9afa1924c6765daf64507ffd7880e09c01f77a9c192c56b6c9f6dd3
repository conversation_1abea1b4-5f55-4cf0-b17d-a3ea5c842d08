package com.hisun.lemon.urm.controller;

import com.hisun.lemon.acm.dto.QueryAcBalRspDTO;
import com.hisun.lemon.cmm.client.CmmServerClient;
import com.hisun.lemon.cmm.client.SmsServerClient;
import com.hisun.lemon.cmm.dto.MessageClientSwitchReqDTO;
import com.hisun.lemon.cmm.dto.SmsCheckReqDTO;
import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.BeanUtils;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.NumberUtils;
import com.hisun.lemon.framework.annotation.LemonBody;
import com.hisun.lemon.framework.controller.BaseController;
import com.hisun.lemon.framework.data.*;
import com.hisun.lemon.framework.utils.LemonUtils;
import com.hisun.lemon.framework.utils.RandomTemplete;
import com.hisun.lemon.jcommon.phonenumber.PhoneNumberUtils;
import com.hisun.lemon.urm.common.AcmService;
import com.hisun.lemon.urm.common.SensitiveDataHandle;
import com.hisun.lemon.urm.constants.URMConstants;
import com.hisun.lemon.urm.constants.URMMessageCode;
import com.hisun.lemon.urm.dao.IBatchOpenUserDao;
import com.hisun.lemon.urm.dao.IUrmSafeInfDao;
import com.hisun.lemon.urm.dao.IUrmSafeLoginDao;
import com.hisun.lemon.urm.dao.IUrmUserBasicInfDao;
import com.hisun.lemon.urm.dto.*;
import com.hisun.lemon.urm.entity.*;
import com.hisun.lemon.urm.service.IMercOprService;
import com.hisun.lemon.urm.service.IUserBasicInfService;
import com.hisun.lemon.urm.service.IUserPasswordService;
import com.hisun.lemon.urm.service.KybCertificationService;
import com.hisun.lemon.urm.utils.KeyDataHideUtils;
import com.hisun.lemon.urm.utils.ObjectUtils;
import com.hisun.lemon.urm.utils.VerifyIdentityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.bouncycastle.util.encoders.Base64;
import org.bouncycastle.util.encoders.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.hisun.lemon.urm.utils.CipherProcessorUtils.convertRSAEncryptedPin;

/**
 * <AUTHOR>
 * @funciton UserBasicInfController
 * @description 用户基本信息管理
 * @date 7/8/2017 FRI
 * @time 3:57 PM
 */
@Api(tags = "UserBasicInfController", description = "用户基本信息管理")
@RestController
@RequestMapping(value = "/urm")
public class UserBasicInfController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(UserBasicInfController.class);
    @Autowired
    @Qualifier("redisTemplateString")
    RedisTemplate<String, String> redisTemplate;
    @Resource
    private IUserBasicInfService basicInfService;
    @Resource
    private IUrmUserBasicInfDao basicInfDao;
    @Resource
    private IUrmSafeInfDao safeInfDao;
    @Resource
    private IUrmSafeLoginDao safeLoginDao;
    @Autowired
    private SmsServerClient smsServerClient;
    @Autowired
    private CmmServerClient cmmServerClient;
    @Autowired
    private SensitiveDataHandle sensData;
    @Resource(name = "bindingTokenRandomTemplete")
    private RandomTemplete randomTemplate;
    @Resource
    private AcmService acm;
    @Resource
    private IMercOprService mercOprService;
    @Resource
    private IUserPasswordService userPasswordService;
    @Resource
    private IBatchOpenUserDao batchOpenUserDao;
    @Resource
    private KybCertificationService kybCertificationService;

    @ApiOperation(value = "用户开户", notes = "用户开户")
    @ApiResponse(code = 200, message = "用户开户")
    @PostMapping(value = "/users")
    public GenericRspDTO<NoBody> openUser(@Validated @RequestBody GenericDTO<UserRegisterDTO> userRegisterDTO) {
        String mblNo = userRegisterDTO.getBody().getMblNo();
        String chkNo = userRegisterDTO.getBody().getChkNo();
        String smsTyp = userRegisterDTO.getBody().getSmsTyp();
        String smsToken = userRegisterDTO.getBody().getSmsToken();
        String usrNation = userRegisterDTO.getBody().getUsrNation();
        String random = userRegisterDTO.getBody().getSeaRandom();
        String email = userRegisterDTO.getBody().getEmail();
        String emailNo = userRegisterDTO.getBody().getEmailNo();

         checkEmail(email, emailNo);

        UrmUserBasicInfDO basicInfDO = basicInfDao.getByLoginId(mblNo);
        if (JudgeUtils.isNotNull(basicInfDO) && JudgeUtils.equals(URMConstants.USR_OPEN, basicInfDO.getUsrSts())) {
            LemonException.throwLemonException(URMMessageCode.USR_ALREADY_REGISTERED);
        }
        if (JudgeUtils.isNotBlank(mblNo)) {
            //校验手机号码是否有效
//            if (!PhoneNumberUtils.isValidNumber(mblNo)) {
//                LemonException.throwBusinessException(URMMessageCode.ILLEGAL_MBL_NO);
//            }
            if (JudgeUtils.isBlank(usrNation)) {
                //默认为cn
                usrNation = "CN";
                //usrNation = PhoneNumberUtils.getGeocoder(mblNo);
            }
            if (JudgeUtils.isNotBlank(chkNo)) {
                GenericDTO<SmsCheckReqDTO> genericSmsReq = new GenericDTO<SmsCheckReqDTO>();
                SmsCheckReqDTO smsReqDTO = new SmsCheckReqDTO();
                smsReqDTO.setMblNo(mblNo);
                smsReqDTO.setSmsCode(chkNo);
                smsReqDTO.setType(smsTyp);
                smsReqDTO.setToken(smsToken);
                genericSmsReq.setBody(smsReqDTO);
                //             smsServerClient.smsCodeCheck(genericSmsReq);
            }
        }
        UrmUserBasicInfDO userBasicInfDO = new UrmUserBasicInfDO();
        UrmSafeInfDO safeInfDO = new UrmSafeInfDO();
        UrmCprExtInfDO cprExtInfDo = null;
        UrmSafeLoginDO safeLoginDO = null;
        BeanUtils.copyProperties(userBasicInfDO, userRegisterDTO.getBody());
        userBasicInfDO.setUsrNation(usrNation);

//        if (JudgeUtils.isNotBlank(userBasicInfDO.getIdNo())) {
//            basicInfService.checkIdNo(userBasicInfDO.getIdNo());
//        }

        BeanUtils.copyProperties(safeInfDO, userRegisterDTO.getBody());
        userBasicInfDO = ObjectUtils.objectNull(userBasicInfDO);
        safeInfDO = ObjectUtils.objectNull(safeInfDO);
//        if (JudgeUtils.isNotNull(safeInfDO)) {
//            if (JudgeUtils.notEquals(LemonUtils.getSource(), URMConstants.INTERNAL_GATE)) {
//                if (JudgeUtils.isNotBlank(safeInfDO.getLoginPwd()) || JudgeUtils.isNotBlank(safeInfDO.getPayPwd())) {
//                    String randomServer = randomTemplate.acquireOnce(URMConstants.PWD_RANDOM);
//
//                    if (JudgeUtils.isNotBlank(safeInfDO.getLoginPwd())) {
//                        String loginPwdRandom = userRegisterDTO.getBody().getLoginPwdRandom();
//                        String loginPwd = convertRSAEncryptedPin(loginPwdRandom, randomServer,
//                                safeInfDO.getLoginPwd(), URMConstants.LOG_PWD_MODE);
//                        safeInfDO.setLoginPwd(loginPwd);
//                    }
//                    if (JudgeUtils.isNotBlank(safeInfDO.getPayPwd())) {
//                        String payPwdRandom = userRegisterDTO.getBody().getPayPwdRandom();
//                        String payPwd = convertRSAEncryptedPin(payPwdRandom, randomServer, safeInfDO.getPayPwd(),
//                                URMConstants.PAY_PWD_MODE);
//                        safeInfDO.setPayPwd(payPwd);
//                    }
//                }
//            } else {
//                if(random != null && !"".equals(random)){
//                    if (JudgeUtils.isNotBlank(safeInfDO.getPayPwd())) {
//                        String payPwdRandom = userRegisterDTO.getBody().getPayPwdRandom();
//                        String payPwd = convertRSAEncryptedPinNew(payPwdRandom, random, safeInfDO.getPayPwd(), URMConstants.PAY_PWD_MODE);
//                        safeInfDO.setPayPwd(payPwd);
//                    }
//                }else{
//                    String payPwd = userRegisterDTO.getBody().getPayPwd();
//                    if (!NumberUtils.isDigits(payPwd) || payPwd.length() != 6) {
//                        LemonException.throwLemonException(URMMessageCode.PIN_MODE_WRONG);
//                    }
//                }
//
//            }
//        }
        safeInfDO.setLoginPwd(safeInfDO.getLoginPwd());
        safeInfDO.setPayPwd(safeInfDO.getPayPwd());

        if (userBasicInfDO == null) {
            userBasicInfDO = new UrmUserBasicInfDO();
        }
        userBasicInfDO.setUsrLvl(URMConstants.ORDINARY_USER);
        this.basicInfService.openUser(userBasicInfDO, safeInfDO, cprExtInfDo, safeLoginDO, random);
        return GenericRspDTO.newSuccessInstance();
    }

    private void checkEmail(String email, String emailNo) {
        String redisRandomKey = "email:random:1:" + email;
        String redisEmailNo = redisTemplate.opsForValue().get(redisRandomKey);
        if (!emailNo.equals(redisEmailNo)) {
            LemonException.throwBusinessException(URMMessageCode.EMAIL_AUTH_FAIL);
        }
        //移除验证码
        redisTemplate.delete(redisRandomKey);

    }

    @ApiOperation(value = "商户开户", notes = "商户开户")
    @ApiResponse(code = 200, message = "商户开户")
    @PostMapping(value = "/merusers")
    public GenericRspDTO<String> openMerUser(@Validated @RequestBody GenericDTO<MerRegisterDTO> merRegisterDTO) {
        String mblNo = merRegisterDTO.getBody().getMblNo();
        String loginId = merRegisterDTO.getBody().getLoginId();
        UrmSafeInfDO safeInfDO = safeInfDao.get(loginId);
        if (JudgeUtils.isNotNull(safeInfDO) && JudgeUtils.equals(URMConstants.SAFE_STS_EFF, safeInfDO.getSafeSts())) {
            LemonException.throwLemonException(URMMessageCode.LOGID_ALREADY_USED);
        }
        if (JudgeUtils.isNotBlank(mblNo)) {
            //校验手机号码是否有效
//            if (!PhoneNumberUtils.isValidNumber(mblNo)) {
//                LemonException.throwBusinessException(URMMessageCode.ILLEGAL_MBL_NO);
//            }
        }
        UrmUserBasicInfDO userBasicInfDO = new UrmUserBasicInfDO();
        String usrLvl = URMConstants.ENTERPRISE_BUSINESS;
        userBasicInfDO.setUsrLvl(usrLvl);
        UrmCprExtInfDO cprExtInfDo = new UrmCprExtInfDO();
        UrmSafeInfDO safeInfDO1 = new UrmSafeInfDO();
        UrmSafeLoginDO safeLoginDO = new UrmSafeLoginDO();
        BeanUtils.copyProperties(cprExtInfDo, merRegisterDTO.getBody());
        BeanUtils.copyProperties(safeInfDO1, merRegisterDTO.getBody());
        BeanUtils.copyProperties(safeLoginDO, merRegisterDTO.getBody());
        userBasicInfDO = ObjectUtils.objectNull(userBasicInfDO);
        cprExtInfDo = ObjectUtils.objectNull(cprExtInfDo);
        safeInfDO1 = ObjectUtils.objectNull(safeInfDO1);
        safeLoginDO = ObjectUtils.objectNull(safeLoginDO);
        if (JudgeUtils.isNull(cprExtInfDo)) {
            LemonException.throwLemonException(URMMessageCode.MER_INF_IS_NULL);
        }
        String userId = this.basicInfService.openUser(userBasicInfDO, safeInfDO1, cprExtInfDo, safeLoginDO, "");
        return GenericRspDTO.newSuccessInstance(userId);
    }

    @ApiOperation(value = "用户注销", notes = "cancelUser")
    @ApiResponse(code = 200, message = "用户注销")
    @DeleteMapping(value = "/users/{userId}")
    public GenericDTO<NoBody> cancelUser(@PathVariable("userId") String userId, GenericDTO<NoBody> genericDTO) {
        this.basicInfService.cancelUser(userId);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "用户信息查询", notes = "queryUser")
    @ApiResponse(code = 200, message = "用户信息查询")
    @GetMapping(value = "/users/{userId}")
    public GenericRspDTO<UserBasicInfDTO> queryUser(@PathVariable("userId") String userId, GenericDTO<NoBody> genericDTO) {
        if (JudgeUtils.isBlank(userId)) {
            userId = LemonUtils.getUserId();
        }
        UserBasicInfDTO userBasicInfDTO = new UserBasicInfDTO();
        Map<String, Object> userInfObj = this.basicInfService.queryUser(userId);
        UrmUserBasicInfDO userBasicInfDO = (UrmUserBasicInfDO) userInfObj.get("basicInf");
        BeanUtils.copyProperties(userBasicInfDTO, userBasicInfDO);
        UrmCprExtInfDO cprExtInfDO = (UrmCprExtInfDO) userInfObj.get("cprExtInf");
        if (JudgeUtils.isNotNull(cprExtInfDO)) {
            BeanUtils.copyProperties(userBasicInfDTO, cprExtInfDO);
        }
        return GenericRspDTO.newSuccessInstance(userBasicInfDTO);
    }

    @ApiOperation(value = "通过登录ID查询用户信息", notes = "queryUserByLoginId")
    @ApiResponse(code = 200, message = "用户信息查询")
    @GetMapping(value = "/users/loginId/{loginId}")
    public GenericRspDTO<UserBasicInfDTO> queryUserByLoginId(@PathVariable("loginId") String loginId,
                                                             GenericDTO<NoBody> genericDTO) {
        UserBasicInfDTO userBasicInfDTO = new UserBasicInfDTO();
        Map<String, Object> userInfObj = this.basicInfService.queryUserByLoginId(loginId);
        UrmUserBasicInfDO userBasicInfDO = (UrmUserBasicInfDO) userInfObj.get("basicInf");
        BeanUtils.copyProperties(userBasicInfDTO, userBasicInfDO);
        UrmSafeLoginDO safeLoginDO = (UrmSafeLoginDO) userInfObj.get("loginInf");
        UrmSafeInfDO safeInfDO = (UrmSafeInfDO) userInfObj.get("safeInf");
        UrmCprExtInfDO cprExtInfDO = (UrmCprExtInfDO) userInfObj.get("cprExtInf");
        if (JudgeUtils.isNotNull(cprExtInfDO)) {
            BeanUtils.copyProperties(userBasicInfDTO, cprExtInfDO);
        }
        if (JudgeUtils.isNotNull(safeLoginDO)) {
            BeanUtils.copyProperties(userBasicInfDTO, safeLoginDO);
        }
        if (JudgeUtils.isNotNull(safeInfDO)) {
            BeanUtils.copyProperties(userBasicInfDTO, safeInfDO);
        }
        return GenericRspDTO.newSuccessInstance(userBasicInfDTO);
    }

    //补全信息
    @ApiOperation(value = "补全实名信息", notes = "completeRealName")
    @ApiResponse(code = 200, message = "补全实名信息")
    @PostMapping(value = "/users/realname")
    public GenericRspDTO<NoBody> completeRealName(@Validated @RequestBody GenericDTO<UserRealNameDTO> realNameDTO) {
        UrmUserBasicInfDO basicInfDO = new UrmUserBasicInfDO();
        String loginId = realNameDTO.getBody().getLoginId();
        String userId = realNameDTO.getBody().getUserId();
        if (JudgeUtils.isBlank(userId)) {
            userId = realNameDTO.getUserId();
        }
        if (JudgeUtils.isNotBlank(loginId)) {
            UrmSafeInfDO safeInfDO = safeInfDao.get(loginId);
            if (JudgeUtils.isNull(safeInfDO) || JudgeUtils.notEquals(userId, safeInfDO.getUserId())) {
                LemonException.throwLemonException(URMMessageCode.USR_NOT_EXIST);
            } else {
                userId = safeInfDO.getUserId();
            }
        }
        String usrCountry = realNameDTO.getBody().getUsrCountry();
        if (JudgeUtils.isBlank(usrCountry)) {
            LemonException.throwLemonException(URMMessageCode.ILLEGAL_ID_INF);
        }
        if (!VerifyIdentityUtils.verifyIdentity(realNameDTO.getBody())) {
            LemonException.throwLemonException(URMMessageCode.ILLEGAL_ID_INF);
        }
        BeanUtils.copyProperties(basicInfDO, realNameDTO.getBody());
        String idNo = basicInfDO.getIdNo();
        String idNoEnc = "";
        try {
            idNoEnc = sensData.encryptSensitiveData(idNo);
        } catch (Exception e) {
            LemonException.throwLemonException(e);
        }

        int count = basicInfDao.countIdNo(idNoEnc);
        int maxSameIdNo = URMConstants.MAX_SAME_ID_NO;
        if (count >= maxSameIdNo) {
            LemonException.throwLemonException(URMMessageCode.ID_EXCEED_MAX_NUM);
        }

        basicInfDO.setUserId(userId);
        basicInfDO.setIdNoHid(KeyDataHideUtils.idNoHide(idNo));
        basicInfDO.setUsrNmHid(basicInfDO.getUsrNm());
        basicInfDO.setIdNo(idNoEnc);
        basicInfDao.update(basicInfDO);
        return GenericRspDTO.newSuccessInstance();
    }

    //登录头像，昵称修改
    @ApiOperation(value = "修改登录信息", notes = "updateLogInf")
    @ApiResponse(code = 200, message = "修改登录信息")
    @PostMapping(value = "/users/loginf")
    public GenericRspDTO<NoBody> updateLogInf(@Validated @RequestBody GenericDTO<UserLogInfDTO> userLogInfDTO) {
        //更新用户头像信息和昵称信息
        String avatarPath = userLogInfDTO.getBody().getAvatarPath();
        String loginId = userLogInfDTO.getBody().getLoginId();
        String displayNm = userLogInfDTO.getBody().getDisplayNm();
        if (JudgeUtils.isBlank(loginId)) {
            LemonException.throwLemonException(URMMessageCode.USR_NOT_EXIST);
        }
        if (JudgeUtils.isNotBlank(displayNm) || JudgeUtils.isNotBlank(avatarPath)) {
            UrmSafeLoginDO safeLoginDO = new UrmSafeLoginDO();
            safeLoginDO.setLoginId(loginId);
            safeLoginDO.setDisplayNm(displayNm);
            safeLoginDO.setAvatarPath(avatarPath);
            safeLoginDao.update(safeLoginDO);
        }
        return GenericRspDTO.newSuccessInstance();
    }

    //升级实名
    @ApiOperation(value = "升级实名信息", notes = "upgradeRealName")
    @ApiResponse(code = 200, message = "升级实名信息")
    @PutMapping(value = "/users/realname")
    public GenericRspDTO<NoBody> upgradeRealName(@Validated @RequestBody GenericDTO<UserRealNameDTO> realNameDTO) {
        UrmUserBasicInfDO basicInfDO = new UrmUserBasicInfDO();
        String userId = realNameDTO.getBody().getUserId();
        if (JudgeUtils.isBlank(userId)) {
            userId = LemonUtils.getUserId();
        }
        if (JudgeUtils.isBlank(userId)) {
            LemonException.throwLemonException(URMMessageCode.USR_NOT_EXIST);
        }
        BeanUtils.copyProperties(basicInfDO, realNameDTO.getBody());
        basicInfDO.setUserId(userId);
        basicInfService.completeRealName(basicInfDO);
        return GenericRspDTO.newSuccessInstance();
    }

    //商户交易权限查询
    @ApiOperation(value = "商户交易权限查询", notes = "queryTradingPrivilege")
    @ApiResponse(code = 200, message = "商户交易权限查询")
    @GetMapping(value = "/users/tradingprivilege")
    public GenericRspDTO<CprItfAuthDTO> queryTradingPrivilege(@Validated @RequestBody GenericDTO<CprItfAuthDTO>
                                                                      cprItfAuthDTO) {
        String userId = cprItfAuthDTO.getBody().getUserId();
        if (JudgeUtils.isBlank(userId)) {
            LemonException.throwLemonException(URMMessageCode.PARAM_IS_NULL);
        }
        String itfNm = cprItfAuthDTO.getBody().getItfNm();
        String version = cprItfAuthDTO.getBody().getVersion();
        UrmCprItfAuthDO cprItfAuthDO = basicInfService.queryTradingPrivilege(userId, itfNm, version);
        CprItfAuthDTO itfAuthDTO = null;
        if (JudgeUtils.isNotNull(cprItfAuthDO)) {
            itfAuthDTO = new CprItfAuthDTO();
            BeanUtils.copyProperties(itfAuthDTO, cprItfAuthDO);
        }
        return GenericRspDTO.newSuccessInstance(itfAuthDTO);
    }

    //查询用户余额信息
    @ApiOperation(value = "查询用户余额", notes = "queryUserBal")
    @ApiResponse(code = 200, message = "查询用户余额信息")
    @GetMapping(value = "/users/bal/{loginId}")
    public GenericRspDTO<List<QueryAcBalRspDTO>> queryUserBal(@Validated @PathVariable("loginId") String loginId,
                                                              GenericDTO<NoBody> genericDTO) {
        Map<String, Object> userInf = basicInfService.queryUserByLoginId(loginId);
        UrmUserBasicInfDO basicInfDO = (UrmUserBasicInfDO) userInf.get("basicInf");
        String userId = basicInfDO.getUserId();
        return GenericRspDTO.newSuccessInstance(acm.queryAcBal(userId));
    }

    //密保问题查询
    @ApiOperation(value = "查询密保问题", notes = "querySafeQues")
    @ApiResponse(code = 200, message = "查询密保问题")
    @GetMapping(value = "/users/safeques/{loginId}")
    public GenericRspDTO<String> querySafeQues(@Validated @PathVariable("loginId") String loginId,
                                               GenericDTO<NoBody> genericDTO) {
        UrmSafeInfDO safeInfDO = safeInfDao.get(loginId);
        String safeQues1 = safeInfDO.getSafeQues1();
        try {
            if (JudgeUtils.isNotBlank(safeQues1)) {
                safeQues1 = sensData.decryptSensitiveData(safeQues1);
            }
        } catch (Exception e) {
            LemonException.throwLemonException(e);
        }
        return GenericRspDTO.newSuccessInstance(safeQues1);
    }

    //所有商户ID查询
    @ApiOperation(value = "所有商户ID", notes = "queryCrpUser")
    @ApiResponse(code = 200, message = "所有商户ID")
    @GetMapping(value = "/users/crpusers")
    public GenericRspDTO<List<String>> queryCrpUser(GenericDTO<NoBody> genericDTO) {
        return GenericRspDTO.newSuccessInstance(this.basicInfDao.getCrpUser());
    }

    //查询用户类型
    @ApiOperation(value = "查询用户类型", notes = "qeryUserType")
    @ApiResponse(code = 200, message = "查询用户类型")
    @GetMapping(value = "/users/usertype/{loginId}")
    public GenericRspDTO<String> queryUserType(@Validated @PathVariable("loginId") String loginId,
                                               GenericDTO<NoBody> genericDTO) {
        if (JudgeUtils.isBlank(loginId)) {
            LemonException.throwLemonException(URMMessageCode.ILLEGAL_MBL_NO);
        }
        String userType = "N";
        UrmSafeInfDO safeInfDO = safeInfDao.get(loginId);
        if (JudgeUtils.isNull(safeInfDO)) {
            LemonException.throwLemonException(URMMessageCode.USR_NOT_EXIST);
        }
        String loginPwd = safeInfDO.getLoginPwd();
        if (JudgeUtils.isBlank(loginPwd)) {
            userType = "O";
        }
        return GenericRspDTO.newSuccessInstance(userType);
    }

    //查询用户类型 2.0
    @ApiOperation(value = "查询用户类型 2.0", notes = "查询用户类型 2.0")
    @ApiResponse(code = 200, message = "返回用户预登录信息")
    @PostMapping(value = "/users/usertype")
    public GenericRspDTO<UserPreLoginRspDTO> queryUserType2(@Validated @RequestBody GenericDTO<UserPreLoginReqDTO>
                                                                    userPreLoginReqGenDTO) {
        UserPreLoginRspDTO userPreLoginRspDTO = new UserPreLoginRspDTO();
        String loginId = userPreLoginReqGenDTO.getBody().getLoginId();
        String clientId = userPreLoginReqGenDTO.getBody().getClientId();
        boolean flg = true;

        //特殊处理
        String[] tmp = clientId.split("@");
        if (tmp.length == 2) {
            clientId = tmp[0];
            flg = false;
        }

        String userType = "N";
        String isHandPwd = "0";
        if (JudgeUtils.isBlank(loginId)) {
            LemonException.throwLemonException(URMMessageCode.ILLEGAL_MBL_NO);
        }

        UrmSafeInfDO safeInfDO = safeInfDao.get(loginId);
        if (JudgeUtils.isNull(safeInfDO)) {
            LemonException.throwLemonException(URMMessageCode.USR_NOT_EXIST);
        }
        String loginPwd = safeInfDO.getLoginPwd();
        if (JudgeUtils.isBlank(loginPwd)) {
            userType = "O";
        }
        userPreLoginRspDTO.setUserType(userType);
        String handflg = safeInfDO.getHandFlg();
        if (handflg == null || URMConstants.HAND_FLG_N.equals(handflg) || URMConstants.HAND_FLG_C.equals(handflg)) {
            isHandPwd = "1";
            userPreLoginRspDTO.setIsHandPwd(isHandPwd);
            return GenericRspDTO.newSuccessInstance(userPreLoginRspDTO);
        } else {
            GenericDTO genericDTO = new GenericDTO();
            MessageClientSwitchReqDTO messageClientSwitchReqDTO = new MessageClientSwitchReqDTO();
            messageClientSwitchReqDTO.setClientId(clientId);
            messageClientSwitchReqDTO.setUserId(safeInfDO.getUserId());
            genericDTO.setBody(messageClientSwitchReqDTO);
            //true 为未切换设备
            GenericRspDTO<Boolean> genericRspDTO = cmmServerClient.messageClientSwitch(genericDTO);
            Boolean rs = genericRspDTO.getBody();


            if (rs && flg) {
                //检查用户是否30天未登录，清空手势密码
                Boolean checkRs = userPasswordService.checkHandPwdValid(safeInfDO);
                //true 说明用户30 天未登录，且设置了手势密码，被清空
                if (checkRs) {
                    isHandPwd = "1";
                    //userPreLoginRspDTO.setIsHandPwd(isHandPwd);
                    //return GenericRspDTO.newSuccessInstance(userPreLoginRspDTO);
                }
            } else if (rs && !flg) {
                basicInfService.cleanHandPw(safeInfDO.getUserId());
                isHandPwd = "1";
            } else {
                isHandPwd = "1";
            }
        }
        userPreLoginRspDTO.setIsHandPwd(isHandPwd);
        return GenericRspDTO.newSuccessInstance(userPreLoginRspDTO);
    }

    //随机数
    @ApiOperation(value = "获取随机数", notes = "getRandomNumber")
    @ApiResponse(code = 200, message = "获取随机数")
    @GetMapping(value = "/random")
    public GenericRspDTO<String> getRandomNumber() {
        String random = randomTemplate.apply(URMConstants.PWD_RANDOM, URMConstants.RANDOM_EFF_TIME, RandomTemplete.RandomType
                .NUMERIC, URMConstants.RANDOM_LENGTH);
        return GenericRspDTO.newSuccessInstance(random);
    }

    @ApiOperation(value = "批量IGW用户开户", notes = "batchOpenIGWUser")
    @ApiResponse(code = 200, message = "批量IGW用户开户")
    @PostMapping(value = "/users/batchopen")
    public GenericRspDTO<NoBody> batchOpenIGWUser(@Validated @RequestBody GenericDTO<UserRegisterDTO> userRegisterDTO) {
        LemonData lemonData = LemonHolder.getLemonData();
        lemonData.setSource(URMConstants.INTERNAL_GATE);
        LemonHolder.setLemonData(lemonData);
        logger.info(LemonHolder.getLemonData().toString());
        String mblNo = userRegisterDTO.getBody().getMblNo();
        String usrNation = userRegisterDTO.getBody().getUsrNation();
        UrmUserBasicInfDO basicInfDO = basicInfDao.getByLoginId(mblNo);
        if (JudgeUtils.isNotNull(basicInfDO) && JudgeUtils.equals(URMConstants.USR_OPEN, basicInfDO.getUsrSts())) {
            LemonException.throwLemonException(URMMessageCode.USR_ALREADY_REGISTERED);
        }
        if (JudgeUtils.isNotBlank(mblNo)) {
            //校验手机号码是否有效
            if (!PhoneNumberUtils.isValidNumber(mblNo)) {
                LemonException.throwBusinessException(URMMessageCode.ILLEGAL_MBL_NO);
            }
            if (JudgeUtils.isBlank(usrNation)) {
                usrNation = PhoneNumberUtils.getGeocoder(mblNo);
            }
        }
        UrmUserBasicInfDO userBasicInfDO = new UrmUserBasicInfDO();
        UrmSafeInfDO safeInfDO = new UrmSafeInfDO();
        BeanUtils.copyProperties(userBasicInfDO, userRegisterDTO.getBody());
        userBasicInfDO.setUsrNation(usrNation);
        BeanUtils.copyProperties(safeInfDO, userRegisterDTO.getBody());
        userBasicInfDO = ObjectUtils.objectNull(userBasicInfDO);
        safeInfDO = ObjectUtils.objectNull(safeInfDO);
        String payPwd = userRegisterDTO.getBody().getPayPwd();
        if (!NumberUtils.isDigits(payPwd) || payPwd.length() != 6) {
            LemonException.throwLemonException(URMMessageCode.PIN_MODE_WRONG);
        }
        userBasicInfDO.setUsrLvl(URMConstants.ORDINARY_USER);
        this.basicInfService.openUser(userBasicInfDO, safeInfDO, null, null, "");
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "添加商户操作员", notes = "添加商户操作员")
    @ApiResponse(code = 200, message = "添加商户操作员")
    @PostMapping(value = "/merc/opr")
    public GenericRspDTO<NoBody> addMercOpr(@Validated @RequestBody GenericDTO<MercOprDTO> genericDTO) {
        String userId = genericDTO.getUserId();
        MercOprDTO mercOprDTO = genericDTO.getBody();

        String loginPwd = mercOprDTO.getLoginPwd();
        if (JudgeUtils.isNotNull(loginPwd)) {
            logger.debug("loginPwd: " + loginPwd);
            String loginPwdRandom = mercOprDTO.getLoginPwdRandom();
            int pwdLength = Hex.toHexString(Base64.decode(loginPwd)).length();
            String randomServer = randomTemplate.acquireOnce(URMConstants.PWD_RANDOM);
            if (JudgeUtils.equals(pwdLength, URMConstants.NEW_USER_PWD)) {
                loginPwd = convertRSAEncryptedPin(loginPwdRandom, randomServer, loginPwd, URMConstants.LOG_PWD_MODE);
            } else {
                LemonException.throwLemonException(URMMessageCode.NO_EFFECTIVE_PWD);
            }
        } else {
            LemonException.throwLemonException(URMMessageCode.PARAM_IS_NULL);
        }

        UrmSafeLoginDO urmSafeLoginDO = new UrmSafeLoginDO();
        BeanUtils.copyProperties(urmSafeLoginDO, mercOprDTO);

        UrmSafeInfDO urmSafeInfDO = new UrmSafeInfDO();
        BeanUtils.copyProperties(urmSafeInfDO, mercOprDTO);
        urmSafeInfDO.setUserId(userId);
        urmSafeInfDO.setLoginPwd(loginPwd);
        urmSafeInfDO.setMblNo(mercOprDTO.getMblNO());

        try {
            mercOprService.addMercOpr(urmSafeLoginDO, urmSafeInfDO);
        } catch (LemonException e) {
            return GenericRspDTO.newInstance(e.getMsgCd(), e.getMsgInfo());
        }
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "修改商户操作员", notes = "修改商户操作员")
    @ApiResponse(code = 200, message = "修改商户操作员")
    @PutMapping(value = "/merc/opr")
    public GenericRspDTO<NoBody> modifyMercOpr(@Validated @RequestBody GenericDTO<MercOprModifyDTO> genericDTO) {
        MercOprModifyDTO mercOprDTO = genericDTO.getBody();
        try {
            mercOprService.modifyOprInfo(mercOprDTO.getLoginId(), mercOprDTO.getMblNO(), mercOprDTO.getDisplayNm());
        } catch (LemonException e) {
            return GenericRspDTO.newInstance(e.getMsgCd(), e.getMsgInfo());
        }
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "删除商户操作员", notes = "删除商户操作员")
    @ApiResponse(code = 200, message = "删除商户操作员")
    @DeleteMapping(value = "/merc/opr")
    public GenericRspDTO<NoBody> deleteMercOpr(@Validated @RequestBody GenericDTO<MercOprAuthDTO> genericDTO) {
        String userId = genericDTO.getUserId();
        String loginId = genericDTO.getBody().getLoginId();
        try {
            mercOprService.deleteMercOpr(loginId, userId);
        } catch (LemonException e) {
            return GenericRspDTO.newInstance(e.getMsgCd(), e.getMsgInfo());
        }
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "查询商户操作员列表", notes = "查询商户操作员列表")
    @ApiResponse(code = 200, message = "查询商户操作员列表")
    @PostMapping(value = "/merc/opr/list")
    public GenericRspDTO<MercOprInfoDTO> queryMercOpr(@Validated @RequestBody GenericDTO<MercOprInfoListDTO> genericDTO) {
        String userId = genericDTO.getUserId();
        MercOprInfoListDTO dto = genericDTO.getBody();
        String displayNm = dto.getDisplayNm();
        String loginId = dto.getLoginId();
        int pageNum = dto.getPageNum();
        int pageSize = dto.getPageSize();

        if (JudgeUtils.isNull(pageNum)) {
            pageNum = 0;
        }
        if (JudgeUtils.isNull(pageSize)) {
            pageSize = 8;
        }

        List<MercOprInfoListDTO> result = new ArrayList<>();
        GenericRspDTO<MercOprInfoDTO> genericRspDTO = null;
        MercOprInfoDTO mercOprInfoDTO = null;
        try {
            List<MercOprInfoDO> list = mercOprService.queryAll(userId, displayNm, loginId, URMConstants.MER_OPE_TYP, pageNum, pageSize);
            for (MercOprInfoDO mercOprInfoDO : list) {
                MercOprInfoListDTO mercOprInfoListDTO = new MercOprInfoListDTO();
                BeanUtils.copyProperties(mercOprInfoListDTO, mercOprInfoDO);
                result.add(mercOprInfoListDTO);
            }
            int totNum = mercOprService.queryListSize(userId);
            mercOprInfoDTO = new MercOprInfoDTO();
            mercOprInfoDTO.setMercOprInfoListDTOList(result);
            mercOprInfoDTO.setTotNum(totNum);
            genericRspDTO = GenericRspDTO.newSuccessInstance(mercOprInfoDTO);
        } catch (LemonException e) {
            logger.error(e.getMsgCd());
            genericRspDTO = GenericRspDTO.newInstance(e.getMsgCd(), mercOprInfoDTO);
            return genericRspDTO;
        }
        return genericRspDTO;
    }

    //供app补全姓名与证件，但不改变实名标志
    @ApiOperation(value = "供app补全姓名与证件，但不改变实名标志", notes = "upgradeInformation")
    @ApiResponse(code = 200, message = "Success")
    @PutMapping(value = "/users/information")
    public GenericRspDTO<UserBasicInfDTO> upgradeInformation(@Validated @RequestBody GenericDTO<UserInformationDTO> genericDTO) {
        UrmUserBasicInfDO basicInfDO = new UrmUserBasicInfDO();
        String userId = genericDTO.getUserId();
        if (JudgeUtils.isBlank(userId)) {
            LemonException.throwLemonException(URMMessageCode.USR_NOT_EXIST);
        }
        BeanUtils.copyProperties(basicInfDO, genericDTO.getBody());
        basicInfDO.setUserId(userId);
        basicInfService.upgradeInformation(basicInfDO);

        UserBasicInfDTO userBasicInfDTO = new UserBasicInfDTO();
        Map<String, Object> userInfObj = this.basicInfService.queryUser(userId);
        UrmUserBasicInfDO userBasicInfDO = (UrmUserBasicInfDO) userInfObj.get("basicInf");
        BeanUtils.copyProperties(userBasicInfDTO, userBasicInfDO);
        UrmCprExtInfDO cprExtInfDO = (UrmCprExtInfDO) userInfObj.get("cprExtInf");
        if (JudgeUtils.isNotNull(cprExtInfDO)) {
            BeanUtils.copyProperties(userBasicInfDTO, cprExtInfDO);
        }
        return GenericRspDTO.newSuccessInstance(userBasicInfDTO);
    }

    @ApiOperation(value = "查询操作员权限", notes = "mercOprAuthority")
    @ApiResponse(code = 200, message = "Success")
    @GetMapping(value = "/users/authority/{loginId}")
    public GenericRspDTO<String> mercOprAuthority(@LemonBody GenericDTO genericDTO, @PathVariable("loginId") String loginId) {
        String auth;
        GenericRspDTO<String> genericRspDTO = new GenericRspDTO<>();
        try {
            auth = mercOprService.queryAuthority(loginId);
        } catch (LemonException e) {
            logger.error(e.getMsgCd());
            genericRspDTO.setMsgCd(e.getMsgCd());
            return genericRspDTO;
        }
        return GenericRspDTO.newSuccessInstance(auth);
    }

    @ApiOperation(value = "修改操作员权限", notes = "mercOprAuthority")
    @ApiResponse(code = 200, message = "Success")
    @PutMapping(value = "/users/authority")
    public GenericRspDTO<NoBody> modifyMercOprAuthority(@Validated @RequestBody GenericDTO<MercOprAuthDTO> genericDTO) {
        MercOprAuthDTO mercOprAuthDTO = genericDTO.getBody();
        try {
            MercOprAuthDo mercOprAuthDo = new MercOprAuthDo();
            BeanUtils.copyProperties(mercOprAuthDo, mercOprAuthDTO);
            mercOprService.modifyMercOprAuth(mercOprAuthDo);
        } catch (LemonException e) {
            logger.error(e.getMsgCd());
            return GenericRspDTO.newInstance(e.getMsgCd());
        }
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "IGW查询旧用户问题序号", notes = "querySafeQuesNo")
    @ApiResponse(code = 200, message = "Success")
    @GetMapping(value = "/users/ques/{loginId}")
    public GenericRspDTO<String> querySafeQuesNo(@PathVariable("loginId") String loginId, @LemonBody GenericDTO genericDTO) {
        String no = basicInfService.querySafeQuesNo(loginId);
        return GenericRspDTO.newSuccessInstance(no);
    }

    @ApiOperation(value = "批量开户", notes = "batchOpenUser")
    @ApiResponse(code = 200, message = "Success")
    @PostMapping(value = "/batch/open")
    public GenericRspDTO<NoBody> batchOpenUser(@Validated @RequestBody GenericDTO<BatchOpenUserDTO> genericDTO) {
        String loginPwd = genericDTO.getBody().getLoginPwd();
        String payPwd = genericDTO.getBody().getPayPwd();
        String ques = genericDTO.getBody().getSafeQues1();
        String ans = genericDTO.getBody().getSafeAns1();
        List<BatchOpenUserDo> list = basicInfService.batchOpenUser(loginPwd, payPwd, ques, ans);

        for (BatchOpenUserDo batchOpenUserDo : list) {
            String mblNo = batchOpenUserDo.getMblNo();
            BatchOpenUserDo result = new BatchOpenUserDo();
            result.setMblNo(mblNo);
            LemonData lemonData = LemonHolder.getLemonData();
            lemonData.setSource(URMConstants.SOURCE_BATCH_OPEN);

            try {
                loginPwd = null;

                UrmUserBasicInfDO basicInfDO = basicInfDao.getByLoginId(mblNo);
                if (JudgeUtils.isNotNull(basicInfDO) && JudgeUtils.equals(URMConstants.USR_OPEN, basicInfDO.getUsrSts())) {
                    LemonException.throwLemonException(URMMessageCode.USR_ALREADY_REGISTERED);
                }
                String usrNation = PhoneNumberUtils.getGeocoder(mblNo);
                if (JudgeUtils.isNotBlank(mblNo)) {
                    if (!PhoneNumberUtils.isValidNumber(mblNo)) {
                        LemonException.throwBusinessException(URMMessageCode.ILLEGAL_MBL_NO);
                    }
                }
                UrmUserBasicInfDO userBasicInfDO = new UrmUserBasicInfDO();
                userBasicInfDO.setUsrNation(usrNation);
                userBasicInfDO.setMblNo(mblNo);

                UrmSafeInfDO safeInfDO = new UrmSafeInfDO();
                safeInfDO.setMblNo(mblNo);
                safeInfDO.setLoginPwd(loginPwd);
                safeInfDO.setPayPwd(payPwd);
                safeInfDO.setSafeQues1(ques);
                safeInfDO.setSafeAns1(ans);

                userBasicInfDO = ObjectUtils.objectNull(userBasicInfDO);
                safeInfDO = ObjectUtils.objectNull(safeInfDO);
                userBasicInfDO.setUsrLvl(URMConstants.ORDINARY_USER);
                basicInfService.openUser(userBasicInfDO, safeInfDO, null, null, "");
            } catch (LemonException e) {
                batchOpenUserDo.setResult(e.getMsgCd());
                batchOpenUserDao.updateResult(batchOpenUserDo);
                continue;
            }
            batchOpenUserDo.setResult("2017-12-14");
            batchOpenUserDao.updateResult(batchOpenUserDo);
        }
        return GenericRspDTO.newSuccessInstance();
    }

    //获取商户管理员登录名
    @ApiOperation(value = "获取商户管理员登录名", notes = "getMercAdminName")
    @ApiResponse(code = 200, message = "获取商户管理员登录名")
    @GetMapping(value = "/merchant/{userId}")
    public GenericRspDTO<String> getMerchantAdminLoginId(@PathVariable("userId") String userId) {
        if (JudgeUtils.isBlank(userId)) {
            throw new LemonException(URMMessageCode.PARAM_IS_NULL);
        }
        List<MercOprInfoDO> list = mercOprService.queryAll(userId, null, null, URMConstants.MER_ADMIN_TYP, 0, 1);
        String loginName = list.get(0).getLoginId();
        return GenericRspDTO.newSuccessInstance(loginName);
    }

    @ApiOperation(value = "上级商户获取下属商户列表", notes = "getAffiliatedList")
    @ApiResponse(code = 200, message = "上级商户获取下属商户列表")
    @GetMapping(value = "/merchant/affiliate/{userId}")
    public GenericRspDTO<List<String>> getAffiliatedList(@LemonBody GenericDTO genericDTO, @PathVariable("userId") String userId) {
        if (JudgeUtils.isBlank(userId)) {
            throw new LemonException(URMMessageCode.PARAM_IS_NULL);
        }
        List<String> list = basicInfService.getAffiliateMerchantList(userId);
        return GenericRspDTO.newSuccessInstance(list);
    }

    @ApiOperation(value = "查询下属商户列表信息", notes = "getMercListInfo")
    @ApiResponse(code = 200, message = "查询下属商户列表信息")
    @ApiImplicitParam(name = "x-lemon-usrid", value = "用户ID", paramType = "header")
    @PostMapping(value = "/merchant/info")
    public GenericRspDTO<MercListInfDTO> getMercListInfo(@Validated @RequestBody GenericDTO<UrmMercInfReqDTO> genericDTO) {
        String userId = LemonUtils.getUserId();
        UrmMercInfReqDTO infDTO = genericDTO.getBody();
        MercListInfDTO mercListInfDTO = new MercListInfDTO();
        mercListInfDTO.setCurrPage(0);
        mercListInfDTO.setTotalNum(0);
        List<String> mercList = basicInfService.getAffiliateMerchantList(userId);
        if (JudgeUtils.isNull(mercList) || mercList.isEmpty()) {
            return GenericRspDTO.newSuccessInstance(mercListInfDTO);
        }
        if (JudgeUtils.isNull(infDTO.getPageNum())) {
            infDTO.setPageNum(0);
        }
        if (JudgeUtils.isNull(infDTO.getPageSize())) {
            infDTO.setPageNum(8);
        }
        MercListInfDTO infoLists = basicInfService.getMercInfoList(mercList, infDTO.getUserId(),
                infDTO.getMercName(), infDTO.getPageNum(), infDTO.getPageSize());
        return GenericRspDTO.newSuccessInstance(infoLists);
    }

    @ApiOperation(value = "提交kyb认证信息", notes = "submitKybInfo")
    @ApiResponse(code = 200, message = "提交kyb认证信息")
    @PostMapping(value = "/submitKybInfo")
    public GenericRspDTO<NoBody> submitKybInfo(@Validated @RequestBody GenericDTO<KybInfoDO> genericDTO) {
        String userId = LemonUtils.getUserId();
        KybInfoDO kybInfoDO = genericDTO.getBody();
        kybInfoDO.setUserId(userId);
        kybCertificationService.submitKybInfo(kybInfoDO);
        return GenericRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "查询kyb认证信息", notes = "queryKybInfo")
    @ApiResponse(code = 200, message = "查询kyb认证信息成功")
    @PostMapping(value = "/queryKybInfo")
    public GenericRspDTO<KybInfoDO> queryKybInfo() {
        return GenericRspDTO.newSuccessInstance(kybCertificationService.queryKybInfo(LemonUtils.getUserId()));
    }

}