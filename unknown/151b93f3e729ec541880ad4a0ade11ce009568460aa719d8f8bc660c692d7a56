<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="nav.csmsub.itemsub.itemproperty.title"></title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>
            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="nav.csmsub.itemsub.itemproperty.content"></h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="nav.csmmgr"></a>
                        </li>
                        <li>
                            <a data-i18n="nav.csmsub.item"></a>
                        </li>
                        <li class="active">
                            <strong data-i18n="nav.csmsub.itemsub.itemproperty.content"></strong>
                        </li>
                    </ol>
                </div>
            </div>
            
            <div class="wrapper wrapper-content animated fadeInRight">
                <!--查询条件-->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content" style="height: 120px;">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12">
                                        <!-- 资金类型 -->
                                        <label class="col-sm-2 control-label" for="itmCls" style="margin-top: 10px;" data-i18n="nav.csmsub.itemsub.itempropertysub.capTyp"></label>
                                        <div class="col-sm-2">
                                            <select name="capTyp" id="capTyp" class="form-control" value="">
	                                            <option value=""   data-i18n="nav.csmsub.itemsub.select"></option>
	                                            <option value="1"  data-i18n="nav.csmsub.itemsub.itempropertysub.capTypsub.1"></option>
	                                            <option value="8"  data-i18n="nav.csmsub.itemsub.itempropertysub.capTypsub.8"></option>
	                                        </select>
                                        </div>
                                        <!-- 科目号-->
                                        <label class="col-sm-2 control-label" for="itmNo" data-i18n="nav.csmsub.itemsub.infsub.itmNo"></label>
                                        <div class="col-sm-2">
                                            <input name="itmNo" id="itmNo" class="form-control" value=""/>
                                        </div>
                                        <div class="col-sm-2 col-sm-offset-2">
                                            <button type="button" id="searchBtn" class="btn btn-primary" onclick="search()" data-i18n="cmm.search"></button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!--数据表格-->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="itemproperty" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="nav.csmsub.itemsub.itempropertysub.capTyp"></th>
                                                <th data-i18n="nav.csmsub.itemsub.infsub.itmNo"></th>
                                                <th data-i18n="nav.csmsub.itemsub.infsub.balDrt"></th>
                                                <th data-i18n="nav.csmsub.itemsub.infsub.balOdFlg"></th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var editor;
        var table;
        var editorSelect;

        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }
            
            i18nLoad.then(function () {
                editor = new $.fn.dataTable.Editor({
                    ajax: {
                        create: {
                            type: 'POST',
                            url: '/acm/item/itemproperty/add',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            },
                            success:function(o){
                                handleErr(o);
                            },
                            error:function(e){
                            	swal($.i18n.t("nav.csmsub.itemsub.message.err.itmexist"),  "error");
                            }
                        },
                        edit: {
                            type: 'POST',
                            url: '/acm/item/itemproperty/modify',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        },
                        remove: {
                            type: 'POST',
                            url: '/acm/item/itemproperty/delete',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            },
                            success: function (data) {
                            },
                            error : function (data) {
                            },
                        }
                    },
                    table: "#itemproperty",
                    idSrc: "itmNo",
                    fields: [ {
                        label: $.i18n.t("nav.csmsub.itemsub.itempropertysub.capTyp"), 
                        name: "capTyp",
                       	type:"select", 
                        options: [
                            { label: $.i18n.t("nav.csmsub.itemsub.itempropertysub.capTypsub.1"), value: "1" },
                            { label: $.i18n.t("nav.csmsub.itemsub.itempropertysub.capTypsub.8"), value: "8" }
                        ]
                    },{
                		label: $.i18n.t("nav.csmsub.itemsub.itempropertysub.CCY"),
                        name: "ccy",
                       	type:"select", 
                        options: [
                            { label: $.i18n.t("CCY.USD"), value: "USD" }
                        ]
                    },{
                        label: $.i18n.t("nav.csmsub.itemsub.infsub.itmNo"), 
                        name: "itmNo"
                    },{
                    	label: $.i18n.t("nav.csmsub.itemsub.infsub.balDrt"), 
                        name: "balDrt",
                       	type:"select", 
                        options: [
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.balDrtsub.A"), value: "A" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.balDrtsub.B"), value: "B" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.balDrtsub.C"), value: "C" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.balDrtsub.D"), value: "D" }
                        ]
                    },{
                    	label: $.i18n.t("nav.csmsub.itemsub.infsub.balOdFlg"), 
                        name: "balOdFlg",
                       	type:"select", 
                        options: [
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.balOdFlgsub.Y"), value: "Y" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.balOdFlgsub.N"), value: "N" }
                        ]
                    },{
                    	label: $.i18n.t("nav.csmsub.itemsub.infsub.updBalFlg"), 
                        name: "updBalFlg",
                       	type:"select", 
                        options: [
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.updBalFlgsub.0"), value: "0" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.updBalFlgsub.1"), value: "1" }
                        ]
                    }
                    ],
                    i18n: {
                        create: {button: $.i18n.t("cmm.button.add"), title: $.i18n.t("cmm.button.add"), submit: $.i18n.t("cmm.button.create")},
                        edit: {button: $.i18n.t("cmm.button.modify"), title: $.i18n.t("cmm.button.modify"), submit: $.i18n.t("cmm.button.update")},
                        remove: {
                            button: $.i18n.t("cmm.button.delete"), title: $.i18n.t("cmm.button.delete"), submit: $.i18n.t("cmm.button.delete"),
                            confirm: {
                                _: $.i18n.t("cmm.button.multi-delete"),
                                1: $.i18n.t("cmm.button.single-delete")
                            }
                        }
                    }
                });
                editorSelect = new $.fn.dataTable.Editor({  
                    i18n : {  
                        edit : {  
                            title : $.i18n.t("nav.csmsub.itemsub.button.detail"),  
                            submit : $.i18n.t("nav.csmsub.itemsub.button.close"),
                           	button: $.i18n.t("nav.csmsub.itemsub.button.query")
                        }  
                    },
                    table : "#itemproperty",
                    idSrc: "itmNo",
                    fields : [ 
                	{
                        label: $.i18n.t("nav.csmsub.itemsub.itempropertysub.capTyp"), 
                        name: "capTyp",
                       	type:"select", 
                        options: [
                            { label: $.i18n.t("nav.csmsub.itemsub.itempropertysub.capTypsub.1"), value: "1" },
                            { label: $.i18n.t("nav.csmsub.itemsub.itempropertysub.capTypsub.8"), value: "8" }
                        ]
                    },{
                		label: $.i18n.t("nav.csmsub.itemsub.itempropertysub.CCY"),
                        name: "ccy",
                       	type:"select", 
                        options: [
                            { label: $.i18n.t("CCY.USD"), value: "USD" }
                        ]
                    },{
                        label: $.i18n.t("nav.csmsub.itemsub.infsub.itmNo"), 
                        name: "itmNo"
                    },{
                    	label: $.i18n.t("nav.csmsub.itemsub.infsub.balDrt"), 
                        name: "balDrt",
                       	type:"select", 
                        options: [
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.balDrtsub.A"), value: "A" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.balDrtsub.B"), value: "B" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.balDrtsub.C"), value: "C" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.balDrtsub.D"), value: "D" }
                        ]
                    },{
                    	label: $.i18n.t("nav.csmsub.itemsub.infsub.balOdFlg"), 
                        name: "balOdFlg",
                       	type:"select", 
                        options: [
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.balOdFlgsub.Y"), value: "Y" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.balOdFlgsub.N"), value: "N" }
                        ]
                    },{
                    	label: $.i18n.t("nav.csmsub.itemsub.infsub.updBalFlg"), 
                        name: "updBalFlg",
                       	type:"select", 
                        options: [
                        	{ label: $.i18n.t("nav.csmsub.itemsub.infsub.updBalFlgsub.0"), value: "0" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.updBalFlgsub.1"), value: "1" }
                        ]
                    },
                    {
                        label: $.i18n.t("nav.csmsub.itemsub.infsub.updOpr"),
                        name: "updOpr"
                    } ],  
              
                });  
                editorSelect.on( 'open', function ( e, o, action ) {
                		this.field( 'capTyp' ).disable();
                		this.field( 'itmNo' ).disable();
                		this.field( 'balDrt' ).disable();
                		this.field( 'balOdFlg' ).disable();
                		this.field( 'updBalFlg' ).disable();
                		this.field( 'updOpr' ).disable();
                		this.field( 'ccy' ).disable();
                } );
                editor.on( 'open', function ( e, o, action ) {
                	if ( action === 'edit' ) {
	            		this.field( 'capTyp' ).disable();
	            		this.field( 'ccy' ).disable();
                	}else{
                		this.field( 'capTyp' ).enable();
	            		this.field( 'ccy' ).enable();
                	}
	            } );
                editor.on( 'preSubmit', function ( e, o, action ) {
                    if ( action !== 'remove' ) {
                    	var itmNo=this.field( 'itmNo' );
                    	if ( ! itmNo.isMultiValue() ) {
                            if ( ! itmNo.val() ) {
                            	itmNo.error( $.i18n.t("nav.csmsub.itemsub.message.null.itmNo") );
                            }
                            if ( itmNo.val().length>10 ) {
                            	itmNo.error( $.i18n.t("nav.csmsub.itemsub.message.length.itmNo") );
                            }
                        }
                    	var su=checkItmNo(itmNo,action);
                        if ( this.inError()||!su) {
                            return false;
                        }
                        
                       
                    }
                } );
                
                table = $('#itemproperty').DataTable({
                    dom: "Blfrtip",
                    ajax: {
                        contentType: 'application/json',
                        url: '/acm/item/itemproperty/findAll',
                        type: 'post',
                        data: function (d) {
                            d.extra_search = {
                                "capTyp" : $("#capTyp").val(),
                                "itmNo" : $("#itmNo").val(),
                            };
                            return JSON.stringify(d);
                        }
                    },
                    serverSide: true,
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    select: true,
                    buttons: [
                        {extend: "create", editor: editor},
                        {extend: "edit", editor: editor},
                        {extend: "edit", editor: editorSelect},
                        {extend: "remove", editor: editor},
                    ],
                    columns: [{
                        data: 'capTyp',
                        render: function (data, type, row) {
                            switch (data) {
                                case "1":
                                    return $.i18n.t("nav.csmsub.itemsub.itempropertysub.capTypsub.1");
                                case "8":
                                    return $.i18n.t("nav.csmsub.itemsub.itempropertysub.capTypsub.8");
                                default:
                                    return data;
                            }
                        }
                    },{
                        data: 'itmNo'
                    },{
                        data: 'balDrt',
                        render: function (data, type, row) {
                            switch (data) {
                                case "A":
                                    return $.i18n.t("nav.csmsub.itemsub.infsub.balDrtsub.A");
                                case "B":
                                    return $.i18n.t("nav.csmsub.itemsub.infsub.balDrtsub.B");
                                case "C":
                                    return $.i18n.t("nav.csmsub.itemsub.infsub.balDrtsub.C");
                                case "D":
                                    return $.i18n.t("nav.csmsub.itemsub.infsub.balDrtsub.D");
                                default:
                                    return data;
                            }
                        }
                    },{
                        data: 'balOdFlg',
                        render: function (data, type, row) {
                            switch (data) {
                                case "Y":
                                    return $.i18n.t("nav.csmsub.itemsub.infsub.balOdFlgsub.Y");
                                case "N":
                                    return $.i18n.t("nav.csmsub.itemsub.infsub.balOdFlgsub.N");
                                default:
                                    return data;
                            }
                        }
                    }
                    ]
                });
            });
            
        });
      //检查在科目属性对照关系表中是否有相同科目号
        function checkItmNo(itmNo,action){
            var itmNoVal=itmNo.val();
            var res=true;
            $.ajax({
                url:"/acm/item/itemproperty/checkItmNo",
                type:"post",
                async: false,
                data :{
                    "itmNoVal": itmNoVal,
                },
                success:function(data) {
                    if (data != 'SUCCESS') {
                    	if(data=='ITMINFNULL'){
                    		itmNo.error( $.i18n.t("nav.csmsub.itemsub.message.err.itmNull") );
                    		res=false;
                    	}
                    	if(data=='ITMPROEXIT'&&action!='edit'){
                    		itmNo.error( $.i18n.t("nav.csmsub.itemsub.message.err.proexit") );
                    		res=false;
                    	}
                    	
                    }
                },
                error: function() {
                    swal($.i18n.t("nav.csmsub.itemsub.message.err.error"),  "error");
                    res=false;
                }
            });
            return res
        };
        <!--查询按钮-->
        function search() {
            table.ajax.reload();
        };
        
        function handleErr(o){
            if(o.code && o.message){
            }
        }
    </script>
</body>
</html>