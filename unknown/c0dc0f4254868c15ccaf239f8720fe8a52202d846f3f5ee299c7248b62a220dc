<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="nav.bussub.oprsub.smsmgr.title"></title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>
            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="nav.bussub.oprsub.smsmgr.content"></h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="nav.busmgr"></a>
                        </li>
                        <li>
                            <a data-i18n="nav.bussub.oprmgr"></a>
                        </li>
                        <li>
                            <strong data-i18n="nav.bussub.oprsub.smsmgr.content"></strong>
                        </li>
                    </ol>
                </div>
            </div>
            
            <div class="wrapper wrapper-content animated fadeInRight">
                <!--查询条件-->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="box-header">
                                    <div class="form-horizontal">
                                        <table style="width: 80%">
                                            <tr>
                                                <!-- 模版编码-->
                                                <td align="center">
                                                    <label class="control-label" data-i18n="cmm.smsTempalte.id"></label>
                                                </td>
                                                <td>
                                                    <input class="form-control" id="id" name="id" />
                                                </td>
                                                <td>&nbsp; &nbsp; </td>
                                                <!--模板内容-->
                                                <td align="center">
                                                    <label class="control-label" data-i18n="cmm.smsTempalte.content"></label>
                                                </td>
                                                <td>
                                                    <input class="form-control" id="content" name="content"/>
                                                </td>
                                                <td>&nbsp; &nbsp; </td>
                                                <!--提交日期-->
                                                <td align="center">
                                                    <label class="control-label" data-i18n="cmm.label.date"></label>
                                                </td>
                                                <td>
                                                    <input  id="beginDate" name="beginDate"/>-
                                                    <input  id="endDate" name="endDate"/>
                                                </td>

                                                <td align="center">
                                                    <button class="btn btn-primary" id="searchBtn" data-i18n="cmm.button.query" onclick="search()" ></button>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                                <hr/>
                                <div class="table-responsive">
                                    <table id="smsTempalteInf" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="cmm.smsTempalte.id"></th>
                                                <th data-i18n="cmm.smsTempalte.templateContentKh"></th>
                                                <th data-i18n="cmm.smsTempalte.templateContentCn"></th>
                                                <th data-i18n="cmm.smsTempalte.templateContentEn"></th>
                                                <th data-i18n="cmm.smsTempalte.effDate"></th>
                                                <th data-i18n="cmm.smsTempalte.expDate"></th>
                                                <th data-i18n="cmm.smsTempalte.stats"></th>
                                                <th data-i18n="cmm.smsTempalte.tmSmp"></th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var editor;
        var table;

        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }

            i18nLoad.then(function () {
                editor = new $.fn.dataTable.Editor({
                    ajax: {
                        create: {
                            type: 'POST',
                            url: '/cmm/smstempaltectrl/add',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        },
                        edit: {
                            type: 'POST',
                            url: '/cmm/smstempaltectrl/modify',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        },
                        remove: {
                            type: 'POST',
                            url: '/cmm/smstempaltectrl/delete',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        }
                    },
                    table: "#smsTempalteInf",
                    idSrc: "id",
                    fields: [ {
                            label: $.i18n.t("cmm.smsTempalte.id"), 
                            name: "id"
                        },{
                            label: $.i18n.t("cmm.smsTempalte.type"),
                            name: "type",
                            type:  "select",
                            options: [
                                { label: $.i18n.t("cmm.ddl.smsType.common"), value: "0" },
                                { label: $.i18n.t("cmm.ddl.smsType.register"), value: "1" },
                                { label: $.i18n.t("cmm.ddl.smsType.login"), value: "2" },
                                { label: $.i18n.t("cmm.ddl.smsType.resetpwd"), value: "3" },
                                { label: $.i18n.t("cmm.ddl.smsType.resetLoginPwd"), value: "4" },
                                { label: $.i18n.t("cmm.ddl.smsType.sign"), value: "5" },
                                { label: $.i18n.t("cmm.ddl.smsType.pay"), value: "6" }
                            ]
                        },{
                            label: "lvl",
                            name: "lvl",
                            type:"hidden"
                        },{
                            label: $.i18n.t("cmm.smsTempalte.replaceField"), 
                            name: "replaceField",
                            type: "textarea"
                        },{
                            label: $.i18n.t("cmm.smsTempalte.templateContentKh"), 
                            name: "templateContentKh",
                            type: "textarea"
                        },{
                            label: $.i18n.t("cmm.smsTempalte.templateContentCn"), 
                            name: "templateContentCn",
                            type: "textarea"
                        },{
                            label: $.i18n.t("cmm.smsTempalte.templateContentEn"), 
                            name: "templateContentEn",
                            type: "textarea"
                        },{
                            label: $.i18n.t("cmm.smsTempalte.effDate"), 
                            name: "effDate",
                            type: "datetime",
                            def:   function () { return new Date();}
                        },{
                            label: $.i18n.t("cmm.smsTempalte.expDate"), 
                            name: "expDate",
                            type: "datetime",
                            def:   function () { return new Date();}
                        },{
                            label: $.i18n.t("cmm.smsTempalte.stats"), 
                            name: "stats",
                            type:  "select",
                            options: [
                                { label: $.i18n.t("cmm.statsEnum.valid"), value: "1" },
                                { label: $.i18n.t("cmm.statsEnum.invalid"), value: "0" }
                                
                            ]
                        }
                    ],
                    i18n: {
                        create: {
                            button: $.i18n.t("cmm.button.add"),
                            title: $.i18n.t("cmm.button.add"),
                            submit: $.i18n.t("cmm.button.add")
                        },
                        edit: {
                            button: $.i18n.t("cmm.button.modify"),
                            title: $.i18n.t("cmm.button.modify"),
                            submit: $.i18n.t("cmm.button.modify")
                        },
                        remove: {
                            button: $.i18n.t("cmm.button.delete"),
                            title: $.i18n.t("cmm.button.delete"),
                            submit: $.i18n.t("cmm.button.delete"),
                            confirm: {
                                _: $.i18n.t("cmm.action.cofirm.delete"),
                                1: $.i18n.t("cmm.action.cofirm.delete")
                            }
                        }
                    }
                });
                
                editor.on( 'preSubmit', function ( e, o, action ) {
                    if ( action !== 'remove' ) {
                        var id = this.field( 'id' );
                        var replaceField = this.field( 'replaceField' );
                        var templateContentKh = this.field( 'templateContentKh' );
                        var templateContentCn = this.field( 'templateContentCn' );
                        var templateContentEn = this.field( 'templateContentEn' );
                        var effDate = this.field( 'effDate' );
                        var expDate = this.field( 'expDate' );
                        // Only validate user input values - different values indicate that
                        // the end user has not entered a value
                        if ( ! id.isMultiValue() ) {
                            if ( ! id.val() ) {
                                id.error( $.i18n.t("cmm.msg.sms.null.id") );
                            }
                             
                            if ( id.val().length > 8 ) {
                                id.error( $.i18n.t("cmm.msg.sms.length.id") );
                            }
                        }
                        
                        if ( ! replaceField.isMultiValue() ) {
                            if ( ! replaceField.val() ) {
                                replaceField.error( $.i18n.t("cmm.msg.sms.null.replaceField") );
                            }
                             
                            if ( replaceField.val().length > 256 ) {
                                replaceField.error( $.i18n.t("cmm.msg.sms.length.replaceField") );
                            }
                        }
                        
                        if ( ! templateContentKh.isMultiValue() ) {
                            if ( ! templateContentKh.val() ) {
                                templateContentKh.error( $.i18n.t("cmm.msg.sms.null.templateContentKh") );
                            }
                             
                            if ( templateContentKh.val().length > 1024 ) {
                                templateContentKh.error( $.i18n.t("cmm.msg.sms.length.templateContentKh") );
                            }
                        }
                        
                        if ( ! templateContentCn.isMultiValue() ) {
                            if ( ! templateContentCn.val() ) {
                                templateContentCn.error( $.i18n.t("cmm.msg.sms.null.templateContentCn") );
                            }
                             
                            if ( templateContentCn.val().length > 1024 ) {
                                templateContentCn.error( $.i18n.t("cmm.msg.sms.length.templateContentCn") );
                            }
                        }
                        
                        if ( ! templateContentEn.isMultiValue() ) {
                            if ( ! templateContentEn.val() ) {
                                templateContentEn.error( $.i18n.t("cmm.msg.sms.null.templateContentEn") );
                            }
                             
                            if ( templateContentEn.val().length > 1024 ) {
                                templateContentEn.error( $.i18n.t("cmm.msg.sms.length.templateContentEn") );
                            }
                        }
                        
                        if ( ! effDate.isMultiValue() ) {
                            if ( ! effDate.val() ) {
                                effDate.error( $.i18n.t("cmm.msg.sms.null.effDate") );
                            }
                        }
                        
                        if ( ! expDate.isMultiValue() ) {
                            if ( ! expDate.val() ) {
                                expDate.error( $.i18n.t("cmm.msg.sms.null.expDate") );
                            }
                        }
             
                        // If any error was reported, cancel the submission so it can be corrected
                        if ( this.inError() ) {
                            return false;
                        }
                    }
                } );
                
                editor.on( 'open', function ( e, o, action ) {
                    if ( action === 'edit' ) {
                        this.field( 'id' ).disable();
                    }
                    if ( action === 'create' ) {
                        this.field( 'id' ).enable();
                    }
                } );

                table = $('#smsTempalteInf').DataTable({
                    dom: "Blfrtip",
                    ajax: {
                        contentType: 'application/json',
                        url: '/cmm/smstempaltectrl/findAll',
                        type: 'post',
                        data: function (d) {
                            d.extra_search = {
                                "id" : $("#id").val(),
                                "content" : $("#content").val(),
                                "beginDate" : $("#beginDate").val(),
                                "endDate" : $("#endDate").val(),
                            };
                            return JSON.stringify(d);
                        },
                        error:function(d){
                            alert(JSON.stringify(d));
                        },
                    },
                    serverSide: true,
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    select: true,
                    buttons: [
                        {extend: "create", editor: editor},
                        {extend: "edit", editor: editor},
                        {extend: "remove", editor: editor},
                    ],
                    columns: [{
                        data: 'id'
                    },{
                        data: 'templateContentKh',
                        render: $.fn.dataTable.render.ellipsis( 10 )
                    },{
                        data: 'templateContentCn',
                        render: $.fn.dataTable.render.ellipsis( 10 )
                    },{
                        data: 'templateContentEn',
                        render: $.fn.dataTable.render.ellipsis( 10 )
                    },{
                        data: 'effDate'
                    },{
                        data: 'expDate'
                    },{
                        data: 'stats',
                        render: function (data, type, row) {
                              switch (data) {
                                  case "0":
                                      return $.i18n.t("cmm.statsEnum.invalid");
                                  case "1":
                                      return $.i18n.t("cmm.statsEnum.valid");
                                  default:
                                      return data;
                            }
                        }
                    },{
                        data: 'tmSmp'
                    }]
                });
            });
        });
        
        <!--初始化日期控件-->
        $('#beginDate').datetimepicker({
            lang:'en',
            timepicker:false,
            validateOnBlur: false,
            format:'Y-m-d',
            formatDate:'Y-m-d'
        });
        
        $('#endDate').datetimepicker({
            lang:'en',
            timepicker:false,
            validateOnBlur: false,
            format:'Y-m-d ',
            formatDate:'Y-m-d',
        });
        
        <!--查询按钮-->
        function search() {
            table.ajax.reload();
        }
        
        <!-- ellipsis插件 -->
        $.fn.dataTable.render.ellipsis = function ( cutoff, wordbreak, escapeHtml ) {
            var esc = function ( t ) {
                return t
                    .replace( /&/g, '&amp;' )
                    .replace( /</g, '&lt;' )
                    .replace( />/g, '&gt;' )
                    .replace( /"/g, '&quot;' );
            };
         
            return function ( d, type, row ) {
                // Order, search and type get the original data
                if ( type !== 'display' ) {
                    return d;
                }
         
                if ( typeof d !== 'number' && typeof d !== 'string' ) {
                    return d;
                }
         
                d = d.toString(); // cast numbers
         
                if ( d.length < cutoff ) {
                    return d;
                }
         
                var shortened = d.substr(0, cutoff-1);
         
                // Find the last white space character in the string
                if ( wordbreak ) {
                    shortened = shortened.replace(/\s([^\s]*)$/, '');
                }
         
                // Protect against uncontrolled HTML input
                if ( escapeHtml ) {
                    shortened = esc( shortened );
                }
         
                return '<span class="ellipsis" title="'+esc(d)+'">'+shortened+'&#8230;</span>';
            };
        };
    </script>
</body>
</html>