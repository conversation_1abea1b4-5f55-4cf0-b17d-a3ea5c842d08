<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="urm.mermgr.kybExamine.title">TMS | Kyb审核</title>
    <div th:replace="head"></div>
</head>

<body>
<div id="wrapper">
    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="urm.mermgr.kybExamine.content">Kyb审核</h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.busmgr">业务管理</a>
                    </li>
                    <li>
                        <a data-i18n="nav.bussub.mermgr">商户管理</a>
                    </li>
                    <li class="active">
                        <strong data-i18n="urm.mermgr.kybExamine.content">Kyb审核</strong>
                    </li>
                </ol>
            </div>
        </div>

        <div class="wrapper wrapper-content animated fadeInRight">
            <!-- 查询表单 -->
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form id="queryForm" class="form-horizontal">
                                <div class="form-group col-sm-12">
                                    <!-- 用户ID -->
                                    <label class="col-sm-2 control-label" for="userId"
                                           data-i18n="urm.mermgr.kybExamine.userId">用户ID</label>
                                    <div class="col-sm-4">
                                        <input name="userId" id="userId" class="form-control" value="" />
                                    </div>
                                    <!-- 审核状态 -->
                                    <label class="col-sm-2 control-label" for="examineStatus"
                                           data-i18n="urm.mermgr.kybExamine.examineStatus">审核状态</label>
                                    <div class="col-sm-4">
                                        <select name="examineStatus" id="examineStatus" class="form-control">
                                            <option value="" data-i18n="tam.selectAll">全部</option>
                                            <option value="00" data-i18n="urm.mermgr.kybExamine.examineWait">待审核</option>
                                            <option value="02" data-i18n="urm.mermgr.kybExamine.examinePass">审核通过</option>
                                            <option value="01" data-i18n="urm.mermgr.kybExamine.examineNoPass">审核拒绝</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-4 col-sm-offset-1">
                                        <button type="button" id="searchBtn" class="btn btn-primary"
                                                onclick="search()" data-i18n="cmm.search">查询</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据表格 -->
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <div class="table-responsive">
                                <table id="kybExamineTable"
                                       class="table table-striped table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th data-i18n="urm.mermgr.kybExamine.kybId">KYB审核ID</th>
                                        <th data-i18n="urm.mermgr.kybExamine.userId">用户ID</th>
                                        <th data-i18n="urm.mermgr.kybExamine.examineStatus">审核状态</th>
                                        <th data-i18n="urm.mermgr.kybExamine.createTime">创建时间</th>
                                        <th data-i18n="tam.operations">操作</th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>
    </div>
</div>

<!-- 详情模态框 -->
<div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="detailModalLabel" data-i18n="urm.mermgr.kybExamine.kybDetail">Kyb信息详情</h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal">
                    <div class="form-group">
                        <label class="col-sm-3 control-label" data-i18n="urm.mermgr.kybExamine.kybId">kyb审核ID</label>
                        <div class="col-sm-9">
                            <p class="form-control-static" id="detail-kybId"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label" data-i18n="urm.mermgr.kybExamine.userId">用户ID</label>
                        <div class="col-sm-9">
                            <p class="form-control-static" id="detail-userId"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label" data-i18n="urm.mermgr.kybExamine.examineStatus">审核状态</label>
                        <div class="col-sm-9">
                            <p class="form-control-static" id="detail-examineStatus"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label" data-i18n="urm.mermgr.kybExamine.createTime">创建时间</label>
                        <div class="col-sm-9">
                            <p class="form-control-static" id="detail-createTime"></p>
                        </div>
                    </div>
                    <!-- 其他KYB信息字段 -->
                    <div class="form-group">
                        <label class="col-sm-3 control-label" data-i18n="mercinfo.mercNm">商户名称</label>
                        <div class="col-sm-9">
                            <p class="form-control-static" id="detail-mercNm"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label" data-i18n="info.prinNm">法人名称</label>
                        <div class="col-sm-9">
                            <p class="form-control-static" id="detail-prinNm"></p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label" data-i18n="info.certNo">法人证件号</label>
                        <div class="col-sm-9">
                            <p class="form-control-static" id="detail-certNo"></p>
                        </div>
                    </div>
                    <!-- 初审信息 -->
                    <div class="form-group">
                        <label class="col-sm-3 control-label" data-i18n="tam.firstAuditInfo">初审信息</label>
                        <div class="col-sm-9">
                            <p class="form-control-static" id="detail-firstAuditInfo"></p>
                        </div>
                    </div>
                    <!-- 拒绝原因 -->
                    <div class="form-group">
                        <label class="col-sm-3 control-label" data-i18n="tam.rejectReason">拒绝原因</label>
                        <div class="col-sm-9">
                            <p class="form-control-static" id="detail-rejectReason"></p>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal" data-i18n="urm.mermgr.close">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 审核模态框 -->
<div class="modal fade" id="auditModal" tabindex="-1" role="dialog" aria-labelledby="auditModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="auditModalLabel" data-i18n="kyb.examineButton">Kyb审核</h4>
            </div>
            <div class="modal-body">
                <form id="auditForm" class="form-horizontal">
                    <input type="hidden" id="audit-kybId" name="kybId">

                    <div class="form-group">
                        <label class="col-sm-3 control-label" data-i18n="tam.auditResult">审核结果</label>
                        <div class="col-sm-9">
                            <select class="form-control" id="audit-result" name="examineStatus">
                                <option value="examinePass" data-i18n="kyb.examinePass">审核通过</option>
                                <option value="examineNoPass" data-i18n="kyb.examineNoPass">审核拒绝</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label" data-i18n="tam.auditOpinion">审核意见</label>
                        <div class="col-sm-9">
                                <textarea class="form-control" id="audit-opinion" name="examineOpinion"
                                          rows="3"></textarea>
                        </div>
                    </div>

                    <div class="form-group reject-reason-group" style="display:none;">
                        <label class="col-sm-3 control-label" data-i18n="tam.rejectReason">拒绝原因</label>
                        <div class="col-sm-9">
                                <textarea class="form-control" id="audit-reject-reason" name="rejectReason"
                                          rows="3"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="tam.cancel">取消</button>
                <button type="button" class="btn btn-primary" id="submitAudit" data-i18n="tam.submit">提交</button>
            </div>
        </div>
    </div>
</div>

<div th:replace="script"></div>

<!-- Page-Level Scripts -->
<script>
    var table;

    $(document).ready(function () {
        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        // 初始化draw值
        $('input[name="draw"]').remove();
        $('body').append('<input type="hidden" name="draw" value="1">');

        // 处理DataTables处理中指示器问题
        $(document).ajaxStop(function () {
            $('.dataTables_processing').hide();
        });

        // 初始化DataTables
        table = $('#kybExamineTable').DataTable({
            dom: 'Blfrtip',
            ajax: {
                contentType: 'application/x-www-form-urlencoded',
                url: '/urm/mermgr/kyb/kybList',
                type: 'post',
                data: function (d) {
                    // 更新draw值供dataFilter使用
                    $('input[name="draw"]').val(d.draw);

                    // 创建空对象用于存储参数
                    var params = {
                        pageNum: d.start / d.length + 1,
                        pageSize: d.length
                    };

                    // 只添加有值的参数
                    var userId = $('#userId').val();
                    if (userId && userId.trim() !== '') {
                        params.userId = userId;
                    }

                    var examineStatus = $('#examineStatus').val();
                    if (examineStatus && examineStatus.trim() !== '') {
                        params.examineStatus = examineStatus;
                    }

                    return params;
                },
                dataFilter: function (data) {
                    var json = JSON.parse(data);
                    console.log("接收到服务器响应:", json);

                    var returnData = {
                        draw: parseInt($('input[name="draw"]').val()) || 1,
                        recordsTotal: json.length,
                        recordsFiltered: json.length,
                        data: json
                    };

                    return JSON.stringify(returnData);
                },
                error: function (xhr, error, thrown) {
                    console.error('数据加载错误:', error);
                    toastr.error('数据加载失败');
                    $('.dataTables_processing').hide();
                }
            },
            serverSide: true,
            searchDelay: 1000,
            responsive: true,
            processing: true,
            language: {
                url: languageUrl,
                processing: "处理中...",
                paginate: {
                    first: "首页",
                    last: "末页",
                    next: "下一页",
                    previous: "上一页"
                },
                info: "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
                infoEmpty: "显示第 0 至 0 项结果，共 0 项",
                infoFiltered: "(由 _MAX_ 项结果过滤)",
                lengthMenu: "显示 _MENU_ 项结果"
            },
            pageLength: 10,
            lengthMenu: [10, 25, 50, 100],
            pagingType: "simple_numbers",
            drawCallback: function(settings) {
                $('.dataTables_processing').hide();
            },
            columns: [
                { data: 'kybId' },
                { data: 'userId' },
                {
                    data: 'examineStatus',
                    render: function (data) {
                        if (data === '00') return '新建待审核';
                        if (data === '01') return '审核拒绝';
                        if (data === '02') return '审核通过';
                        return data;
                    }
                },
                {
                    data: 'createTime',
                    render: function (data) {
                        if (!data) return '';
                        return new Date(data).toLocaleString();
                    }
                },
                {
                    data: null,
                    orderable: false,
                    render: function (data, type, row) {
                        var buttons = '<button type="button" class="btn btn-xs btn-info view-detail" userId="' + row.userId + '">详情</button>';

                        // 只有待审核状态可以进行审核操作
                        if (row.examineStatus === 'examineWait' || row.examineStatus === 'examineModify') {
                            buttons += '<button type="button" class="btn btn-xs btn-primary examine-btn" data-id="' + row.kybId + '">审核</button>';
                        }

                        return buttons;
                    }
                }
            ],
            buttons: [
                { extend: 'copyHtml5' },
                { extend: 'csvHtml5' },
                { extend: 'excelHtml5', title: 'Kyb审核列表' }
            ]
        });

        // 给表格添加绘制完成事件处理
        $('#kybExamineTable').on('draw.dt', function() {
            $('.dataTables_processing').hide();
        });

        // 添加页面长度变化事件监听
        $('#kybExamineTable').on('length.dt', function() {
            $('.dataTables_processing').hide();
        });

        // 添加页面变化事件监听
        $('#kybExamineTable').on('page.dt', function() {
            setTimeout(function() {
                $('.dataTables_processing').hide();
            }, 500);
        });

        // 查看详情
        $('#kybExamineTable').on('click', '.view-detail', function () {
            console.log($(this));
            var userId = $(this).context.getAttribute('userId')
            // 构建查询字符串
            var queryParams = 'userId=' + encodeURIComponent(userId);
            console.log("userid:",userId);
            $.ajax({
                url: '/urm/mermgr/kyb/detail?'+queryParams,
                type: 'post',
                success: function (response) {
                    var data = response.body;

                    // 填充详情数据
                    $('#detail-kybId').text(data.kybId || '');
                    $('#detail-userId').text(data.userId || '');

                    // 格式化审核状态
                    var statusText = '';
                    if (data.examineStatus === 'examineWait') statusText = '新建待审核';
                    else if (data.examineStatus === 'examinePass') statusText = '审核通过';
                    else if (data.examineStatus === 'examineNoPass') statusText = '审核拒绝';
                    else statusText = data.examineStatus || '';
                    $('#detail-examineStatus').text(statusText);

                    $('#detail-createTime').text(data.createTime ? new Date(data.createTime).toLocaleString() : '');
                    $('#detail-mercNm').text(data.mercNm || '');
                    $('#detail-prinNm').text(data.prinNm || '');
                    $('#detail-certNo').text(data.certNo || '');

                    // 初审信息
                    var firstAuditInfo = '';
                    if (data.examineUser) firstAuditInfo += '审核人: ' + data.examineUser + ' ';
                    if (data.examineTime) firstAuditInfo += '审核时间: ' + new Date(data.examineTime).toLocaleString() + ' ';
                    if (data.examineOpinion) firstAuditInfo += '审核意见: ' + data.examineOpinion;
                    $('#detail-firstAuditInfo').text(firstAuditInfo || '');

                    // 拒绝原因
                    $('#detail-rejectReason').text(data.rejectReason || '');

                    // 显示模态框
                    $('#detailModal').modal('show');
                },
                error: function (xhr, status, error) {
                    toastr.error('获取详情失败: ' + error);
                }
            });
        });

        // 审核操作
        $('#kybExamineTable').on('click', '.examine-btn', function () {
            var kybId = $(this).data('id');
            $('#audit-kybId').val(kybId);
            $('#audit-result').val('examinePass'); // 默认通过
            $('#audit-opinion').val('');
            $('#audit-reject-reason').val('');
            $('.reject-reason-group').hide(); // 隐藏拒绝原因
            $('#auditModal').modal('show');
        });

        // 审核结果改变时显示/隐藏拒绝原因字段
        $('#audit-result').change(function () {
            if ($(this).val() === 'examineNoPass') {
                $('.reject-reason-group').show();
            } else {
                $('.reject-reason-group').hide();
            }
        });

        // 提交审核
        $('#submitAudit').click(function () {
            var formData = {
                kybId: $('#audit-kybId').val(),
                examineStatus: $('#audit-result').val(),
                examineOpinion: $('#audit-opinion').val(),
                rejectReason: $('#audit-reject-reason').val()
            };

            $.ajax({
                url: '/urm/mermgr/kyb/kybExamine',
                type: 'post',
                contentType: 'application/json',
                data: JSON.stringify(formData),
                success: function (response) {
                    $('#auditModal').modal('hide');
                    toastr.success('审核成功');
                    // 刷新表格
                    table.ajax.reload(function () {
                        $('.dataTables_processing').hide();
                    }, false);
                },
                error: function (xhr, status, error) {
                    toastr.error('审核失败: ' + error);
                }
            });
        });
    });

    // 搜索方法
    function search() {
        // 刷新表格
        table.ajax.reload(function() {
            $('.dataTables_processing').hide();
        }, false);
    }
</script>
</body>

</html>
