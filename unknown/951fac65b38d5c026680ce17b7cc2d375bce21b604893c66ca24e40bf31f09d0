package com.hisun.lemon.urm.service;

import com.hisun.lemon.urm.entity.MercOprAuthDo;
import com.hisun.lemon.urm.entity.MercOprInfoDO;
import com.hisun.lemon.urm.entity.UrmSafeInfDO;
import com.hisun.lemon.urm.entity.UrmSafeLoginDO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2017/10/12
 */
public interface IMercOprService {

    void addMercOpr(UrmSafeLoginDO urmSafeLoginDO, UrmSafeInfDO urmSafeInfDO);

    void modifyOprInfo(String loginId, String mblNo, String displayNm);

    void deleteMercOpr(String loginId, String userId);

    List<MercOprInfoDO> queryAll(String userId, String displayNm, String loginId, String oprTyp, int num, int size);

   int queryListSize(String userId);

    String queryAuthority(String loginId);

    void modifyMercOprAuth(MercOprAuthDo mercOprAuthDo);
}
