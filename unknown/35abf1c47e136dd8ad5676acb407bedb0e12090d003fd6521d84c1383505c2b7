<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
    <head>
        <title data-i18n="rsm.title"></title>
        <div th:replace="head"></div>
    </head>
    <body>
        <div id="wrapper">
            <div th:replace="nav"></div>
            <div id="page-wrapper" class="gray-bg">
                <div th:replace="top"></div>
                <div class="row wrapper border-bottom white-bg page-heading">
                    <div class="col-lg-10">
                        <h2 data-i18n="rsm.check.censor.title"></h2>
                        <ol class="breadcrumb">
                            <li>
                                <a data-i18n="nav.rsm"></a>
                            </li>
                            <li>
                                <a data-i18n="rsm.check.title"></a>
                            </li>
                            <li class="active">
                                <strong data-i18n="rsm.check.censor.title"></strong>
                            </li>
                        </ol>
                    </div>
                </div>
                <div class="wrapper wrapper-content  animated fadeInRight">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="ibox float-e-margins">
                                <div class="ibox-content">

                                    <div class="box-header">
                                        <table style="width: 80%">
                                            <tr>
                                                <td align="center">
                                                    <label class="control-label" data-i18n="rsm.riskList.txTyp"></label>
                                                </td>
                                                <td>
                                                    <select class="form-control" name="txTyp">
                                                        <option value="00" data-i18n="rsm.constant.txTyp.all"></option>
                                                        <option value="01" data-i18n="rsm.constant.txTyp.recharge"></option>
                                                        <option value="02" data-i18n="rsm.constant.txTyp.consume"></option>
                                                        <option value="03" data-i18n="rsm.constant.txTyp.transfer"></option>
                                                        <option value="04" data-i18n="rsm.constant.txTyp.withdraw"></option>
                                                        <option value="05" data-i18n="rsm.constant.txTyp.seatel"></option>
                                                        <option value="07" data-i18n="rsm.constant.txTyp.interest"></option>
                                                        <option value="08" data-i18n="rsm.constant.txTyp.payment"></option>
                                                    </select>
                                                </td>
                                                <td align="center">
                                                    <label class="control-label" data-i18n="rsm.check.censor.lmtLvl"></label>
                                                </td>
                                                <td>
                                                    <select class="form-control" name="lmtLvl">
                                                        <option value="1" data-i18n="rsm.check.censor.lmtLvlUser"></option>
                                                    </select>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>

                                    <div class="table-responsive">
                                        <table id="example" class="table table-striped table-bordered table-hover">
                                            <thead>
                                                <tr>
                                                    <th data-i18n="rsm.check.censor.select"></th>
                                                    <th data-i18n="rsm.check.censor.ruleId"></th>
                                                    <th data-i18n="rsm.check.censor.ruleNm"></th>
                                                    <th data-i18n="rsm.check.censor.oprTyp"></th>
                                                </tr>
                                            </thead>
                                        </table>
                                    </div>

                                    <div align="center">
                                        <button type="button" class="btn btn-w-m btn-primary _right" data-i18n="rsm.submit"
                                                onclick="submitForm()"></button>
                                        <a href="/rsm/check/censor">
                                            <button type="button" class="btn btn-w-m btn-primary _right" data-i18n="rsm.return"></button>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div th:replace="footer"></div>
                <input type="hidden" id="checkRuleId" th:value="${checkRuleId}">
                <input type="hidden" id="checkTxTyp" th:value="${txTyp}">
                <input type="hidden" id="checkLmtLvl" th:value="${lmtLvl}">
            </div>
        </div>
        <div th:replace="script"></div>
        <!-- Page-Level Scripts -->
        <script>
            var table;
            $(document).ready(function () {

                var languageUrl;
                switch ($.cookie('lang')) {
                    case 'zh':
                        languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                        break;
                    case 'en':
                        languageUrl = '/datatables/plugins/i18n/English.lang';
                        break;
                    case 'km':
                        languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                        break;
                }

                i18nLoad.then(function () {

                    table = $('#example').DataTable({
                        dom: 'rti',
                        ajax: {
                            contentType: 'application/json',
                            url: '/rsm/check/censor/findList',
                            type: 'POST',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        },
                        order: [1, 'asc'],
                        serverSide: true,
                        searchDelay: 1000,
                        responsive: true,
                        processing: true,
                        language: {
                            url: languageUrl
                        },
                        select: {
                            style: 'muti',
                            selector: 'td:first-child'
                        },
                        columnDefs: [
                            {
                                targets: 3,
                                render: function (data, type, row) {
                                    return '<select>' +
                                        '<option value="1">' +
                                        $.i18n.t("rsm.check.censor.oprTypRefuse") +
                                        '</option>' +
                                        '</select>';
                                },
                                orderable: false,
                                searchableOption: false
                            }
                        ],
                        columns: [
                            {
                                defaultContent: '',
                                orderable: false,
                                className: 'select-checkbox',
                                searchableOption: false
                            }, {
                                data: 'recId'
                            }, {
                                data: 'ruleNm'
                            }, {
                                defaultContent: '',
                                orderable: false,
                                searchableOption: false
                            }
                        ],
                        initComplete: function () {
                            defaultSelect();
                        }
                    });
                });

            });

            function submitForm() {
                var txTyp = $("select[name='txTyp']").val();
                var lmtLvl = $("select[name='lmtLvl']").val();

                var arr = new Array();
                $("#example").find("tr.selected").each(function() {
                    var temp = $(this).children();
                    arr.push({
                        "ruleId":temp.eq(1).html(),
                        "oprTyp":temp.eq(3).children().eq(0).val(),
                        "txTyp":txTyp,
                        "lmtLvl":lmtLvl
                    });
                });
                $.ajax({
                    url: "/rsm/check/censor/save",
                    type: 'POST',
                    data: JSON.stringify(arr),
                    contentType: 'application/json',
                    success: function(d){
                        if (d == "success") {
                            swal($.i18n.t("cpi.swal-success"), "", "success");
                            window.location.href="/rsm/check/censor";
                        }
                    }
                });
            }

            function defaultSelect() {
                if ($("#checkRuleId").val() != null && $("#checkRuleId").val() != "") {
                    $("select[name='txTyp']").val($("#checkTxTyp").val());
                    $("select[name='lmtLvl']").val($("#checkLmtLvl").val());
                    $("select[name='txTyp']").attr("disabled","disabled");
                    $("select[name='lmtLvl']").attr("disabled","disabled");
                    $.ajax({
                        url: "/rsm/check/censor/info?checkId=" + $("#checkRuleId").val(),
                        type: 'GET',
                        success: function(d){
                            var desc = d.split("|");
                            $.each(desc, function(index, value) {
                                if (value != "") {
                                    var data = value.split(",");
                                    $("#example").find("tr").each(function() {
                                        var temp = $(this).children();
                                        var id = temp.eq(1).html();
                                        if (id == data[0]) {
                                            temp.eq(0).click();
                                            temp.eq(3).children().eq(0).val(data[1]);
                                        }
                                    });
                                }
                            });
                        }
                    });
                }
            }
        </script>
    </body>
</html>