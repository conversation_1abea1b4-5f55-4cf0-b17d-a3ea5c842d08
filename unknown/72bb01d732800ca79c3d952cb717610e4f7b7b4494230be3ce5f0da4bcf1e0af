<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">

<head>

    <title data-i18n="urm.balance.merTitle"></title>

    <div th:replace="head"></div>
</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="urm.balance.mercBalance"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.busmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.bussub.mermgr"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="urm.balance.mercBalance"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <div class="searchDiv">
                                <div id="queryForm">

                                    <!--<span class=" col-search-1 control-label" data-i18n="acm.mobile"></span>-->
                                    <!--<input type="text" class="searchInput" name="mobile" id="mobile">-->

                                    <span class=" col-search-1 control-label" data-i18n="urm.info.mercId"></span>
                                    <input type="text" class="searchInput" name="userId" id="userId">
                                    <button type="button" id="query" class="btn btn-primary" data-i18n="acm.query"></button>
                                </div>
                            </div>
                            <hr/>
                            <div class="table-responsive">
                                <table id="dataTables" class="table table-striped table-bordered table-hover ">
                                    <thead>
                                    <tr>
                                        <th data-i18n="urm.info.mercId"></th>
                                        <th data-i18n="urm.balance.cashAcc"></th>
                                        <th data-i18n="urm.balance.merNm"></th>
                                        <th data-i18n="urm.balance.cashBal"></th>
                                        <th data-i18n="urm.balance.cashUnBal"></th>
                                        <th data-i18n="urm.balance.settleBal"></th>
                                        <th data-i18n="urm.balance.settleUnBal"></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>
    </div>
</div>
<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script type="text/javascript" th:inline="javascript">
    var table;
    $(document).ready(function () {
        var languageUrl;

        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }
        i18nLoad.then(function () {
        	var d_url=document.location.href;
            var d_url1=d_url.split("id=")[1];
            if (d_url1 != null) {
                $("#queryForm").hide();
                // 修复URL参数处理，进行URL解码
                var userId = decodeURIComponent(d_url1);
                // 如果参数中包含其他参数，只取第一个
                if (userId.indexOf('&') > -1) {
                    userId = userId.split('&')[0];
                }
                document.getElementById("userId").value=userId;
            }else {
                $("#queryForm").show();
            }
            
            // 延迟初始化DataTable，确保参数设置完成
            setTimeout(function() {
                 table = $('#dataTables').DataTable({
                    ajax: {
                        contentType: 'application/json',
                        url: '/urm/mermgr/info/queryUserBalance',
                        type: 'POST',
                        data: function (d) {
                            var userId = $("#userId").val();
                            var mobile = $("#mobile").val();

                            d.extra_search = {
                                "userId":userId,
                                "mobile":mobile,
                            };
                            return JSON.stringify(d);
                        }
                    },
                    serverSide: true,
                    responsive: true,
                    processing: true,
                     searching: false,
                    language: {
                        url: languageUrl
                        //url: '//cdn.datatables.net/plug-ins/1.10.15/i18n/Chinese.json'
                    },
                    columns: [{
                        data: 'userId'
                    }, {
                        data: 'cashAcc'
                    }, {
                        data: 'userNm'
                    }, {
                        data: 'cashAcCurBal'
                    },{
                        data: 'cashAcUavaBal'
                    }, {
                        data: 'settleAcCurBal'
                    }, {
                        data: 'settleAcUavaBal'
                    }
                    ]
                });
            }, 100); // 延迟100ms确保DOM操作完成
        });
    });
     $("#query").click(function () {
         table.ajax.reload();
     })

    function ajaxClick(name, type, id) {
        var url = $("span[name='" + name + "'][id='" + id + "']").attr("data");
        if (name == "lock") {
            $.ajax({
                type: type,
                url: url,
                success: function (data) {
                    if (data) {
                        alert(data);
                    } else {
                        window.location.reload();
                    }
                }
            })
        } else if (name == "trash") {
            swal({
                title: $.i18n.t("user.swal-title"),
                text: "",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: $.i18n.t("user.swal-confirm"),
                cancelButtonText: $.i18n.t("user.swal-cancel"),
                closeOnConfirm: false
            }, function (isConfirm) {
                if (!isConfirm) return;
                $.ajax({
                    url: url,
                    type: type,
                    success: function () {
                        swal($.i18n.t("user.swal-sucess"), "", "success");
                        $('.confirm').click(function () {   //额外绑定一个事件，当确定执行之后返回成功的页面的确定按钮，点击之后刷新当前页面或者跳转其他页面
                            location.reload();
                        });
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        swal($.i18n.t("user.swal-error"), $.i18n.t("user.swal-error-tips"), "error");
                    }
                });
            });
        }
    }
</script>
</body>

</html>
