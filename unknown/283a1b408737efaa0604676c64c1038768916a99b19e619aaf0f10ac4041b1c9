<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="nav.busmgr.mkmmgr.pact.title">协议管理</title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>

            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="nav.busmgr.mkmmgr.pact.content">协议管理</h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="nav.busmgr">业务管理</a>
                        </li>
                        <li>
                            <a data-i18n="nav.busmgr.mkmmgr">营销管理</a>
                        </li>
                        <li class="active">
                            <strong data-i18n="nav.busmgr.mkmmgr.pact.content">协议管理</strong>
                        </li>
                    </ol>
                </div>
                <div class="col-lg-2">
                    <div class="btn-group pull-right" style="margin-top: 30px;">
                        <button type="button" class="btn btn-primary btn-sm" id="btnAdd">
                            <i class="fa fa-plus"></i> <span data-i18n="mkm.pact.add">新增</span>
                        </button>
                    </div>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!-- 查询表单 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12">
                                        <!-- 协议ID -->
                                        <label class="col-sm-2 control-label" for="searchId"
                                            data-i18n="mkm.pact.id">协议ID</label>
                                        <div class="col-sm-4">
                                            <input name="id" id="searchId" class="form-control" value="" />
                                        </div>
                                        <!-- 协议标题 -->
                                        <label class="col-sm-2 control-label" for="searchTitle"
                                            data-i18n="mkm.pact.title">协议标题</label>
                                        <div class="col-sm-4">
                                            <input name="title" id="searchTitle" class="form-control" value="" />
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-sm-4 col-sm-offset-1">
                                            <button type="button" id="searchBtn" class="btn btn-primary"
                                                onclick="search()" data-i18n="mkm.pact.search">查询</button>
                                            <button type="button" id="resetBtn" class="btn btn-default"
                                                onclick="resetForm()" data-i18n="mkm.pact.reset">重置</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="pactTable" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="mkm.pact.id">协议ID</th>
                                                <th data-i18n="mkm.pact.title">协议标题</th>
                                                <th data-i18n="mkm.pact.type">协议类型</th>
                                                <th data-i18n="mkm.pact.status">协议状态</th>
                                                <th data-i18n="mkm.pact.createUser">创建人</th>
                                                <th data-i18n="mkm.pact.createTime">创建时间</th>
                                                <th data-i18n="mkm.pact.operations">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <!-- 新增/编辑模态框 -->
    <div class="modal fade" id="pactModal" tabindex="-1" role="dialog" aria-labelledby="pactModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="pactModalLabel" data-i18n="mkm.pact.addTitle">新增协议</h4>
                </div>
                <div class="modal-body">
                    <form id="pactForm" class="form-horizontal">
                        <input type="hidden" id="id" name="id" />
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="title" data-i18n="mkm.pact.title">协议标题</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="title" name="title" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="type" data-i18n="mkm.pact.type">协议类型</label>
                            <div class="col-sm-9">
                                <select class="form-control" id="type" name="type" required>
                                    <option value="USER">用户协议</option>
                                    <option value="MERCHANT">商户协议</option>
                                    <option value="PAYMENT">支付协议</option>
                                    <option value="OTHER">其他</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="status" data-i18n="mkm.pact.status">协议状态</label>
                            <div class="col-sm-9">
                                <select class="form-control" id="status" name="status" required>
                                    <option value="ACTIVE">激活</option>
                                    <option value="INACTIVE">未激活</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="content"
                                data-i18n="mkm.pact.content">协议内容</label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="content" name="content" rows="10"
                                    required></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="remark" data-i18n="mkm.pact.remark">备注</label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="remark" name="remark" rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="mkm.pact.cancel">取消</button>
                    <button type="button" class="btn btn-primary" id="btnSave" data-i18n="mkm.pact.save">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 查看详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="detailModalLabel" data-i18n="mkm.pact.detailTitle">协议详情</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="mkm.pact.title">协议标题</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailTitle"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="mkm.pact.type">协议类型</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailType"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="mkm.pact.status">协议状态</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailStatus"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="mkm.pact.content">协议内容</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailContent" style="white-space: pre-line;"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="mkm.pact.remark">备注</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailRemark"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="mkm.pact.createUser">创建人</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailCreateUser"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="mkm.pact.createTime">创建时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailCreateTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="mkm.pact.updateUser">更新人</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUpdateUser"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="mkm.pact.updateTime">更新时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUpdateTime"></p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="mkm.pact.close">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var table;
        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/js/Chinese.json';
                    break;
                case 'en':
                    languageUrl = '/js/English.json';
                    break;
                default:
                    languageUrl = '/js/Chinese.json';
            }

            // 初始化DataTables
            table = $('#pactTable').DataTable({
                dom: 'Blfrtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/mkm/pact/findAll',
                    type: 'post',
                    data: function (d) {
                        // 添加额外的查询参数
                        d.extra_search = {
                            "id": $("#searchId").val() || "",
                            "title": $("#searchTitle").val() || ""
                        };
                        return JSON.stringify(d);
                    },
                    dataSrc: function (json) {
                        // 确保返回的是数组
                        return json.data || [];
                    },
                    error: function (xhr, error, thrown) {
                        console.error('DataTables AJAX error:', error, thrown);
                        toastr.error('加载数据失败，请刷新页面重试');
                    }
                },
                serverSide: true,
                searching: false, // 禁用内置搜索
                searchDelay: 1000,
                responsive: true,
                processing: true,
                language: {
                    url: languageUrl
                },
                columns: [
                    { data: 'id' },
                    { data: 'title' },
                    {
                        data: 'type',
                        render: function (data) {
                            switch (data) {
                                case 'USER': return '用户协议';
                                case 'MERCHANT': return '商户协议';
                                case 'PAYMENT': return '支付协议';
                                case 'OTHER': return '其他';
                                default: return data;
                            }
                        }
                    },
                    {
                        data: 'status',
                        render: function (data) {
                            if (data === 'ACTIVE') {
                                return '<span class="label label-primary">激活</span>';
                            } else if (data === 'INACTIVE') {
                                return '<span class="label label-default">未激活</span>';
                            } else {
                                return data;
                            }
                        }
                    },
                    { data: 'createUser' },
                    {
                        data: 'createTime',
                        render: function (data) {
                            if (!data) return '';
                            return new Date(data).toLocaleString();
                        }
                    },
                    {
                        data: null,
                        orderable: false,
                        render: function (data, type, row) {
                            var buttons = '<button type="button" class="btn btn-xs btn-info view-detail" data-id="' + row.id + '">详情</button> ';
                            buttons += '<button type="button" class="btn btn-xs btn-primary btn-edit" data-id="' + row.id + '">编辑</button> ';
                            buttons += '<button type="button" class="btn btn-xs btn-danger btn-delete" data-id="' + row.id + '">删除</button>';
                            return buttons;
                        }
                    }
                ],
                buttons: [
                    { extend: 'copyHtml5' },
                    { extend: 'csvHtml5' },
                    { extend: 'excelHtml5', title: '协议列表' }
                ]
            });

            // 新增按钮
            $("#btnAdd").click(function () {
                $("#pactModalLabel").text("新增协议");
                $("#pactForm")[0].reset();
                $("#id").val("");
                $("#pactModal").modal("show");
            });

            // 保存按钮
            $("#btnSave").click(function () {
                if (!$("#pactForm").valid()) {
                    toastr.error('请填写必填项');
                    return;
                }

                var id = $("#id").val();
                var formData = $("#pactForm").serialize();
                var url = id ? "/mkm/pact/modify/" + id : "/mkm/pact/add";

                $.ajax({
                    url: url,
                    type: "POST",
                    data: formData,
                    success: function (res) {
                        if (res.result === "MKM00000") {
                            toastr.success('操作成功');
                            $("#pactModal").modal("hide");
                            table.ajax.reload();
                        } else {
                            toastr.error('操作失败：' + res.result);
                        }
                    },
                    error: function (xhr, status, error) {
                        toastr.error('操作失败：' + error);
                    }
                });
            });

            // 查看详情
            $(document).on("click", ".view-detail", function () {
                var id = $(this).data("id");
                $.ajax({
                    url: "/mkm/pact/getPact",
                    type: "POST",
                    data: { id: id },
                    success: function (data) {
                        $("#detailTitle").text(data.title || '');

                        // 类型转换
                        var typeText = '';
                        switch (data.type) {
                            case 'USER': typeText = '用户协议'; break;
                            case 'MERCHANT': typeText = '商户协议'; break;
                            case 'PAYMENT': typeText = '支付协议'; break;
                            case 'OTHER': typeText = '其他'; break;
                            default: typeText = data.type;
                        }
                        $("#detailType").text(typeText);

                        // 状态转换
                        var statusText = data.status === 'ACTIVE' ? '激活' : '未激活';
                        $("#detailStatus").text(statusText);

                        $("#detailContent").text(data.content || '');
                        $("#detailRemark").text(data.remark || '');
                        $("#detailCreateUser").text(data.createUser || '');
                        $("#detailCreateTime").text(data.createTime ? new Date(data.createTime).toLocaleString() : '');
                        $("#detailUpdateUser").text(data.updateUser || '');
                        $("#detailUpdateTime").text(data.updateTime ? new Date(data.updateTime).toLocaleString() : '');
                        $("#detailModal").modal("show");
                    },
                    error: function (xhr, status, error) {
                        toastr.error('获取详情失败：' + error);
                    }
                });
            });

            // 编辑按钮
            $(document).on("click", ".btn-edit", function () {
                var id = $(this).data("id");
                $.ajax({
                    url: "/mkm/pact/getPact",
                    type: "POST",
                    data: { id: id },
                    success: function (data) {
                        $("#pactModalLabel").text("编辑协议");
                        $("#id").val(data.id);
                        $("#title").val(data.title);
                        $("#type").val(data.type);
                        $("#status").val(data.status);
                        $("#content").val(data.content);
                        $("#remark").val(data.remark);
                        $("#pactModal").modal("show");
                    },
                    error: function (xhr, status, error) {
                        toastr.error('获取详情失败：' + error);
                    }
                });
            });

            // 删除按钮
            $(document).on("click", ".btn-delete", function () {
                var id = $(this).data("id");
                swal({
                    title: "确定要删除该协议吗？",
                    text: "删除后将无法恢复！",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#DD6B55",
                    confirmButtonText: "确定删除",
                    cancelButtonText: "取消",
                    closeOnConfirm: false
                }, function () {
                    $.ajax({
                        url: "/mkm/pact/delete/" + id,
                        type: "DELETE",
                        success: function (res) {
                            if (res === "1") {
                                swal("删除成功！", "协议已被删除。", "success");
                                table.ajax.reload();
                            } else {
                                swal("删除失败", "请稍后重试", "error");
                            }
                        },
                        error: function (xhr, status, error) {
                            swal("删除失败", "错误: " + error, "error");
                        }
                    });
                });
            });

            // 表单验证
            $("#pactForm").validate({
                rules: {
                    title: {
                        required: true,
                        maxlength: 100
                    },
                    type: {
                        required: true
                    },
                    status: {
                        required: true
                    },
                    content: {
                        required: true,
                        maxlength: 2000
                    },
                    remark: {
                        maxlength: 500
                    }
                },
                messages: {
                    title: {
                        required: "请输入协议标题",
                        maxlength: "协议标题不能超过100个字符"
                    },
                    type: {
                        required: "请选择协议类型"
                    },
                    status: {
                        required: "请选择协议状态"
                    },
                    content: {
                        required: "请输入协议内容",
                        maxlength: "协议内容不能超过2000个字符"
                    },
                    remark: {
                        maxlength: "备注不能超过500个字符"
                    }
                }
            });
        });

        // 搜索方法
        function search() {
            table.ajax.reload();
        }

        // 重置表单
        function resetForm() {
            $("#queryForm")[0].reset();
            search();
        }
    </script>
</body>

</html>