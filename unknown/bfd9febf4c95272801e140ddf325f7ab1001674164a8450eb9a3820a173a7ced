<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>

    <title data-i18n="nav.bussub.trdsub.orderqueryctrl.title"></title>

    <div th:replace="head"></div>
</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="nav.bussub.trdsub.orderqueryctrl.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.busmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.bussub.trdmgr"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="nav.bussub.trdsub.orderqueryctrl.content"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form id="queryForm" class="form-horizontal">
                                <div class="form-group col-sm-12">
                                    <!--手机号/商户号-->
                                    <label class="col-sm-2 control-label" for="mercId" data-i18n="onr.mercId"></label>
                                    <div class="col-sm-4">
                                        <input name="mercId" id="mercId" class="form-control" value=""/>
                                    </div>
                                    <label class="col-sm-2 control-label" for="cshOrderNo" data-i18n="onr.cshOrderNo"></label>
                                    <div class="col-sm-4">
                                        <input name="cshOrderNo" id="cshOrderNo" class="form-control" value=""/>
                                    </div>
                                </div>
                                <div class="form-group col-sm-8">
                                    <!--付款银行-->
                                    <label class="col-sm-3 control-label" for="orderStat" data-i18n="onr.orderStat"></label>
                                    <div class="col-sm-5">
                                        <select name="orderStat" id="orderStat" class="form-control" value="">
                                            <option value=""   data-i18n="onr.select"></option>
                                            <option value="P"  data-i18n="onr.statsub.P"></option>
                                            <option value="F"  data-i18n="onr.statsub.F"></option>
                                            <option value="S"  data-i18n="onr.statsub.S"></option>
                                            <option value="PR" data-i18n="onr.statsub.PR"></option>
                                            <option value="RB" data-i18n="onr.statsub.RB"></option>
                                            <option value="R"  data-i18n="onr.statsub.R"></option>
                                            <option value="PZ" data-i18n="onr.statsub.PZ"></option>
                                            <option value="Z"  data-i18n="onr.statsub.Z"></option>
                                            <option value="D"  data-i18n="onr.statsub.D"></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group col-sm-12">
                                    <!--提交日期-->
                                    <label class="col-sm-2 control-label" for="beginDate" data-i18n="onr.cshOrderDt"></label>
                                    <div class="col-sm-4">
                                        <input name="beginDate" id="beginDate" class="form-control" value=""/>
                                    </div>
                                    <div class="col-sm-4">
                                        <input name="endDate" id="endDate" class="form-control" value=""/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-4 col-sm-offset-1">
                                        <button type="button" id="searchBtn" class="btn btn-default" onclick="search()" data-i18n="onr.search"></button>
                                    </div>
                                </div>
                            </form>
                            <div class="table-responsive">
                                <table id="orderInf" class="table table-striped table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th data-i18n="onr.cshOrderNo"></th>
                                        <th data-i18n="onr.mercId"></th>
                                        <th data-i18n="onr.userId"></th>
                                        <th data-i18n="onr.cshOrderDt"></th>
                                        <th data-i18n="onr.orderAmt"></th>
                                        <th data-i18n="onr.payAmt"></th>
                                        <th data-i18n="onr.refAmt"></th>
                                        <th data-i18n="onr.ccy"></th>
                                        <th data-i18n="nav.paytype"></th>
                                        <th data-i18n="onr.couponType"></th>
                                        <th data-i18n="onr.couponAmt"></th>
                                        <th data-i18n="onr.orderStat"></th>
                                        <th data-i18n="onr.orderDesc"></th>
                                        <!--<th data-i18n="onr.operate"></th>-->
                                    </tr>
                                    </thead>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>

    </div>
</div>
<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script>
    var editor;
    //A 2D array in which the first array is used to define the value options and the second array the displayed options (useful for language strings such as 'All').
    //    var array1 = [10, 25, 50, -1];
    //    var array2 = [10, 25, 50, "All"];
    var table;
    
    <!--查询按钮-->
    function search() {
        table.ajax.reload();
    }

    $(document).ready(function () {

        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        i18nLoad.then(function () {
            //editor = new $.fn.dataTable.Editor({
                //ajax: {
                 //  create: {
                 //      type: 'POST',
                 //      url: '/onr/transferctrl/add',
                 //      contentType: 'application/json',
                 //      data: function (d) {
                 //          return JSON.stringify(d);
                 //      }
                 //  },
                 //  edit: {
                 //      type: 'POST',
                 //      url: '/onr/mercinfctrl/modify',
                 //      contentType: 'application/json',
                 //      data: function (d) {
                 //          return JSON.stringify(d);
                 //      }
                 //  },
                 //  remove: {
                 //      type: 'POST',
                 //      url: '/onr/mercinfctrl/delete',
                 //      contentType: 'application/json',
                 //      data: function (d) {
                 //          return JSON.stringify(d);
                 //      }
                 //  }
                //},
                //table: "#orderInf",
                //idSrc: 'onrOrderNo',
             //   fields: [
                  //  {name: "id", type: "hidden"},
                  //  {label: $.i18n.t("onr.onrOrderDt"),name: "onrOrderDt"},
                  //  {label: $.i18n.t("onr.onrOrderNo"), name: "onrOrderNo"},
                  //  {label: $.i18n.t("onr.userNo"), name: "userNo"},
                  //  {label: $.i18n.t("onr.mblNo"), name: "mblNo"},
                  //  {label: $.i18n.t("onr.bossCopType"), name: "bossCopType"},
                  //  {label: $.i18n.t("onr.orderType"), name: "orderType"},
                  //  {label: $.i18n.t("onr.orderAmt"), name: "orderAmt"},
                  //  {label: $.i18n.t("onr.orderSts"), name: "orderSts"},
                  //  {label: $.i18n.t("onr.payType"), name: "payType"},
                  //  {label: $.i18n.t("onr.payAmt"), name: "payAmt"}
                    //{
                        //label: $.i18n.t("demo.startDate"),
                        //name: "startDate",
                        //type: "datetime",
                        //def: function () {
                            //return new Date();
                       // },
                       // format: "YYYY-MM-DD HH:mm:ss"
                   // },
                    //{label: $.i18n.t("demo.salary"), name: "salary"}
              //  ],
                //i18n: {
                  // create: {button: $.i18n.t("onr.add"), title: $.i18n.t("onr.add"), submit: $.i18n.t("onr.create")},
                  // edit: {button: $.i18n.t("onr.modify"), title: $.i18n.t("onr.modify"), submit: $.i18n.t("onr.update")},
                  // remove: {
                  //     button: $.i18n.t("onr.delete"), title: $.i18n.t("onr.delete"), submit: $.i18n.t("onr.delete"),
                  //     confirm: {
                  //         _: $.i18n.t("onr.multi-delete"),
                  //         1: $.i18n.t("onr.single-delete")
                  //     }
                  // }
                //}
            //});

            //editor.on('preSubmit', function (e, o, action) {
            //    var id = editor.field('onrOrderNo');
            //    o.id = id.val();  // create a new parameter to pass over to the server called entityId
            //});

            table = $('#orderInf').DataTable({
                dom: 'Blfrtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/onr/orderqueryctrl/findAll',
                    type: 'POST',
                    data: function (d) {
                    	d.extra_search = {
                            "cshOrderNo" : $("#cshOrderNo").val(),
                            "mercId" : $("#mercId").val(),
                            "orderStat" : $("#orderStat").val(),
                            "beginDate" : $("#beginDate").val(),
                            "endDate" : $("#endDate").val()
                            };
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
                lengthMenu: [10, 50, 100, 500, 1000],
                language: {
//                "decimal": ",",
//                "thousands": ".",
//                    url: '/datatables/plugins/i18n/Chinese.lang'
                    url: languageUrl
                    //url: '//cdn.datatables.net/plug-ins/1.10.15/i18n/Chinese.json'
                },
                select: true,
                columnDefs: [{
                    targets:[0,1,2],//指定哪几列
                    render: function(data){
                        return "\u200C" + data;
                    }
                }],
                buttons: [
                    //{extend: "create", editor: editor},
                    //{extend: "edit", editor: editor},
                    //{extend: "remove", editor: editor},
                   // {extend: 'copyHtml5'},
                   // {extend: 'csvHtml5'},
                   {
                       extend: 'excelHtml5',
                       filename: "Export",
                       exportOptions: {
                           columns: [ 0,1,2,3,4,5,6,7,8,9,10,11 ]
                       }
                   }

               // {
               //     className: 'buttons-alert',                         //Adds the "Export all to Excel" button
               //     id: 'ExportButton',
               //     text: "Export All To Excel",
               //     action: function (e, dt, node, config) {
               //         alert("12");
               //         // window.location.href = '';
               //     }
               // }
                    // 'copy',
                    // 'csv',
                    // 'excel'
//                'pdf',
//                'print'
                ],
                columns: [{
                    data: 'cshOrderNo'
                },{
                    data: 'mercId'
                },{
                    data: 'userId'
                },{
                    data: 'cshOrderDt'
                },{
                    data: 'orderAmt',
                    render: $.fn.dataTable.render.number(',', '.', 2, '$')
                },{
                    data: 'payAmt',
                    render: $.fn.dataTable.render.number(',', '.', 2, '$')
                },{
                    data: 'refAmt',
                    render: $.fn.dataTable.render.number(',', '.', 2, '$')
                },{
                    data: 'ccy',
                    render: function (data, type, row) {
                        switch (data) {
                            case "CNY":
                                return $.i18n.t("CCY.CNY");
                            case "USD":
                                return $.i18n.t("CCY.USD");
                            case "KHR":
                                return $.i18n.t("CCY.KHR");
                            default:
                                return data;
                        }
                    }
                }, {
                   data: "payTyp"
                }, {
                    data: 'couponType',
                    render: function (data, type, row) {
                        switch (data) {
                            case "00":
                                return $.i18n.t("onr.coupon.00");
                            case "01":
                                return $.i18n.t("onr.coupon.01");
                            case "02":
                                return $.i18n.t("onr.coupon.02");
                            case "03":
                                return $.i18n.t("onr.coupon.03");
                            case "04":
                                return $.i18n.t("onr.coupon.04");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'couponAmt'
                },{
                    data: 'orderStat',
                    render: function (data, type, row) {
                        switch (data) {
                            case "U":
                            	return $.i18n.t("onr.statsub.U");
                        	case "W":
                        		return $.i18n.t("onr.statsub.W");
                        	case "P":
                        		return $.i18n.t("onr.statsub.P");
                        	case "S":
                        		return $.i18n.t("onr.statsub.S");
                        	case "F":
                        		return $.i18n.t("onr.statsub.F");
                        	case "PR":
                        	    return $.i18n.t("onr.statsub.PR");
                        	case "RB":
                                return $.i18n.t("onr.statsub.RB");
                        	case "R":
                        		return $.i18n.t("onr.statsub.R");
                        	case "PZ":
                        	    return $.i18n.t("onr.statsub.PZ");
                        	case "Z":
                        		return $.i18n.t("onr.statsub.Z");
                        	case "D":
                        		return $.i18n.t("onr.statsub.D");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'orderDesc'
                // },{
                //     data: 'orderStat',
                //    	render: function (data, type, row) {
                //    		if(data=="S"){
                //    			return '<td><a href="/onr/orderqueryctrl/rfdform?id=' + row.onrOrderNo +
                //             '"><span class="fa fa-reply">'+$.i18n.t("onr.refund")+'</span></a></td>';
                //    		}else if(data=="R"){
                //             return '<td><a href="/onr/onrrfdordctrl?id=' + row.onrOrderNo +
                //             '"><span class="fa fa-reply-all">' + $.i18n.t("onr.refundetail") + '</span></a></td>';
                //         }else if(data=="RB"){
                //         	return '<td><a href="/onr/orderqueryctrl/rfdform?id=' + row.onrOrderNo +
                //             '"><span class="fa fa-reply">'+$.i18n.t("onr.refund")+'</span></a></td>'+"   "+'<td><a href="/onr/onrrfdordctrl?id=' + row.onrOrderNo +
                //             '"><span class="fa fa-reply-all">' + $.i18n.t("onr.refundetail") + '</span></a></td>';
                //         }else{
                //    			return "";
                //    		}
                //    	}
                }
                ]
            });
        });
    });
    /**初始化日期控件**/
    var beginTimePick = $('#beginDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        onChangeDate: function(dateText, inst) {
            endTimePick.datetimepicker('minDate',new Date(dateText.replace("-", "-")));
        }
    });

    var endTimePick = $('#endDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d'
        // maxDate:'+1970/01/01',
    });
</script>
</body>

</html>