<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">

<head>

    <title data-i18n="role.title"></title>

    <div th:replace="head"></div>

    <style>
        div.floatright {
            display: inline;
            float: right;
            padding-left: 20px;
            padding-right: 20px;
        }

        ._right {
            display: inline;
            float: right;
            padding-right: 15px;
        }
    </style>
</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="role.head"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.cmmmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.cmmsub.usrmgr"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="nav.systemsub.role"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <div class="box-header">
                                <table style="width: 80%">
                                    <tr>
                                        <td align="center">
                                            <label class="control-label" data-i18n="role.roleId"></label>
                                        </td>
                                        <td>&nbsp; </td>
                                        <td>
                                            <input class="form-control" name="id"/>
                                        </td>
                                        <td>&nbsp;</td>
                                        <td align="center">
                                            <label class="control-label" data-i18n="role.rolename"></label>
                                        </td>
                                        <td>&nbsp;</td>
                                        <td>
                                            <input class="form-control" name="roleName"/>
                                        </td>
                                        <td>&nbsp;</td>
                                        <td align="center">
                                            <label class="control-label" data-i18n="role.role"></label>
                                        </td>
                                        <td>&nbsp;</td>
                                        <td>
                                            <input class="form-control" name="role"/>
                                        </td>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td align="center">
                                            <label class="control-label" data-i18n="role.status"></label>
                                        </td>
                                        <td>&nbsp; </td>
                                        <td>
                                            <select class="form-control" name="status">
                                                <option value="" data-i18n="role.constant.all"></option>
                                                <option value="0" data-i18n="role.normal"></option>
                                                <option value="1" data-i18n="role.lapse"></option>
                                            </select>
                                        </td>
                                        <td>&nbsp;</td>
                                        <td align="center">
                                            <label class="control-label" data-i18n="role.officeid"></label>
                                        </td>
                                        <td>&nbsp;</td>
                                        <td>
                                            <input class="form-control" name="officeId"/>
                                        </td>
                                        <td>&nbsp;</td>
                                        <td align="center">
                                            <label class="control-label" data-i18n="role.branchid"></label>
                                        </td>
                                        <td>&nbsp;</td>
                                        <td>
                                            <input class="form-control" name="branchId"/>
                                        </td>
                                        <td>&nbsp;</td>
                                        <td>
                                            <button type="button" class="btn btn-w-m btn-primary _right"
                                                    data-i18n="role.search" onclick="searchButton()">
                                            </button>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                            <div class="table-responsive">
                                <table id="example" class="table table-striped table-bordered table-hover dataTables-example">
                                    <thead>
                                    <tr>
                                        <th data-i18n="role.roleId"></th>
                                        <th data-i18n="role.role"></th>
                                        <th data-i18n="role.rolename"></th>
                                        <th data-i18n="role.officeid"></th>
                                        <th data-i18n="role.branchid"></th>
                                        <th data-i18n="role.status"></th>
                                        <th sec:authorize="hasPermission('','/cmmmgr/user/role:resource') or hasRole('ROLE_ADMIN')"
                                            data-i18n="role.assign"></th>
                                        <th sec:authorize="hasPermission('','/cmmmgr/user/role:delete') or hasRole('ROLE_ADMIN')"
                                            data-i18n="role.delete"></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                            <div class="modal inmodal" id="Modal" tabindex="-1" role="dialog" aria-hidden="true"
                                 style="display: none;" data-backdrop="static">
                                <div class="modal-dialog">
                                    <div class="modal-content animated flipInY">
                                        <div class="modal-body">
                                            <div class="ibox float-e-margins">
                                                <div class="ibox-title">
                                                    <h5 data-i18n="role.modal"></h5>
                                                </div>
                                                <div class="ibox-content">
                                                    <div id="using_json"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-white" data-dismiss="modal"
                                                    data-i18n="role.close"></button>
                                            <button id="save" type="button" class="btn btn-primary"
                                                    data-i18n="role.save"></button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>

    </div>
</div>
<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script type="text/javascript" th:inline="javascript">
    var table;
    var editor;
    var order = [1, 'asc'];
    $(document).ready(function () {
        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }
        i18nLoad.then(function () {
            editor = new $.fn.dataTable.Editor({
                ajax: {
                    create: {
                        type: 'POST',
                        url: '/system/role/add',
                        contentType: 'application/json',
                        data: function (d) {
                            return JSON.stringify(d);
                        },
                        success:function (data) {
                            //console.log("hshsh");
                        }
                    },
                    edit: {
                        type: 'POST',
                        url: '/system/role/edit',
                        contentType: 'application/json',
                        data: function (d) {
                            return JSON.stringify(d);
                        },
                        success:function (data) {

                        },
                        error:function(data){

                        }
                    }
                },
                table: "#example",
                idSrc: 'id',
                fields: [
                    {name: "id", type: "hidden"},
                    {label: $.i18n.t("role.rolename"), name: "roleName"},
                    {label: $.i18n.t("role.officeid"), name: "officeId",
                        type: "select",
                        id: "officeName"
                    },
                    {label: $.i18n.t("role.branchid"), name: "branchId",
                        type: "select",
                        id: "braId"
                    },
                    {label: $.i18n.t("role.role"), name: "role" ,class:"disabled"},
                    {label: $.i18n.t("role.status"), name: "status",
                        type: "select",
                        options: [
                            {label: $.i18n.t("role.normal"), value: "0"},
                            {label: $.i18n.t("role.lapse"), value: "1"}
                        ]
                    },
                ],
                i18n: {
                    create: {
                        button: $.i18n.t("role.add"),
                        title: $.i18n.t("role.addrole"),
                        submit: $.i18n.t("role.add")
                    },
                    edit: {
                        button: $.i18n.t("role.edit"),
                        title: $.i18n.t("role.editrole"),
                        submit: $.i18n.t("role.edit")
                    }
                }
            });

            editor.on('preSubmit', function (e, o, action) {
                var id = editor.field('id');
            });
            editor.on( 'open', function ( e, o, action ) {
                if ( action === 'edit' ) {
                    // 初始化公司
                    this.field( 'role' ).disable();
                    initOfficeNm();
                }
                if ( action === 'create' ) {
                    this.field( 'role' ).enable();
                    initOfficeNm();
                }
            } );
            $( 'select', editor.field('officeId').node() ).on( 'change',
                function () {
                    initBraeNm();
                });
             table = $('.dataTables-example').DataTable({
                dom: 'B<"floatright"l>rtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/system/role/findAll',
                    type: 'POST',
                    data: function (d) {
                        return JSON.stringify(d);
                    }
                },
                searching: true,
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
                language: {
                    url: languageUrl
                },
                select: true,
                buttons: [
                    /*[# sec:authorize="hasPermission('','/cmmmgr/user/role:add') or hasRole('ROLE_ADMIN')"]*/
                    {extend: "create", editor: editor},
                    /*[/]*/
                    /*[# sec:authorize="hasPermission('','/cmmmgr/user/role:add') or hasRole('ROLE_ADMIN')"]*/
                    {extend: "edit", editor: editor}
                    /*[/]*/
                ],
                columns: [{
                    data: 'id'
                },{
                    data: 'role'
                }, {
                    data: 'roleName'
                }, {
                    data: 'officeId'
                }, {
                    data: 'branchId'
                }, {
                    data: 'status',
                    render: function (data, type, row) {
                        switch (data) {
                            case '0':
                                return $.i18n.t("role.normal");
                            case '1':
                                return $.i18n.t("role.lapse");
                        }
                    }
                },
                    /*[# sec:authorize="hasPermission('','/cmmmgr/user/role:resource') or hasRole('ROLE_ADMIN')"]*/
                    {
                        data: 'id',
                        orderable: false,
                        searchable: false,
                        render: function (data, type, row) {
//                        return '<th><a href="#"><span style="cursor: pointer" class="fa fa-list-ul" title="分配资源" data-toggle="modal" data-target="#Modal" data-role="' + data + '"></span></a></th>';
                            return '<th><a href="#Modal" style="cursor: pointer" class="fa fa-list-ul" title="' + $.i18n.t("role.assign") + '" data-toggle="modal" data-role="' + data + '"></a></th>';
                        }
                    },
                    /*[/]*/
                    /*[# sec:authorize="hasPermission('','/cmmmgr/user/role:delete') or hasRole('ROLE_ADMIN')"]*/
                    {
                        data: 'id',
                        orderable: false,
                        searchable: false,
                        render: function (data, type, row) {
                            return '<span name="trash" title="' + $.i18n.t("role.delete") + '" id="' + row.id + '" onclick="ajaxClick(\'trash\',\'DELETE\',\'' + row.id + '\')" ' +
                                'data="/system/role/delete/' + data + '" ' + 'style="cursor: pointer" class="fa fa-trash"></span></td>';
                        }
                    }
                    /*[/]*/
                ]
            });
        });

        $('#Modal').on('show.bs.modal',
            function (event) {
                var a = $(event.relatedTarget);
                var recipient = a.data('role');
                $("#save").attr("data-role", recipient);
                $("#using_json").jstree({
                    'core': {
                        'themes': {
                            'icons': false
                        },
                        'data': {
                            "url": function () {
                                return "/system/role/getResourceTree";
                            },
                            'data': function () {
                                return {
                                    roleId: recipient
                                };
                            }
                        }
                    },
                    'plugins': ['checkbox', 'themes']
                });
            });

        $('#Modal').on('hide.bs.modal',
            function () {
                $.jstree.destroy();
            });

        // 仅保存已选中叶子节点状态
        $("#save").on("click", function () {
            var url = "/system/role/saveResource/" + $("#save").data("role");
            var nodes = $("#using_json").jstree(true).get_selected("full", true);
            var selectedLeafElmsIds = [];
            $.each(nodes,function(i, node){
                if(node.children.length == 0){
                    selectedLeafElmsIds.push(this.id);
                }
            });
            var post_data = {'array[]': selectedLeafElmsIds};
            //$.post(url, post_data );
            $.ajax({
                type : "post",
                url : url,
                data : post_data,
                async : false,
                success : function(data){

                }
            });
            $(this).prev().click();
        });
    });

    $.ajaxSetup({headers: {'X-CSRF-TOKEN': $('#csrf_token').attr('content')}});

    function ajaxClick(name, type, id) {
        var url = $("span[name='" + name + "'][id='" + id + "']").attr("data");
        swal({
            title: $.i18n.t("role.swal-title"),
            text: "",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: $.i18n.t("role.swal-confirm"),
            cancelButtonText: $.i18n.t("role.swal-cancel"),
            closeOnConfirm: false
        }, function (isConfirm) {
            if (!isConfirm) return;
            $.ajax({
                url: url,
                type: type,
                success: function (data) {
                    if (data != null && data !="") {
                        swal($.i18n.t("role.swal-error"), "", "error");
                    }else {
                        swal($.i18n.t("role.swal-sucess"), "", "success");
                        $('.confirm').click(function () {   //额外绑定一个事件，当确定执行之后返回成功的页面的确定按钮，点击之后刷新当前页面或者跳转其他页面
                            location.reload();
                        });
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    swal($.i18n.t("role.swal-error"), $.i18n.t("role.swal-error-tips"), "error");
                }
            });
        });
    }

    /* 表头搜索框 */
    function searchButton() {
        var id = $("input[name='id']").val();
        var role = $("input[name='role']").val();
        var roleName = $("input[name='roleName']").val();
        var branchId = $("input[name='branchId']").val();
        var officeId = $("input[name='officeId']").val();
        var status = $("select[name='status']").val();
        table.column(0).search(id)
            .column(1).search(role)
            .column(2).search(roleName)
            .column(3).search(officeId)
            .column(4).search(branchId)
            .column(5).search(status)
            .draw();
    }

    /* 初始化公司 */
    function initOfficeNm() {
        $.getJSON('/cmm/officectrl/findAll', {
            ajax : 'true'
        }, function(data) {
            var len = data.length;
            var d = [];
            for (var i = 0; i < len; i++) {
                d.push({
                    value: data[i].officeNm,
                    label: data[i].officeNm
                });
            }
            editor.field('officeId').update(d);
        });
    };

    /* 根据公司获取部门 */
    function initBraeNm() {
        var officeId = $('#officeName').val();
        $.getJSON('/cmm/branchctrl/findByName', {
            officeNm : officeId,
            ajax : 'true'
        }, function(data) {
            var len = data.length;
            var d = [];
            for (var i = 0; i < len; i++) {
                d.push({
                    value: data[i].braNm,
                    label: data[i].braNm
                });
            }
            editor.field('branchId').update(d);
        });
    };
</script>
</body>

</html>