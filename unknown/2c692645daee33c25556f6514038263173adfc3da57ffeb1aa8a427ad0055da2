<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">

<head>

    <title data-i18n="nav.examine.title"></title>

    <div th:replace="head"></div>
</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="nav.examine.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.busmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="activity.sysmanage"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="nav.examine.content"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form id="queryForm" class="form-horizontal">
                                <div class="form-group col-sm-4">
                                    <!--请求订单号-->
                                    <label class="col-sm-4 control-label" for="sId" data-i18n="activity.id"></label>
                                    <div class="col-sm-8">
                                        <input type="text" class="form-control" name="sId" id="sId">
                                    </div>

                                    <label class="col-sm-4 control-label" for="sBeginTime" data-i18n="activity.startTm" ></label>
                                    <div class="col-sm-8">
                                        <input type="text" class="form-control" name="sBeginTime" id="sBeginTime">
                                    </div>
                                </div>
                                <div class="form-group col-sm-4">
                                    <label class="col-sm-4 control-label" for="sId" data-i18n="activity.atvNm"></label>
                                    <div class="col-sm-8">
                                        <input type="text" class="form-control" name="sAtvNm" id="sAtvNm">
                                    </div>
                                    <label class="col-sm-4 control-label" for="sBeginTime" data-i18n="activity.endTm" ></label>
                                    <div class="col-sm-8">
                                        <input type="text" class="form-control" name="sEndTime" id="sEndTime">
                                    </div>
                                </div>
                                <div class="form-group col-sm-4">
                                    <!--付款银行-->
                                    <label class="col-sm-4 control-label" for="sexamimeStatus" data-i18n="activity.examimeStatus"></label>
                                    <div class="col-sm-8">
                                        <select class="form-control"  name="sexamimeStatus" id="sexamimeStatus" >
                                            <option value='' data-i18n="activity.all"></option>
                                            <option value='01' data-i18n="activity.examimeNo"></option>
                                            <option value='00' data-i18n="activity.examimeNoPass"></option>
                                            <!--<option value='02' data-i18n="activity.examimePass"></option>-->
                                        </select>

                                    </div>
                                </div>
                                <div class="form-group">
                                        <div class="col-sm-4 col-sm-offset-1">
                                            <button type="button" id="query" class="btn btn-primary" data-i18n="activity.search"></button>
                                            <button type="button" id="examineBtn" class="btn btn-primary" data-toggle="modal"  data-i18n="activity.examine"></button>
                                        </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <!--活动增加和修改详情-->
            <div th:replace="mkm/activity/activityModal"></div>
            <!--数据表格-->
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <div class="table-responsive">
                                <div class="table-responsive">
                                    <table id="dataTables" class="table table-striped table-bordered table-hover ">
                                        <thead>
                                        <tr>
                                            <th data-i18n="activity.id"></th>
                                            <th data-i18n="activity.atvNm"></th>
                                            <th data-i18n="activity.examineStatus"></th>
                                            <th data-i18n="activity.mkTool"></th>
                                            <th data-i18n="activity.tatol"></th>
                                            <th data-i18n="activity.tatolAmt"></th>
                                            <th data-i18n="activity.releaseNum"></th>
                                            <th data-i18n="activity.releaseAmt"></th>
                                            <th data-i18n="activity.aclt"></th>
                                            <th data-i18n="activity.acltAmt"></th>
                                            <th data-i18n="activity.amt"></th>
                                            <th data-i18n="activity.receiveTimes"></th>
                                            <th data-i18n="activity.startTm"></th>
                                            <th data-i18n="activity.endTm"></th>
                                            <th sec:authorize="hasPermission('','/mkm/activity:delete')"
                                                data-i18n="activity.delete"></th>
                                        </tr>
                                        </thead>
                                    </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
        <div th:replace="footer"></div>
    </div>
</div>

<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script type="text/javascript" th:inline="javascript">
    var table;
    $(document).ready(function () {
        var languageUrl;

        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }
        i18nLoad.then(function () {
            table = $('#dataTables').DataTable({
                ajax: {
                    contentType: 'application/json',
                    url: '/mkm/activity/findAll',
                    type: 'POST',
                    data: function (d) {
                        var id = $("#sId").val();
                        var atvNm = $("#sAtvNm").val();
                        var examimeStatus = $("#sexamimeStatus").val();
                        var endTime = $("#sEndTime").val();
                        var beginTime = $("#sBeginTime").val();

                        d.extra_search = {
                            "id":id,
                            "atvNm":atvNm,
                            "endTime":endTime,
                            "beginTime":beginTime,
                            "noExmineStatus" : "02",
                            "examimeStatus":examimeStatus
                        };
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                responsive: true,
                processing: true,
                searching: false,
                select: true,
                language: {
                    url: languageUrl
                    //url: '//cdn.datatables.net/plug-ins/1.10.15/i18n/Chinese.json'
                },
                columns: [{
                    data: 'id'
                }, {
                    data: 'atvNm'
                }, {
                    data: 'examineStatus',
                    render: function (data, type, row) {
                        switch (data) {
                            case '01':
                                return $.i18n.t("activity.examimeNo");
                            case '02':
                                return $.i18n.t("activity.examimePass");
                            case '00':
                                return $.i18n.t("activity.examimeNoPass");
                            default:
                                return "";
                        }
                    }
                }, {
                    data: 'mkTool',
                    render: function (data, type, row) {
                        switch (data) {
                            case '01':
                                return $.i18n.t("activity.eleCoupon");
                            case '02':
                                return $.i18n.t("activity.seaCyy");
                            case '03':
                                return $.i18n.t("activity.coupon");
                            case '04':
                                return $.i18n.t("activity.discount");
                            default:
                                return "";

                        }
                    }
                },{
                    data: 'total'
                },{
                    data: 'totalAmt'
                },{
                    data: 'remainNum'
                },{
                    data: 'remainAmt'
                },{
                    data: 'aclt'
                },{
                    data: 'acltAmt'
                },{
                    data: 'amt'
                },{
                    data: 'receiveTimes'
                },{
                    data: 'beginTime'
                },{
                    data: 'endTime'
                },

                    /*[# sec:authorize="hasPermission('','/mkm/activity:delete')"]*/
                    {
                        data: 'id',
                        orderable: false,
                        searchable: false,
                        render: function (data, type, row) {
                            if(row.status != '00'){
                                return '<span name="trash" title="' + $.i18n.t("activity.delete") + '" id="' + row.id + '" onclick="ajaxClick(\'trash\',\'DELETE\',\'' + row.id + '\')" ' +
                                    'data="/mkm/activity/delete/' + data + '" ' + 'style="cursor: pointer" class="fa fa-trash"></span></td>';
                            } else {
                                return "";
                            }
                        }
                    }
                    /*[/]*/
                ]
            });
        });

        $("#query").click(function () {
            table.ajax.reload();
        })

        //点击审核按钮

        $("#examineBtn").click(function () {
            $("#examineResonDiv").show();
            $("#mkTool").attr("disabled", "disabled");
            $("#atvNm").attr("disabled", "disabled");
            $("#endTime").attr("disabled", "disabled");
            $("#beginTime").attr("disabled", "disabled");
            $("#total").attr("disabled", "disabled");
            $("#totalAmt").attr("disabled", "disabled");
            $("#receiveTimes").attr("disabled", "disabled");
            $("#item").attr("disabled", "disabled");
            $("#amt").attr("disabled", "disabled");
            $("#discount").attr("disabled", "disabled");
            $("#couponValTm").attr("disabled", "disabled");
            $("#couponInvalTm").attr("disabled", "disabled");
            $("#minAmt").attr("disabled", "disabled");
            $("#maxAmt").attr("disabled", "disabled");
            $("#instId").attr("disabled", "disabled");
            $("#costSide").attr("disabled", "disabled");
            $("#couponName").attr("disabled", "disabled");
            $("#startDays").attr("disabled", "disabled");
            $("#userScope").attr("disabled", "disabled");
            $("#couponValDays").attr("disabled", "disabled");
            $("#receiveCycle").attr("disabled", "disabled");
            $("#activityModify").hide();
            $("#activityAdd").hide();
            var row = table.row('.selected');
            if(row.length == 0) {
                $("#handleModal").modal("hide");
                swal($.i18n.t("activity.swal-fail"), $.i18n.t("activity.shouldSelectOne"), "error");
                return;
            }
            $("#handleModal").modal("show");
            <!--获取选中行的内容-->
            var rowData = table.row(row).data();

            <!--ajax异步调起后台服务，根据内部订单号查询明细-->
            var id = rowData["id"];
            $.ajax({
                url:"/mkm/activity/getActivity",
                data:{
                    "id": id
                },
                dataType: "json",
                type:"post",
                success:function(data) {
                    if (data != null) {
                        <!--获取选中行的内容-->
                        $("#atvNm").val(data.atvNm) ;
                        $("#mkTool").val(data.mkTool);
                        if ("02" == data.mkTool){
                            $("#coupon").hide();
                        }else {
                            $("#coupon").show();
                        }
                        $("#endTime").val(data.endTime);
                        $("#beginTime").val(data.beginTime);
                        $("#total").val(data.total);
                        $("#totalAmt").val(data.totalAmt);
                        $("#receiveTimes").val(data.receiveTimes);
                        $("#item").val(data.item);
                        $("#amt").val(data.amt);
                        $("#couponValTm").val(data.couponValTm);
                        $("#couponInvalTm").val(data.couponInvalTm);
                        $("#maxAmt").val(data.maxAmt);
                        $("#minAmt").val(data.minAmt);
                        $("#instId").val(data.instId);
                        $("#discount").val(data.discount);
                        $("#id").val(id);
                        $("#instId").val(data.instId);
                        $("#costSide").val(data.costSide);
                        $("#couponName").val(data.couponName);
                        $("#userScope").val(data.userScope);
                        $("#startDays").val(data.startDays);
                        $("#couponValDays").val(data.couponValDays);
                        $("#examineReson").val(data.examineReson);
                        $("#receiveCycle").val(data.receiveCycle);
                    } else {
                        <!--隐藏模态框-->
                        $("#handleModal").modal("hide");
                        swal($.i18n.t("activity.swal-fail"), $.i18n.t("activity.detailNull"), "error");
                    }
                },
                error: function() {
                    $("#handleModal").modal("hide");
                    swal($.i18n.t("activity.swal-fail"),  "error");
                }
            });

        });
        //审核
        $("#activityExamine").click(function () {

            var examine = $("#examineStatus").val();
            $.ajax({
                url:"/mkm/activity/maintian",
                data:{
                    "id":   $("#id").val(),
                    "examineStatus" : examine,
                    "examineReson" : $("#examineReson").val(),
                },
                dataType: "json",
                type:"post",
                success:function(data) {
                    var result = data.result;
                    if (result !='MKM00000') {
                        swal($.i18n.t("activity.swal-fail"), result , "error");
                    } else {
                        $("#handleModal").modal("hide");
                        swal($.i18n.t("activity.success"));
                        table.ajax.reload();
                    }
                },
                error: function() {
                    swal($.i18n.t("activity.swal-fail"),  "error");
                }
            });

        });


//        //modal模态框隐藏后处理
//        $('#handleModal').on('shown.bs.modal',
//            function () {
//                var row = table.row('.selected');
//                if(row.length == 0) {
//                    $("#handleModal").modal("hide");
//                }
//            }
//        );
    });


    /**初始化日期控件**/

    var sbeginTimePick = $('#sBeginTime').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        onChangeDate: function(dateText, inst) {
            endTimePick.datetimepicker('minDate',new Date(dateText.replace("-", "-")));
        }
    });

    var sendTimePick = $('#sEndTime').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });

    function ajaxClick(name, type, id) {
        var url = $("span[name='" + name + "'][id='" + id + "']").attr("data");
        if (name == "lock") {
            $.ajax({
                type: type,
                url: url,
                success: function (data) {
                    if (data) {
                        alert(data);
                    } else {
                        window.location.reload();
                    }
                }
            })
        } else if (name == "trash") {
            swal({
                title: $.i18n.t("user.swal-title"),
                text: "",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: $.i18n.t("user.swal-confirm"),
                cancelButtonText: $.i18n.t("user.swal-cancel"),
                closeOnConfirm: false
            }, function (isConfirm) {
                if (!isConfirm) return;
                $.ajax({
                    url: url,
                    type: type,
                    success: function () {
                        swal($.i18n.t("user.swal-sucess"), "", "success");
                        $('.confirm').click(function () {   //额外绑定一个事件，当确定执行之后返回成功的页面的确定按钮，点击之后刷新当前页面或者跳转其他页面
                            location.reload();
                        });
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        swal($.i18n.t("user.swal-error"), $.i18n.t("user.swal-error-tips"), "error");
                    }
                });
            });
        }
    }
</script>
</body>

</html>
