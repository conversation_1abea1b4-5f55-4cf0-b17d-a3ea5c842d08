package com.hisun.lemon.urm.service.impl;

import com.hisun.lemon.common.exception.LemonException;
import com.hisun.lemon.common.utils.JudgeUtils;
import com.hisun.lemon.common.utils.NumberUtils;
import com.hisun.lemon.framework.service.BaseService;
import com.hisun.lemon.framework.utils.IdGenUtils;
import com.hisun.lemon.framework.utils.PageUtils;
import com.hisun.lemon.urm.constants.URMConstants;
import com.hisun.lemon.urm.constants.URMMessageCode;
import com.hisun.lemon.urm.dao.IUrmMercOprAuthDao;
import com.hisun.lemon.urm.dao.IUrmSafeInfDao;
import com.hisun.lemon.urm.dao.IUrmSafeLoginDao;
import com.hisun.lemon.urm.entity.*;
import com.hisun.lemon.urm.service.IMercOprService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

import static com.hisun.lemon.urm.utils.CipherProcessorUtils.convertPwdfromZpkToPvk;

/**
 * <AUTHOR>
 * @create 2017/10/12
 */
@Service
@Transactional
public class MercOprServiceImpl extends BaseService implements IMercOprService {

    private static final Logger logger = LoggerFactory.getLogger(MercOprServiceImpl.class);

    @Resource
    private IUrmSafeLoginDao urmSafeLoginDao;

    @Resource
    private IUrmSafeInfDao urmSafeInfDao;

    @Resource
    private IUrmMercOprAuthDao urmMercOprAuthDao;

    @Override
    public void addMercOpr(UrmSafeLoginDO urmSafeLoginDO, UrmSafeInfDO urmSafeInfDO) {

        UrmSafeLoginDO search = urmSafeLoginDao.get(urmSafeLoginDO.getLoginId());
        if (JudgeUtils.isNotNull(search)) {
            throw new LemonException(URMMessageCode.LOGIN_ID_IS_EXISTS);
        }

        List<UrmSafeInfDO> temp = urmSafeInfDao.findByUserId(urmSafeInfDO.getUserId());
        String password = null;
        for (UrmSafeInfDO infDO : temp) {
            if (JudgeUtils.equals(infDO.getOprTyp(), URMConstants.MER_ADMIN_TYP)) {
                password = infDO.getPayPwd();
            }
        }

        if (JudgeUtils.isNull(password)) {
            LemonException.throwBusinessException(URMMessageCode.MER_INF_IS_NULL);
        }
        String userId = urmSafeInfDO.getUserId();
        String loginPwd = urmSafeInfDO.getLoginPwd();
        if (loginPwd.length() != URMConstants.FIFTEEN_PWD_LEN) {
            loginPwd = convertPwdfromZpkToPvk(userId, loginPwd);
        }
        urmSafeInfDO.setLoginPwd(loginPwd);

        String safeId = IdGenUtils.generateId("SafeId", 16);
        urmSafeLoginDO.setSafeId(safeId);
        urmSafeLoginDO.setLoginTyp(URMConstants.USER_DEFINE_LOGIN_ID);
        urmSafeLoginDO.setCreateTime(LocalDateTime.now());
        urmSafeLoginDO.setModifyTime(LocalDateTime.now());

        urmSafeInfDO.setSafeId(safeId);
        urmSafeInfDO.setOprTyp(URMConstants.MER_OPE_TYP);
        urmSafeInfDO.setSafeSts(URMConstants.SAFE_STS_EFF);
        urmSafeInfDO.setPwdSalt(urmSafeInfDO.getUserId());
        urmSafeInfDO.setLoginFailCnt(NumberUtils.toByte("0"));
        urmSafeInfDO.setPayPwd(password);
        urmSafeInfDO.setPayFailCnt(NumberUtils.toByte("0"));
        urmSafeInfDO.setCreDt(LocalDate.now());
        urmSafeInfDO.setCreTm(LocalTime.now());
        urmSafeInfDO.setCreateTime(LocalDateTime.now());
        urmSafeInfDO.setModifyTime(LocalDateTime.now());

        MercOprAuthDo mercOprAuthDo = new MercOprAuthDo();
        mercOprAuthDo.setLoginId(urmSafeLoginDO.getLoginId());
        mercOprAuthDo.setCreateTime(LocalDateTime.now());
        mercOprAuthDo.setModifyTime(LocalDateTime.now());
        mercOprAuthDo.setAuthority(URMConstants.MERC_OPR_INIT_AUTH);

        urmMercOprAuthDao.insert(mercOprAuthDo);
        urmSafeLoginDao.insert(urmSafeLoginDO);
        urmSafeInfDao.insert(urmSafeInfDO);
    }

    @Override
    public void modifyOprInfo(String loginId, String mblNo, String displayNm) {
        String safeId = urmSafeLoginDao.get(loginId).getSafeId();
        UrmSafeLoginDO urmSafeLoginDO = new UrmSafeLoginDO();
        urmSafeLoginDO.setLoginId(loginId);
        urmSafeLoginDO.setDisplayNm(displayNm);
        urmSafeLoginDao.update(urmSafeLoginDO);

        UrmSafeInfDO urmSafeInfDO = new UrmSafeInfDO();
        urmSafeInfDO.setSafeId(safeId);
        urmSafeInfDO.setMblNo(mblNo);
        urmSafeInfDO.setModifyTime(LocalDateTime.now());
        urmSafeInfDao.update(urmSafeInfDO);
    }

    @Override
    public void deleteMercOpr(String loginId, String userId) {
        List<MercOprInfoDO> list = urmSafeInfDao.queryMercOprInfoList(userId, null, loginId, URMConstants.MER_OPE_TYP);
        if (list.isEmpty()) {
            throw new LemonException(URMMessageCode.NO_PERMISSION);
        }

        UrmSafeLoginDO urmSafeLoginDO = urmSafeLoginDao.get(loginId);
        String safeId = urmSafeLoginDO.getSafeId();
        UrmSafeInfDO urmSafeInfDO = urmSafeInfDao.getBySafeId(safeId);
        if (JudgeUtils.notEquals(urmSafeInfDO.getOprTyp(), URMConstants.MER_OPE_TYP)) {
            throw new LemonException(URMMessageCode.NOT_MERCHANT_OPERATOR);
        }

        urmSafeLoginDao.delete(loginId);
        urmSafeInfDao.delete(safeId);
        urmMercOprAuthDao.delete(loginId);
    }

    @Override
    public List<MercOprInfoDO> queryAll(String userId, String displayNm, String loginId, String oprTyp, int num, int size) {
        return PageUtils.pageQuery(num, size, () -> urmSafeInfDao.queryMercOprInfoList(userId, displayNm, loginId, oprTyp));
    }

    @Override
    public int queryListSize(String userId) {
        return urmSafeInfDao.queryTotNum(userId);
    }

    @Override
    public String queryAuthority(String loginId) {
        UrmSafeLoginDO urmSafeLoginDO = urmSafeLoginDao.get(loginId);
        String safeId = urmSafeLoginDO.getSafeId();
        UrmSafeInfDO urmSafeInfDO = urmSafeInfDao.getBySafeId(safeId);

        if (JudgeUtils.equals(urmSafeInfDO.getOprTyp(), URMConstants.USR_TYP)) {
            throw new LemonException(URMMessageCode.NOT_MERCHANT_OPERATOR);
        }

        if (JudgeUtils.equals(urmSafeInfDO.getOprTyp(), URMConstants.MER_ADMIN_TYP)) {
            return URMConstants.MERC_OPR_ADMIN_AUTH;
        }

        MercOprAuthDo mercOprAuthDo = urmMercOprAuthDao.get(loginId);
        if (JudgeUtils.isNull(mercOprAuthDo)) {
            mercOprAuthDo.setLoginId(loginId);
            mercOprAuthDo.setCreateTime(LocalDateTime.now());
            mercOprAuthDo.setModifyTime(LocalDateTime.now());
            mercOprAuthDo.setAuthority(URMConstants.MERC_OPR_INIT_AUTH);
            urmMercOprAuthDao.insert(mercOprAuthDo);
        }
        return urmMercOprAuthDao.get(loginId).getAuthority();
    }

    @Override
    public void modifyMercOprAuth(MercOprAuthDo mercOprAuthDo) {
        mercOprAuthDo.setModifyTime(LocalDateTime.now());
        urmMercOprAuthDao.update(mercOprAuthDo);
    }
}
