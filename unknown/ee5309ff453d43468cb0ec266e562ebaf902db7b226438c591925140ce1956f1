<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title>TMS | 法币开户审核</title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>

            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2>法币开户审核</h2>
                    <ol class="breadcrumb">
                        <li>
                            <a>业务管理</a>
                        </li>
                        <li>
                            <a>商户管理</a>
                        </li>
                        <li class="active">
                            <strong>法币开户审核</strong>
                        </li>
                    </ol>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!-- 查询表单 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12">
                                        <!-- 账户号 -->
                                        <label class="col-sm-2 control-label" for="acNo">账户号</label>
                                        <div class="col-sm-4">
                                            <input name="acNo" id="acNo" class="form-control" value="" />
                                        </div>
                                        <!-- 用户ID -->
                                        <label class="col-sm-2 control-label" for="userId">用户ID</label>
                                        <div class="col-sm-4">
                                            <input name="userId" id="userId" class="form-control" value="" />
                                        </div>
                                    </div>
                                    <div class="form-group col-sm-12">
                                        <!-- 币种 -->
                                        <label class="col-sm-2 control-label" for="ccy">币种</label>
                                        <div class="col-sm-4">
                                            <input name="ccy" id="ccy" class="form-control" value="" />
                                        </div>
                                        <!-- 币种类型 -->
                                        <label class="col-sm-2 control-label" for="ccyType">币种类型</label>
                                        <div class="col-sm-4">
                                            <select name="ccyType" id="ccyType" class="form-control">
                                                <option value="">全部</option>
                                                <option value="FM">法币</option>
                                                <option value="DM">数币</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group col-sm-12">
                                        <!-- 账户状态 -->
                                        <label class="col-sm-2 control-label" for="acSts">账户状态</label>
                                        <div class="col-sm-4">
                                            <select name="acSts" id="acSts" class="form-control">
                                                <option value="">全部</option>
                                                <option value="0">开户</option>
                                                <option value="1">销户</option>
                                                <option value="2" selected>待审核</option>
                                                <option value="3">审核不通过</option>
                                            </select>
                                        </div>
                                        <!-- 开户行/机构 -->
                                        <label class="col-sm-2 control-label" for="bank">开户行/机构</label>
                                        <div class="col-sm-4">
                                            <input name="bank" id="bank" class="form-control" value="" />
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-sm-4 col-sm-offset-1">
                                            <button type="button" id="searchBtn" class="btn btn-primary"
                                                onclick="search()">查询</button>
                                            <button type="button" id="resetBtn" class="btn btn-white"
                                                onclick="resetForm()">重置</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="accountTable" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th>账户号</th>
                                                <th>用户ID</th>
                                                <th>币种</th>
                                                <th>币种类型</th>
                                                <th>账户状态</th>
                                                <th>开户行/机构</th>
                                                <th>账户创建日期</th>
                                                <th>账户创建时间</th>
                                                <th>账户销户日期</th>
                                                <th>账户销户时间</th>
                                                <th>创建时间</th>
                                                <th>更新时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="detailModalLabel">账户详情</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">账户号</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-acNo"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">用户ID</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-userId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">币种</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-ccy"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">币种类型</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-ccyType"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">账户状态</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-acSts"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">开户行/机构</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-bank"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">账户创建日期</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-acCreDt"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">账户创建时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-acCreTm"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">账户销户日期</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-acClsDt"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">账户销户时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-acClsTm"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">创建时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-createTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">更新时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-modifyTime"></p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 审核模态框 -->
    <div class="modal fade" id="auditModal" tabindex="-1" role="dialog" aria-labelledby="auditModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="auditModalLabel">法币开户审核</h4>
                </div>
                <div class="modal-body">
                    <form id="auditForm" class="form-horizontal">
                        <input type="hidden" id="audit-acNo" name="acNo">

                        <div class="form-group">
                            <label class="col-sm-3 control-label">审核结果</label>
                            <div class="col-sm-9">
                                <select class="form-control" id="audit-result" name="checkResult">
                                    <option value="0">审核通过</option>
                                    <option value="3">审核拒绝</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">审核意见</label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="audit-opinion" name="auditOpinion" rows="3"
                                    placeholder="请输入审核意见"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="submitAudit">提交</button>
                </div>
            </div>
        </div>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var table;

        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }

            // 初始化draw值
            $('input[name="draw"]').remove();
            $('body').append('<input type="hidden" name="draw" value="1">');

            // 处理DataTables处理中指示器问题
            $(document).ajaxStop(function () {
                $('.dataTables_processing').hide();
            });

            // 初始化DataTables
            table = $('#accountTable').DataTable({
                dom: 'Blfrtip',
                ajax: {
                    url: '/acm/acc/manage/findAll',
                    type: 'post',
                    data: function (d) {
                        // 添加额外的查询参数
                        var searchData = {
                            acNo: $('#acNo').val(),
                            userId: $('#userId').val(),
                            ccy: $('#ccy').val(),
                            ccyType: $('#ccyType').val(),
                            acSts: $('#acSts').val(),
                            bank: $('#bank').val()
                        };
                        // 将DataTables的参数和查询参数合并
                        return $.extend({}, d, searchData);
                    },
                    dataFilter: function (data) {
                        try {
                            var json = JSON.parse(data);
                            console.log("接收到服务器响应:", json);

                            // 检查返回的数据是否已经是DataTables格式
                            if (json.data && typeof json.recordsTotal !== 'undefined') {
                                return data;
                            }
                            // 如果不是，则手动封装
                            var returnData = {
                                draw: parseInt($('input[name="draw"]').val()) || 1,
                                recordsTotal: json.length,
                                recordsFiltered: json.length,
                                data: json
                            };
                            console.log("返回给DataTables的数据:", returnData);
                            return JSON.stringify(returnData);
                        } catch (e) {
                            console.error("解析服务器响应失败:", e);
                            return JSON.stringify({
                                draw: parseInt($('input[name="draw"]').val()) || 1,
                                recordsTotal: 0,
                                recordsFiltered: 0,
                                data: []
                            });
                        }
                    },
                    error: function (xhr, error, thrown) {
                        console.error('数据加载错误:', error);
                        toastr.error('数据加载失败');
                        $('.dataTables_processing').hide();
                    },
                    complete: function () {
                        $('.dataTables_processing').hide();
                    }
                },
                responsive: true,
                processing: true,
                language: {
                    url: languageUrl,
                    processing: "处理中...",
                    paginate: {
                        first: "首页",
                        last: "末页",
                        next: "下一页",
                        previous: "上一页"
                    },
                    info: "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
                    infoEmpty: "显示第 0 至 0 项结果，共 0 项",
                    infoFiltered: "(由 _MAX_ 项结果过滤)",
                    lengthMenu: "显示 _MENU_ 项结果"
                },
                pageLength: 10,
                lengthMenu: [10, 25, 50, 100],
                pagingType: "simple_numbers",
                drawCallback: function (settings) {
                    $('.dataTables_processing').hide();
                },
                columns: [
                    { data: 'acNo' },
                    { data: 'userId' },
                    { data: 'ccy' },
                    {
                        data: 'ccyType',
                        render: function (data) {
                            if (data === 'FM') return '法币';
                            if (data === 'DM') return '数币';
                            return data || '';
                        }
                    },
                    {
                        data: 'acSts',
                        render: function (data) {
                            if (data === '0') return '<span class="label label-primary">开户</span>';
                            if (data === '1') return '<span class="label label-default">销户</span>';
                            if (data === '2') return '<span class="label label-warning">待审核</span>';
                            if (data === '3') return '<span class="label label-danger">审核不通过</span>';
                            return data || '';
                        }
                    },
                    { data: 'bank' },
                    {
                        data: 'acCreDt',
                        render: function (data) {
                            if (!data) return '';
                            return new Date(data).toLocaleDateString();
                        }
                    },
                    {
                        data: 'acCreTm',
                        render: function (data) {
                            if (!data) return '';
                            return data;
                        }
                    },
                    {
                        data: 'acClsDt',
                        render: function (data) {
                            if (!data) return '';
                            return new Date(data).toLocaleDateString();
                        }
                    },
                    {
                        data: 'acClsTm',
                        render: function (data) {
                            if (!data) return '';
                            return data;
                        }
                    },
                    {
                        data: 'createTime',
                        render: function (data) {
                            if (!data) return '';
                            return new Date(data).toLocaleString();
                        }
                    },
                    {
                        data: 'modifyTime',
                        render: function (data) {
                            if (!data) return '';
                            return new Date(data).toLocaleString();
                        }
                    },
                    {
                        data: null,
                        orderable: false,
                        render: function (data, type, row) {
                            var buttons = '<button type="button" class="btn btn-xs btn-info view-detail" data-row=\'' + JSON.stringify(row) + '\'>详情</button> ';

                            // 只有待审核状态可以进行审核操作
                            if (row.acSts === '2') {
                                buttons += '<button type="button" class="btn btn-xs btn-warning audit-btn" data-acno="' + row.acNo + '">审核</button>';
                            }

                            return buttons;
                        }
                    }
                ],
                buttons: [
                    { extend: 'copyHtml5' },
                    { extend: 'csvHtml5' },
                    { extend: 'excelHtml5', title: '法币开户审核列表' }
                ]
            });

            // 给表格添加绘制完成事件处理
            $('#accountTable').on('draw.dt', function () {
                $('.dataTables_processing').hide();
            });

            // 添加页面长度变化事件监听
            $('#accountTable').on('length.dt', function () {
                $('.dataTables_processing').hide();
            });

            // 添加页面变化事件监听
            $('#accountTable').on('page.dt', function () {
                setTimeout(function () {
                    $('.dataTables_processing').hide();
                }, 500);
            });

            // 查看详情
            $('#accountTable').on('click', '.view-detail', function () {
                var rowData = JSON.parse($(this).attr('data-row'));

                // 填充详情数据
                $('#detail-acNo').text(rowData.acNo || '');
                $('#detail-userId').text(rowData.userId || '');
                $('#detail-ccy').text(rowData.ccy || '');

                var ccyTypeText = rowData.ccyType || '';
                if (rowData.ccyType === 'FM') ccyTypeText = '法币';
                else if (rowData.ccyType === 'DM') ccyTypeText = '数币';
                $('#detail-ccyType').text(ccyTypeText);

                var acStsText = rowData.acSts || '';
                if (rowData.acSts === '0') acStsText = '开户';
                else if (rowData.acSts === '1') acStsText = '销户';
                else if (rowData.acSts === '2') acStsText = '待审核';
                else if (rowData.acSts === '3') acStsText = '审核不通过';
                $('#detail-acSts').text(acStsText);

                $('#detail-bank').text(rowData.bank || '');
                $('#detail-acCreDt').text(rowData.acCreDt ? new Date(rowData.acCreDt).toLocaleDateString() : '');
                $('#detail-acCreTm').text(rowData.acCreTm || '');
                $('#detail-acClsDt').text(rowData.acClsDt ? new Date(rowData.acClsDt).toLocaleDateString() : '');
                $('#detail-acClsTm').text(rowData.acClsTm || '');
                $('#detail-createTime').text(rowData.createTime ? new Date(rowData.createTime).toLocaleString() : '');
                $('#detail-modifyTime').text(rowData.modifyTime ? new Date(rowData.modifyTime).toLocaleString() : '');

                $('#detailModal').modal('show');
            });

            // 审核操作
            $('#accountTable').on('click', '.audit-btn', function () {
                var acNo = $(this).data('acno');
                $('#audit-acNo').val(acNo);
                $('#audit-result').val('0'); // 默认通过
                $('#audit-opinion').val('');
                $('#auditModal').modal('show');
            });

            // 提交审核
            $('#submitAudit').click(function () {
                var acNo = $('#audit-acNo').val();
                var checkResult = $('#audit-result').val();
                var auditOpinion = $('#audit-opinion').val();

                if (!auditOpinion.trim()) {
                    toastr.warning('请输入审核意见');
                    return;
                }

                $.ajax({
                    url: '/acm/acc/manage/check',
                    type: 'post',
                    data: {
                        acNo: acNo,
                        checkResult: checkResult,
                        auditOpinion: auditOpinion
                    },
                    success: function (response) {
                        $('#auditModal').modal('hide');
                        if (response && response.msgCd === '1') {
                            toastr.success('审核成功');
                        } else {
                            toastr.error('审核失败: ' + (response.msgInfo || '未知错误'));
                        }
                        // 刷新表格
                        table.ajax.reload(function () {
                            $('.dataTables_processing').hide();
                        }, false);
                    },
                    error: function (xhr, status, error) {
                        toastr.error('审核失败: ' + error);
                    }
                });
            });
        });

        // 查询函数
        function search() {
            table.ajax.reload();
        }

        // 重置表单函数
        function resetForm() {
            $('#queryForm')[0].reset();
            // 重新设置默认选中的待审核状态
            $('#acSts').val('2');
            table.ajax.reload();
        }
    </script>
</body>

</html>