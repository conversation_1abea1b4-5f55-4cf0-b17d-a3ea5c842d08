<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.lemon.urm.dao.IUrmSafeInfDao" >

    <resultMap id="BaseResultMap" type="com.hisun.lemon.urm.entity.UrmSafeInfDO" >
        <id column="SAFE_ID" property="safeId" jdbcType="VARCHAR" />
        <result column="USER_ID" property="userId" jdbcType="VARCHAR" />
        <result column="MBL_NO" property="mblNo" jdbcType="VARCHAR" />
        <result column="EMAIL" property="email" jdbcType="VARCHAR" />
        <result column="OPR_TYP" property="oprTyp" jdbcType="CHAR" />
        <result column="SAFE_STS" property="safeSts" jdbcType="CHAR" />
        <result column="SAFE_STSW" property="safeStsw" jdbcType="VARCHAR" />
        <result column="PWD_SALT" property="pwdSalt" jdbcType="VARCHAR" />
        <result column="LOGIN_PWD" property="loginPwd" jdbcType="VARCHAR" />
        <result column="HAND_PWD" property="handPwd" jdbcType="VARCHAR" />
        <result column="HAND_FLG" property="handFlg" jdbcType="VARCHAR" />
        <result column="HAND_FAIL_CNT" property="handFailCnt" jdbcType="TINYINT" />
        <result column="LOGIN_FAIL_CNT" property="loginFailCnt" jdbcType="TINYINT" />
        <result column="LOGIN_LCK_DT" property="loginLckDt" jdbcType="DATE" />
        <result column="LOGIN_LCK_TM" property="loginLckTm" jdbcType="TIME" />
        <result column="PAY_PWD" property="payPwd" jdbcType="VARCHAR" />
        <result column="PAY_FAIL_CNT" property="payFailCnt" jdbcType="TINYINT" />
        <result column="PAY_LCK_DT" property="payLckDt" jdbcType="DATE" />
        <result column="PAY_LCK_TM" property="payLckTm" jdbcType="TIME" />
        <result column="SAFE_MBL_NO" property="safeMblNo" jdbcType="VARCHAR" />
        <result column="SAFE_EMAIL" property="safeEmail" jdbcType="VARCHAR" />
        <result column="SAFE_QUES1" property="safeQues1" jdbcType="VARCHAR" />
        <result column="SAFE_ANS1" property="safeAns1" jdbcType="VARCHAR" />
        <result column="SAFE_QUES2" property="safeQues2" jdbcType="VARCHAR" />
        <result column="SAFE_ANS2" property="safeAns2" jdbcType="VARCHAR" />
        <result column="SAFE_QUES3" property="safeQues3" jdbcType="VARCHAR" />
        <result column="SAFE_ANS3" property="safeAns3" jdbcType="VARCHAR" />
        <result column="SAFE_QUES4" property="safeQues4" jdbcType="VARCHAR" />
        <result column="SAFE_ANS4" property="safeAns4" jdbcType="VARCHAR" />
        <result column="SAFE_QUES5" property="safeQues5" jdbcType="VARCHAR" />
        <result column="SAFE_ANS5" property="safeAns5" jdbcType="VARCHAR" />
        <result column="CRE_DT" property="creDt" jdbcType="DATE" />
        <result column="CRE_TM" property="creTm" jdbcType="TIME" />
        <result column="EXP_DT" property="expDt" jdbcType="DATE" />
        <result column="EXP_TM" property="expTm" jdbcType="TIME" />
        <result column="UPD_BUS_CNL" property="updBusCnl" jdbcType="VARCHAR" />
        <result column="UPD_IP_ADR" property="updIpAdr" jdbcType="VARCHAR" />
        <result column="LAST_LOGIN_DT" property="lastLoginDt" jdbcType="DATE" />
        <result column="LAST_LOGIN_TM" property="lastLoginTm" jdbcType="TIME" />
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        SAFE_ID, USER_ID, MBL_NO, EMAIL, OPR_TYP, SAFE_STS, SAFE_STSW, PWD_SALT, LOGIN_PWD, 
        LOGIN_FAIL_CNT, LOGIN_LCK_DT, LOGIN_LCK_TM,  PAY_PWD, PAY_FAIL_CNT, PAY_LCK_DT, PAY_LCK_TM, SAFE_MBL_NO, SAFE_EMAIL,
        SAFE_QUES1, SAFE_ANS1, SAFE_QUES2, SAFE_ANS2, SAFE_QUES3, SAFE_ANS3, SAFE_QUES4, HAND_PWD,HAND_FLG,
        SAFE_ANS4, SAFE_QUES5, SAFE_ANS5, CRE_DT, CRE_TM, EXP_DT, EXP_TM, UPD_BUS_CNL, UPD_IP_ADR, 
        LAST_LOGIN_DT, LAST_LOGIN_TM,HAND_FAIL_CNT
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from urm_safe_inf
        where SAFE_ID IN (
        select safe_id from urm_safe_login where LOGIN_ID = #{loginId, jdbcType=VARCHAR}
        )
    </select>

    <select id="findByUserId" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from urm_safe_inf
        where
        USER_ID = #{userId, jdbcType=VARCHAR}  AND SAFE_STS = '0'
    </select>

    <select id="getBySafeId" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from urm_safe_inf
        where
        SAFE_ID = #{safeId, jdbcType=VARCHAR}  AND SAFE_STS = '0'
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from urm_safe_inf
        where SAFE_ID = #{safeId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.hisun.lemon.urm.entity.UrmSafeInfDO" >
        insert into urm_safe_inf
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="safeId != null" >
                SAFE_ID,
            </if>
            <if test="userId != null" >
                USER_ID,
            </if>
            <if test="mblNo != null" >
                MBL_NO,
            </if>
            <if test="email != null" >
                EMAIL,
            </if>
            <if test="oprTyp != null" >
                OPR_TYP,
            </if>
            <if test="safeSts != null" >
                SAFE_STS,
            </if>
            <if test="safeStsw != null" >
                SAFE_STSW,
            </if>
            <if test="pwdSalt != null" >
                PWD_SALT,
            </if>
            <if test="loginPwd != null" >
                LOGIN_PWD,
            </if>
            <if test="loginFailCnt != null" >
                LOGIN_FAIL_CNT,
            </if>
            <if test="loginLckDt != null" >
                LOGIN_LCK_DT,
            </if>
            <if test="loginLckTm != null" >
                LOGIN_LCK_TM,
            </if>
            <if test="payPwd != null" >
                PAY_PWD,
            </if>
            <if test="payFailCnt != null" >
                PAY_FAIL_CNT,
            </if>
            <if test="payLckDt != null" >
                PAY_LCK_DT,
            </if>
            <if test="payLckTm != null" >
                PAY_LCK_TM,
            </if>
            <if test="safeMblNo != null" >
                SAFE_MBL_NO,
            </if>
            <if test="safeEmail != null" >
                SAFE_EMAIL,
            </if>
            <if test="safeQues1 != null" >
                SAFE_QUES1,
            </if>
            <if test="safeAns1 != null" >
                SAFE_ANS1,
            </if>
            <if test="safeQues2 != null" >
                SAFE_QUES2,
            </if>
            <if test="safeAns2 != null" >
                SAFE_ANS2,
            </if>
            <if test="safeQues3 != null" >
                SAFE_QUES3,
            </if>
            <if test="safeAns3 != null" >
                SAFE_ANS3,
            </if>
            <if test="safeQues4 != null" >
                SAFE_QUES4,
            </if>
            <if test="safeAns4 != null" >
                SAFE_ANS4,
            </if>
            <if test="safeQues5 != null" >
                SAFE_QUES5,
            </if>
            <if test="safeAns5 != null" >
                SAFE_ANS5,
            </if>
            <if test="creDt != null" >
                CRE_DT,
            </if>
            <if test="creTm != null" >
                CRE_TM,
            </if>
            <if test="expDt != null" >
                EXP_DT,
            </if>
            <if test="expTm != null" >
                EXP_TM,
            </if>
            <if test="updBusCnl != null" >
                UPD_BUS_CNL,
            </if>
            <if test="updIpAdr != null" >
                UPD_IP_ADR,
            </if>
            <if test="lastLoginDt != null" >
                LAST_LOGIN_DT,
            </if>
            <if test="lastLoginTm != null" >
                LAST_LOGIN_TM,
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME,
            </if>
            <if test="createTime != null" >
                CREATE_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="safeId != null" >
                #{safeId,jdbcType=VARCHAR},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="mblNo != null" >
                #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="email != null" >
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="oprTyp != null" >
                #{oprTyp,jdbcType=CHAR},
            </if>
            <if test="safeSts != null" >
                #{safeSts,jdbcType=CHAR},
            </if>
            <if test="safeStsw != null" >
                #{safeStsw,jdbcType=VARCHAR},
            </if>
            <if test="pwdSalt != null" >
                #{pwdSalt,jdbcType=VARCHAR},
            </if>
            <if test="loginPwd != null" >
                #{loginPwd,jdbcType=VARCHAR},
            </if>
            <if test="loginFailCnt != null" >
                #{loginFailCnt,jdbcType=TINYINT},
            </if>
            <if test="loginLckDt != null" >
                #{loginLckDt,jdbcType=DATE},
            </if>
            <if test="loginLckTm != null" >
                #{loginLckTm,jdbcType=TIME},
            </if>
            <if test="payPwd != null" >
                #{payPwd,jdbcType=VARCHAR},
            </if>
            <if test="payFailCnt != null" >
                #{payFailCnt,jdbcType=TINYINT},
            </if>
            <if test="payLckDt != null" >
                #{payLckDt,jdbcType=DATE},
            </if>
            <if test="payLckTm != null" >
                #{payLckTm,jdbcType=TIME},
            </if>
            <if test="safeMblNo != null" >
                #{safeMblNo,jdbcType=VARCHAR},
            </if>
            <if test="safeEmail != null" >
                #{safeEmail,jdbcType=VARCHAR},
            </if>
            <if test="safeQues1 != null" >
                #{safeQues1,jdbcType=VARCHAR},
            </if>
            <if test="safeAns1 != null" >
                #{safeAns1,jdbcType=VARCHAR},
            </if>
            <if test="safeQues2 != null" >
                #{safeQues2,jdbcType=VARCHAR},
            </if>
            <if test="safeAns2 != null" >
                #{safeAns2,jdbcType=VARCHAR},
            </if>
            <if test="safeQues3 != null" >
                #{safeQues3,jdbcType=VARCHAR},
            </if>
            <if test="safeAns3 != null" >
                #{safeAns3,jdbcType=VARCHAR},
            </if>
            <if test="safeQues4 != null" >
                #{safeQues4,jdbcType=VARCHAR},
            </if>
            <if test="safeAns4 != null" >
                #{safeAns4,jdbcType=VARCHAR},
            </if>
            <if test="safeQues5 != null" >
                #{safeQues5,jdbcType=VARCHAR},
            </if>
            <if test="safeAns5 != null" >
                #{safeAns5,jdbcType=VARCHAR},
            </if>
            <if test="creDt != null" >
                #{creDt,jdbcType=DATE},
            </if>
            <if test="creTm != null" >
                #{creTm,jdbcType=TIME},
            </if>
            <if test="expDt != null" >
                #{expDt,jdbcType=DATE},
            </if>
            <if test="expTm != null" >
                #{expTm,jdbcType=TIME},
            </if>
            <if test="updBusCnl != null" >
                #{updBusCnl,jdbcType=VARCHAR},
            </if>
            <if test="updIpAdr != null" >
                #{updIpAdr,jdbcType=VARCHAR},
            </if>
            <if test="lastLoginDt != null" >
                #{lastLoginDt,jdbcType=DATE},
            </if>
            <if test="lastLoginTm != null" >
                #{lastLoginTm,jdbcType=TIME},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.hisun.lemon.urm.entity.UrmSafeInfDO" >
        update urm_safe_inf
        <set >
            <if test="userId != null" >
                USER_ID = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="mblNo != null" >
                MBL_NO = #{mblNo,jdbcType=VARCHAR},
            </if>
            <if test="email != null" >
                EMAIL = #{email,jdbcType=VARCHAR},
            </if>
            <if test="oprTyp != null" >
                OPR_TYP = #{oprTyp,jdbcType=CHAR},
            </if>
            <if test="safeSts != null" >
                SAFE_STS = #{safeSts,jdbcType=CHAR},
            </if>
            <if test="safeStsw != null" >
                SAFE_STSW = #{safeStsw,jdbcType=VARCHAR},
            </if>
            <if test="pwdSalt != null" >
                PWD_SALT = #{pwdSalt,jdbcType=VARCHAR},
            </if>
            <if test="loginPwd != null" >
                LOGIN_PWD = #{loginPwd,jdbcType=VARCHAR},
            </if>
            <if test="loginFailCnt != null" >
                LOGIN_FAIL_CNT = #{loginFailCnt,jdbcType=TINYINT},
            </if>
            <if test="loginLckDt != null" >
                LOGIN_LCK_DT = #{loginLckDt,jdbcType=DATE},
            </if>
            <if test="loginLckTm != null" >
                LOGIN_LCK_TM = #{loginLckTm,jdbcType=TIME},
            </if>
            <if test="payPwd != null" >
                PAY_PWD = #{payPwd,jdbcType=VARCHAR},
            </if>
            <if test="payFailCnt != null" >
                PAY_FAIL_CNT = #{payFailCnt,jdbcType=TINYINT},
            </if>
            <if test="payLckDt != null" >
                PAY_LCK_DT = #{payLckDt,jdbcType=DATE},
            </if>
            <if test="payLckTm != null" >
                PAY_LCK_TM = #{payLckTm,jdbcType=TIME},
            </if>
            <if test="safeMblNo != null" >
                SAFE_MBL_NO = #{safeMblNo,jdbcType=VARCHAR},
            </if>
            <if test="safeEmail != null" >
                SAFE_EMAIL = #{safeEmail,jdbcType=VARCHAR},
            </if>
            <if test="safeQues1 != null" >
                SAFE_QUES1 = #{safeQues1,jdbcType=VARCHAR},
            </if>
            <if test="safeAns1 != null" >
                SAFE_ANS1 = #{safeAns1,jdbcType=VARCHAR},
            </if>
            <if test="safeQues2 != null" >
                SAFE_QUES2 = #{safeQues2,jdbcType=VARCHAR},
            </if>
            <if test="safeAns2 != null" >
                SAFE_ANS2 = #{safeAns2,jdbcType=VARCHAR},
            </if>
            <if test="safeQues3 != null" >
                SAFE_QUES3 = #{safeQues3,jdbcType=VARCHAR},
            </if>
            <if test="safeAns3 != null" >
                SAFE_ANS3 = #{safeAns3,jdbcType=VARCHAR},
            </if>
            <if test="safeQues4 != null" >
                SAFE_QUES4 = #{safeQues4,jdbcType=VARCHAR},
            </if>
            <if test="safeAns4 != null" >
                SAFE_ANS4 = #{safeAns4,jdbcType=VARCHAR},
            </if>
            <if test="safeQues5 != null" >
                SAFE_QUES5 = #{safeQues5,jdbcType=VARCHAR},
            </if>
            <if test="safeAns5 != null" >
                SAFE_ANS5 = #{safeAns5,jdbcType=VARCHAR},
            </if>
            <if test="creDt != null" >
                CRE_DT = #{creDt,jdbcType=DATE},
            </if>
            <if test="creTm != null" >
                CRE_TM = #{creTm,jdbcType=TIME},
            </if>
            <if test="expDt != null" >
                EXP_DT = #{expDt,jdbcType=DATE},
            </if>
            <if test="expTm != null" >
                EXP_TM = #{expTm,jdbcType=TIME},
            </if>
            <if test="updBusCnl != null" >
                UPD_BUS_CNL = #{updBusCnl,jdbcType=VARCHAR},
            </if>
            <if test="updIpAdr != null" >
                UPD_IP_ADR = #{updIpAdr,jdbcType=VARCHAR},
            </if>
            <if test="lastLoginDt != null" >
                LAST_LOGIN_DT = #{lastLoginDt,jdbcType=DATE},
            </if>
            <if test="lastLoginTm != null" >
                LAST_LOGIN_TM = #{lastLoginTm,jdbcType=TIME},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="handPwd != null" >
                HAND_PWD = #{handPwd,jdbcType=VARCHAR},
            </if>
            <if test="handFlg != null" >
                HAND_FLG = #{handFlg,jdbcType=VARCHAR},
            </if>
            <if test="handFailCnt != null" >
                HAND_FAIL_CNT = #{handFailCnt,jdbcType=TINYINT},
            </if>
        </set>
        where SAFE_ID = #{safeId,jdbcType=VARCHAR}
    </update>

    <update id="initPwdInf" parameterType="com.hisun.lemon.urm.entity.UrmSafeInfDO" >
        update urm_safe_inf
        <set >
            <if test="loginFailCnt != null" >
                LOGIN_FAIL_CNT = #{loginFailCnt,jdbcType=TINYINT},
            </if>
            <if test="loginLckDt != null" >
                LOGIN_LCK_DT = #{loginLckDt,jdbcType=DATE},
            </if>
            <if test="loginLckTm != null" >
                LOGIN_LCK_TM = #{loginLckTm,jdbcType=TIME},
            </if>
            <if test="payFailCnt != null" >
                PAY_FAIL_CNT = #{payFailCnt,jdbcType=TINYINT},
            </if>
            <if test="payLckDt != null" >
                PAY_LCK_DT = #{payLckDt,jdbcType=DATE},
            </if>
            <if test="payLckTm != null" >
                PAY_LCK_TM = #{payLckTm,jdbcType=TIME},
            </if>
            <if test="updBusCnl != null" >
                UPD_BUS_CNL = #{updBusCnl,jdbcType=VARCHAR},
            </if>
            <if test="updIpAdr != null" >
                UPD_IP_ADR = #{updIpAdr,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null" >
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where USER_ID = #{userId,jdbcType=VARCHAR}
    </update>

    <update id="updateFlg" >
        update urm_safe_inf set hand_flg = #{flg,jdbcType=VARCHAR}, HAND_PWD = null
        where hand_flg = #{flgOld,jdbcType=VARCHAR}
        and USER_ID = #{userId,jdbcType=VARCHAR}
    </update>

    <update id="updateHandInfo" >
        update urm_safe_inf set hand_flg = 0 , HAND_PWD = null, HAND_FAIL_CNT = 0
        where  USER_ID = #{userId,jdbcType=VARCHAR}
    </update>

    <select id="queryMercOprInfoList" parameterType="java.lang.String" resultType="com.hisun.lemon.urm.entity.MercOprInfoDO">
        select info.SAFE_ID as safeId, login.LOGIN_ID as loginId, login.DISPLAY_NM as displayNm, info.OPR_TYP as oprTyp
        from urm_safe_inf info LEFT JOIN urm_safe_login login ON info.SAFE_ID = login.SAFE_ID
        WHERE info.USER_ID = #{userId,jdbcType=VARCHAR}
        <if test="oprTyp != null" >
            AND info.OPR_TYP = #{oprTyp,jdbcType=VARCHAR}
        </if>
        <if test="loginId != null" >
            AND login.LOGIN_ID like concat('%',#{loginId,jdbcType=VARCHAR},'%')
        </if>
        <if test="displayNm != null" >
            AND login.DISPLAY_NM like concat('%',#{displayNm,jdbcType=VARCHAR},'%')
        </if>
    </select>

    <select id="queryTotNum" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(info.SAFE_ID) as totNum from urm_safe_inf info
        WHERE info.USER_ID = #{userId,jdbcType=VARCHAR} and info.OPR_TYP = '1'
    </select>

</mapper>