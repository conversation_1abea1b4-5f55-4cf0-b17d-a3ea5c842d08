<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title>TMS | KYB复核</title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>

            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2>KYB复核</h2>
                    <ol class="breadcrumb">
                        <li>
                            <a>业务管理</a>
                        </li>
                        <li>
                            <a>商户管理</a>
                        </li>
                        <li class="active">
                            <strong>KYB复核</strong>
                        </li>
                    </ol>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!-- 查询表单 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12">
                                        <!-- 用户ID -->
                                        <label class="col-sm-2 control-label" for="userId">用户ID</label>
                                        <div class="col-sm-4">
                                            <input name="userId" id="userId" class="form-control" value="" />
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-sm-4 col-sm-offset-1">
                                            <button type="button" id="searchBtn" class="btn btn-primary">查询</button>
                                            <button type="button" id="resetBtn" class="btn btn-default">重置</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="kybTable" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th>KYB ID</th>
                                                <th>用户ID</th>
                                                <th>审核状态</th>
                                                <th>初审人</th>
                                                <th>初审时间</th>
                                                <th>创建时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="detailModalLabel">KYB信息详情</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">KYB审核ID</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-kybId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">用户ID</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-userId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">审核状态</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-examineStatus"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">创建时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-createTime"></p>
                            </div>
                        </div>
                        <!-- 企业信息 -->
                        <div class="form-group">
                            <label class="col-sm-3 control-label">企业全称中文</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-corpFullNameCn"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">企业全称英文</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-corpFullNameEng"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">注册国家/地区</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-registRegion"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">联系人姓名</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-contactName"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">联系人邮箱</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-contactEmail"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">联系人电话</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-contactNumber"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">企业法定性质</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-entityLegalForm"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">企业业务性质</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-entityBusinessForm"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">企业网站</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-webUrl"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">注册日期</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-registTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">注册地址</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-registAddr"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">实际地址</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-actualAddr"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">注册号码</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-registCode"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">公司注册证明书CI</label>
                            <div class="col-sm-9" id="detail-certificateOfIncorporationCi">
                                <!-- 文件链接将通过JS动态添加 -->
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">商业登记证BR</label>
                            <div class="col-sm-9" id="detail-businessRegistrationBr">
                                <!-- 文件链接将通过JS动态添加 -->
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">NNC1/NAR1</label>
                            <div class="col-sm-9" id="detail-nnc1Nar1">
                                <!-- 文件链接将通过JS动态添加 -->
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">公司章程</label>
                            <div class="col-sm-9" id="detail-articleOfAssociation">
                                <!-- 文件链接将通过JS动态添加 -->
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">股权证明</label>
                            <div class="col-sm-9" id="detail-structureOfMembers">
                                <!-- 文件链接将通过JS动态添加 -->
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">董事/同等职位人员</label>
                            <div class="col-sm-9" id="detail-ceoIdentification">
                                <!-- 文件链接将通过JS动态添加 -->
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">实际受益人</label>
                            <div class="col-sm-9" id="detail-ultimateBeneficialOwners">
                                <!-- 实际受益人信息将通过JS动态添加 -->
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">企业主要业务描述</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-businessDesc"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">预计月交易额</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-expectedMonthAmt"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">主要资金来源</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-sourceOfFund"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">是否存在敏感身份</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-isSensitive"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">附加说明/补充材料</label>
                            <div class="col-sm-9" id="detail-additional">
                                <!-- 文件链接将通过JS动态添加 -->
                            </div>
                        </div>
                        <!-- 初审信息 -->
                        <div class="form-group">
                            <label class="col-sm-3 control-label">初审人</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-firstAuditUser"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">初审时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-firstAuditTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">初审意见</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-firstAuditOpinion"></p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 复核模态框 -->
    <div class="modal fade" id="reviewModal" tabindex="-1" role="dialog" aria-labelledby="reviewModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="reviewModalLabel">KYB复核</h4>
                </div>
                <div class="modal-body">
                    <form id="reviewForm" class="form-horizontal">
                        <input type="hidden" id="review-kybId" name="kybId">

                        <div class="form-group">
                            <label class="col-sm-3 control-label">复核结果</label>
                            <div class="col-sm-9">
                                <select class="form-control" id="review-result" name="auditResult">
                                    <option value="00">复核通过</option>
                                    <option value="01">复核拒绝</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">复核意见</label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="review-opinion" name="auditOpinion"
                                    rows="3"></textarea>
                            </div>
                        </div>

                        <div class="form-group reject-reason-group" style="display:none;">
                            <label class="col-sm-3 control-label">拒绝原因</label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="review-reject-reason" name="rejectReason"
                                    rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="submitReview">提交</button>
                </div>
            </div>
        </div>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var table;

        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }

            // 初始化draw值
            $('input[name="draw"]').remove();
            $('body').append('<input type="hidden" name="draw" value="1">');

            // 处理DataTables处理中指示器问题
            $(document).ajaxStop(function () {
                $('.dataTables_processing').hide();
            });

            // 初始化DataTables
            table = $('#kybTable').DataTable({
                dom: 'Blfrtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/urm/mermgr/kyb/review/list',
                    type: 'post',
                    data: function (d) {
                        // 更新draw值供dataFilter使用
                        $('input[name="draw"]').val(d.draw);

                        // 创建请求参数
                        var param = {
                            body: {
                                pageNum: d.start / d.length + 1,
                                pageSize: d.length,
                                userId: $('#userId').val()
                            }
                        };

                        return JSON.stringify(param);
                    },
                    dataFilter: function (data) {
                        var json = JSON.parse(data);
                        console.log("接收到服务器响应:", json);

                        var returnData = {
                            draw: parseInt($('input[name="draw"]').val()) || 1,
                            recordsTotal: json.body.queryKybList.length,
                            recordsFiltered: json.body.queryKybList.length,
                            data: json.body.queryKybList
                        };

                        return JSON.stringify(returnData);
                    },
                    error: function (xhr, error, thrown) {
                        console.error('数据加载错误:', error);
                        toastr.error('数据加载失败');
                        $('.dataTables_processing').hide();
                    }
                },
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
                language: {
                    url: languageUrl,
                    processing: "处理中...",
                    paginate: {
                        first: "首页",
                        last: "末页",
                        next: "下一页",
                        previous: "上一页"
                    },
                    info: "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
                    infoEmpty: "显示第 0 至 0 项结果，共 0 项",
                    infoFiltered: "(由 _MAX_ 项结果过滤)",
                    lengthMenu: "显示 _MENU_ 项结果"
                },
                pageLength: 10,
                lengthMenu: [10, 25, 50, 100],
                pagingType: "simple_numbers",
                drawCallback: function (settings) {
                    $('.dataTables_processing').hide();
                },
                columns: [
                    { data: 'kybId' },
                    { data: 'userId' },
                    {
                        data: 'examineStatus',
                        render: function (data) {
                            if (data === '00') return '待审核';
                            if (data === '02') return '初审中';
                            if (data === '03') return '复核中';
                            if (data === '04') return '审核通过';
                            if (data === '05') return '拒绝';
                            return data;
                        }
                    },
                    { data: 'firstAuditUser' },
                    {
                        data: 'firstAuditTime',
                        render: function (data) {
                            if (!data) return '';
                            return new Date(data).toLocaleString();
                        }
                    },
                    {
                        data: 'createTime',
                        render: function (data) {
                            if (!data) return '';
                            return new Date(data).toLocaleString();
                        }
                    },
                    {
                        data: null,
                        orderable: false,
                        render: function (data, type, row) {
                            var buttons = '<button type="button" class="btn btn-xs btn-info view-detail" data-id="' + row.kybId + '">详情</button> ';

                            // 只有复核中状态可以进行复核操作
                            if (row.examineStatus === '03') {
                                buttons += '<button type="button" class="btn btn-xs btn-primary review-btn" data-id="' + row.kybId + '">复核</button>';
                            }

                            return buttons;
                        }
                    }
                ],
                buttons: [
                    { extend: 'copyHtml5' },
                    { extend: 'csvHtml5' },
                    { extend: 'excelHtml5', title: 'KYB复核列表' }
                ]
            });

            // 给表格添加绘制完成事件处理
            $('#kybTable').on('draw.dt', function () {
                $('.dataTables_processing').hide();
            });

            // 添加页面长度变化事件监听
            $('#kybTable').on('length.dt', function () {
                $('.dataTables_processing').hide();
            });

            // 添加页面变化事件监听
            $('#kybTable').on('page.dt', function () {
                setTimeout(function () {
                    $('.dataTables_processing').hide();
                }, 500);
            });

            // 查询按钮点击事件
            $("#searchBtn").click(function () {
                table.ajax.reload(function () {
                    $('.dataTables_processing').hide();
                }, false);
            });

            // 重置按钮点击事件
            $("#resetBtn").click(function () {
                $("#userId").val("");
                table.ajax.reload(function () {
                    $('.dataTables_processing').hide();
                }, false);
            });

            // 查看详情
            $('#kybTable').on('click', '.view-detail', function () {
                var kybId = $(this).data('id');
                $.ajax({
                    url: '/urm/mermgr/kyb/review/detail',
                    type: 'post',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        body: {
                            kybId: kybId
                        }
                    }),
                    success: function (response) {
                        if (response.body) {
                            var data = response.body;
                            // 填充详情数据
                            $('#detail-kybId').text(data.kybId || '');
                            $('#detail-userId').text(data.userId || '');

                            // 格式化审核状态
                            var statusText = '';
                            if (data.examineStatus === '00') statusText = '待审核';
                            else if (data.examineStatus === '02') statusText = '初审中';
                            else if (data.examineStatus === '03') statusText = '复核中';
                            else if (data.examineStatus === '04') statusText = '审核通过';
                            else if (data.examineStatus === '05') statusText = '拒绝';
                            else statusText = data.examineStatus || '';
                            $('#detail-examineStatus').text(statusText);

                            $('#detail-createTime').text(data.createTime ? new Date(data.createTime).toLocaleString() : '');
                            $('#detail-corpFullNameCn').text(data.corpFullNameCn || '');
                            $('#detail-corpFullNameEng').text(data.corpFullNameEng || '');
                            $('#detail-registRegion').text(data.registRegion || '');
                            $('#detail-contactName').text(data.contactName || '');
                            $('#detail-contactEmail').text(data.contactEmail || '');
                            $('#detail-contactNumber').text(data.contactNumber || '');
                            $('#detail-entityLegalForm').text(data.entityLegalForm || '');
                            $('#detail-entityBusinessForm').text(data.entityBusinessForm || '');
                            $('#detail-webUrl').text(data.webUrl || '');
                            $('#detail-registTime').text(data.registTime ? new Date(data.registTime).toLocaleString() : '');
                            $('#detail-registAddr').text(data.registAddr || '');
                            $('#detail-actualAddr').text(data.actualAddr || '');
                            $('#detail-registCode').text(data.registCode || '');
                            $('#detail-businessDesc').text(data.businessDesc || '');
                            $('#detail-expectedMonthAmt').text(data.expectedMonthAmt || '');
                            $('#detail-sourceOfFund').text(data.sourceOfFund || '');
                            $('#detail-isSensitive').text(data.isSensitive === 'Y' ? '是' : '否');
                            
                            // 处理文件类型字段
                            renderFileLinks('#detail-certificateOfIncorporationCi', data.certificateOfIncorporationCi);
                            renderFileLinks('#detail-businessRegistrationBr', data.businessRegistrationBr);
                            renderFileLinks('#detail-nnc1Nar1', data.nnc1Nar1);
                            renderFileLinks('#detail-articleOfAssociation', data.articleOfAssociation);
                            renderFileLinks('#detail-structureOfMembers', data.structureOfMembers);
                            renderFileLinks('#detail-ceoIdentification', data.ceoIdentification);
                            renderFileLinks('#detail-additional', data.additional);
                            
                            // 处理实际受益人信息
                            renderUboInfo('#detail-ultimateBeneficialOwners', data.ultimateBeneficialOwners);

                            // 初审信息
                            $('#detail-firstAuditUser').text(data.firstAuditUser || '');
                            $('#detail-firstAuditTime').text(data.firstAuditTime ? new Date(data.firstAuditTime).toLocaleString() : '');
                            $('#detail-firstAuditOpinion').text(data.firstAuditOpinion || '');

                            // 显示模态框
                            $('#detailModal').modal('show');
                        } else {
                            toastr.error('获取详情失败');
                        }
                    },
                    error: function (xhr, status, error) {
                        toastr.error('获取详情失败: ' + error);
                    }
                });
            });

            // 复核操作
            $('#kybTable').on('click', '.review-btn', function () {
                var kybId = $(this).data('id');
                $('#review-kybId').val(kybId);
                $('#review-result').val('00'); // 默认通过
                $('#review-opinion').val('');
                $('#review-reject-reason').val('');
                $('.reject-reason-group').hide(); // 隐藏拒绝原因
                $('#reviewModal').modal('show');
            });

            // 复核结果改变时显示/隐藏拒绝原因字段
            $('#review-result').change(function () {
                if ($(this).val() === '01') {
                    $('.reject-reason-group').show();
                } else {
                    $('.reject-reason-group').hide();
                }
            });

            // 渲染文件链接
            function renderFileLinks(selector, files) {
                var container = $(selector);
                container.empty();
                
                if (!files || files.length === 0) {
                    container.append('<p class="form-control-static">无</p>');
                    return;
                }
                
                var fileList = $('<div class="file-list"></div>');
                
                $.each(files, function(index, filePath) {
                    var fileName = filePath.split('/').pop();
                    var fileExt = fileName.split('.').pop().toLowerCase();
                    var isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp'].indexOf(fileExt) !== -1;
                    var isPdf = fileExt === 'pdf';
                    
                    var fileLink = $('<div class="file-item"></div>');
                    
                    if (isImage) {
                        fileLink.append('<a href="' + filePath + '" target="_blank" class="file-link">' +
                            '<img src="' + filePath + '" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">' +
                            '<span class="file-name">' + fileName + '</span></a>');
                    } else if (isPdf) {
                        fileLink.append('<a href="' + filePath + '" target="_blank" class="file-link">' +
                            '<i class="fa fa-file-pdf-o" style="font-size: 24px; color: #e74c3c;"></i> ' +
                            '<span class="file-name">' + fileName + '</span></a>');
                    } else {
                        fileLink.append('<a href="' + filePath + '" target="_blank" class="file-link">' +
                            '<i class="fa fa-file-o" style="font-size: 24px;"></i> ' +
                            '<span class="file-name">' + fileName + '</span></a>');
                    }
                    
                    fileList.append(fileLink);
                });
                
                container.append(fileList);
            }
            
            // 渲染实际受益人信息
            function renderUboInfo(selector, uboList) {
                var container = $(selector);
                container.empty();
                
                if (!uboList || uboList.length === 0) {
                    container.append('<p class="form-control-static">无</p>');
                    return;
                }
                
                var uboContainer = $('<div class="ubo-container"></div>');
                
                $.each(uboList, function(index, ubo) {
                    var uboItem = $('<div class="ubo-item panel panel-default"></div>');
                    var uboHeader = $('<div class="panel-heading">实际受益人 #' + (index + 1) + '</div>');
                    var uboBody = $('<div class="panel-body"></div>');
                    
                    // 添加角色描述
                    if (ubo.ultimateBeneficialOwnersRoleDesc) {
                        uboBody.append('<div class="ubo-role"><strong>角色描述：</strong>' + 
                            ubo.ultimateBeneficialOwnersRoleDesc + '</div>');
                    }
                    
                    // 添加文件
                    if (ubo.ultimateBeneficialOwnersFilePath && ubo.ultimateBeneficialOwnersFilePath.length > 0) {
                        var fileContainer = $('<div class="ubo-files"><strong>相关文件：</strong></div>');
                        var fileList = $('<div class="file-list"></div>');
                        
                        $.each(ubo.ultimateBeneficialOwnersFilePath, function(fileIndex, filePath) {
                            var fileName = filePath.split('/').pop();
                            var fileExt = fileName.split('.').pop().toLowerCase();
                            var isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp'].indexOf(fileExt) !== -1;
                            var isPdf = fileExt === 'pdf';
                            
                            var fileLink = $('<div class="file-item"></div>');
                            
                            if (isImage) {
                                fileLink.append('<a href="' + filePath + '" target="_blank" class="file-link">' +
                                    '<img src="' + filePath + '" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">' +
                                    '<span class="file-name">' + fileName + '</span></a>');
                            } else if (isPdf) {
                                fileLink.append('<a href="' + filePath + '" target="_blank" class="file-link">' +
                                    '<i class="fa fa-file-pdf-o" style="font-size: 24px; color: #e74c3c;"></i> ' +
                                    '<span class="file-name">' + fileName + '</span></a>');
                            } else {
                                fileLink.append('<a href="' + filePath + '" target="_blank" class="file-link">' +
                                    '<i class="fa fa-file-o" style="font-size: 24px;"></i> ' +
                                    '<span class="file-name">' + fileName + '</span></a>');
                            }
                            
                            fileList.append(fileLink);
                        });
                        
                        fileContainer.append(fileList);
                        uboBody.append(fileContainer);
                    }
                    
                    uboItem.append(uboHeader).append(uboBody);
                    uboContainer.append(uboItem);
                });
                
                container.append(uboContainer);
            }
            
            // 提交复核
            $('#submitReview').click(function () {
                var auditResult = $('#review-result').val();
                var auditOpinion = $('#review-opinion').val();
                var rejectReason = $('#review-reject-reason').val();

                if (!auditOpinion) {
                    toastr.warning('请填写复核意见');
                    return;
                }

                if (auditResult === '01' && !rejectReason) {
                    toastr.warning('请填写拒绝原因');
                    return;
                }

                var formData = {
                    body: {
                        kybId: parseInt($('#review-kybId').val()),
                        auditTimes: 1, // 复核
                        auditResult: auditResult,
                        auditOpinion: auditOpinion,
                        rejectReason: rejectReason
                    }
                };

                $.ajax({
                    url: '/urm/mermgr/kyb/review/audit',
                    type: 'post',
                    contentType: 'application/json',
                    data: JSON.stringify(formData),
                    success: function (response) {
                        $('#reviewModal').modal('hide');
                        toastr.success('复核成功');
                        // 刷新表格
                        table.ajax.reload(function () {
                            $('.dataTables_processing').hide();
                        }, false);
                    },
                    error: function (xhr, status, error) {
                        toastr.error('复核失败: ' + error);
                    }
                });
            });
        });
    </script>
</body>

</html>