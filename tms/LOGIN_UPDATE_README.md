# TMS 登录页面更新说明

## 概述
根据 Figma 设计 (Web3.0钱包-PC) 更新了 TMS 系统的登录页面，采用现代化的 Web3.0 风格设计。

## 主要变更

### 1. 视觉设计更新
- **新的配色方案**: 采用紫色主题 (rgb(145, 85, 253))
- **现代化卡片设计**: 白色卡片带圆角和阴影效果
- **渐变背景**: 紫色渐变背景增强视觉效果
- **装饰性元素**: 添加背景装饰图案

### 2. 用户界面改进
- **输入框重设计**: 带标签和图标的现代输入框
- **按钮样式更新**: 紫色主题按钮带悬停效果
- **语言切换器**: 重新设计的语言选择器，位于右下角
- **响应式设计**: 适配移动端和桌面端

### 3. 技术实现
- **CSS 变量系统**: 使用 CSS 自定义属性管理设计令牌
- **模块化样式**: 独立的 CSS 文件 (`login-web3.css`)
- **保持原有逻辑**: 完全保留原有的 JavaScript 功能
- **国际化支持**: 保持 i18next 国际化功能

## 文件变更

### 新增文件
1. `tms/src/main/resources/static/css/login-web3.css` - 新的登录页面样式
2. `tms/src/main/resources/static/figma-assets/` - Figma 设计资源
   - `Bg-x1.png` - 背景图片
   - `Tree-x1.png` - 装饰树图案
   - `Mask-x1.png` - 装饰遮罩图案
   - `reference.png` - 设计参考图
3. `tms/src/main/resources/static/test-login.html` - 测试页面

### 修改文件
1. `tms/src/main/resources/templates/login.html` - 主登录页面模板

## 设计特性

### CSS 变量系统
```css
:root {
    --color-primary: rgb(145, 85, 253);
    --color-primary-dark: rgb(130, 71, 229);
    --color-background: rgb(244, 245, 250);
    --color-white: rgb(255, 255, 255);
    --shadow-card: 0px 2px 10px 0px rgba(58, 53, 65, 0.1);
    --radius-card: 6px;
    /* ... 更多变量 */
}
```

### 响应式断点
- **桌面端**: 默认样式
- **移动端**: `@media (max-width: 768px)`

### 动画效果
- **卡片入场动画**: `fadeInUp` 动画
- **按钮悬停效果**: 透明度和位移变化
- **输入框焦点效果**: 边框颜色和阴影变化

## 功能保持

### JavaScript 功能完全保留
1. **国际化 (i18next)**
   - 多语言支持 (中文/英文/柬埔寨语)
   - 动态语言切换
   - Cookie 语言记忆

2. **表单增强**
   - 输入验证
   - 加载状态显示
   - 焦点状态管理

3. **用户体验**
   - 错误消息显示
   - 记住密码功能
   - 忘记密码链接

## 兼容性

### 浏览器支持
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### 设备支持
- 桌面端 (1200px+)
- 平板端 (768px - 1199px)
- 移动端 (< 768px)

## 测试

### 测试页面
访问 `/test-login.html` 查看新设计的静态版本

### 功能测试
1. 语言切换功能
2. 表单输入和验证
3. 响应式布局
4. 动画效果
5. 错误状态显示

## 部署注意事项

1. **资源文件**: 确保 `figma-assets` 目录下的图片文件正确部署
2. **CSS 文件**: 确保 `login-web3.css` 文件可访问
3. **字体支持**: 系统会回退到系统字体
4. **图片回退**: 如果 Figma 资源加载失败，会显示 CSS 渐变背景

## 维护建议

1. **样式修改**: 优先修改 CSS 变量而不是具体样式
2. **响应式调整**: 使用现有的断点系统
3. **颜色更新**: 通过 CSS 变量统一管理
4. **动画调整**: 修改 CSS 动画参数而不是 JavaScript

## 回滚方案

如需回滚到原始设计：
1. 恢复 `login.html` 的原始版本
2. 移除 `login-web3.css` 引用
3. 删除 `figma-assets` 目录

原始备份文件建议保存在版本控制系统中。
