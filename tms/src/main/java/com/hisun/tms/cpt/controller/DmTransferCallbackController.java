package com.hisun.tms.cpt.controller;

import com.hisun.tms.cpt.entity.DmTransferCallbackDO;
import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.service.IDmTransferCallbackService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;

/**
 * Cregis回调记录查询控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/30
 */
@Controller
@RequestMapping("/cpt/dcorder/callback")
public class DmTransferCallbackController {
    
    private static final Logger logger = LoggerFactory.getLogger(DmTransferCallbackController.class);

    @Resource
    private IDmTransferCallbackService dmTransferCallbackService;

    /**
     * 返回Cregis回调记录查询功能主页面
     * 
     * @return
     */
    @GetMapping
    @PreAuthorize("hasPermission('','/cptmgr/dcorder/callback') or hasRole('ROLE_ADMIN')")
    public ModelAndView callbackList() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("cpt/dcorder/callback/callback");
        return modelAndView;
    }

    /**
     * Cregis回调记录列表查询
     * 
     * @param input
     * @return
     */
    @PostMapping(value = "/findAll")
    @PreAuthorize("hasPermission('','/cptmgr/dcorder/callback') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DataTablesOutput<DmTransferCallbackDO> findAll( @RequestBody GenericParamInput input) {
        return dmTransferCallbackService.findAll(input);
    }

    /**
     * 根据ID查询回调记录详细信息
     * 
     * @param id
     * @return
     */
    @PostMapping(value = "/findDetail")
    @PreAuthorize("hasPermission('','/cptmgr/dcorder/callback') or hasRole('ROLE_ADMIN')")
    @ResponseBody
    public DmTransferCallbackDO findDetail(@RequestParam Long id) {
        logger.debug("tms DmTransferCallbackController.findDetail() id = " + id);
        return dmTransferCallbackService.getDetail(id);
    }
}