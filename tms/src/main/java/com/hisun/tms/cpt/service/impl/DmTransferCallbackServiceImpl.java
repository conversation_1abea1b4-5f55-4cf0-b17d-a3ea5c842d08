package com.hisun.tms.cpt.service.impl;

import com.hisun.tms.cpt.dao.IDmTransferCallbackDao;
import com.hisun.tms.cpt.entity.DmTransferCallbackDO;
import com.hisun.tms.cpt.model.GenericParamInput;
import com.hisun.tms.cpt.service.IDmTransferCallbackService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数币-交易回调记录查询服务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/30
 */
@Service("dmTransferCallbackService")
public class DmTransferCallbackServiceImpl implements IDmTransferCallbackService {

    private static final Logger logger = LoggerFactory.getLogger(DmTransferCallbackServiceImpl.class);

    @Resource
    private IDmTransferCallbackDao dmTransferCallbackDao;

    @Override
    public DataTablesOutput<DmTransferCallbackDO> findAll(GenericParamInput input) {
        DataTablesOutput<DmTransferCallbackDO> dataTablesOutput = new DataTablesOutput<>();
        
        try {
            Map<String, Object> param = new HashMap<>();

            // 处理查询条件
            if (input.getExtra_search() != null && !input.getExtra_search().isEmpty()) {
                param.putAll(input.getExtra_search());
            }
            
            // 分页参数
            param.put("start", input.getStart());
            param.put("length", input.getLength());
            
            // 查询数据
            List<DmTransferCallbackDO> data = dmTransferCallbackDao.findByQueryCondition(param);
            int total = dmTransferCallbackDao.countTotal(param);
            
            dataTablesOutput.setData(data);
            dataTablesOutput.setRecordsTotal(total);
            dataTablesOutput.setRecordsFiltered(total);
            dataTablesOutput.setDraw(input.getDraw());
            
        } catch (Exception e) {
            logger.error("查询Cregis回调记录失败", e);
            dataTablesOutput.setError("查询失败：" + e.getMessage());
        }
        
        return dataTablesOutput;
    }

    @Override
    public DmTransferCallbackDO getDetail(Long id) {
        try {
            return dmTransferCallbackDao.getById(id);
        } catch (Exception e) {
            logger.error("获取Cregis回调记录详情失败，ID：" + id, e);
            return null;
        }
    }
}