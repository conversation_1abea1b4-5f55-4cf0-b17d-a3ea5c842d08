package com.hisun.tms.cpt.service;

import com.hisun.tms.cpt.entity.DmTransferCallbackDO;
import com.hisun.tms.cpt.model.GenericParamInput;
import org.springframework.data.jpa.datatables.mapping.DataTablesOutput;

/**
 * 数币-交易回调记录查询服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/30
 */
public interface IDmTransferCallbackService {
    
    /**
     * 根据条件查询回调记录列表
     * 
     * @param input 查询参数
     * @return 回调记录列表
     */
    DataTablesOutput<DmTransferCallbackDO> findAll(GenericParamInput input);
    
    /**
     * 根据ID获取回调记录详情
     * 
     * @param id 主键ID
     * @return 回调记录详情
     */
    DmTransferCallbackDO getDetail(Long id);
}