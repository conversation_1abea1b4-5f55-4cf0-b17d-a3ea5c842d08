package com.hisun.tms.cpt.entity;

import com.hisun.lemon.framework.data.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 数币-交易回调记录表实体类
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/30
 */
public class DmTransferCallbackDO extends BaseDO {
    
    /**
     * 自增主键
     */
    private Long id;
    
    /**
     * 网络标识（如solana-testnet）
     */
    private String txBaseNetwork;
    
    /**
     * 区块ID
     */
    private Long txBaseBlockId;
    
    /**
     * 交易哈希（链上唯一标识）
     */
    private String txBaseTxId;
    
    /**
     * 错误码（成功时为空）
     */
    private String txBaseEcode;
    
    /**
     * 交易组ID
     */
    private String txBaseGroupId;
    
    /**
     * 交易手续费
     */
    private BigDecimal txBaseFee;
    
    /**
     * 交易状态（如ACCEPTED/SUCCESS/FAILED）
     */
    private String txBaseStatus;
    
    /**
     * 交易创建时间（精确到微秒）
     */
    private LocalDateTime txBaseCreateTime;
    
    /**
     * 区块哈希
     */
    private String txBaseBlockHash;
    
    /**
     * 合约程序ID
     */
    private String txBaseProgramId;
    
    /**
     * 消耗的计算单元
     */
    private Long txBaseComputeUnitsConsumed;
    
    /**
     * 交易备注信息
     */
    private String txBaseMemo;
    
    /**
     * 所属金库编码
     */
    private String vaultCode;
    
    /**
     * 关联账户ID
     */
    private Long accountId;
    
    /**
     * 账户类型（PS:平台, 商户:MA）
     */
    private String accountType;
    
    /**
     * 平台订单ID（内部唯一标识）
     */
    private String orderId;
    
    /**
     * 账单编号(关联bil_user_order表order_no)
     */
    private String bilOrderNo;
    
    /**
     * 币种标识（如SOL）
     */
    private String coinId;
    
    /**
     * 网络标识（同tx_base_network，冗余存储）
     */
    private String network;
    
    /**
     * 本平台地址
     */
    private String address;
    
    /**
     * 对手方地址
     */
    private String cpAddress;
    
    /**
     * 交易金额
     */
    private BigDecimal amount;
    
    /**
     * 交易方向（IN：入账；OUT：出账）
     */
    private String direction;
    
    /**
     * 交易渠道（默认：C）
     */
    private String channel;
    
    /**
     * 回调接收时间
     */
    private LocalDateTime callbackTime;
    
    /**
     * 是否处理（0：未处理；1：已处理）
     */
    private Integer isProcessed;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTxBaseNetwork() {
        return txBaseNetwork;
    }

    public void setTxBaseNetwork(String txBaseNetwork) {
        this.txBaseNetwork = txBaseNetwork;
    }

    public Long getTxBaseBlockId() {
        return txBaseBlockId;
    }

    public void setTxBaseBlockId(Long txBaseBlockId) {
        this.txBaseBlockId = txBaseBlockId;
    }

    public String getTxBaseTxId() {
        return txBaseTxId;
    }

    public void setTxBaseTxId(String txBaseTxId) {
        this.txBaseTxId = txBaseTxId;
    }

    public String getTxBaseEcode() {
        return txBaseEcode;
    }

    public void setTxBaseEcode(String txBaseEcode) {
        this.txBaseEcode = txBaseEcode;
    }

    public String getTxBaseGroupId() {
        return txBaseGroupId;
    }

    public void setTxBaseGroupId(String txBaseGroupId) {
        this.txBaseGroupId = txBaseGroupId;
    }

    public BigDecimal getTxBaseFee() {
        return txBaseFee;
    }

    public void setTxBaseFee(BigDecimal txBaseFee) {
        this.txBaseFee = txBaseFee;
    }

    public String getTxBaseStatus() {
        return txBaseStatus;
    }

    public void setTxBaseStatus(String txBaseStatus) {
        this.txBaseStatus = txBaseStatus;
    }

    public LocalDateTime getTxBaseCreateTime() {
        return txBaseCreateTime;
    }

    public void setTxBaseCreateTime(LocalDateTime txBaseCreateTime) {
        this.txBaseCreateTime = txBaseCreateTime;
    }

    public String getTxBaseBlockHash() {
        return txBaseBlockHash;
    }

    public void setTxBaseBlockHash(String txBaseBlockHash) {
        this.txBaseBlockHash = txBaseBlockHash;
    }

    public String getTxBaseProgramId() {
        return txBaseProgramId;
    }

    public void setTxBaseProgramId(String txBaseProgramId) {
        this.txBaseProgramId = txBaseProgramId;
    }

    public Long getTxBaseComputeUnitsConsumed() {
        return txBaseComputeUnitsConsumed;
    }

    public void setTxBaseComputeUnitsConsumed(Long txBaseComputeUnitsConsumed) {
        this.txBaseComputeUnitsConsumed = txBaseComputeUnitsConsumed;
    }

    public String getTxBaseMemo() {
        return txBaseMemo;
    }

    public void setTxBaseMemo(String txBaseMemo) {
        this.txBaseMemo = txBaseMemo;
    }

    public String getVaultCode() {
        return vaultCode;
    }

    public void setVaultCode(String vaultCode) {
        this.vaultCode = vaultCode;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getBilOrderNo() {
        return bilOrderNo;
    }

    public void setBilOrderNo(String bilOrderNo) {
        this.bilOrderNo = bilOrderNo;
    }

    public String getCoinId() {
        return coinId;
    }

    public void setCoinId(String coinId) {
        this.coinId = coinId;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCpAddress() {
        return cpAddress;
    }

    public void setCpAddress(String cpAddress) {
        this.cpAddress = cpAddress;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public LocalDateTime getCallbackTime() {
        return callbackTime;
    }

    public void setCallbackTime(LocalDateTime callbackTime) {
        this.callbackTime = callbackTime;
    }

    public Integer getIsProcessed() {
        return isProcessed;
    }

    public void setIsProcessed(Integer isProcessed) {
        this.isProcessed = isProcessed;
    }
}