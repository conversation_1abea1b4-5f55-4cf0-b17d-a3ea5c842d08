package com.hisun.tms.cpt.dao;

import com.hisun.tms.cpt.entity.DmTransferCallbackDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 数币-交易回调记录表数据访问接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/30
 */
@Mapper
public interface IDmTransferCallbackDao {
    
    /**
     * 根据查询条件查询回调记录
     * 
     * @param param 查询参数
     * @return 回调记录列表
     */
    List<DmTransferCallbackDO> findByQueryCondition(Map<String, Object> param);

    /**
     * 查询总数
     * 
     * @param param 查询参数
     * @return 总数
     */
    int countTotal(Map<String, Object> param);

    /**
     * 根据ID获取回调记录详情
     * 
     * @param id 主键ID
     * @return 回调记录对象
     */
    DmTransferCallbackDO getById(Long id);
}