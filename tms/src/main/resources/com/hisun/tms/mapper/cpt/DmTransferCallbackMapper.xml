<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hisun.tms.cpt.dao.IDmTransferCallbackDao" >

    <resultMap id="BaseResultMap" type="com.hisun.tms.cpt.entity.DmTransferCallbackDO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="tx_base_network" property="txBaseNetwork" jdbcType="VARCHAR" />
        <result column="tx_base_block_id" property="txBaseBlockId" jdbcType="BIGINT" />
        <result column="tx_base_tx_id" property="txBaseTxId" jdbcType="VARCHAR" />
        <result column="tx_base_ecode" property="txBaseEcode" jdbcType="VARCHAR" />
        <result column="tx_base_group_id" property="txBaseGroupId" jdbcType="VARCHAR" />
        <result column="tx_base_fee" property="txBaseFee" jdbcType="DECIMAL" />
        <result column="tx_base_status" property="txBaseStatus" jdbcType="VARCHAR" />
        <result column="tx_base_create_time" property="txBaseCreateTime" jdbcType="TIMESTAMP" />
        <result column="tx_base_block_hash" property="txBaseBlockHash" jdbcType="VARCHAR" />
        <result column="tx_base_program_id" property="txBaseProgramId" jdbcType="VARCHAR" />
        <result column="tx_base_compute_units_consumed" property="txBaseComputeUnitsConsumed" jdbcType="BIGINT" />
        <result column="tx_base_memo" property="txBaseMemo" jdbcType="VARCHAR" />
        <result column="vault_code" property="vaultCode" jdbcType="VARCHAR" />
        <result column="account_id" property="accountId" jdbcType="BIGINT" />
        <result column="account_type" property="accountType" jdbcType="VARCHAR" />
        <result column="order_id" property="orderId" jdbcType="VARCHAR" />
        <result column="bil_order_no" property="bilOrderNo" jdbcType="VARCHAR" />
        <result column="coin_id" property="coinId" jdbcType="VARCHAR" />
        <result column="network" property="network" jdbcType="VARCHAR" />
        <result column="address" property="address" jdbcType="VARCHAR" />
        <result column="cp_address" property="cpAddress" jdbcType="VARCHAR" />
        <result column="amount" property="amount" jdbcType="DECIMAL" />
        <result column="direction" property="direction" jdbcType="VARCHAR" />
        <result column="channel" property="channel" jdbcType="VARCHAR" />
        <result column="callback_time" property="callbackTime" jdbcType="TIMESTAMP" />
        <result column="is_processed" property="isProcessed" jdbcType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, tx_base_network, tx_base_block_id, tx_base_tx_id, tx_base_ecode, tx_base_group_id,
        tx_base_fee, tx_base_status, tx_base_create_time, tx_base_block_hash, tx_base_program_id,
        tx_base_compute_units_consumed, tx_base_memo, vault_code, account_id, account_type,
        order_id, bil_order_no, coin_id, network, address, cp_address, amount, direction,
        channel, callback_time, is_processed
    </sql>

    <!-- 根据查询条件查询回调记录 -->
    <select id="findByQueryCondition" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM dm_transfer_callback
        <where>
            <if test="id != null and id != ''">
                AND id = #{id}
            </if>
        </where>
        ORDER BY callback_time DESC
        <if test="start != null and length != null">
            LIMIT #{start}, #{length}
        </if>
    </select>

    <!-- 查询总数 -->
    <select id="countTotal" parameterType="java.util.Map" resultType="int">
        SELECT COUNT(*)
        FROM dm_transfer_callback
        <where>
            <if test="id != null and id != ''">
                AND id = #{id}
            </if>
        </where>
    </select>

    <!-- 根据ID获取回调记录详情 -->
    <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM dm_transfer_callback
        WHERE id = #{id}
    </select>

</mapper>