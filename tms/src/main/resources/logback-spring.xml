<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProperty scope="context" name="appName" source="spring.application.name" defaultValue="appName"/>
    <property name="PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread%X{sourceThread}]%logger{24} - %msg%n"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{MM/dd/yyyy HH:mm:ss} %-5level [%thread%X{sourceThread}]%logger{24} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${lemon.log.path}/${appName}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--<fileNamePattern>tms.%d{yyyy-MM-dd}.log.gz</fileNamePattern>-->
            <fileNamePattern>${lemon.log.path}/${appName}-%d{yyyy-MM-dd}.log.gz</fileNamePattern>
            <maxHistory>7</maxHistory>
        </rollingPolicy>Í
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{MM/dd/yyyy HH:mm:ss.SSS} %-5level [%thread]%logger{16} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="SQL" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${lemon.log.path}/${appName}-sql.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${lemon.log.path}/${appName}-sql-%d{yyyy-MM-dd}.log.gz</fileNamePattern>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{MM/dd/yyyy HH:mm:ss} %-5level [%thread]%logger{16} - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="TRACE"/>
    <logger name="org.hibernate.type.descriptor.sql.BasicExtractor" level="DEBUG"/>
    <logger name="org.hibernate.SQL" level="DEBUG"/>
    <logger name="org.hibernate.engine.QueryParameters" level="DEBUG"/>
    <logger name="org.hibernate.engine.query.HQLQueryPlan" level="DEBUG"/>

    <logger name="org.hibernate">
        <appender-ref ref="SQL"/>
    </logger>
    <logger name="org.springframework.web.servlet.mvc" level="info"/>
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE" />
    </root>

    <springProfile name="dev,test,uat">
        <logger name="com.hisun.tms" level="debug"/>
    </springProfile>
    <springProfile name="prod">
        <logger name="com.hisun.tms" level="info"/>
    </springProfile>
</configuration>
