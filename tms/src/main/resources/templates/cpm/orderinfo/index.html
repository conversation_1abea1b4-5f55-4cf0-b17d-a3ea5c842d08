<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>

    <title data-i18n="nav.bussub.trdsub.cpmquery.title"></title>

    <div th:replace="head"></div>
</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="nav.bussub.trdsub.cpmquery.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.busmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.bussub.trdmgr"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="nav.bussub.trdsub.cpmquery.content"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form id="queryForm" class="form-horizontal">
                                <div class="form-group col-sm-12">
                                    <!--手机号/商户号-->
                                    <label class="col-sm-2 control-label" for="userNo" data-i18n="cpm.userNo"></label>
                                    <div class="col-sm-4">
                                        <input name="userNo" id="userNo" class="form-control" value=""/>
                                    </div>
                                    <label class="col-sm-2 control-label" for="mblNo" data-i18n="cpm.mblNo"></label>
                                    <div class="col-sm-4">
                                        <input name="mblNo" id="mblNo" class="form-control" value=""/>
                                    </div>
                                </div>
                                <div class="form-group col-sm-12">
                                    <label class="col-sm-2 control-label" for="bossCopType" data-i18n="cpm.bossCopType"></label>
                                    <div class="col-sm-4">
                                        <input name="bossCopType" id="bossCopType" class="form-control" value=""/>
                                    </div>
                                    <!--缴费业务类型-->
                                    <label class="col-sm-2 control-label" for="orderType" data-i18n="cpm.orderType"></label>
                                    <div class="col-sm-4">
                                        <select name="orderType" id="orderType" class="form-control" value="">
                                            <option value="" data-i18n="cpm.select"></option>
                                            <option value="TEL" data-i18n="cpm.TEL"></option>
                                            <option value="FLOW" data-i18n="cpm.FLOW"></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group col-sm-12">
                                    <!--提交日期-->
                                    <label class="col-sm-2 control-label" for="beginDate" data-i18n="cpm.tradeDate"></label>
                                    <div class="col-sm-3">
                                        <input name="beginDate" id="beginDate" class="form-control" value=""/>
                                    </div>
                                    <div class="col-sm-3">
                                        <input name="endDate" id="endDate" class="form-control" value=""/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-4 col-sm-offset-1">
                                        <button type="button" id="searchBtn" class="btn btn-default" onclick="search()" data-i18n="cpm.search"></button>
                                    </div>
                                </div>
                            </form>
                            <div class="table-responsive">
                                <table id="orderInf" class="table table-striped table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th data-i18n="cpm.cpmOrderDt"></th>
                                        <th data-i18n="cpm.cpmOrderNo"></th>
                                        <th data-i18n="cpm.userNo"></th>
                                        <th data-i18n="cpm.mblNo"></th>
                                        <th data-i18n="cpm.bossCopType"></th>
                                        <th data-i18n="cpm.orderType"></th>
                                        <th data-i18n="cpm.orderAmt"></th>
                                        <th data-i18n="cpm.orderSts"></th>
                                        <th data-i18n="cpm.payType"></th>
                                        <th data-i18n="cpm.payAmt"></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>

    </div>
</div>
<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script>
    var editor;
    //A 2D array in which the first array is used to define the value options and the second array the displayed options (useful for language strings such as 'All').
    //    var array1 = [10, 25, 50, -1];
    //    var array2 = [10, 25, 50, "All"];
    var table;
    <!--查询按钮-->
    function search() {
        table.ajax.reload();
    }
    $(document).ready(function () {

        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        i18nLoad.then(function () {
            editor = new $.fn.dataTable.Editor({
                ajax: {
                 //  create: {
                 //      type: 'POST',
                 //      url: '/cpm/transferctrl/add',
                 //      contentType: 'application/json',
                 //      data: function (d) {
                 //          return JSON.stringify(d);
                 //      }
                 //  },
                 //  edit: {
                 //      type: 'POST',
                 //      url: '/cpm/mercinfctrl/modify',
                 //      contentType: 'application/json',
                 //      data: function (d) {
                 //          return JSON.stringify(d);
                 //      }
                 //  },
                 //  remove: {
                 //      type: 'POST',
                 //      url: '/cpm/mercinfctrl/delete',
                 //      contentType: 'application/json',
                 //      data: function (d) {
                 //          return JSON.stringify(d);
                 //      }
                 //  }
                },
                table: "#orderInf",
                idSrc: 'cpmOrderNo',
                fields: [
                   // {name: "id", type: "hidden"},
                   // {label: $.i18n.t("cpm.cpmOrderDt"),name: "cpmOrderDt"},
                   // {label: $.i18n.t("cpm.cpmOrderNo"), name: "cpmOrderNo"},
                   // {label: $.i18n.t("cpm.userNo"), name: "userNo"},
                   // {label: $.i18n.t("cpm.mblNo"), name: "mblNo"},
                   // {label: $.i18n.t("cpm.bossCopType"), name: "bossCopType"},
                   // {label: $.i18n.t("cpm.orderType"), name: "orderType"},
                   // {label: $.i18n.t("cpm.orderAmt"), name: "orderAmt"},
                   // {label: $.i18n.t("cpm.orderSts"), name: "orderSts"},
                   // {label: $.i18n.t("cpm.payType"), name: "payType"},
                   // {label: $.i18n.t("cpm.payAmt"), name: "payAmt"}
                    //{
                        //label: $.i18n.t("demo.startDate"),
                        //name: "startDate",
                        //type: "datetime",
                        //def: function () {
                            //return new Date();
                       // },
                       // format: "YYYY-MM-DD HH:mm:ss"
                   // },
                    //{label: $.i18n.t("demo.salary"), name: "salary"}
                ],
                //i18n: {
                  // create: {button: $.i18n.t("cpm.add"), title: $.i18n.t("cpm.add"), submit: $.i18n.t("cpm.create")},
                  // edit: {button: $.i18n.t("cpm.modify"), title: $.i18n.t("cpm.modify"), submit: $.i18n.t("cpm.update")},
                  // remove: {
                  //     button: $.i18n.t("cpm.delete"), title: $.i18n.t("cpm.delete"), submit: $.i18n.t("cpm.delete"),
                  //     confirm: {
                  //         _: $.i18n.t("cpm.multi-delete"),
                  //         1: $.i18n.t("cpm.single-delete")
                  //     }
                  // }
                //}
            });

            //editor.on('preSubmit', function (e, o, action) {
            //    var id = editor.field('cpmOrderNo');
            //    o.id = id.val();  // create a new parameter to pass over to the server called entityId
            //});

            table = $('#orderInf').DataTable({
                dom: 'Blfrtip',
                columnDefs: [{
                    targets:[0,1,8],//指定哪几列
                    render: function(data){
                        return "\u200C" + data;
                    }
                }],
                ajax: {
                    contentType: 'application/json',
                    url: '/cpm/orderctrl/findAll',
                    type: 'POST',
                    data: function (d) {
                    	d.extra_search = {
                                "userNo" : $("#userNo").val(),
                                "mblNo" : $("#mblNo").val(),
                                "bossCopType" : $("#bossCopType").val(),
                                "orderType" : $("#orderType").val(),
                                "beginDate" : $("#beginDate").val(),
                                "endDate" : $("#endDate").val()
                                };
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
//            "lengthMenu": [array1, array2],
                language: {
//                "decimal": ",",
//                "thousands": ".",
//                    url: '/datatables/plugins/i18n/Chinese.lang'
                    url: languageUrl
                    //url: '//cdn.datatables.net/plug-ins/1.10.15/i18n/Chinese.json'
                },
                select: true,
                buttons: [
                    //{extend: "create", editor: editor},
                    //{extend: "edit", editor: editor},
                    //{extend: "remove", editor: editor},
//                {extend: 'copyHtml5'},
//                {extend: 'csvHtml5'},
//                {extend: 'excelHtml5', title: '文件名'},
                    'copy',
                    'csv',
                    'excel'
//                'pdf',
//                'print'
                ],
                columns: [{
                    data: 'cpmOrderDt'
                },{
                    data: 'cpmOrderNo'
                },{
                    data: 'userNo'
                },{
                    data: 'mblNo'
//                orderable: false
                },{
                    data: 'bossCopType'
                //    render: $.fn.dataTable.render.number(',', '.', 2, '$')
                },{
                    data: 'orderType',
                    render: function (data, type, row) {
                        switch (data) {
                            case "TEL":
                                return $.i18n.t("cpm.TEL");
                            case "FLOW":
                                return $.i18n.t("cpm.FLOW");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'orderAmt',
                    render: $.fn.dataTable.render.number(',', '.', 2, '$')
                },{
                    data: 'orderSts',
                    render: function (data, type, row) {
                        switch (data) {
                            case "U":
                                return $.i18n.t("cpm.orderStsSub.U");
                            case "W":
                                return $.i18n.t("cpm.orderStsSub.W");
                            case "P":
                                return $.i18n.t("cpm.orderStsSub.P");
                            case "S":
                                return $.i18n.t("cpm.orderStsSub.S");
                            case "F":
                                return $.i18n.t("cpm.orderStsSub.F");
                            case "T":
                                return $.i18n.t("cpm.orderStsSub.T");
                            case "D":
                                return $.i18n.t("cpm.orderStsSub.D");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'payType'
                },{
                    data: 'payAmt',
                    render: $.fn.dataTable.render.number(',', '.', 2, '$')
                }
                ]
            });
        });
    });
    /**初始化日期控件**/
    var beginTimePick = $('#beginDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        onChangeDate: function(dateText, inst) {
            endTimePick.datetimepicker('minDate',new Date(dateText.replace("-", "-")));
        }
    });

    var endTimePick = $('#endDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d'
        // maxDate:'+1970/01/01',
    });
</script>
</body>

</html>