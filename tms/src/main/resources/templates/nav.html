<nav class="navbar-default navbar-static-side" role="navigation">
    <div class="sidebar-collapse">
        <ul class="nav metismenu" id="side-menu">
            <li class="active">
                <a href="/index.html"><i class="fa fa-home"></i> <span class="nav-label" data-i18n="nav.index"></span>
                </a>
            </li>
            <!-- 公共管理 -->
            <li sec:authorize="hasPermission('','/cmmmgr')">
                <a href="#"><i class="fa fa-globe"></i><span class="nav-label" data-i18n="nav.cmmmgr"></span><span
                        class="fa arrow"></span></a>
                <ul class="nav nav-second-level collapse">
                    <!-- 柜员管理 -->
                    <li sec:authorize="hasPermission('','/cmmmgr/user')">
                        <a href="#"><i class="fa fa-cogs"></i><span class="nav-label"
                                data-i18n="nav.cmmsub.usrmgr"></span><span class="fa arrow"></span></a>
                        <ul class="nav nav-third-level collapse">
                            <li sec:authorize="hasPermission('','/cmmmgr/user/user') or hasRole('ROLE_ADMIN')"><a
                                    href="/system/user"><i class="fa fa-user"></i><span
                                        data-i18n="nav.systemsub.user"></span></a></li>
                            <li sec:authorize="hasPermission('','/cmmmgr/user/role') or hasRole('ROLE_ADMIN')"><a
                                    href="/system/role"><i class="fa fa-users"></i><span
                                        data-i18n="nav.systemsub.role"></span></a></li>
                        </ul>
                    </li>
                    <!-- 机构管理 -->
                    <li sec:authorize="hasPermission('','/cmmmgr/orgmgr')">
                        <a href="#"><i class="fa fa-tasks"></i><span class="nav-label"
                                data-i18n="nav.cmmsub.orgmgr"></span><span class="fa arrow"></span></a>
                        <ul class="nav nav-third-level collapse">
                            <li sec:authorize="hasPermission('','/cmmmgr/orgmgr/office')"><a href="/cmm/officectrl"><i
                                        class="fa fa-building"></i><span
                                        data-i18n="nav.cmmsub.orgsub.officemgr"></span></a></li>
                            <li sec:authorize="hasPermission('','/cmmmgr/orgmgr/branch')"><a href="/cmm/branchctrl"><i
                                        class="fa fa-slideshare"></i><span
                                        data-i18n="nav.cmmsub.orgsub.branchmgr"></span></a></li>
                        </ul>
                    </li>
                    <!-- 系统管理 -->
                    <li sec:authorize="hasPermission('','/cmmmgr/sysmgr')">
                        <a href="#"><i class="fa fa-tasks"></i><span class="nav-label"
                                data-i18n="nav.cmmsub.sysmgr"></span><span class="fa arrow"></span></a>
                        <ul class="nav nav-third-level collapse">
                            <li sec:authorize="hasPermission('','/cmmmgr/sysmgr/phsctrl')"><a href="/cmm/phsctrl"><i
                                        class="fa fa-tty"></i><span data-i18n="nav.cmmsub.syssub.phsmgr"></span></a>
                            </li>
                            <li sec:authorize="hasPermission('','/cmmmgr/sysmgr/constants')"><a href="/cmm/constants"><i
                                        class="fa fa-font"></i><span data-i18n="nav.cmmsub.constants"></span></a></li>
                            <li sec:authorize="hasPermission('','/cptmgr/param/cardbin')"><a
                                    href="/cpt/param/cardbin"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.cardbin.constants"></span></a></li>

                            <li sec:authorize="hasPermission('','/cmmmgr/sysmgr/userOpr')"><a href="/system/userOpr"><i
                                        class="fa fa-coffee"></i><span data-i18n="nav.cmmsub.userOpr"></span></a></li>
                        </ul>
                    </li>
                </ul>
            </li>
            <!-- 清结算管理 -->
            <li sec:authorize="hasPermission('','/csmmgr')">
                <a href="#"><i class="fa fa-clock-o"></i> <span class="nav-label" data-i18n="nav.csmmgr"></span><span
                        class="fa arrow"></span></a>
                <ul class="nav nav-second-level collapse">
                    <li sec:authorize="hasPermission('','/csmmgr/item')">
                        <a href="#"><i class="fa fa-user"></i><span class="nav-label"
                                data-i18n="nav.csmsub.item"></span><span class="fa arrow"></span></a>
                        <ul class="nav nav-third-level collapse">
                            <li sec:authorize="hasPermission('','/csmmgr/item/iteminf')"><a href="/acm/item/iteminf"><i
                                        class="fa fa-coffee"></i><span
                                        data-i18n="nav.csmsub.itemsub.iteminf"></span></a></li>
                            <li sec:authorize="hasPermission('','/csmmgr/item/itemproperty')"><a
                                    href="/acm/item/itemproperty"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.csmsub.itemsub.itemproperty.content"></span></a></li>
                        </ul>
                    </li>
                    <li sec:authorize="hasPermission('','/csmmgr/adjust')">
                        <a href="#"><i class="fa fa-user"></i><span class="nav-label"
                                data-i18n="nav.csmsub.adjust"></span><span class="fa arrow"></span></a>
                        <ul class="nav nav-third-level collapse">
                            <li sec:authorize="hasPermission('','/csmmgr/adjust/record')"><a
                                    href="/acm/adjust/record"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.csmsub.adjustsub.record"></span></a></li>
                            <li sec:authorize="hasPermission('','/csmmgr/adjust/audit')"><a href="/acm/adjust/audit"><i
                                        class="fa fa-coffee"></i><span
                                        data-i18n="nav.csmsub.adjustsub.audit"></span></a></li>
                            <li sec:authorize="hasPermission('','/csmmgr/adjust/query')"><a
                                    href="/acm/adjust/audit?audited=1"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.csmsub.adjustsub.query.content"></span></a></li>
                        </ul>
                    </li>
                    <li sec:authorize="hasPermission('','/csmmgr/voucherquery')"><a href="/acm/voucherquery"><i
                                class="fa fa-coffee"></i><span data-i18n="nav.csmsub.voucherquery"></span></a></li>
                    <li sec:authorize="hasPermission('','/csmmgr/disqueryctrl')"><a href="/tfm/disqueryctrl"><i
                                class="fa fa-coffee"></i><span data-i18n="nav.disqueryctrl.content"></span></a></li>
                    <li sec:authorize="hasPermission('','/csmmgr/settle')">
                        <a href="#"><i class="fa fa-user"></i><span class="nav-label"
                                data-i18n="settle.conment"></span><span class="fa arrow"></span></a>
                        <ul class="nav nav-third-level collapse">
                            <li sec:authorize="hasPermission('','/csmmgr/settle/record')"><a
                                    href="/cpt/payment/settle"><i class="fa fa-coffee"></i><span
                                        data-i18n="settle.thirtLevel"></span></a></li>

                        </ul>
                    </li>
                </ul>
            </li>
            <!-- 业务管理 -->
            <li sec:authorize="hasPermission('','/busmgr')">
                <a href="#"><i class="fa fa-archive"></i> <span class="nav-label" data-i18n="nav.busmgr"></span><span
                        class="fa arrow"></span></a>
                <ul class="nav nav-second-level collapse">
                    <li sec:authorize="hasPermission('','/busmgr/usrmgr')">
                        <a href="#"><i class="fa fa-user"></i><span class="nav-label"
                                data-i18n="nav.bussub.usrmgr"></span><span class="fa arrow"></span></a>
                        <ul class="nav nav-third-level collapse">
                            <li sec:authorize="hasPermission('','/busmgr/usrmgr/userbasicinfo')"><a
                                    href="/busmgr/userbasicinfoctrl"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.bussub.usrsub.userbasicinfoctrl.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/usrmgr/usrBalance')"><a
                                    href="/urm/queryBalanceView"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.bussub.usrBalance"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/usrmgr/amtinfo')"><a
                                    href="/urm/usrmgr/amtinfo"><i class="fa fa-coffee"></i><span
                                        data-i18n="urm.usrmgr.amtInfo"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/usrmgr/userhistory')"><a
                                    href="/busmgr/userhistoryctrl"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.bussub.usrsub.userhistoryctrl.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/usrmgr/status')"><a
                                    href="/urm/usrmgr/status"><i class="fa fa-coffee"></i><span
                                        data-i18n="urm.usrmgr.status.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/usrmgr/pswdRst')"><a href="/urm/userPswdRst"><i
                                        class="fa fa-coffee"></i><span
                                        data-i18n="urm.usrmgr.pswdrst.content"></span></a></li>
                        </ul>
                    </li>
                    <li sec:authorize="hasPermission('','/busmgr/oprmgr')">
                        <a href="#"><i class="fa fa-tasks"></i><span class="nav-label"
                                data-i18n="nav.bussub.oprmgr"></span><span class="fa arrow"></span></a>
                        <ul class="nav nav-third-level collapse">
                            <li sec:authorize="hasPermission('','/busmgr/oprmgr/banner')"><a href="/cmm/bannerctrl"><i
                                        class="fa fa-picture-o"></i><span
                                        data-i18n="nav.bussub.oprsub.bannermgr.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/oprmgr/campaign')"><a
                                    href="/cmm/campaignctrl"><i class="fa fa-bell"></i><span
                                        data-i18n="nav.bussub.oprsub.campaignmgr.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/oprmgr/notic')"><a href="/cmm/noticctrl"><i
                                        class="fa fa-bullhorn"></i><span
                                        data-i18n="nav.bussub.oprsub.noticmgr.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/oprmgr/smstempalte')"><a
                                    href="/cmm/smstempaltectrl"><i class="fa fa-tablet"></i><span
                                        data-i18n="nav.bussub.oprsub.smsmgr.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/oprmgr/message')"><a href="/cmm/messagectrl"><i
                                        class="fa fa-rss"></i><span
                                        data-i18n="nav.bussub.oprsub.messagemgr.content"></span></a></li>
                            <!-- 邮件模板管理 -->
                            <li sec:authorize="hasPermission('','/cmmmgr/sysmgr/emailtemplate')">
                                <a href="/cmm/emailtemplatectrl"><i class="fa fa-envelope-o"></i><span>邮件模板管理</span></a>
                            </li>
                        </ul>
                    </li>
                    <!-- 商户管理 -->
                    <li sec:authorize="hasPermission('','/busmgr/mermgr')">
                        <a href="#"><i class="fa fa-user"></i><span class="nav-label"
                                data-i18n="nav.bussub.mermgr"></span><span class="fa arrow"></span></a>
                        <ul class="nav nav-third-level collapse">
                            <!-- KYB审核 -->
                            <li sec:authorize="hasPermission('','/busmgr/mermgr/kyb')"><a
                                    href="/urm/mermgr/kyb/audit/page"><i class="fa fa-coffee"></i><span>KYB审核</span></a>
                            </li>
                            <!-- KYB复核 -->
                            <li sec:authorize="hasPermission('','/busmgr/mermgr/kybreview')"><a
                                    href="/urm/mermgr/kyb/review/page"><i
                                        class="fa fa-coffee"></i><span>KYB复核</span></a></li>
                            <!-- 法币开户审核 -->
                            <li sec:authorize="hasPermission('','/csmmgr/acc/manage')"><a href="/acm/acc/manage"><i
                                        class="fa fa-money"></i><span>法币开户审核</span></a></li>
                            <!-- 商户资料维护 -->
                            <!--                            <li sec:authorize="hasPermission('','/busmgr/mermgr/manage')"><a-->
                            <!--                                    href="/urm/mermgr/manage"><i class="fa fa-coffee"></i><span-->
                            <!--                                        data-i18n="urm.mermgr.manage.content"></span></a></li>-->
                            <!-- 商户审核 -->
                            <li sec:authorize="hasPermission('','/busmgr/mermgr/examine')"><a
                                    href="/urm/mermgr/examine"><i class="fa fa-coffee"></i><span
                                        data-i18n="urm.mermgr.examine.content"></span></a></li>
                            <!-- 商户资料管理 -->
                            <!--                            <li sec:authorize="hasPermission('','/busmgr/mermgr/info')"><a href="/urm/mermgr/info"><i-->
                            <!--                                        class="fa fa-coffee"></i><span-->
                            <!--                                        data-i18n="urm.mermgr.infoMng.content"></span></a></li>-->
                            <!-- 商户状态管理 -->
                            <li sec:authorize="hasPermission('','/busmgr/mermgr/status')"><a
                                    href="/urm/mermgr/status"><i class="fa fa-coffee"></i><span
                                        data-i18n="urm.mermgr.statusMng.content"></span></a></li>
                            <!-- 商户余额查询 -->
                            <li sec:authorize="hasPermission('','/busmgr/mermgr/info/balance')"><a
                                    href="/urm/mermgr/info/balance"><i class="fa fa-coffee"></i><span
                                        data-i18n="urm.balance.mercBalance"></span></a></li>
                            <!-- 商户注册管理 -->
                            <li sec:authorize="hasPermission('','/busmgr/mermgr/register')"><a
                                    href="/urm/mermgr/register"><i class="fa fa-coffee"></i><span
                                        data-i18n="urm.register.content">商户注册管理</span></a></li>
                            <!-- 商户收支明细 -->
                            <li sec:authorize="hasPermission('','/busmgr/mermgr/amtinfo')"><a
                                    href="/urm/mermgr/amtinfo"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.bussub.mersub.amtInfo.content"></span></a></li>
                            <!-- 商户登录密码重置 -->
                            <li sec:authorize="hasPermission('','/busmgr/mermgr/mercloginpswdreset')"><a
                                    href="/busmgr/mercloginpswdresetctrl"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.bussub.mersub.mercloginpswdresetctrl.content"></span></a></li>
                            <!-- 商户支付密码重置 -->
                            <li sec:authorize="hasPermission('','/busmgr/mermgr/mercpaypswdreset')"><a
                                    href="/busmgr/mercpaypswdresetctrl"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.bussub.mersub.mercpaypswdresetctrl.content"></span></a></li>
                            <!-- 商户密钥重置 -->
                            <li sec:authorize="hasPermission('','/busmgr/mermgr/merckeyreset')"><a
                                    href="/busmgr/keyresetctrl"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.bussub.mersub.merckeyresetctrl.content"></span></a></li>
                            <!-- 商户交易权限设置-->
                            <li sec:authorize="hasPermission('','/busmgr/mermgr/mercitf')"><a
                                    href="/busmgr/mercitfInfoctrl"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.bussub.mersub.mercitfInfoctrl.content"></span></a></li>
                        </ul>
                    </li>
                    <li sec:authorize="hasPermission('','/busmgr/trdmgr')">
                        <a href="#"><i class="fa fa-mobile"></i><span class="nav-label"
                                data-i18n="nav.bussub.trdmgr"></span><span class="fa arrow"></span></a>
                        <ul class="nav nav-third-level collapse">
                            <li sec:authorize="hasPermission('','/busmgr/trdmgr/cpmorder')"><a href="/cpm/orderctrl"><i
                                        class="fa fa-coffee"></i><span
                                        data-i18n="nav.bussub.trdsub.cpmquery.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/trdmgr/orderquery')"><a
                                    href="/onr/orderqueryctrl"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.bussub.trdsub.orderqueryctrl"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/trdmgr/onrrfdord')"><a
                                    href="/onr/onrrfdordctrl"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.bussub.trdsub.onrrfdordctrl.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/trdmgr/paytype')"><a href="/csh/paytype"><i
                                        class="fa fa-coffee"></i><span data-i18n="csh.paytype.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/trdmgr/exchangeaudit')">
                                <a href="/tam/exchange/audit/page">
                                    <i class="fa fa-coffee"></i>
                                    <span data-i18n="nav.bussub.trdsub.exchangeaudit.content"></span>
                                </a>
                            </li>
                            <li sec:authorize="hasPermission('','/busmgr/trdmgr/exchangeWaitReview')">
                                <a href="/tam/exchange/waitReview/page">
                                    <i class="fa fa-coffee"></i>
                                    <span data-i18n="nav.bussub.trdsub.exchangeWaitReview.content">兑换复核查询</span>
                                </a>
                            </li>

                            <li id="busmgr_trdmgr_dmbill">
                                <a href="/bil/dmbill/page">
                                    <i class="fa fa-exchange"></i> <span>数币交易管理</span>
                                </a>
                            </li>

                        </ul>
                    </li>
                    <li sec:authorize="hasPermission('','/busmgr/invmgr')">
                        <a href="#"><i class="fa fa-cny"></i><span class="nav-label"
                                data-i18n="nav.bussub.invmgr"></span><span class="fa arrow"></span></a>
                        <ul class="nav nav-third-level collapse">
                            <li sec:authorize="hasPermission('','/busmgr/invmgr/proctrl')"><a href="/inv/proctrl"><i
                                        class="fa fa-coffee"></i><span
                                        data-i18n="nav.bussub.invsub.proinf.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/invmgr/regproctrl')"><a
                                    href="/inv/regproctrl"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.bussub.invsub.regproinf.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/invmgr/feectrl')"><a href="/inv/feectrl"><i
                                        class="fa fa-coffee"></i><span
                                        data-i18n="nav.bussub.invsub.fee.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/invmgr/regfeectrl')"><a
                                    href="/inv/regfeectrl"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.bussub.invsub.regfee.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/invmgr/orderctrl')"><a href="/inv/orderctrl"><i
                                        class="fa fa-coffee"></i><span
                                        data-i18n="nav.bussub.invsub.order.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/invmgr/regorderctrl')"><a
                                    href="/inv/regorderctrl"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.bussub.invsub.regorder.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/invmgr/dataratectrl')"><a
                                    href="/inv/dataratectrl"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.bussub.invsub.datarate.content"></span></a></li>
                        </ul>
                    </li>
                    <li sec:authorize="hasPermission('','/busmgr/mkmmgr')">
                        <a href="#"><i class="fa fa-cogs"></i><span class="nav-label" data-i18n="nav.mkm"></span><span
                                class="fa arrow"></span></a>
                        <ul class="nav nav-third-level collapse">
                            <li sec:authorize="hasPermission('','/busmgr/mkmmgr/activity')"><a href="/mkm/activity"><i
                                        class="fa fa-user"></i><span data-i18n="nav.activity"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/mkmmgr/examine')"><a
                                    href="/mkm/activity/examine"><i class="fa fa-user"></i><span
                                        data-i18n="nav.examine.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/mkmmgr/releaseDetail')"><a
                                    href="/mkm/releaseDetail"><i class="fa fa-user"></i><span
                                        data-i18n="nav.releaseDetail.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/mkmmgr/consumeDetail')"><a
                                    href="/mkm/consumeDetail"><i class="fa fa-user"></i><span
                                        data-i18n="nav.consumeDetail.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/mkmmgr/mkmToolBatchMng')"><a
                                    href="/mkm/mkmToolBatchMng"><i class="fa fa-user"></i><span
                                        data-i18n="nav.mkmToolBatchMng"></span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/mkmmgr/faq')"><a href="/mkm/faq"><i
                                        class="fa fa-question-circle"></i><span>常见问题管理</span></a></li>
                            <li sec:authorize="hasPermission('','/busmgr/mkmmgr/pact')"><a href="/mkm/pact"><i
                                        class="fa fa-file-text-o"></i><span>协议管理</span></a></li>
                        </ul>
                    </li>
                </ul>
            </li>
            <li sec:authorize="hasPermission('','/cptmgr')">
                <a href="#"><i class="fa fa-coffee"></i><span class="nav-label" data-i18n="nav.cptmgr"></span><span
                        class="fa arrow"></span></a>
                <ul class="nav nav-second-level collapse">
                    <li sec:authorize="hasPermission('','/cptmgr/param')">
                        <a href="#"><i class="fa fa-coffee"></i> <span class="nav-label"
                                data-i18n="nav.parammgr"></span><span class="fa arrow"></span></a>
                        <ul class="nav nav-third-level collapse">
                            <li sec:authorize="hasPermission('','/cptmgr/param/card')"><a href="/cpt/param/card"><i
                                        class="fa fa-coffee"></i><span data-i18n="nav.cardctrl"></span></a></li>

                            <!-- 新增汇率查询菜单 -->
                            <li sec:authorize="hasPermission('','/cptmgr/param/exchangerate')">
                                <a href="/tfm/exchangeRate/index">
                                    <i class="fa fa-exchange"></i>
                                    <span>汇率查询</span>
                                </a>
                            </li>
                            <!-- 新增手续费参数管理菜单 -->
                            <li sec:authorize="hasPermission('','/cptmgr/param/feerule')">
                                <a href="/tfm/feerule">
                                    <i class="fa fa-calculator"></i>
                                    <span>手续费参数管理</span>
                                </a>
                            </li>
                            <!-- 新增账户地址管理菜单 -->
                            <li sec:authorize="hasPermission('','/acmmgr/accountaddress')">
                                <a href="/acm/accountaddress">
                                    <i class="fa fa-link"></i>
                                    <span>账户地址管理</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li sec:authorize="hasPermission('','/cptmgr/order')">
                        <a href="#"><i class="fa fa-coffee"></i> <span class="nav-label"
                                data-i18n="nav.ordermgr"></span><span class="fa arrow"></span></a>
                        <ul class="nav nav-third-level collapse">
                            <!-- 新增手续费查询菜单 -->
                            <li sec:authorize="hasPermission('','/cptmgr/param/fee')"><a href="/cpt/param/feeorder"><i
                                        class="fa fa-money"></i><span>手续费订单查询</span></a></li>

                            <li sec:authorize="hasPermission('','/cptmgr/order/withdraw')"><a
                                    href="/cpt/order/withdraw"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.withdrawctrl"></span></a></li>
                            <li sec:authorize="hasPermission('','/cptmgr/order/withdrawWaitReview')"><a
                                    href="/cpt/order/withdrawWaitReview"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.withdrawWaitReview"></span></a></li>
                            <li sec:authorize="hasPermission('','/cptmgr/order/refund')"><a href="/cpt/order/refund"><i
                                        class="fa fa-coffee"></i><span data-i18n="nav.refundctrl"></span></a></li>
                            <li sec:authorize="hasPermission('','/cptmgr/order/fund')"><a href="/cpt/order/fund"><i
                                        class="fa fa-coffee"></i><span data-i18n="nav.fundctrl"></span></a></li>
                            <li sec:authorize="hasPermission('','/cptmgr/order/fundWaitReview')"><a
                                    href="/cpt/order/fundWaitReview"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.fundctrlWaitReview"></span></a></li>
                            <li sec:authorize="hasPermission('','/cptmgr/order/transfer')"><a
                                    href="/cpt/order/transfer"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.transfer"></span></a></li>
                            <li sec:authorize="hasPermission('','/cptmgr/order/transferWaitReview')"><a
                                    href="/cpt/order/transferWaitReview"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.transferWaitReview"></span></a></li>
                        </ul>
                    </li>
                    <li sec:authorize="hasPermission('','/cptmgr/dcorder')">
                        <a href="#"><i class="fa fa-bitcoin"></i> <span class="nav-label">数币订单管理</span><span
                                class="fa arrow"></span></a>
                        <ul class="nav nav-third-level collapse">
                            <li sec:authorize="hasPermission('','/cptmgr/dcorder/callback')"><a href="/cpt/dcorder/callback"><i
                                        class="fa fa-coffee"></i><span>Cregis回调记录查询</span></a>
                            </li>
                            <li sec:authorize="hasPermission('','/cptmgr/dcorder/withdraw')"><a
                                    href="/cpt/dcorder/withdraw"><i class="fa fa-coffee"></i><span>数币付款订单查询</span></a>
                            </li>
                            <li sec:authorize="hasPermission('','/cptmgr/dcorder/withdrawWaitReview')"><a
                                    href="/cpt/dcorder/withdrawWaitReview"><i
                                        class="fa fa-coffee"></i><span>数币待复核付款订单查询</span></a></li>
                            <li sec:authorize="hasPermission('','/cptmgr/dcorder/transfer')"><a
                                    href="/cpt/dcorder/transfer"><i class="fa fa-coffee"></i><span>数币转账订单查询</span></a>
                            </li>
                            <li sec:authorize="hasPermission('','/cptmgr/dcorder/transferWaitReview')"><a
                                    href="/cpt/dcorder/transferWaitReview"><i
                                        class="fa fa-coffee"></i><span>数币待复核转账订单查询</span></a></li>
                        </ul>
                    </li>
                    <li sec:authorize="hasPermission('','/cptmgr/check')">
                        <a href="#"><i class="fa fa-coffee"></i> <span class="nav-label"
                                data-i18n="nav.chk"></span><span class="fa arrow"></span></a>
                        <ul class="nav nav-third-level collapse">
                            <li sec:authorize="hasPermission('','/cptmgr/check/controller')"><a
                                    href="/chk/controller"><i class="fa fa-coffee"></i><span
                                        data-i18n="nav.controller"></span></a></li>
                            <li sec:authorize="hasPermission('','/cptmgr/check/error')"><a href="/chk/error"><i
                                        class="fa fa-coffee"></i><span data-i18n="nav.error"></span></a></li>
                        </ul>
                    </li>
                    <li sec:authorize="hasPermission('','/cptmgr/cpiorg')">
                        <a href="#"><i class="fa fa-coffee"></i> <span class="nav-label"
                                data-i18n="nav.cpiorgmgr"></span><span class="fa arrow"></span></a>
                        <ul class="nav nav-third-level collapse">
                            <li sec:authorize="hasPermission('','/cptmgr/cpiorg/info')"><a href="/cpt/cpiorg/info"><i
                                        class="fa fa-coffee"></i><span data-i18n="nav.orginfo.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/cptmgr/cpiorg/busi')"><a href="/cpt/cpiorg/busi"><i
                                        class="fa fa-coffee"></i><span data-i18n="nav.orgbusi.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/cptmgr/cpiorg/route')"><a href="/cpt/cpiorg/route"><i
                                        class="fa fa-coffee"></i><span data-i18n="nav.orgroute.content"></span></a></li>
                        </ul>
                    </li>
                    <li sec:authorize="hasPermission('','/cptmgr/cpoorg')">
                        <a href="#"><i class="fa fa-coffee"></i> <span class="nav-label"
                                data-i18n="nav.cpoorgmgr"></span><span class="fa arrow"></span></a>
                        <ul class="nav nav-third-level collapse">
                            <li sec:authorize="hasPermission('','/cptmgr/cpoorg/info')"><a href="/cpt/cpoorg/info"><i
                                        class="fa fa-coffee"></i><span data-i18n="nav.orginfo.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/cptmgr/cpoorg/busi')"><a href="/cpt/cpoorg/busi"><i
                                        class="fa fa-coffee"></i><span data-i18n="nav.orgbusi.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/cptmgr/cpoorg/route')"><a href="/cpt/cpoorg/route"><i
                                        class="fa fa-coffee"></i><span data-i18n="nav.orgroute.content"></span></a></li>
                        </ul>
                    </li>
                </ul>
            </li>
            <!--风控管理-->
            <li sec:authorize="hasPermission('','/rsmmgr')">
                <a href="#"><i class="fa fa-coffee"></i><span class="nav-label" data-i18n="nav.rsm"></span><span
                        class="fa arrow"></span></a>
                <ul class="nav nav-second-level collapse">
                    <!--黑白名单管理-->
                    <li sec:authorize="hasPermission('','/rsmmgr/riskList')">
                        <a href="#"><i class="fa fa-coffee"></i><span data-i18n="rsm.riskList.title"></span><span
                                class="fa arrow"></span></a>
                        <ul class="nav nav-third-level collapse">
                            <li sec:authorize="hasPermission('','/rsmmgr/riskList/black')"><a
                                    href="/rsm/risklist/black"><i class="fa fa-coffee"></i><span
                                        data-i18n="rsm.riskList.blackList.content"></span></a></li>
                            <li sec:authorize="hasPermission('','/rsmmgr/riskList/white')"><a
                                    href="/rsm/risklist/white"><i class="fa fa-coffee"></i><span
                                        data-i18n="rsm.riskList.whiteList.content"></span></a></li>
                        </ul>
                    </li>
                    <!--实时风控管理-->
                    <li sec:authorize="hasPermission('','/rsmmgr/check')">
                        <a href="#"><i class="fa fa-coffee"></i><span data-i18n="rsm.check.title"></span><span
                                class="fa arrow"></span></a>
                        <ul class="nav nav-third-level collapse">
                            <li sec:authorize="hasPermission('','/rsmmgr/check/rule')"><a href="/rsm/check/rule"><i
                                        class="fa fa-coffee"></i><span data-i18n="rsm.check.rule.content"></span></a>
                            </li>
                            <li sec:authorize="hasPermission('','/rsmmgr/check/censor')"><a href="/rsm/check/censor"><i
                                        class="fa fa-coffee"></i><span data-i18n="rsm.check.censor.content"></span></a>
                            </li>
                            <li sec:authorize="hasPermission('','/rsmmgr/check/param')"><a href="/rsm/check/param"><i
                                        class="fa fa-coffee"></i><span data-i18n="rsm.check.param.content"></span></a>
                            </li>
                        </ul>
                    </li>
                    <li sec:authorize="hasPermission('','/rsmmgr/highrisk')"><a href="/rsm/highrisk"><i
                                class="fa fa-coffee"></i><span data-i18n="rsm.highrisk.content"></span></a></li>
                </ul>
            </li>
            <li sec:authorize="hasPermission('','/rptmgr')">
                <a href="#"><i class="fa fa-book"></i> <span class="nav-label" data-i18n="nav.rpt"></span><span
                        class="fa arrow"></span></a>
                <ul class="nav nav-second-level collapse">
                    <li sec:authorize="hasPermission('','/rptmgr/define')"><a href="/rpt/mgr/define"><i
                                class="fa fa-coffee"></i><span data-i18n="rpt.rptRun.mgrDef.content"></span></a></li>
                    <li sec:authorize="hasPermission('','/rptmgr/opera')"><a href="/rpt/mgr/opera"><i
                                class="fa fa-coffee"></i><span data-i18n="rpt.rptRun.mgrOpr.content"></span></a></li>
                </ul>
            </li>
        </ul>
    </div>
</nav>