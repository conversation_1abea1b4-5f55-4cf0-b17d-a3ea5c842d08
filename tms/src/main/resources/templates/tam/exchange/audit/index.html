<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="nav.bussub.trdsub.exchangeaudit.title">兑换审核查询</title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>

            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="nav.bussub.trdsub.exchangeaudit.content">兑换审核查询</h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="nav.busmgr">业务管理</a>
                        </li>
                        <li>
                            <a data-i18n="nav.bussub.trdmgr">交易管理</a>
                        </li>
                        <li class="active">
                            <strong data-i18n="nav.bussub.trdsub.exchangeaudit.content">兑换审核查询</strong>
                        </li>
                    </ol>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!-- 查询表单 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12">
                                        <!-- 用户ID -->
                                        <label class="col-sm-2 control-label" for="userId"
                                            data-i18n="tam.userId">用户ID</label>
                                        <div class="col-sm-4">
                                            <input name="userId" id="userId" class="form-control" value="" />
                                        </div>
                                        <!-- 订单号 -->
                                        <label class="col-sm-2 control-label" for="orderNo"
                                            data-i18n="tam.orderNo">订单号</label>
                                        <div class="col-sm-4">
                                            <input name="orderNo" id="orderNo" class="form-control" value="" />
                                        </div>
                                    </div>
                                    <div class="form-group col-sm-12">
                                        <!-- 兑换方向 -->
                                        <label class="col-sm-2 control-label" for="direction"
                                            data-i18n="tam.direction">兑换方向</label>
                                        <div class="col-sm-4">
                                            <select name="direction" id="direction" class="form-control">
                                                <option value="" data-i18n="tam.selectAll">全部</option>
                                                <option value="S2F" data-i18n="tam.directionS2F">数兑法</option>
                                                <option value="F2S" data-i18n="tam.directionF2S">法兑数</option>
                                                <option value="F2F" data-i18n="tam.directionF2F">法兑法</option>
                                            </select>
                                        </div>
                                        <!-- 订单状态 -->
                                        <label class="col-sm-2 control-label" for="status"
                                            data-i18n="tam.status">订单状态</label>
                                        <div class="col-sm-4">
                                            <select name="status" id="status" class="form-control">
                                                <option value="" data-i18n="tam.selectAll">全部</option>
                                                <option value="PENDING" data-i18n="tam.statusPending">待审核</option>
                                                <option value="FIRST_AUDIT" data-i18n="tam.statusFirstAudit">初审中
                                                </option>
                                                <option value="SECOND_AUDIT" data-i18n="tam.statusSecondAudit">复核中
                                                </option>
                                                <option value="APPROVED" data-i18n="tam.statusApproved">审核通过</option>
                                                <option value="REJECTED" data-i18n="tam.statusRejected">拒绝</option>
                                                <option value="SUCCESS" data-i18n="tam.statusSuccess">成功</option>
                                                <option value="FAILED" data-i18n="tam.statusFailed">失败</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-sm-4 col-sm-offset-1">
                                            <button type="button" id="searchBtn" class="btn btn-primary"
                                                onclick="search()" data-i18n="tam.search">查询</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="exchangeAuditTable"
                                        class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="tam.userId">用户ID</th>
                                                <th data-i18n="tam.orderNo">订单号</th>
                                                <th data-i18n="tam.direction">兑换方向</th>
                                                <th data-i18n="tam.fromCoin">转出币种</th>
                                                <th data-i18n="tam.fromAmount">转出金额</th>
                                                <th data-i18n="tam.toCoin">转入币种</th>
                                                <th data-i18n="tam.toAmount">转入金额</th>
                                                <th data-i18n="tam.status">状态</th>
                                                <th data-i18n="tam.createTime">创建时间</th>
                                                <th data-i18n="tam.operations">操作</th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="detailModalLabel" data-i18n="tam.exchangeDetail">兑换交易详情</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.userId">用户ID</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-userId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.orderNo">订单号</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-orderNo"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.direction">兑换方向</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-direction"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.fromCoin">转出币种</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-fromCoin"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.fromAmount">转出金额</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-fromAmount"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.toCoin">转入币种</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-toCoin"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.toAmount">转入金额</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-toAmount"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.feeCoin">手续费币种</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-feeCoin"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.feeAmount">手续费金额</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-feeAmount"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.exchangeRate">兑换汇率</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-exchangeRate"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.status">状态</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-status"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.firstAuditInfo">初审信息</label>
                            <div class="col-sm-9">
                                <p class="form-control-static">
                                    <span id="detail-firstAuditUser"></span>
                                    <span id="detail-firstAuditTime"></span>
                                    <span id="detail-firstAuditResult"></span>
                                </p>
                                <p class="form-control-static" id="detail-firstAuditOpinion"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.secondAuditInfo">复核信息</label>
                            <div class="col-sm-9">
                                <p class="form-control-static">
                                    <span id="detail-secondAuditUser"></span>
                                    <span id="detail-secondAuditTime"></span>
                                    <span id="detail-secondAuditResult"></span>
                                </p>
                                <p class="form-control-static" id="detail-secondAuditOpinion"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.rejectReason">拒绝原因</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-rejectReason"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.id">订单ID</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-id"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.fromUsdRate">转出币种USD汇率</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-fromUsdRate"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.toUsdRate">转入币种USD汇率</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-toUsdRate"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.fromAmountUsd">转出金额USD等值</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-fromAmountUsd"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.toAmountUsd">转入金额USD等值</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-toAmountUsd"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.executeTime">执行时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-executeTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.createTime">创建时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-createTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.updateTime">更新时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detail-updateTime"></p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal" data-i18n="tam.close">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 审核模态框 -->
    <div class="modal fade" id="auditModal" tabindex="-1" role="dialog" aria-labelledby="auditModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="auditModalLabel" data-i18n="tam.audit">审核兑换交易</h4>
                </div>
                <div class="modal-body">
                    <form id="auditForm" class="form-horizontal">
                        <input type="hidden" id="audit-id" name="id">
                        <input type="hidden" id="audit-times" name="auditTimes">

                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.auditResult">审核结果</label>
                            <div class="col-sm-9">
                                <select class="form-control" id="audit-result" name="auditResult">
                                    <option value="0" data-i18n="tam.auditApproved">审核通过</option>
                                    <option value="1" data-i18n="tam.auditRejected">审核拒绝</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tam.auditOpinion">审核意见</label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="audit-opinion" name="auditOpinion"
                                    rows="3"></textarea>
                            </div>
                        </div>

                        <div class="form-group reject-reason-group" style="display:none;">
                            <label class="col-sm-3 control-label" data-i18n="tam.rejectReason">拒绝原因</label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="audit-reject-reason" name="rejectReason"
                                    rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="tam.cancel">取消</button>
                    <button type="button" class="btn btn-primary" id="submitAudit" data-i18n="tam.submit">提交</button>
                </div>
            </div>
        </div>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var table;
        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }

            // 初始化draw值
            $('input[name="draw"]').remove();
            $('body').append('<input type="hidden" name="draw" value="1">');

            // 处理DataTables处理中指示器问题
            $(document).ajaxStop(function () {
                $('.dataTables_processing').hide();
            });

            // 初始化DataTables
            table = $('#exchangeAuditTable').DataTable({
                dom: 'Blfrtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/tam/exchange/audit/list',
                    type: 'post',
                    data: function (d) {
                        // 更新draw值供dataFilter使用
                        $('input[name="draw"]').val(d.draw);

                        var params = {
                            pageNum: d.start / d.length + 1,
                            pageSize: d.length,
                            userId: $('#userId').val(),
                            orderNo: $('#orderNo').val(),
                            direction: $('#direction').val(),
                            status: $('#status').val()
                        };

                        console.log("发送请求参数:", params);

                        return JSON.stringify({
                            body: params
                        });
                    },
                    dataFilter: function (data) {
                        var json = JSON.parse(data);
                        console.log("接收到服务器响应:", json);

                        // 获取总记录数和当前页记录
                        var total = 0;
                        var records = [];
                        if (json.body && json.body.queryExchangeAuditList) {
                            total = json.body.queryExchangeAuditList.length;
                            records = json.body.queryExchangeAuditList;
                        }

                        console.log("总记录数:", total);
                        console.log("当前页记录数:", records.length);

                        var returnData = {
                            draw: parseInt($('input[name="draw"]').val()) || 1,
                            recordsTotal: total,
                            recordsFiltered: total,
                            data: records
                        };

                        console.log("返回给DataTables的数据:", returnData);
                        return JSON.stringify(returnData);
                    },
                    error: function (xhr, error, thrown) {
                        console.error('数据加载错误:', error);
                        toastr.error('数据加载失败');
                        // 确保处理中指示器消失
                        $('.dataTables_processing').hide();
                    },
                    complete: function () {
                        // 确保处理中指示器消失
                        $('.dataTables_processing').hide();
                    }
                },
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
                language: {
                    url: languageUrl,
                    processing: "处理中...",
                    paginate: {
                        first: "首页",
                        last: "末页",
                        next: "下一页",
                        previous: "上一页"
                    },
                    info: "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
                    infoEmpty: "显示第 0 至 0 项结果，共 0 项",
                    infoFiltered: "(由 _MAX_ 项结果过滤)",
                    lengthMenu: "显示 _MENU_ 项结果"
                },
                pageLength: 10,
                lengthMenu: [10, 25, 50, 100],
                pagingType: "simple_numbers",
                drawCallback: function (settings) {
                    console.log("表格绘制完成, 页面信息:", settings._iDisplayStart, "至", settings._iDisplayStart + settings._iDisplayLength, "共", settings._iRecordsTotal, "条");
                    $('.dataTables_processing').hide();
                },
                columns: [
                    { data: 'userId' },
                    { data: 'orderNo' },
                    {
                        data: 'direction',
                        render: function (data) {
                            if (data === 'S2F') return '数兑法';
                            if (data === 'F2S') return '法兑数';
                            if (data === 'F2F') return '法兑法';
                            return data;
                        }
                    },
                    { data: 'fromCoin' },
                    { data: 'fromAmount' },
                    { data: 'toCoin' },
                    { data: 'toAmount' },
                    {
                        data: 'status',
                        render: function (data) {
                            if (data === 'PENDING') return '待审核';
                            if (data === 'FIRST_AUDIT') return '初审中';
                            if (data === 'SECOND_AUDIT') return '复核中';
                            if (data === 'APPROVED') return '审核通过';
                            if (data === 'REJECTED') return '拒绝';
                            if (data === 'SUCCESS') return '成功';
                            if (data === 'FAILED') return '失败';
                            return data;
                        }
                    },
                    {
                        data: 'createTime',
                        render: function (data) {
                            if (!data) return '';
                            return new Date(data).toLocaleString();
                        }
                    },
                    {
                        data: null,
                        orderable: false,
                        render: function (data, type, row) {
                            var buttons = '<button type="button" class="btn btn-xs btn-info view-detail" data-id="' + row.id + '">详情</button> ';

                            // 根据状态决定是否显示审核按钮
                            if (row.status === 'PENDING' || row.status === 'FIRST_AUDIT') {
                                buttons += '<button type="button" class="btn btn-xs btn-primary first-audit" data-id="' + row.id + '">初审</button>';
                            } else if (row.status === 'SECOND_AUDIT') {
                                buttons += '<button type="button" class="btn btn-xs btn-warning second-audit" data-id="' + row.id + '">复核</button>';
                            }

                            return buttons;
                        }
                    }
                ],
                buttons: [
                    { extend: 'copyHtml5' },
                    { extend: 'csvHtml5' },
                    { extend: 'excelHtml5', title: '兑换审核列表' }
                ]
            });

            // 给表格添加绘制完成事件处理
            $('#exchangeAuditTable').on('draw.dt', function () {
                $('.dataTables_processing').hide();
            });

            // 添加页面长度变化事件监听
            $('#exchangeAuditTable').on('length.dt', function () {
                $('.dataTables_processing').hide();
            });

            // 添加页面变化事件监听
            $('#exchangeAuditTable').on('page.dt', function () {
                setTimeout(function () {
                    $('.dataTables_processing').hide();
                }, 500);
            });

            // 查看详情
            $('#exchangeAuditTable').on('click', '.view-detail', function () {
                var id = $(this).data('id');
                $.ajax({
                    url: '/tam/exchange/audit/detail',
                    type: 'post',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        body: {
                            id: id
                        }
                    }),
                    success: function (response) {
                        var data = response.body;

                        // 填充详情数据
                        $('#detail-userId').text(data.userId || '');
                        $('#detail-orderNo').text(data.orderNo || '');

                        // 兑换方向转义显示
                        var directionText = data.direction || '';
                        if (data.direction === 'S2F') directionText = '数兑法';
                        else if (data.direction === 'F2S') directionText = '法兑数';
                        else if (data.direction === 'F2F') directionText = '法兑法';
                        $('#detail-direction').text(directionText);

                        $('#detail-fromCoin').text(data.fromCoin || '');
                        $('#detail-fromAmount').text(data.fromAmount || '');
                        $('#detail-toCoin').text(data.toCoin || '');
                        $('#detail-toAmount').text(data.toAmount || '');
                        $('#detail-feeCoin').text(data.feeCoin || '');
                        $('#detail-feeAmount').text(data.feeAmount || '');
                        $('#detail-exchangeRate').text(data.exchangeRate || '');

                        // 状态转义显示
                        var statusText = data.status || '';
                        if (data.status === 'PENDING') statusText = '待审核';
                        else if (data.status === 'FIRST_AUDIT') statusText = '初审中';
                        else if (data.status === 'SECOND_AUDIT') statusText = '复核中';
                        else if (data.status === 'APPROVED') statusText = '审核通过';
                        else if (data.status === 'REJECTED') statusText = '拒绝';
                        else if (data.status === 'SUCCESS') statusText = '成功';
                        else if (data.status === 'FAILED') statusText = '失败';
                        $('#detail-status').text(statusText);
                        $('#detail-id').text(data.id || '');
                        $('#detail-fromUsdRate').text(data.fromUsdRate || '');
                        $('#detail-toUsdRate').text(data.toUsdRate || '');
                        $('#detail-fromAmountUsd').text(data.fromAmountUsd || '');
                        $('#detail-toAmountUsd').text(data.toAmountUsd || '');

                        // 初审信息
                        var firstAuditInfo = '';
                        if (data.firstAuditUser) {
                            firstAuditInfo += '审核人: ' + data.firstAuditUser + ' ';
                        }
                        if (data.firstAuditTime) {
                            firstAuditInfo += '审核时间: ' + new Date(data.firstAuditTime).toLocaleString() + ' ';
                        }
                        if (data.firstAuditResult) {
                            firstAuditInfo += '审核结果: ' + data.firstAuditResult;
                        }
                        $('#detail-firstAuditUser').text(firstAuditInfo);
                        $('#detail-firstAuditOpinion').text('审核意见: ' + (data.firstAuditOpinion || ''));

                        // 复核信息
                        var secondAuditInfo = '';
                        if (data.secondAuditUser) {
                            secondAuditInfo += '审核人: ' + data.secondAuditUser + ' ';
                        }
                        if (data.secondAuditTime) {
                            secondAuditInfo += '审核时间: ' + new Date(data.secondAuditTime).toLocaleString() + ' ';
                        }
                        if (data.secondAuditResult) {
                            secondAuditInfo += '审核结果: ' + data.secondAuditResult;
                        }
                        $('#detail-secondAuditUser').text(secondAuditInfo);
                        $('#detail-secondAuditOpinion').text('审核意见: ' + (data.secondAuditOpinion || ''));

                        // 拒绝原因
                        $('#detail-rejectReason').text(data.rejectReason || '');

                        // 时间相关字段
                        $('#detail-executeTime').text(data.executeTime ? new Date(data.executeTime).toLocaleString() : '');
                        $('#detail-createTime').text(data.createTime ? new Date(data.createTime).toLocaleString() : '');
                        $('#detail-updateTime').text(data.updateTime ? new Date(data.updateTime).toLocaleString() : '');

                        // 显示模态框
                        $('#detailModal').modal('show');
                    },
                    error: function (xhr, status, error) {
                        toastr.error('获取详情失败: ' + error);
                    }
                });
            });

            // 初审
            $('#exchangeAuditTable').on('click', '.first-audit', function () {
                var id = $(this).data('id');
                $('#audit-id').val(id);
                $('#audit-times').val(0); // 初审
                $('#auditModalLabel').text('初审兑换交易');
                $('#auditModal').modal('show');
            });

            // 复核
            $('#exchangeAuditTable').on('click', '.second-audit', function () {
                var id = $(this).data('id');
                $('#audit-id').val(id);
                $('#audit-times').val(1); // 复核
                $('#auditModalLabel').text('复核兑换交易');
                $('#auditModal').modal('show');
            });

            // 审核结果改变时显示/隐藏拒绝原因字段
            $('#audit-result').change(function () {
                if ($(this).val() === '1') {
                    $('.reject-reason-group').show();
                } else {
                    $('.reject-reason-group').hide();
                }
            });

            // 提交审核
            $('#submitAudit').click(function () {
                var formData = {
                    id: $('#audit-id').val(),
                    auditTimes: parseInt($('#audit-times').val()),
                    auditResult: parseInt($('#audit-result').val()),
                    auditOpinion: $('#audit-opinion').val(),
                    rejectReason: $('#audit-reject-reason').val()
                };

                $.ajax({
                    url: '/tam/exchange/audit',
                    type: 'post',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        body: formData
                    }),
                    success: function (response) {
                        $('#auditModal').modal('hide');
                        toastr.success('审核成功');
                        // 先隐藏处理中指示器（如果存在）
                        $('.dataTables_processing').hide();
                        // 刷新表格
                        table.ajax.reload(function () {
                            // 回调中再次确保处理中指示器消失
                            $('.dataTables_processing').hide();
                        }, false);
                    },
                    error: function (xhr, status, error) {
                        toastr.error('审核失败: ' + error);
                    }
                });
            });
        });

        // 搜索方法
        function search() {
            console.log("执行搜索操作");
            // 先隐藏处理中指示器（如果存在）
            $('.dataTables_processing').hide();
            // 刷新表格
            table.ajax.reload(function () {
                console.log("表格刷新完成");
                // 回调中再次确保处理中指示器消失
                $('.dataTables_processing').hide();
            }, false);
        }
    </script>
</body>

</html>