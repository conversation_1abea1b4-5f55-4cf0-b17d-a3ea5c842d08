<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title data-i18n="csm.vouchertitle"></title>
    <div th:replace="head"></div>
    <style>
        div.floatright{
            display:inline;
            float:right;
            padding-left:20px;
            padding-right:20px;
        }

        ._right{
            display:inline;
            float:right;
            padding-right:15px;
        }
    </style>
</head>
<body>
<div id="wrapper">
    <div th:replace="nav"></div>
    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>
        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="csm.voucherhead"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.csmmgr"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="csm.voucherhead"></strong>
                    </li>
                </ol>
            </div>
        </div>

        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">

                            <!-- 表头导航栏 -->
                            <div class="box-header">
                                <table style="width: 80%">
                                    <tr>
                                        <td align="center">
                                            <label class="control-label" data-i18n="csm.voucher.jrnNo"></label>
                                        </td>
                                        <td>
                                            <input class="form-control" name="jrnNo" id = "jrnNo"/>
                                        </td>
                                        <td align="center">
                                            <label class="control-label" data-i18n="csm.voucher.mblNo"></label>
                                        </td>
                                        <td>
                                            <input class="form-control" name="mblNo" id = "mblNo" />
                                        </td>
                                        <td align="center">
                                            <label class="control-label" data-i18n="csm.voucher.userId"></label>
                                        </td>
                                        <td>
                                            <input class="form-control" name="userId" id = "userId" />
                                        </td>
                                    </tr>
                                    <tr></tr>
                                    <tr>
                                        <td align="center">
                                            <label class="control-label" data-i18n="csm.voucher.queryDt"></label>
                                        </td>
                                        <td>
                                            <input type="text" class="searchInput" name="queryDtS" id="queryDtS">-
                                            <input type="text" class="searchInput" name="queryDtE" id="queryDtE">
                                        </td>
                                        <td align="center">
                                            <label class="control-label" data-i18n="csm.voucher.acTyp"></label>
                                        </td>
                                        <td>
                                            <select class="form-control" name="acTyp" id = "acTyp">
                                                <option value="" data-i18n="csm.voucher.payAc"></option>
                                            </select>
                                        </td>
                                        <td></td>
                                        <td>
                                            <button type="button" class="btn btn-w-m btn-primary _right"
                                                    data-i18n="csm.voucher.query" onclick="searchButton()">
                                            </button>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <hr/>

                            <!-- 表格主体 -->
                            <div class="table-responsive">
                                <table id="dataTables" class="table table-striped table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th data-i18n="csm.voucher.acDt"></th>
                                        <th data-i18n="csm.voucher.sysTm"></th>
                                        <th data-i18n="csm.voucher.jrnNo"></th>
                                        <th data-i18n="csm.voucher.jrnSeq"></th>
                                        <th data-i18n="csm.voucher.acNo"></th>
                                        <th data-i18n="csm.voucher.capTyp"></th>
                                        <th data-i18n="csm.voucher.drAmt"></th>
                                        <th data-i18n="csm.voucher.crAmt"></th>
                                        <th data-i18n="csm.voucher.txTyp"></th>
                                        <th data-i18n="csm.voucher.acTyp"></th>
                                        <th data-i18n="csm.voucher.rmk"></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>
    </div>
</div>
<div th:replace="script"></div>
<script>
    $(document).ready(function () {

        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        i18nLoad.then(function () {
            table = $('#dataTables').DataTable({
                ajax: {
                    contentType: 'application/json',
                    url: '/acm/voucherquery/findAll',
                    type: 'POST',
                    data: function (d) {
                        var jrnNo = $("#jrnNo").val();
                        var mblNo = $("#mblNo").val();
                        var userId = $("#userId").val();
                        var queryDtS = $("#queryDtS").val();
                        var queryDtE = $("#queryDtE").val();

                        d.extra_search = {
                            "jrnNo": jrnNo,
                            "mblNo": mblNo,
                            "userId": userId,
                            "queryDtS": queryDtS,
                            "queryDtE" : queryDtE
                        };
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                responsive: true,
                processing: true,
                searching: false,
                language: {
                    url: languageUrl
                },
                columns: [{
                    data: 'acDt'
                },{
                    data: 'createTime',
                    type: "datetime",
                    def: function () {
                        return new Date();
                    },
                    format: "YYYY-MM-DD HH:mm:ss"
                },{
                    data: 'jrnNo'
                },{
                    data: 'jrnSeq'
                },{
                    data: 'acNo'
                },{
                    data: 'capTyp' ,
                    render: function (data, type, row) {
                        if (data == '1') {
                            return $.i18n.t("acm.cashAcc");
                        }
                        if (data == '8') {
                            return $.i18n.t("acm.settleAcc");
                        }
                        return data;
                    }
                },{
                    data: 'drAmt'
                },{
                    data: 'crAmt'
                },{
                    data: 'txTyp',
                    render: function (data, type, row) {
                        switch (data) {
                            case '00':
                                return $.i18n.t("rsm.constant.txTyp.all");
                            case '01':
                                return $.i18n.t("rsm.constant.txTyp.recharge");
                            case '02':
                                return $.i18n.t("rsm.constant.txTyp.consume");
                            case '03':
                                return $.i18n.t("rsm.constant.txTyp.transfer");
                            case '04':
                                return $.i18n.t("rsm.constant.txTyp.withdraw");
                            case '05':
                                return $.i18n.t("rsm.constant.txTyp.seatel");
                            case '06':
                                return $.i18n.t("rsm.constant.txTyp.refunds");
                            case '07':
                                return $.i18n.t("rsm.constant.txTyp.interest");
                            case '08':
                                return $.i18n.t("rsm.constant.txTyp.payment");
                            case '09':
                                return $.i18n.t("rsm.constant.txTyp.revoke");
                        }
                    }
                },{
                    data: '',
                    render:function () {
                        return $.i18n.t("csm.voucher.payAc")
                    }
                },{
                    data: 'rmk'
                }
                ]
            });
        });
    });

    function searchButton() {
        table.ajax.reload();
    }


    /**初始化日期控件**/
    var beginTimePick = $('#queryDtS').datetimepicker({
        lang: 'en',
        timepicker: false,
        validateOnBlur: false,
        format: 'Y-m-d ',
        formatDate: 'Y-m-d',
        onChangeDate: function (dateText, inst) {
            endTimePick.datetimepicker('minDate', new Date(dateText.replace("-", "-")));
        }
    });

    var endTimePick = $('#queryDtE').datetimepicker({
        lang: 'en',
        timepicker: false,
        validateOnBlur: false,
        format: 'Y-m-d ',
        formatDate: 'Y-m-d',
        // maxDate:'+1970/01/01',
    });
</script>
</body>
</html>