<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title data-i18n="urm.mermgr.title"></title>
    <div th:replace="head"></div>
    <style>
        div.floatright{
            display:inline;
            float:right;
            padding-left:20px;
            padding-right:20px;
        }

        ._right{
            display:inline;
            float:right;
            padding-right:15px;
        }
    </style>
</head>
<body>
<div id="wrapper">
    <div th:replace="nav"></div>
    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>
        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="urm.mermgr.manage"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.busmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.bussub.mermgr"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="urm.mermgr.manage"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <!--商户录入modal-->
        <div th:replace="urm/mermgr/merModal"></div>
        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">

                            <!-- 表头导航栏 -->
                            <div class="box-header">
                                <table style="width: 80%">
                                    <tr>
                                        <td align="center">
                                            <label class="control-label" data-i18n="urm.mermgr.mercId"></label>
                                        </td>
                                        <td>
                                            <input class="form-control" name="searchMercId" id = "searchMercId"/>
                                        </td>
                                        <td align="center">
                                            <label class="control-label" data-i18n="urm.mermgr.mercNm"></label>
                                        </td>
                                        <td>
                                            <input class="form-control" name="searchMercNm" id = "searchMercNm" />
                                        </td>
                                        <td align="center">
                                            <label class="control-label" data-i18n="urm.mermgr.mercSts"></label>
                                        </td>
                                        <td>
                                            <select class="form-control" name="searchMercSts" id = "searchMercSts">
                                                <option value="" data-i18n="urm.mermgr.passed"></option>
                                                <option value="0" data-i18n="urm.mermgr.normalAccount"></option>
                                                <option value="1" data-i18n="urm.mermgr.cancelAccount"></option>
                                                <option value="2" data-i18n="urm.mermgr.underReview"></option>
                                                <option value="3" data-i18n="urm.mermgr.reviewRejected"></option>
                                            </select>
                                        </td>
                                        <!--</tr>-->
                                        <!--<tr>-->
                                        <!--<td align="center">-->
                                        <!--<label class="control-label" data-i18n="urm.beginDt"></label>-->
                                        <!--</td>-->
                                        <!--<td>-->
                                        <!--<input class="form-control" name="searchBeginDt" id="beginTime"/>-->
                                        <!--</td>-->
                                        <!--<td align="center">-->
                                        <!--<label class="control-label" data-i18n="urm.endDt"></label>-->
                                        <!--</td>-->
                                        <!--<td>-->
                                        <!--<input class="form-control" name="searchEndDt" id="endTime"/>-->
                                        <!--</td>-->
                                        <td></td>
                                        <td>
                                            <button type="button" class="btn btn-w-m btn-primary _right" data-i18n="urm.search" onclick="searchButton()">
                                            </button>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <hr/>

                            <!-- 表格主体 -->
                            <div class="table-responsive">
                                <table id="example" class="table table-striped table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th data-i18n="urm.mermgr.mercId"></th>
                                        <th data-i18n="urm.mermgr.mercNm"></th>
                                        <th data-i18n="urm.mermgr.mercSts"></th>
                                        <th data-i18n="urm.mermgr.createTime"></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>
    </div>
</div>
<div th:replace="script"></div>
<script>
    var editor;
    var table;

    $(document).ready(function () {

        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        i18nLoad.then(function () {
            editor = new $.fn.dataTable.Editor({
                ajax: {
                    remove: {
                        type: 'POST',
                        url: '#',
                        contentType: 'application/json',
                        data: function (d) {
                            return JSON.stringify(d);
                        }
                    }
                },
                table: "#example",
                idSrc: 'mercId',
                fields: [
                    {name: "mercId", type: "hidden"},
                    {
                        label: $.i18n.t("urm.mermgr.mercSts"), name: "mercSts",
                        type:  "select",
                        options: [
                            { label: $.i18n.t("urm.mermgr.normalAccount"), value: "0" },
                            { label: $.i18n.t("urm.mermgr.cancelAccount"), value: "1" }
                        ]
                    }
                ],
                i18n: {
                    remove: {
                        button: $.i18n.t("demo.delete"), title: $.i18n.t("demo.delete"), submit: $.i18n.t("demo.delete"),
                        confirm: {
                            _: $.i18n.t("demo.multi-delete"),
                            1: $.i18n.t("demo.single-delete")
                        }
                    }
                }
            });

            table = $('#example').DataTable({
                dom: 'B<"floatright"l>rtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/urm/mermgr/info/findAll',
                    type: 'POST',
                    data: function (d) {
                        return JSON.stringify(d);
                    }
                },
                drawCallback: function(settings) {
                    table.button(1).disable();
//                    table.button(2).disable();
//                    table.button(3).disable();
                },
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
                select: {
                    style:    'single',
                    selector: 'td:first-child'
                },
                language: {
                    url: languageUrl
                },
                buttons: [
                    {
                        text: $.i18n.t("urm.mermgr.add"),
                        action: function ( e, dt, node, config ) {
                            //按钮js
                           addMerChant() ;
                        }
                    }, {
                        text: $.i18n.t("urm.mermgr.edit"),
                        action: function ( e, dt, node, config ) {
                            //按钮js
                            modifyMerChant();
                        }
                    }
//                    , {
//                        extend: "remove", editor: editor
//                    }, {
//                        text: $.i18n.t("urm.mermgr.review"),
//                        action: function ( e, dt, node, config ) {
//                            //按钮js
//                        }
//                    }
                ],
                columns: [
                    {
                        data: 'mercId'
                    }, {
                        data: 'mercNm'
                    }, {
                        data: 'mercSts',
                        render: function (data, type, row) {
                            switch (data) {
                                case '0':
                                    return $.i18n.t("urm.mermgr.normalAccount");
                                case '1':
                                    return $.i18n.t("urm.mermgr.cancelAccount");
                                case '2':
                                    return $.i18n.t("urm.mermgr.underReview");
                                case '3':
                                    return $.i18n.t("urm.mermgr.reviewRejected");
                            }
                        }
                    }, {
                        data: 'createTime',
                        type: "datetime",
                        def: function () {
                            return new Date();
                        },
                        format: "YYYY-MM-DD HH:mm:ss"
                    }
                ],
                initComplete: function () {
                    table.button(1).disable();
//                    table.button(2).disable();
//                    table.button(3).disable();
                }
            });

            $('#example').on('select.dt deselect.dt', function() {
                table.buttons(1).enable(
                    table.rows({selected: true}).indexes().length === 0 ? false : true
                );
//                table.buttons(2).enable(
//                    table.rows({selected: true}).indexes().length === 0 ? false : true
//                );
//                table.buttons(3).enable(
//                    table.rows({selected: true}).indexes().length === 0 ? false : true
//                );
            })
        });

//        $.getJSON("/locales/zh.json",function(data){
//            var mercTrdCls = $("#mercTrdCls");
//            mercTrdCls.empty();//清空内容
//            var cls =data["cls"];
//            for(var key in cls){
//                strHtml +="<option value='"+cls[key]+"''></option>";
//            }
//
//            mercTrdCls.html(strHtml);
//        });
        $("imercLogo").click(function () {
            $("#mercLogo").click();
        });
        $("ibusLicImg").click(function () {
            $("#busLicImg").click();
        });
        $("icertImg").click(function () {
            $("#certImg").click();
        });
        $("icertImgB").click(function () {
            $("#certImgB").click();
        });
        $("icardImg").click(function () {
            $("#cardImg").click();
        });
        $("icardImgB").click(function () {
            $("#cardImgB").click();
        });
        $("imerPotocolImg").click(function () {
            $("#merPotocolImg").click();
        });
        if (window.FileReader) {
            $("#mercLogo").change(function() {
                mercLogoPath = '' ;
                changeFile(this,"mercLogo","imercLogo");
            });
            $("#busLicImg").change(function() {
                busLicImgPath = '' ;
                changeFile(this,"busLicImg","ibusLicImg");
            });
            $("#certImg").change(function() {
                certImgPath = '' ;
                changeFile(this,"certImg","icertImg");
            });
            $("#certImgB").change(function() {
                certImgPath = '' ;
                changeFile(this,"certImgB","icertImgB");
            });
            $("#cardImg").change(function() {
                cardImgPath = '' ;
                changeFile(this,"cardImg","icardImg");
            });
            $("#cardImgB").change(function() {
                cardImgPath = '' ;
                changeFile(this,"cardImgB","icardImgB");
            });
            $("#merPotocolImg").change(function() {
                merPotocolImgPath = '';
                changeFile(this,"merPotocolImg","imerPotocolImg");
            });
        } else {
            //$inputImage.addClass("hide");
        }

        //添加
        $("#mermgrAdd").click(function () {
            mermgrAdd();
        });
        //修改
        $("#mercModify").click(function () {
            mermgrModify();
        });

        //结算地点选择点击事件
        $("#settleSite").change(function () {

            var settleSite = $("#settleSite").val();
            if (settleSite == '001') {
                $("#capCardNo").attr("disabled",false);
                $("#capCardName").attr("disabled",false);
                $("#capCorgNm").attr("disabled",false);
                $("#subbranch").attr("disabled",false);
                $("#settleCycleType").attr("disabled",false);
                $("#settleEffDate").attr("disabled",false);
                $("#settleExpDate").attr("disabled",false);
                $("#hallSites").attr("disabled",true);
                $("#settleType").attr("disabled",false);
                $("#settleType").val("auto");
            }else {
                $("#hallSites").attr("disabled",false);
                $("#capCardNo").attr("disabled",true);
                $("#capCardName").attr("disabled",true);
                $("#capCorgNm").attr("disabled",true);
                $("#subbranch").attr("disabled",true);
                $("#settleType").attr("disabled",true);
                $("#settleCycleType").attr("disabled",true);
                $("#settleType").val("hall");

            }
        });

        //计费方式选择事件
        $("#calculateType").change(function () {
             calculateTypeChange("calculateType","");
        });
        $("#calculateType1").change(function () {
             calculateTypeChange("calculateType","1");
        });

        //检查唯一性
        $("#mngAcc").blur(function () {
            if(!checkMerAcc()){
                return ;
            }
            $.ajax({
                url:"/urm/mermgr/info/checkMngAcc",
                dataType: "json",
                type:"post",
                data :{
                    "mngAcc": $("#mngAcc").val(),
                    "merId":   $("#mercId").val()
                },
                success:function(data) {
                    if (data.result != '1') {
                        swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.mngAccExist"), "error");
                        $("#mngAcc").val("");
                    }
                },
                error: function() {
                    swal($.i18n.t("urm.mermgr.failed"),  "error");
                }
            });
        });
        //检查手机号的合法性
        $("#mngMoblie").blur(function () {
            checkMobile($("#mngMoblie").val() ,"mngMoblie");
        });
        $("#recMobile").blur(function () {
            checkMobile($("#recMobile").val() , "recMobile");
        });

        //初始化商户级别下拉选择框
        innitMerLevel();

        //添加结算模式changeshijian
        $("#settleType").change(function () {
            changeSettleType();
        });

    })


    function changeSettleType() {
        var settleType =  $("#settleType").val();
        if (settleType == 'self') {
            $("#settleCycleType").attr("disabled",true);
        } else {
            $("#settleCycleType").attr("disabled",false);
        }
    }
    //初始化商户级别下拉选择框
    function innitMerLevel() {
        var html="";
        //html = "  <option value='7' >护手霜</option>";
        $.ajax({
            url:"/urm/mermgr/info/getMerLevel",
            dataType: "json",
            type:"post",
            data :{
            },
            success:function(data) {
                if (data.list != null) {
                    list = data.list;
                    for (var i=0 ; i< list.length ; i++ ) {
                        html += "<option value='" +list[i].parmVal + "'>" + list[i].parmDispNm + "</option>";
                        $("#merLvl").html(html);
                    }
                }
            },
            error: function() {
                swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.innitMerLevelFailed"), "error");
            }
        });

    }
    //点击事件
    function calculateTypeChange(id , index) {
        var calculateType = $("#calculateType"+index ).val();
        if (calculateType == 'fixed') {
            $("#minFee"+index).attr("disabled",true);
            $("#maxFee"+index).attr("disabled",true);
            $("#minFee"+index).val("");
            $("#maxFee"+index).val("");

        } else {
            $("#minFee"+index).attr("disabled",false);
            $("#maxFee"+index).attr("disabled",false);
        }
    }
    //检查手机号的合法性
    function  checkMobile(mobile , id) {
        $.ajax({
            url:"/urm/mermgr/info/checkMobile",
            dataType: "json",
            type:"post",
            data :{
                "mobile": mobile,
            },
            success:function(data) {
                if (data.result != '1') {
                    swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkMoblieFormat"), "error");
                    $("#" + id).val("");
                }
            },
            error: function() {
                swal($.i18n.t("urm.mermgr.failed"),  "error");
                $("#" + id).val("");
            }
        });
    }
    //图片文件路径全局变量,做修改等操作前需要重制先
    var mercLogoPath = '' ,busLicImgPath = '' ,certImgPath = '' , cardImgPath = '' ,merPotocolImgPath = '';
    //file改变出发时间
    function changeFile(object, buId ,imgId){
        var fileReader = new FileReader(),
            files = object.files,
            file;
        if (!files.length) {
            return;
        }
        file = files[0];
        if (/^image\/\w+$/.test(file.type)) {
            fileReader.readAsDataURL(file);
            //fileReader.readAsText(file, "UTF-8");
            fileReader.onload = function () {
                $("#"+buId).val("");
                var base64String = this.result.split(",");
                //上传图片
             //   $("#"+imgId).attr("src",this.result);
                uploadImg(file.name,file.type,base64String[1],buId);
            };
        } else {
            alert("Please choose an image file.");
        }
    }
    //上传图片
    function uploadImg(name,imgType,imgStr ,type) {
        $.ajax({
            url:"/file/upload/base64Img",
            data:{
                "name": name,
                "fileType":imgType,
                "base64String":imgStr,
                "uploadToFileServer" :"Y",
                "type":type
            },
            dataType: "json",
            type:"post",
            success:function(data) {
                if (data != null) {
                   // mercLogoPath = '' ,busLicImgPath = '' ,certImgPath = '' , cardImgPath = '' ,merPotocolImgPath = '';
                    if(type == 'mercLogo') {
                        $("#imercLogo").attr("src",data.path);

                    }else if ( type == "busLicImg") {
                        $("#ibusLicImg").attr("src",data.path);

                    } else if (type == "certImg") {
                        $("#icertImg").attr("src",data.path);

                    } else if (type == "certImgB") {
                        $("#icertImgB").attr("src",data.path);

                    } else if(type == "cardImg") {
                        $("#icardImg").attr("src",data.path);

                    } else if(type == "cardImgB") {
                        $("#icardImgB").attr("src",data.path);

                    }else if (type == "merPotocolImg"){
                        $("#imerPotocolImg").attr("src",data.path);
                    } else {
                        alert("not exist this type");
                    }

                } else {
                    <!--隐藏模态框-->
                    swal($.i18n.t("activity.swal-fail"), $.i18n.t("activity.detailNull"), "error");
                }
            },
            error: function() {
                swal($.i18n.t("activity.swal-fail"),  "error");
            }
        });
    }
    function searchButton() {
        var id = $("#searchMercId").val();
        var nm = $("#searchMercNm").val();
        var sts = $("#searchMercSts").val();
        table.column(0).search(id)
            .column(1).search(nm)
            .column(2).search(sts);
            table.draw();
    }
    //弹出录入窗口
    function addMerChant() {
        //重设置输入
        clear();
        $("#mermgrAdd").show();
        $("#mercModify").hide();
        $("#handleModal").modal("show");
    }


    //弹出修改窗口
    function modifyMerChant() {
        //重设置输入
        $("#mermgrAdd").hide();
        $("#mercModify").show();
        clear();
        //获取修改数据 根据如果商户状态为未审核则数据从审批流水表中获取，如果商户状态为审核通过，则从商户信息表中获取
        var row = table.row('.selected');
        if(row.length == 0) {
            $("#handleModal").modal("hide");
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.shouldSelectOne"), "error");
            return;
        }
        var rowData =row.data();
        var mercId = rowData["mercId"];
        var mercSts = rowData["mercSts"];
        $("#mercId").val(mercId);
        $("#mercSts").val(mercSts);
        var putdata = {
            "mercId" : mercId,
            "mercSts" : mercSts
        }
        <!--ajax异步调起后台服务，用户号查询商户信息-->
        $.ajax({
            url:"/urm/mermgr/info/modifyView",
            data: putdata,
            dataType: "json",
            type:"post",
            success:function(data) {
                if (data != null) {
                    putData(data);
                    var settleSite = $("#settleSite").val();
                    if (settleSite != '001') {
                        $("#hallSites").attr("disabled", false);
                        $("#capCardNo").attr("disabled", true);
                        $("#capCardName").attr("disabled", true);
                        $("#capCorgNm").attr("disabled", true);
                        $("#subbranch").attr("disabled", true);
                        $("#settleType").attr("disabled", true);
                        $("#settleCycleType").attr("disabled", true);
                        $("#settleType").val("hall");
                    }
                    calculateTypeChange("calculateType" ,"");
                    calculateTypeChange("calculateType" ,"1");
                    $("#handleModal").modal("show");
                } else {
                    <!--隐藏模态框-->
                    $("#handleModal").modal("hide");
                    swal($.i18n.t("urm.mermgr.failed"), data.msgInfo, "error");
                }
            },
            error: function() {
                $("#handleModal").modal("hide");
                swal($.i18n.t("urm.mermgr.failed"),  "error");
            }
        });
    }

    //控制重复提交参数
    var repeat = false ;
    //修改商户
    function mermgrModify() {
        if(!check()) {
            return ;
        }

        //控制重复提交
        if (repeat) {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.repeat"), "error");
        } else {
            repeat = true
        }
        var putdata = getData();
        $.ajax({
            contentType: 'application/json',
            url:"/urm/mermgr/info/modify",
            data: JSON.stringify(putdata),
            dataType: "json",
            type:"post",
            success:function(data) {
                if (data.msgCd == "1") {
                    repeat = false
                    $("#handleModal").modal("hide");
                    swal($.i18n.t("urm.mermgr.success"), $.i18n.t("urm.mermgr.success"), "success");

                } else {
                    repeat = false
                    <!--隐藏模态框-->
                    //$("#handleModal").modal("hide");
                    swal($.i18n.t("urm.mermgr.failed"), data.msgInfo, "error");
                }
            },
            error: function() {
                repeat = false
                //$("#handleModal").modal("hide");
                swal($.i18n.t("urm.mermgr.failed"),  "error");
            }
        });
    }
    //添加商户
    function mermgrAdd() {
        if(!check()) {
            return ;
        }
        var putdata = getData();
        $.ajax({
            contentType: 'application/json',
            url:"/urm/mermgr/info/merRegister",
            data: JSON.stringify(putdata),
            dataType: "json",
            type:"post",
            success:function(data) {
                if (data.msgCd == "1") {
                    $("#handleModal").modal("hide");
                    swal($.i18n.t("urm.mermgr.success"), $.i18n.t("urm.mermgr.success"), "success");

                } else {
                    <!--隐藏模态框-->
                    //$("#handleModal").modal("hide");
                    swal($.i18n.t("urm.mermgr.failed"), data.msgInfo, "error");
                }
            },
            error: function() {
                //$("#handleModal").modal("hide");
                swal($.i18n.t("urm.mermgr.failed"),  "error");
            }
        });
    }


    //展示数据
    function putData(mercobj) {

        $("#imercLogo").attr("src",mercobj.mercLogoPath);
        $("#ibusLicImg").attr("src",mercobj.busLicImgPath);
        $("#icertImg").attr("src",mercobj.cardImgPath);
        $("#icertImgB").attr("src",mercobj.cardImgPathB);
        $("#icardImg").attr("src",mercobj.certImgPath);
        $("#icardImgB").attr("src",mercobj.certImgPathB);
        $("#imerPotocolImg").attr("src",mercobj.merPotocolImgPath);
        $("#mercNm").val(mercobj.mercName);
        $("#mercShortName").val(mercobj.mercShortName);
        $("#comercReg").val(mercobj.comercReg);
        $("#mercTrdCls").val(mercobj.mercTrdCls);
        $("#cprTyp").val(mercobj.cprTyp);
        $("#mgtScp").val(mercobj.mgtScp);
        $("#merRsgaddr").val(mercobj.merRegAddr);
        $("#merRsgaddrS").val(mercobj.merRegAddr);
        $("#merRsgaddrI").val("");
        $("#fixedaddrBut").val("");
        $("#prinNm").val(mercobj.crpNm);
        $("#term").val(mercobj.opnBusDtSr);
        $("#certTyp").val(mercobj.crpIdTyp);
        $("#certNo").val(mercobj.crpIdNo);
        $("#mangerNm").val(mercobj.diplayNm);
        $("#mngMoblie").val(mercobj.mblNo);
        $("#mngAcc").val(mercobj.loginId);
        $("#mngEmail").val(mercobj.email);
        $("#recMobile").val(mercobj.refereeMblNo);
        $("#merLvl").val(mercobj.merLvl);
        $("#capCardNo").val(mercobj.capCardNo);
        $("#capCardName").val(mercobj.capCardName);
        $("#capCorgNm").val(mercobj.capCorgNo);
        $("#subbranch").val(mercobj.subbranch);
        $("#settleType").val(mercobj.settleType);
        $("#settleCycleType").val(mercobj.settleCycleType);
        $("#settleEffDate").val(mercobj.settleEffDate);
        $("#settleExpDate").val(mercobj.settleExpDate);
        $("#drawDays").val(mercobj.drawDays);
        $("#settleSite").val(mercobj.settleSite);
        $("#hallSites").val(mercobj.hallSites);

        if(mercobj.rateInfolist[0] != null) {
            $("#busType").val(mercobj.rateInfolist[0].busType);
            $("#chargeType").val(mercobj.rateInfolist[0].chargeType);
            $("#calculateType").val(mercobj.rateInfolist[0].calculateType);
            $("#beginCalAmt").val(mercobj.rateInfolist[0].beginCalAmt);
            $("#rate").val(mercobj.rateInfolist[0].rate);
            $("#minFee").val(mercobj.rateInfolist[0].minFee);
            $("#maxFee").val(mercobj.rateInfolist[0].maxFee);
            $("#expDate").val(mercobj.rateInfolist[0].expDate);
            $("#effDate").val(mercobj.rateInfolist[0].effDate);
            $("#beginCalFee").val(mercobj.rateInfolist[0].beginCalFee);
            $("#calculateMod").val(mercobj.rateInfolist[0].calculateMod);
        }
        if (mercobj.rateInfolist[1] != null) {
            $("#busType1").val(mercobj.rateInfolist[1].busType);
            $("#chargeType1").val(mercobj.rateInfolist[1].chargeType);
            $("#calculateType1").val(mercobj.rateInfolist[1].calculateType);
            $("#beginCalAmt1").val(mercobj.rateInfolist[1].beginCalAmt);
            $("#rate1").val(mercobj.rateInfolist[1].rate);
            $("#minFee1").val(mercobj.rateInfolist[1].minFee);
            $("#maxFee1").val(mercobj.rateInfolist[1].maxFee);
            $("#expDate1").val(mercobj.rateInfolist[1].expDate);
            $("#effDate1").val(mercobj.rateInfolist[1].effDate);
            $("#beginCalFee1").val(mercobj.rateInfolist[1].beginCalFee);
            $("#calculateMod1").val(mercobj.rateInfolist[1].calculateMod);

        }
        //判断下拉框的选项
        changeSettleType();
    }
    function getData() {
        var capCorgNm = "corg." +  $("#capCorgNm").val()
        capCorgNm = $.i18n.t(capCorgNm);
        var data = {
            "userId": $("#mercId").val(),
            "mercSts": $("#mercSts").val(),
            "mercName": $("#mercNm").val(),
             "mercShortName": $("#mercShortName").val(),
            "comercReg": $("#comercReg").val(),
            "mercTrdCls": $("#mercTrdCls").val(),
            "cprTyp": $("#cprTyp").val(),
            "opnBusDtSr": $("#term").val(),
            "mgtScp": $("#mgtScp").val(),
            "merRegAddr": $("#merRsgaddrS").val(),
            "crpNm": $("#prinNm").val(),
            "crpIdTyp": $("#certTyp").val(),
            "crpIdNo": $("#certNo").val(),
            "diplayNm": $("#mangerNm").val(),
            "mblNo": $("#mngMoblie").val(),
            "loginId": $("#mngAcc").val(),
            "email": $("#mngEmail").val(),
            "refereeMblNo": $("#recMobile").val(),
            "merLvl": $("#merLvl").val(),
            "capCardNo": $("#capCardNo").val(),
            "capCardName": $("#capCardName").val(),
            "capCorgNo": $("#capCorgNm").val(),
            "capCorgNm": capCorgNm,
            "subbranch": $("#subbranch").val(),
            "settleType": $("#settleType").val(),
            "settleCycleType": $("#settleCycleType").val(),
            "drawDays": $("#drawDays").val(),
            "hallSites": $("#hallSites").val(),
            "settleSite": $("#settleSite").val(),
            "settleEffDate": $("#settleEffDate").val(),
            "settleExpDate": $("#settleExpDate").val(),
            "mercLogoPath":  $("#imercLogo").attr("src"),
            "busLicImgPath":  $("#ibusLicImg").attr("src"),
            "certImgPath": $("#icertImg").attr("src"),
            "certImgPathB": $("#icertImgB").attr("src"),
            "cardImgPath": $("#icardImg").attr("src"),
            "cardImgPathB": $("#icardImgB").attr("src"),
            "merPotocolImgPath":  $("#imerPotocolImg").attr("src"),
            "rateInfolist" :[
                {
                    "busType":$("#busType").val(),
                    "chargeType":$("#chargeType").val(),
                    "calculateType":$("#calculateType").val(),
                    "beginCalAmt":$("#beginCalAmt").val(),
                    "rate":$("#rate").val(),
                    "minFee":$("#minFee").val(),
                    "maxFee":$("#maxFee").val(),
                    "expDate":$("#expDate").val(),
                    "effDate":$("#effDate").val(),
                    "beginCalFee":$("#beginCalFee").val(),
                    "calculateMod":$("#calculateMod").val()
                },
                {
                    "busType":$("#busType1").val(),
                    "chargeType":$("#chargeType1").val(),
                    "calculateType":$("#calculateType1").val(),
                    "beginCalAmt":$("#beginCalAmt1").val(),
                    "rate":$("#rate1").val(),
                    "minFee":$("#minFee1").val(),
                    "maxFee":$("#maxFee1").val(),
                    "expDate":$("#expDate1").val(),
                    "effDate":$("#effDate1").val(),
                    "beginCalFee":$("#beginCalFee1").val(),
                    "calculateMod":$("#calculateMod1").val()
                }
            ]
        };
        return data ;
    }
    function clear() {
        $("#imercLogo").attr("src","/img/uploadBut.png");
        $("#ibusLicImg").attr("src","/img/uploadBut.png");
        $("#icertImg").attr("src","/img/uploadBut.png");
        $("#icardImg").attr("src","/img/uploadBut.png");
        $("#icertImgB").attr("src","/img/uploadBut.png");
        $("#icardImgB").attr("src","/img/uploadBut.png");
        $("#imerPotocolImg").attr("src","/img/uploadBut.png");
        $("#mercNm").val("");
        $("#mercShortName").val("");
        $("#comercReg").val("");
        $("#mercTrdCls").val("7395");
        $("#cprTyp").val("01");
        $("#mgtScp").val("");
        $("#merRsgaddr").val("");
        $("#merRsgaddrS").val("baiLin");
        $("#merRsgaddrI").val("");
        $("#fixedaddrBut").val("");
        $("#term").val("");
        $("#prinNm").val("");
        $("#certTyp").val("0");
        $("#certNo").val("");
        $("#mangerNm").val("");
        $("#mngMoblie").val("");
        $("#mngAcc").val("");
        $("#mngEmail").val("");
        $("#recMobile").val("");
        $("#mercLogo").val("");
        $("#busLicImg").val("");
        $("#certImg").val("");
        $("#cardImg").val("");
        $("#merPotocolImg").val("");
        $("#capCardNo").val("");
        $("#capCardName").val("");
        $("#capCorgNm").val("ICBC");
        $("#subbranch").val("");
        $("#settleType").val("auto");
        $("#settleCycleType").val("daily");
        $("#settleEffDate").val();
        $("#settleExpDate").val();
        $("#drawDays").val("");
        $("#settleSite").val("001");
        $("#hallSites").val("no");
        $("#busType").val("1010");
        $("#chargeType").val("single");
        $("#calculateType").val("percent");
        $("#beginCalAmt").val("");
        $("#calculateMod").val("internal");
        $("#calculateMod").attr("disabled",true);
        $("#rate").val("");
        $("#minFee").val("");
        $("#minFee").attr("disabled",false);
        $("#maxFee").val("");
        $("#maxFee").attr("disabled",false);
        $("#expDate").val("");
        $("#effDate").val("");
        $("#busType1").val("0403");
        $("#chargeType1").val("single");
        $("#calculateType1").val("percent");
        $("#calculateMod1").val("internal");
        $("#calculateMod1").attr("disabled",true);
        $("#beginCalAmt1").val("");
        $("#rate1").val("");
        $("#minFee1").val("");
        $("#minFee1").attr("disabled",false);
        $("#maxFee1").val("");
        $("#maxFee1").attr("disabled",false);
        $("#expDate1").val("");
        $("#effDate1").val("");
        $("#mercId").val("");
        $("#merLvl").val("1");

        $("#capCardNo").attr("disabled",false);
        $("#capCardName").attr("disabled",false);
        $("#capCorgNm").attr("disabled",false);
        $("#subbranch").attr("disabled",false);
        $("#settleType").attr("disabled",false);
        $("#settleCycleType").attr("disabled",false);

        mercLogoPath = '' ,busLicImgPath = '' ,certImgPath = '' , cardImgPath = '' ,merPotocolImgPath = '';
    }

    //校验商户输入必须为字母和数字
    function checkMerAcc() {
        var mngAcc = $("#mngAcc").val();
        if(mngAcc.replace(/[a-zA-Z]/,"").length==mngAcc.length) {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkMngAcc"), "error");
            $("#mngAcc").val("");
            return false;
        }
        if(mngAcc.replace(/[0-9]/,"").length==mngAcc.length) {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkMngAcc"), "error");
            $("#mngAcc").val("");
            return false;
        }
        return true;
    }
    //检查数据
    function check() {
        //商户信息校验
        var mercNm = $("#mercNm").val();
        if (mercNm == '') {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkMercNm"), "error");
            return false;
        }
        var mercShortName = $("#mercShortName").val();
        if (mercShortName == '') {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkMercShortName"), "error");
            return false;
        }
        var comercReg = $("#comercReg").val();
        if (comercReg == '') {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkComercReg"), "error");
            return false;
        }
        var prinNm = $("#prinNm").val();
        if (prinNm == '') {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkPrinNm"), "error");
            return false;
        }
        var certNo = $("#certNo").val();
        if (certNo == '') {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkCertNo"), "error");
            return false;
        }
        var mangerNm = $("#mangerNm").val();
        if (mangerNm == '') {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkMangerNm"), "error");
            return false;
        }
        var mngMoblie = $("#mngMoblie").val();
        if (mngMoblie == '') {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkMngMoblie"), "error");
            return false;
        }
        var mngEmail = $("#mngEmail").val();
        if (mngEmail == '') {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkMngEmail"), "error");
            return false;
        }

        //校验商户账号的格式化
        if(!checkMerAcc()) {
            return false;
        }

        //商户结算信息校验
        var settleSite = $("#settleSite").val();
        if (settleSite == '001') {
            var settleType = $("#settleType").val();
            if (settleType == 'hall') {
                swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkSettleType"), "error");
                return false;
            }
            var capCardNo = $("#capCardNo").val();
            if (capCardNo == ''){
                swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkCapCardNo"), "error");
                return false;
            }
            var capCardName = $("#capCardName").val();
            if (capCardName == ''){
                swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkCapCardName"), "error");
                return false;
            }
        }
        var settleEffDate = $("#settleEffDate").val();
        if (settleEffDate == ''){
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkSettleEffDate"), "error");
            return false;
        }
        var settleExpDate = $("#settleExpDate").val();
        if (settleExpDate == ''){
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkSettleExpDate"), "error");
            return false;
        }

        var drawDays = $("#drawDays").val();
        if (drawDays != ''){
            if (drawDays.replace(/[0-9]/g,"").length !=0 ) {
                swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkDrawDays"), "error");
                return false;
            }
        } else {
            //初始化
            $("#drawDays").val("0");
        }
        //费率信息校验
        var rate  = $("#rate").val();
        var rate1  = $("#rate1").val();
        if (rate == '' || rate1 == '') {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkRate"), "error");
            return false;
        }

        var expDate = $("#expDate").val();
        var expDate1 = $("#expDate1").val();
        if (expDate == '' || expDate1 == '') {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkSettleExpDate"), "error");
            return false;
        }
        var effDate = $("#effDate").val();
        var effDate1 = $("#effDate1").val();
        if (effDate == '' || effDate1 == '') {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkSettleEffDate"), "error");
            return false;
        }
        var calculateType = $("#calculateType").val();
        var rate = $("#rate").val();
        if (calculateType == 'percent') {
            if (rate >= 100) {
                swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.rateCheck"), "error");
                $("#rate").val("");
                return false;
            }
        }
        var calculateType = $("#calculateType1").val();
        var rate = $("#rate1").val();
        if (calculateType == 'percent') {
            if (rate >= 100) {
                swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.rateCheck"), "error");
                $("#rate1").val("");
                return false;
            }
        }


        return true;
    }
    var expDate = $('#expDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });
    var effDate = $('#effDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });

    var expDate1 = $('#expDate1').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });
    var effDate1 = $('#effDate1').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });

    var settleEffDate = $('#settleEffDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });
    var settleExpDate = $('#settleExpDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });

    var term = $('#term').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });



</script>
</body>
</html>