<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="nav.csmsub.itemsub.title"></title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>
            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="nav.csmsub.itemsub.iteminf"></h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="nav.csmmgr"></a>
                        </li>
                        <li>
                            <a data-i18n="nav.csmsub.item"></a>
                        </li>
                        <li class="active">
                            <strong data-i18n="nav.csmsub.itemsub.iteminf"></strong>
                        </li>
                    </ol>
                </div>
            </div>
            
            <div class="wrapper wrapper-content animated fadeInRight">
                <!--查询条件-->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content" style="height: 120px;">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12">
                                        <!-- 科目类别-->
                                        <label class="col-sm-2 control-label" for="itmTyp" data-i18n="nav.csmsub.itemsub.infsub.itmTyp"></label>
                                        <div class="col-sm-2">
                                            <select name="itmTyp" id="itmTyp" class="form-control" value="">
	                                            <option value=""   data-i18n="nav.csmsub.itemsub.select"></option>
	                                            <option value="A"  data-i18n="nav.csmsub.itemsub.infsub.itmTypsub.A"></option>
	                                            <option value="L"  data-i18n="nav.csmsub.itemsub.infsub.itmTypsub.L"></option>
	                                            <option value="C"  data-i18n="nav.csmsub.itemsub.infsub.itmTypsub.C"></option>
	                                            <option value="I" data-i18n="nav.csmsub.itemsub.infsub.itmTypsub.I"></option>
	                                            <option value="E" data-i18n="nav.csmsub.itemsub.infsub.itmTypsub.E"></option>
	                                            <option value="O"  data-i18n="nav.csmsub.itemsub.infsub.itmTypsub.O"></option>
	                                            <option value="S" data-i18n="nav.csmsub.itemsub.infsub.itmTypsub.S"></option>
	                                        </select>
                                        </div>
                                        <!-- 科目分类 -->
                                        <label class="col-sm-2 control-label" for="itmCls" style="margin-top: 10px;" data-i18n="nav.csmsub.itemsub.infsub.itmCls"></label>
                                        <div class="col-sm-2">
                                            <select name="itmCls" id="itmCls" class="form-control" value="">
	                                            <option value=""   data-i18n="nav.csmsub.itemsub.select"></option>
	                                            <option value="1"  data-i18n="nav.csmsub.itemsub.infsub.itmClssub.1"></option>
	                                            <option value="2"  data-i18n="nav.csmsub.itemsub.infsub.itmClssub.2"></option>
	                                            <option value="3"  data-i18n="nav.csmsub.itemsub.infsub.itmClssub.3"></option>
	                                        </select>
                                        </div>
                                    </div>
                                    <div class="form-group col-sm-12">
                                    	 <!-- 科目号-->
                                        <label class="col-sm-2 control-label" for="itmNo" data-i18n="nav.csmsub.itemsub.infsub.itmNo"></label>
                                        <div class="col-sm-2">
                                            <input name="itmNo" id="itmNo" class="form-control" value=""/>
                                        </div>
                                        <!-- 科目中文名称-->
                                        <label class="col-sm-2 control-label" for="itmCnm" data-i18n="nav.csmsub.itemsub.infsub.itmCnm"></label>
                                        <div class="col-sm-2">
                                            <input name="itmCnm" id="itmCnm" class="form-control" value=""/>
                                        </div>
                                        <div>
                                            <button type="button" id="searchBtn" class="btn btn-primary" onclick="search()" data-i18n="cmm.search"></button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!--数据表格-->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="itemInf" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="nav.csmsub.itemsub.infsub.itmNo"></th>
                                                <th data-i18n="nav.csmsub.itemsub.infsub.itmCnm"></th>
                                                <th data-i18n="nav.csmsub.itemsub.infsub.itmTyp"></th>
                                                <th data-i18n="nav.csmsub.itemsub.infsub.itmCls"></th>
                                                <th data-i18n="nav.csmsub.itemsub.infsub.itmSts"></th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var editor;
        var table;
        var editorSelect;

        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }
            
            i18nLoad.then(function () {
                editor = new $.fn.dataTable.Editor({
                    ajax: {
                        create: {
                            type: 'POST',
                            url: '/acm/item/iteminf/add',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            },
                            success:function(o){
                                handleErr(o);
                            },
                            error:function(e){

                            }
                        },
                        edit: {
                            type: 'POST',
                            url: '/acm/item/iteminf/modify',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        },
                        remove: {
                            type: 'POST',
                            url: '/acm/item/iteminf/delete',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            },
                            success: function (data) {
                            },
                            error : function (data) {
                            },
                        }
                    },
                    table: "#itemInf",
                    idSrc: "itmNo",
                    fields: [ {
	                        name: "updBalFlg",
	                        type:"hidden"
	                    },{
                    		label: $.i18n.t("nav.csmsub.itemsub.infsub.itmNo"), 
	                        name: "itmNo"
	                    },{
                            label: $.i18n.t("nav.csmsub.itemsub.infsub.itmEnm"), 
                            name: "itmEnm"
                        },{
                        	label: $.i18n.t("nav.csmsub.itemsub.infsub.itmCnm"), 
                            name: "itmCnm"
                        },{
                        	label: $.i18n.t("nav.csmsub.itemsub.infsub.itmLvl"), 
                            name: "itmLvl",
                           	type:"select", 
                            options: [
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmLvlsub.1"), value: "1" },
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmLvlsub.2"), value: "2" },
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmLvlsub.3"), value: "3" }
                            ]
                        },{
                        	label: $.i18n.t("nav.csmsub.itemsub.infsub.upItmNo"), 
                            name: "upItmNo"
                        },{
                        	label: $.i18n.t("nav.csmsub.itemsub.infsub.btmItmFlg"), 
                            name: "btmItmFlg",
                           	type:"select", 
                            options: [
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.btmItmFlgsub.Y"), value: "Y" },
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.btmItmFlgsub.N"), value: "N" }
                            ]
                        },{
                        	label: $.i18n.t("nav.csmsub.itemsub.infsub.itmTyp"), 
                            name: "itmTyp",
                           	type:"select", 
                            options: [
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmTypsub.A"), value: "A" },
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmTypsub.L"), value: "L" },
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmTypsub.C"), value: "C" },
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmTypsub.I"), value: "I" },
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmTypsub.E"), value: "E" },
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmTypsub.O"), value: "O" },
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmTypsub.S"), value: "S" }
                            ]
                        },{
                        	label: $.i18n.t("nav.csmsub.itemsub.infsub.itmCls"), 
                            name: "itmCls",
                           	type:"select", 
                            options: [
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmClssub.1"), value: "1" },
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmClssub.2"), value: "2" },
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmClssub.3"), value: "3" }
                            ]
                        },{
                        	label: $.i18n.t("nav.csmsub.itemsub.infsub.itmZbalFlg"), 
                            name: "itmZbalFlg",
                           	type:"select", 
                            options: [
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmZbalFlgsub.D"), value: "D" },
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmZbalFlgsub.M"), value: "M" },
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmZbalFlgsub.Y"), value: "Y" },
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmZbalFlgsub.N"), value: "N" }
                            ]
                        },{
                        	label: $.i18n.t("nav.csmsub.itemsub.infsub.lpBfFlg"), 
                            name: "lpBfFlg",
                           	type:"select", 
                            options: [
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.lpBfFlgsub.Y"), value: "Y" },
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.lpBfFlgsub.N"), value: "N" }
                            ]
                        },{
                        	label: $.i18n.t("nav.csmsub.itemsub.infsub.balDrt"), 
                            name: "balDrt",
                           	type:"select", 
                            options: [
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.balDrtsub.A"), value: "A" },
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.balDrtsub.B"), value: "B" },
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.balDrtsub.C"), value: "C" },
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.balDrtsub.D"), value: "D" }
                            ]
                        },{
                        	label: $.i18n.t("nav.csmsub.itemsub.infsub.balOdFlg"), 
                            name: "balOdFlg",
                           	type:"select", 
                            options: [
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.balOdFlgsub.Y"), value: "Y" },
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.balOdFlgsub.N"), value: "N" }
                            ]
                        },{
                        	label: $.i18n.t("nav.csmsub.itemsub.infsub.itmSts"), 
                            name: "itmSts",
                           	type:"select", 
                            options: [
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmStssub.0"), value: "0" },
                                { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmStssub.1"), value: "1" }
                            ]
                        },{
                            label: $.i18n.t("nav.csmsub.itemsub.infsub.effDt"),
                            name: "effDt",
                            type: "datetime",
                            def: function () {
                                return new Date();
                            },
                            format: "YYYY-MM-DD"
                        },
                        {
                            label: $.i18n.t("nav.csmsub.itemsub.infsub.expDt"),
                            name: "expDt",
                            type: "datetime",
                            def: function () {
                                return new Date();
                            },
                            format: "YYYY-MM-DD"
                        }
                    ],
                    i18n: {
                        create: {button: $.i18n.t("cmm.button.add"), title: $.i18n.t("cmm.button.add"), submit: $.i18n.t("cmm.button.create")},
                        edit: {button: $.i18n.t("cmm.button.modify"), title: $.i18n.t("cmm.button.modify"), submit: $.i18n.t("cmm.button.update")},
                        remove: {
                            button: $.i18n.t("cmm.button.delete"), title: $.i18n.t("cmm.button.delete"), submit: $.i18n.t("cmm.button.delete"),
                            confirm: {
                                _: $.i18n.t("cmm.button.multi-delete"),
                                1: $.i18n.t("cmm.button.single-delete")
                            }
                        }
                    }
                });
                editorSelect = new $.fn.dataTable.Editor({  
                    i18n : {  
                        edit : {  
                            title : $.i18n.t("nav.csmsub.itemsub.button.detail"),  
                            submit : $.i18n.t("nav.csmsub.itemsub.button.close"),
                           	button: $.i18n.t("nav.csmsub.itemsub.button.query")
                        }  
                    },
                    table : "#itemInf",
                    idSrc: "itmNo",
                    fields : [ {
                        name: "updBalFlg",
                        type:"hidden"
                    },{
                		label: $.i18n.t("nav.csmsub.itemsub.infsub.itmNo"), 
                        name: "itmNo"
                    },{
                        label: $.i18n.t("nav.csmsub.itemsub.infsub.itmEnm"), 
                        name: "itmEnm"
                    },{
                    	label: $.i18n.t("nav.csmsub.itemsub.infsub.itmCnm"), 
                        name: "itmCnm"
                    },{
                    	label: $.i18n.t("nav.csmsub.itemsub.infsub.itmLvl"), 
                        name: "itmLvl",
                       	type:"select", 
                        options: [
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmLvlsub.1"), value: "1" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmLvlsub.2"), value: "2" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmLvlsub.3"), value: "3" }
                        ]
                    },{
                    	label: $.i18n.t("nav.csmsub.itemsub.infsub.upItmNo"), 
                        name: "upItmNo"
                    },{
                    	label: $.i18n.t("nav.csmsub.itemsub.infsub.btmItmFlg"), 
                        name: "btmItmFlg",
                       	type:"select", 
                        options: [
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.btmItmFlgsub.Y"), value: "Y" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.btmItmFlgsub.N"), value: "N" }
                        ]
                    },{
                    	label: $.i18n.t("nav.csmsub.itemsub.infsub.itmTyp"), 
                        name: "itmTyp",
                       	type:"select", 
                        options: [
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmTypsub.A"), value: "A" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmTypsub.L"), value: "L" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmTypsub.C"), value: "C" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmTypsub.I"), value: "I" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmTypsub.E"), value: "E" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmTypsub.O"), value: "O" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmTypsub.S"), value: "S" }
                        ]
                    },{
                    	label: $.i18n.t("nav.csmsub.itemsub.infsub.itmCls"), 
                        name: "itmCls",
                       	type:"select", 
                        options: [
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmClssub.1"), value: "1" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmClssub.2"), value: "2" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmClssub.3"), value: "3" }
                        ]
                    },{
                    	label: $.i18n.t("nav.csmsub.itemsub.infsub.itmZbalFlg"), 
                        name: "itmZbalFlg",
                       	type:"select", 
                        options: [
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmZbalFlgsub.D"), value: "D" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmZbalFlgsub.M"), value: "M" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmZbalFlgsub.Y"), value: "Y" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmZbalFlgsub.N"), value: "N" }
                        ]
                    },{
                    	label: $.i18n.t("nav.csmsub.itemsub.infsub.lpBfFlg"), 
                        name: "lpBfFlg",
                       	type:"select", 
                        options: [
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.lpBfFlgsub.Y"), value: "Y" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.lpBfFlgsub.N"), value: "N" }
                        ]
                    },{
                    	label: $.i18n.t("nav.csmsub.itemsub.infsub.balDrt"), 
                        name: "balDrt",
                       	type:"select", 
                        options: [
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.balDrtsub.A"), value: "A" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.balDrtsub.B"), value: "B" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.balDrtsub.C"), value: "C" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.balDrtsub.D"), value: "D" }
                        ]
                    },{
                    	label: $.i18n.t("nav.csmsub.itemsub.infsub.balOdFlg"), 
                        name: "balOdFlg",
                       	type:"select", 
                        options: [
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.balOdFlgsub.Y"), value: "Y" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.balOdFlgsub.N"), value: "N" }
                        ]
                    },{
                    	label: $.i18n.t("nav.csmsub.itemsub.infsub.itmSts"), 
                        name: "itmSts",
                       	type:"select", 
                        options: [
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmStssub.0"), value: "0" },
                            { label: $.i18n.t("nav.csmsub.itemsub.infsub.itmStssub.1"), value: "1" }
                        ]
                    },{
                        label: $.i18n.t("nav.csmsub.itemsub.infsub.effDt"),
                        name: "effDt",
                        type: "datetime",
                        def: function () {
                            return new Date();
                        },
                        format: "YYYY-MM-DD"
                    },
                    {
                        label: $.i18n.t("nav.csmsub.itemsub.infsub.expDt"),
                        name: "expDt",
                        type: "datetime",
                        def: function () {
                            return new Date();
                        },
                        format: "YYYY-MM-DD"
                    },
                    {
                        label: $.i18n.t("nav.csmsub.itemsub.infsub.updOpr"),
                        name: "updOpr"
                    } ],  
              
                });  
                editorSelect.on( 'open', function ( e, o, action ) {
                		this.field( 'itmNo' ).disable();
                		this.field( 'itmEnm' ).disable();
                		this.field( 'itmCnm' ).disable();
                		this.field( 'itmLvl' ).disable();
                		this.field( 'upItmNo' ).disable();
                		this.field( 'btmItmFlg' ).disable();
                		this.field( 'itmTyp' ).disable();
                		this.field( 'itmCls' ).disable();
                		this.field( 'itmZbalFlg' ).disable();
                		this.field( 'lpBfFlg' ).disable();
                		this.field( 'balDrt' ).disable();
                		this.field( 'balOdFlg' ).disable();
                		this.field( 'itmSts' ).disable();
                		this.field( 'effDt' ).disable();
                		this.field( 'expDt' ).disable();
                		this.field( 'updOpr' ).disable();
                } );
                editor.on( 'open', function ( e, o, action ) {
                	if ( action === 'edit' ) {
                		this.field( 'itmNo' ).disable();
                	}else{
                		this.field( 'itmNo' ).enable();
                	}
            		
            	} );
                editor.field('itmLvl').input().on('change', function (e, d) {
                	var cbk = document.getElementById('DTE_Field_itmLvl');
                	if(cbk.value==='1'){
                		editor.field( 'upItmNo' ).hide(); 
                	}else{
                		editor.field( 'upItmNo' ).show(); 
                	}
                });
                editor.on( 'preSubmit', function ( e, o, action ) {
                    if ( action !== 'remove' ) {
                    	var itmNo=this.field( 'itmNo' );
                    	var itmEnm=this.field( 'itmEnm' );
                    	var itmCnm=this.field( 'itmCnm' );
                    	var effDt=this.field( 'effDt' );
                    	var expDt=this.field( 'expDt' );
                    	var upItmNo=this.field('upItmNo');
                    	var itmLvl=this.field( 'itmLvl' );
                    	if(itmLvl.val()!=='1'){
                    		if ( ! upItmNo.isMultiValue() ) {
                                if ( ! upItmNo.val() ) {
                                	upItmNo.error( $.i18n.t("nav.csmsub.itemsub.message.null.upItmNo") );
                                }
                                if ( upItmNo.val().length>10 ) {
                                	upItmNo.error( $.i18n.t("nav.csmsub.itemsub.message.length.upItmNo") );
                                }
                            }
                    	}
                    	if ( ! itmNo.isMultiValue() ) {
                            if ( ! itmNo.val() ) {
                            	itmNo.error( $.i18n.t("nav.csmsub.itemsub.message.null.itmNo") );
                            }
                            if ( itmNo.val().length>10 ) {
                            	itmNo.error( $.i18n.t("nav.csmsub.itemsub.message.length.itmNo") );
                            }
                        }
                    	if ( ! itmEnm.isMultiValue() ) {
                            if ( ! itmEnm.val() ) {
                            	itmEnm.error( $.i18n.t("nav.csmsub.itemsub.message.null.itmEnm") );
                            }
                        }
                    	if ( ! itmCnm.isMultiValue() ) {
                            if ( ! itmCnm.val() ) {
                            	itmCnm.error( $.i18n.t("nav.csmsub.itemsub.message.null.itmCnm") );
                            }
                        }
                    	if ( ! effDt.isMultiValue() ) {
                            if ( ! effDt.val() ) {
                            	effDt.error( $.i18n.t("nav.csmsub.itemsub.message.null.effDt") );
                            }
                        }
                    	if ( ! expDt.isMultiValue() ) {
                            if ( ! expDt.val() ) {
                            	expDt.error( $.i18n.t("nav.csmsub.itemsub.message.null.expDt") );
                            }
                        }
                    	var tm=Date.parse(effDt.val());
                        var now=Date.parse(expDt.val());
                        if(now<=tm){
                        	effDt.error( $.i18n.t("nav.csmsub.itemsub.message.err.effDt") );
                        	expDt.error( $.i18n.t("nav.csmsub.itemsub.message.err.expDt") );
                        }
                        var su=true;
                        if(itmNo.val()!=''){
                        	su=checkItmNo(itmNo,upItmNo,action);
                        }
                        if ( this.inError()||!su) {
                            return false;
                        }
                    }
                } );
                
                table = $('#itemInf').DataTable({
                    dom: "Blfrtip",
                    ajax: {
                        contentType: 'application/json',
                        url: '/acm/item/iteminf/findAll',
                        type: 'post',
                        data: function (d) {
                            d.extra_search = {
                                "itmTyp" : $("#itmTyp").val(),
                                "itmCls" : $("#itmCls").val(),
                                "itmNo" : $("#itmNo").val(),
                                "itmCnm" : $("#itmCnm").val()
                            };
                            return JSON.stringify(d);
                        }
                    },
                    serverSide: true,
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    select: true,
                    buttons: [
                        {extend: "create", editor: editor},
                        {extend: "edit", editor: editor},
                        {extend: "edit", editor: editorSelect},
                        {extend: "remove", editor: editor},
                    ],
                    columns: [{
                        data: 'itmNo'
                    },{
                        data: 'itmCnm'
                    },{
                        data: 'itmTyp',
                        render: function (data, type, row) {
                            switch (data) {
                                case "A":
                                    return $.i18n.t("nav.csmsub.itemsub.infsub.itmTypsub.A");
                                case "L":
                                    return $.i18n.t("nav.csmsub.itemsub.infsub.itmTypsub.L");
                                case "C":
                                    return $.i18n.t("nav.csmsub.itemsub.infsub.itmTypsub.C");
                                case "I":
                                    return $.i18n.t("nav.csmsub.itemsub.infsub.itmTypsub.I");
                                case "E":
                                    return $.i18n.t("nav.csmsub.itemsub.infsub.itmTypsub.E");
                                case "O":
                                    return $.i18n.t("nav.csmsub.itemsub.infsub.itmTypsub.O");
                                case "S":
                                    return $.i18n.t("nav.csmsub.itemsub.infsub.itmTypsub.S");
                                default:
                                    return data;
                            }
                        }
                    },{
                        data: 'itmCls',
                        render: function (data, type, row) {
                            switch (data) {
                                case "1":
                                    return $.i18n.t("nav.csmsub.itemsub.infsub.itmClssub.1");
                                case "2":
                                    return $.i18n.t("nav.csmsub.itemsub.infsub.itmClssub.2");
                                case "3":
                                    return $.i18n.t("nav.csmsub.itemsub.infsub.itmClssub.3");
                                default:
                                    return data;
                            }
                        }
                    },{
                        data: 'itmSts',
                        render: function (data, type, row) {
                            switch (data) {
                                case "0":
                                    return $.i18n.t("nav.csmsub.itemsub.infsub.itmStssub.0");
                                case "1":
                                    return $.i18n.t("nav.csmsub.itemsub.infsub.itmStssub.1");
                                default:
                                    return data;
                            }
                        }
                    }]
                });
            });
            
            // 初始化省份
        });
      //检查在科目属性对照关系表中是否有相同科目号
        function checkItmNo(itmNo,upItmNo,action){
            var itmNoVal=itmNo.val();
            var upItmNoVal=upItmNo.val();
            var res=true;
            $.ajax({
                url:"/acm/item/iteminf/checkItmNo",
                type:"post",
                async: false,
                data :{
                    "itmNoVal": itmNoVal,
                    "upItmNoVal": upItmNoVal
                },
                success:function(data) {
                    if (data != 'SUCCESS') {
                    	if(data=='ITMINFEXIT'&&action!='edit'){
                    		itmNo.error( $.i18n.t("nav.csmsub.itemsub.message.err.itminfexit") );
                    		res=false;
                    	}
                    	if(data=='UITMINFNOTEXIT'){
                    		upItmNo.error( $.i18n.t("nav.csmsub.itemsub.message.err.uitminfnotexit") );
                    		res=false;
                    	}
                    }
                },
                error: function() {
                    swal($.i18n.t("nav.csmsub.itemsub.message.err.error"),  "error");
                    res=false;
                }
            });
            return res
        };
        <!--查询按钮-->
        function search() {
            table.ajax.reload();
        };
        
        function handleErr(o){
            if(o.code && o.message){
            }
        }
    </script>
</body>
</html>