<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>

    <title data-i18n="nav.csmsub.adjustsub.record"></title>
    <div th:replace="head"></div>
    <style>
        .input-table {
            display: inline-block;
            width: 45%;
        }

        .pb20 {
            padding-bottom: 20px;
        }

        .pl20 {
            padding-left: 20px;
        }
    </style>
</head>

<body>

<div id="wrapper">
    <div th:replace="nav"></div>
    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>
        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="nav.csmsub.adjustsub.record"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.csmmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.csmsub.adjust"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="nav.csmsub.adjustsub.record"></strong>
                    </li>

                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content animated fadeInRight">
            <div class="ibox-content">


                <div class="table-responsive">
                    <table class="table table-striped table-bordered table-hover dataTables-example">
                        <thead>
                        <tr>
                            <th data-i18n="manrecinput.tableTh.number"></th>
                            <th data-i18n="manrecinput.tableTh.acType"></th>
                            <th data-i18n="manrecinput.tableTh.amtType"></th>
                            <th data-i18n="manrecinput.tableTh.acnum"></th>
                            <th></th>
                            <th data-i18n="manrecinput.tableTh.usernm"></th>
                            <th data-i18n="manrecinput.tableTh.realAmt"></th>
                            <th data-i18n="manrecinput.tableTh.loaned"></th>
                            <th data-i18n="manrecinput.tableTh.amt"></th>
                            <th data-i18n="manrecinput.tableTh.notices"></th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td><input type="text" th:value="${details1}? ${details1.seq}"
                                       required="required" th:disabled="${details1}? true"
                                       class=" form-control"></td>
                            <td class="col-sm-2 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details1}? true"
                                        name="searchacType">
                                    <option th:if="${null == details1} or ${details1.acTyp eq 'U'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.payone"></option>
                                    <option th:if="${null != details1} and ${details1.acTyp eq 'I'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.paytwo"></option>
                                </select>
                            </td>
                            <td class="col-sm-2">
                                <select class="form-control" required="required" th:disabled="${details1}? true"
                                        name="searchamtType">
                                    <option th:if="${null == details1} or ${details1.capTyp eq '1'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.cash"></option>
                                    <option th:if="${null != details1} and ${details1.capTyp eq '8'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.beway"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details1}? ${details1.acNo}"
                                       required="required" th:disabled="${details1}? true"
                                       class="form-control"></td>
                            <td>
                                <button type="button" class="btn btn-link" data-i18n="manrecinput.queryBtn" diabled = true></button>
                            </td>
                            <td><input type="text" th:value="${details1}? ${details1.acNm}"
                                       required="required" th:disabled="${details1}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details1}? ${details1.curBal}"
                                       required="required" th:disabled="${details1}? true"
                                       class=" form-control"></td>

                            <td class="col-sm-1 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details1}? true"
                                        name="searchloan">
                                    <option th:if="${null == details1} or ${details1.dcFlg eq 'D'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.borrow"></option>
                                    <option th:if="${null != details1} and ${details1.dcFlg eq 'C'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.lend"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details1}? ${details1.txAmt}"
                                       required="required" th:disabled="${details1}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details1}? ${details1.rmk}"
                                       required="required" th:disabled="${details1}? true"
                                       class=" form-control"></td>
                        </tr>
                        <tr>
                            <td><input type="text" th:value="${details2}? ${details2.seq}"
                                       required="required" th:disabled="${details2}? true"
                                       class=" form-control"></td>
                            <td class="col-sm-2 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details2}? true"
                                        name="searchacType">
                                    <option th:if="${null == details2} or ${details2.acTyp eq 'U'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.payone"></option>
                                    <option th:if="${null != details2} and ${details2.acTyp eq 'I'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.paytwo"></option>
                                </select>
                            </td>
                            <td class="col-sm-2">
                                <select class="form-control" required="required" th:disabled="${details2}? true"
                                        name="searchamtType">
                                    <option th:if="${null == details2} or ${details2.capTyp eq '1'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.cash"></option>
                                    <option th:if="${null != details2} and ${details2.capTyp eq '8'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.beway"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details2}? ${details2.acNo}"
                                       required="required" th:disabled="${details2}? true"
                                       class="form-control"></td>
                            <td>
                                <button type="button" class="btn btn-link" data-i18n="manrecinput.queryBtn" diabled = true></button>
                            </td>
                            <td><input type="text" th:value="${details2}? ${details2.acNm}"
                                       required="required" th:disabled="${details2}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details2}? ${details2.curBal}"
                                       required="required" th:disabled="${details2}? true"
                                       class=" form-control"></td>

                            <td class="col-sm-1 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details2}? true"
                                        name="searchloan">
                                    <option th:if="${null == details2} or ${details2.dcFlg eq 'D'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.borrow"></option>
                                    <option th:if="${null != details2} and ${details2.dcFlg eq 'C'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.lend"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details2}? ${details2.txAmt}"
                                       required="required" th:disabled="${details2}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details2}? ${details2.rmk}"
                                       required="required" th:disabled="${details2}? true"
                                       class=" form-control"></td>
                        </tr>
                        <tr>
                            <td><input type="text" th:value="${details3}? ${details3.seq}"
                                       required="required" th:disabled="${details3}? true"
                                       class=" form-control"></td>
                            <td class="col-sm-2 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details3}? true"
                                        name="searchacType">
                                    <option th:if="${null == details3} or ${details3.acTyp eq 'U'}"
                                            th:selected="selected"
                                            data-i18n="manrecinput.acType.payone"></option>
                                    <option th:if="${null != details3} and ${details3.acTyp eq 'I'}"
                                            th:selected="selected"
                                            data-i18n="manrecinput.acType.paytwo"></option>
                                </select>
                            </td>
                            <td class="col-sm-2">
                                <select class="form-control" required="required" th:disabled="${details3}? true"
                                        name="searchamtType">
                                    <option th:if="${null == details3} or ${details3.capTyp eq '1'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.cash"></option>
                                    <option th:if="${null != details3} and ${details3.capTyp eq '8'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.beway"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details3}? ${details3.acNo}"
                                       required="required" th:disabled="${details3}? true"
                                       class="form-control"></td>
                            <td>
                                <button type="button" class="btn btn-link" data-i18n="manrecinput.queryBtn" diabled = true></button>
                            </td>
                            <td><input type="text" th:value="${details3}? ${details3.acNm}"
                                       required="required" th:disabled="${details3}? true"
                                       class=" form-control"></td>

                            <td><input type="text" th:value="${details3}? ${details3.curBal}"
                                       required="required" th:disabled="${details3}? true"
                                       class=" form-control"></td>
                            <td class="col-sm-1 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details3}? true"
                                        name="searchloan">
                                    <option th:if="${null == details3} or ${details3.dcFlg eq 'D'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.borrow"></option>
                                    <option th:if="${null != details3} and ${details3.dcFlg eq 'C'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.lend"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details3}? ${details3.txAmt}"
                                       required="required" th:disabled="${details3}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details3}? ${details3.rmk}"
                                       required="required" th:disabled="${details3}? true"
                                       class=" form-control"></td>
                        </tr>
                        <tr>
                            <td><input type="text" th:value="${details4}? ${details4.seq}"
                                       required="required" th:disabled="${details4}? true"
                                       class=" form-control"></td>
                            <td class="col-sm-2 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details4}? true"
                                        name="searchacType">
                                    <option th:if="${null == details4} or ${details4.acTyp eq 'U'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.payone"></option>
                                    <option th:if="${null != details4} and ${details4.acTyp eq 'I'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.paytwo"></option>
                                </select>
                            </td>
                            <td class="col-sm-2">
                                <select class="form-control" required="required" th:disabled="${details4}? true"
                                        name="searchamtType">
                                    <option th:if="${null == details4} or ${details4.capTyp eq '1'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.cash"></option>
                                    <option th:if="${null != details4} and ${details4.capTyp eq '8'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.beway"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details4}? ${details4.acNo}"
                                       required="required" th:disabled="${details4}? true"
                                       class="form-control"></td>
                            <td>
                                <button type="button" class="btn btn-link" data-i18n="manrecinput.queryBtn" diabled = true></button>
                            </td>
                            <td><input type="text" class=" form-control"></td>
                            <td><input type="text" th:value="${details4}? ${details4.acNm}"
                                       required="required" th:disabled="${details4}? true"
                                       class=" form-control"></td>

                            <td class="col-sm-1 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details4}? true"
                                        name="searchloan">
                                    <option th:if="${null == details4} or ${details4.dcFlg eq 'D'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.borrow"></option>
                                    <option th:if="${null != details4} and ${details4.dcFlg eq 'C'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.lend"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details4}? ${details4.txAmt}"
                                       required="required" th:disabled="${details4}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details4}? ${details4.rmk}"
                                       required="required" th:disabled="${details4}? true"
                                       class=" form-control"></td>
                        </tr>
                        <tr>
                            <td><input type="text" th:value="${details5}? ${details5.seq}"
                                       required="required" th:disabled="${details5}? true"
                                       class=" form-control"></td>
                            <td class="col-sm-2 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details5}? true"
                                        name="searchacType">
                                    <option th:if="${null == details5} or ${details5.acTyp eq 'U'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.payone"></option>
                                    <option th:if="${null != details5} and ${details5.acTyp eq 'I'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.paytwo"></option>
                                </select>
                            </td>
                            <td class="col-sm-2">
                                <select class="form-control" required="required" th:disabled="${details5}? true"
                                        name="searchamtType">
                                    <option th:if="${null == details5} or ${details5.capTyp eq '1'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.cash"></option>
                                    <option th:if="${null != details5} and ${details5.capTyp eq '8'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.beway"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details5}? ${details5.acNo}"
                                       required="required" th:disabled="${details5}? true"
                                       class="form-control"></td>
                            <td>
                                <button type="button" class="btn btn-link" data-i18n="manrecinput.queryBtn" diabled = true></button>
                            </td>
                            <td><input type="text" th:value="${details5}? ${details5.acNm}"
                                       required="required" th:disabled="${details5}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details5}? ${details5.curBal}"
                                       required="required" th:disabled="${details5}? true"
                                       class=" form-control"></td>

                            <td class="col-sm-1 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details5}? true"
                                        name="searchloan">
                                    <option th:if="${null == details5} or ${details5.dcFlg eq 'D'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.borrow"></option>
                                    <option th:if="${null != details5} and ${details5.dcFlg eq 'C'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.lend"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details5}? ${details5.txAmt}"
                                       required="required" th:disabled="${details5}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details5}? ${details5.rmk}"
                                       required="required" th:disabled="${details5}? true"
                                       class=" form-control"></td>
                        </tr>
                        <tr>
                            <td><input type="text" th:value="${details6}? ${details6.seq}"
                                       required="required" th:disabled="${details6}? true"
                                       class=" form-control"></td>
                            <td class="col-sm-2 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details6}? true"
                                        name="searchacType">
                                    <option th:if="${null == details6} or ${details6.acTyp eq 'U'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.payone"></option>
                                    <option th:if="${null != details6} and ${details6.acTyp eq 'I'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.paytwo"></option>
                                </select>
                            </td>
                            <td class="col-sm-2">
                                <select class="form-control" required="required" th:disabled="${details6}? true"
                                        name="searchamtType">
                                    <option th:if="${null == details6} or ${details6.capTyp eq '1'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.cash"></option>
                                    <option th:if="${null != details6} and ${details6.capTyp eq '8'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.beway"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details6}? ${details6.acNo}"
                                       required="required" th:disabled="${details6}? true"
                                       class="form-control"></td>
                            <td>
                                <button type="button" class="btn btn-link" data-i18n="manrecinput.queryBtn" diabled = true></button>
                            </td>
                            <td><input type="text" th:value="${details6}? ${details6.acNm}"
                                       required="required" th:disabled="${details6}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details6}? ${details6.curBal}"
                                       required="required" th:disabled="${details6}? true"
                                       class=" form-control"></td>


                            <td class="col-sm-1 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details6}? true"
                                        name="searchloan">
                                    <option th:if="${null == details6} or ${details6.dcFlg eq 'D'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.borrow"></option>
                                    <option th:if="${null != details6} and ${details6.dcFlg eq 'C'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.lend"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details6}? ${details6.txAmt}"
                                       required="required" th:disabled="${details6}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details6}? ${details6.rmk}"
                                       required="required" th:disabled="${details6}? true"
                                       class=" form-control"></td>
                        </tr>
                        <tr>
                            <td><input type="text" th:value="${details7}? ${details7.seq}"
                                       required="required" th:disabled="${details7}? true"
                                       class=" form-control"></td>
                            <td class="col-sm-2 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details7}? true"
                                        name="searchacType">
                                    <option th:if="${null == details7} or ${details7.acTyp eq 'U'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.payone"></option>
                                    <option th:if="${null != details7} and ${details7.acTyp eq 'I'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.paytwo"></option>
                                </select>
                            </td>
                            <td class="col-sm-2">
                                <select class="form-control" required="required" th:disabled="${details7}? true"
                                        name="searchamtType">
                                    <option th:if="${null == details7} or ${details7.capTyp eq '1'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.cash"></option>
                                    <option th:if="${null != details7} and ${details7.capTyp eq '8'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.beway"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details7}? ${details7.acNo}"
                                       required="required" th:disabled="${details7}? true"
                                       class="form-control"></td>
                            <td>
                                <button type="button" class="btn btn-link" data-i18n="manrecinput.queryBtn" diabled = true></button>
                            </td>
                            <td><input type="text" th:value="${details7}? ${details7.acNm}"
                                       required="required" th:disabled="${details7}? true"
                                       class=" form-control"></td>

                            <td><input type="text" th:value="${details7}? ${details7.curBal}"
                                       required="required" th:disabled="${details7}? true"
                                       class=" form-control"></td>

                            <td class="col-sm-1 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details7}? true"
                                        name="searchloan">
                                    <option th:if="${null == details7} or ${details7.dcFlg eq 'D'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.borrow"></option>
                                    <option th:if="${null != details7} and ${details7.dcFlg eq 'C'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.lend"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details7}? ${details7.txAmt}"
                                       required="required" th:disabled="${details7}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details7}? ${details7.rmk}"
                                       required="required" th:disabled="${details7}? true"
                                       class=" form-control"></td>
                        </tr>
                        <tr>
                            <td><input type="text" th:value="${details8}? ${details8.seq}"
                                       required="required" th:disabled="${details8}? true"
                                       class=" form-control"></td>
                            <td class="col-sm-2 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details8}? true"
                                        name="searchacType">
                                    <option th:if="${null == details8} or ${details8.acTyp eq 'U'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.payone"></option>
                                    <option th:if="${null != details8} and ${details8.acTyp eq 'I'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.paytwo"></option>
                                </select>
                            </td>
                            <td class="col-sm-2">
                                <select class="form-control" required="required" th:disabled="${details8}? true"
                                        name="searchamtType">
                                    <option th:if="${null == details8} or ${details8.capTyp eq '1'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.cash"></option>
                                    <option th:if="${null != details8} and ${details8.capTyp eq '8'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.beway"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details8}? ${details8.acNo}"
                                       required="required" th:disabled="${details8}? true"
                                       class="form-control"></td>
                            <td>
                                <button type="button" class="btn btn-link" data-i18n="manrecinput.queryBtn" diabled = true></button>
                            </td>
                            <td><input type="text" th:value="${details8}? ${details8.acNm}"
                                       required="required" th:disabled="${details8}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details8}? ${details8.curBal}"
                                       required="required" th:disabled="${details8}? true"
                                       class=" form-control"></td>

                            <td class="col-sm-1 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details8}? true"
                                        name="searchloan">
                                    <option th:if="${null == details8} or ${details8.dcFlg eq 'D'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.borrow"></option>
                                    <option th:if="${null != details8} and ${details8.dcFlg eq 'C'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.lend"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details8}? ${details8.txAmt}"
                                       required="required" th:disabled="${details8}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details8}? ${details8.rmk}"
                                       required="required" th:disabled="${details8}? true"
                                       class=" form-control"></td>
                        </tr>
                        <tr>
                            <td><input type="text" th:value="${details9}? ${details9.seq}"
                                       required="required" th:disabled="${details9}? true"
                                       class=" form-control"></td>
                            <td class="col-sm-2 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details9}? true"
                                        name="searchacType">
                                    <option th:if="${null == details9} or ${details9.acTyp eq 'U'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.payone"></option>
                                    <option th:if="${null != details9} and ${details9.acTyp eq 'I'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.paytwo"></option>
                                </select>
                            </td>
                            <td class="col-sm-2">
                                <select class="form-control" required="required" th:disabled="${details9}? true"
                                        name="searchamtType">
                                    <option th:if="${null == details9} or ${details9.capTyp eq '1'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.cash"></option>
                                    <option th:if="${null != details9} and ${details9.capTyp eq '8'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.beway"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details9}? ${details9.acNo}"
                                       required="required" th:disabled="${details9}? true"
                                       class="form-control"></td>
                            <td>
                                <button type="button" class="btn btn-link" data-i18n="manrecinput.queryBtn" diabled = true></button>
                            </td>
                            <td><input type="text" th:value="${details9}? ${details9.acNm}"
                                       required="required" th:disabled="${details9}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details9}? ${details9.curBal}"
                                       required="required" th:disabled="${details9}? true"
                                       class=" form-control"></td>

                            <td class="col-sm-1 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details9}? true"
                                        name="searchloan">
                                    <option th:if="${null == details9} or ${details9.dcFlg eq 'D'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.borrow"></option>
                                    <option th:if="${null != details9} and ${details9.dcFlg eq 'C'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.lend"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details9}? ${details9.txAmt}"
                                       required="required" th:disabled="${details9}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details9}? ${details9.rmk}"
                                       required="required" th:disabled="${details9}? true"
                                       class=" form-control"></td>
                        </tr>
                        <tr>
                            <td><input type="text" th:value="${details10}? ${details10.seq}"
                                       required="required" th:disabled="${details10}? true"
                                       class=" form-control"></td>
                            <td class="col-sm-2 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details10}? true"
                                        name="searchacType">
                                    <option th:if="${null == details10} or ${null == details10} or ${details10.acTyp eq 'U'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.payone"></option>
                                    <option th:if="${null != details10} and ${details10.acTyp eq 'I'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.paytwo"></option>
                                </select>
                            </td>
                            <td class="col-sm-2">
                                <select class="form-control" required="required" th:disabled="${details10}? true"
                                        name="searchamtType">
                                    <option th:if="${null == details10} or ${details10.capTyp eq '1'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.cash"></option>
                                    <option th:if="${null != details10} and ${details10.capTyp eq '8'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.beway"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details10}? ${details10.acNo}"
                                       required="required" th:disabled="${details10}? true"
                                       class="form-control"></td>
                            <td>
                                <button type="button" class="btn btn-link" data-i18n="manrecinput.queryBtn" diabled = true></button>
                            </td>
                            <td><input type="text" th:value="${details10}? ${details10.acNm}"
                                       required="required" th:disabled="${details10}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details10}? ${details10.curBal}"
                                       required="required" th:disabled="${details10}? true"
                                       class=" form-control"></td>

                            <td class="col-sm-1 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details10}? true"
                                        name="searchloan">
                                    <option th:if="${null == details10} or ${details10.dcFlg eq 'D'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.borrow"></option>
                                    <option th:if="${null != details10} and ${details10.dcFlg eq 'C'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.lend"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details10}? ${details10.txAmt}"
                                       required="required" th:disabled="${details10}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details10}? ${details10.rmk}"
                                       required="required" th:disabled="${details10}? true"
                                       class=" form-control"></td>
                        </tr>
                        <tr>
                            <td><input type="text" th:value="${details11}? ${details11.seq}"
                                       required="required" th:disabled="${details11}? true"
                                       class=" form-control"></td>
                            <td class="col-sm-2 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details11}? true"
                                        name="searchacType">
                                    <option th:if="${null == details11} or ${details11.acTyp eq 'U'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.payone"></option>
                                    <option th:if="${null != details11} and ${details11.acTyp eq 'I'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.paytwo"></option>
                                </select>
                            </td>
                            <td class="col-sm-2">
                                <select class="form-control" required="required" th:disabled="${details11}? true"
                                        name="searchamtType">
                                    <option th:if="${null == details11} or ${details11.capTyp eq '1'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.cash"></option>
                                    <option th:if="${null != details11} and ${details11.capTyp eq '8'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.beway"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details11}? ${details11.acNo}"
                                       required="required" th:disabled="${details11}? true"
                                       class="form-control"></td>
                            <td>
                                <button type="button" class="btn btn-link" data-i18n="manrecinput.queryBtn" diabled = true></button>
                            </td>
                            <td><input type="text" th:value="${details11}? ${details11.acNm}"
                                       required="required" th:disabled="${details11}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details11}? ${details11.curBal}"
                                       required="required" th:disabled="${details11}? true"
                                       class=" form-control"></td>

                            <td class="col-sm-1 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details11}? true"
                                        name="searchloan">
                                    <option th:if="${null == details11} or ${details11.dcFlg eq 'D'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.borrow"></option>
                                    <option th:if="${null != details11} and ${details11.dcFlg eq 'C'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.lend"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details11}? ${details11.txAmt}"
                                       required="required" th:disabled="${details11}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details11}? ${details11.rmk}"
                                       required="required" th:disabled="${details11}? true"
                                       class=" form-control"></td>
                        </tr>
                        <tr>
                            <td><input type="text" th:value="${details12}? ${details12.seq}"
                                       required="required" th:disabled="${details12}? true"
                                       class=" form-control"></td>
                            <td class="col-sm-2 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details12}? true"
                                        name="searchacType">
                                    <option th:if="${null == details12} or ${details12.acTyp eq 'U'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.payone"></option>
                                    <option th:if="${null != details12} and ${details12.acTyp eq 'I'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.paytwo"></option>
                                </select>
                            </td>
                            <td class="col-sm-2">
                                <select class="form-control" required="required" th:disabled="${details12}? true"
                                        name="searchamtType">
                                    <option th:if="${null == details12} or ${details12.capTyp eq '1'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.cash"></option>
                                    <option th:if="${null != details12} and ${details12.capTyp eq '8'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.beway"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details12}? ${details12.acNo}"
                                       required="required" th:disabled="${details12}? true"
                                       class="form-control"></td>
                            <td>
                                <button type="button" class="btn btn-link" data-i18n="manrecinput.queryBtn" diabled = true></button>
                            </td>
                            <td><input type="text" th:value="${details12}? ${details12.acNm}"
                                       required="required" th:disabled="${details12}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details12}? ${details12.curBal}"
                                       required="required" th:disabled="${details12}? true"
                                       class=" form-control"></td>


                            <td class="col-sm-1 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details12}? true"
                                        name="searchloan">
                                    <option th:if="${null == details12} or ${details12.dcFlg eq 'D'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.borrow"></option>
                                    <option th:if="${null != details12} and ${details12.dcFlg eq 'C'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.lend"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details12}? ${details12.txAmt}"
                                       required="required" th:disabled="${details12}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details12}? ${details12.rmk}"
                                       required="required" th:disabled="${details12}? true"
                                       class=" form-control"></td>
                        </tr>
                        <tr>
                            <td><input type="text" th:value="${details13}? ${details13.seq}"
                                       required="required" th:disabled="${details13}? true"
                                       class=" form-control"></td>
                            <td class="col-sm-2 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details13}? true"
                                        name="searchacType">
                                    <option th:if="${null == details13} or ${details13.acTyp eq 'U'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.payone"></option>
                                    <option th:if="${null != details13} and ${details13.acTyp eq 'I'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.paytwo"></option>
                                </select>
                            </td>
                            <td class="col-sm-2">
                                <select class="form-control" required="required" th:disabled="${details13}? true"
                                        name="searchamtType">
                                    <option th:if="${null == details13} or ${details13.capTyp eq '1'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.cash"></option>
                                    <option th:if="${null != details13} and ${details13.capTyp eq '8'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.beway"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details13}? ${details13.acNo}"
                                       required="required" th:disabled="${details13}? true"
                                       class="form-control"></td>
                            <td>
                                <button type="button" class="btn btn-link" data-i18n="manrecinput.queryBtn" diabled = true></button>
                            </td>
                            <td><input type="text" th:value="${details13}? ${details13.acNm}"
                                       required="required" th:disabled="${details13}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details13}? ${details13.curBal}"
                                       required="required" th:disabled="${details13}? true"
                                       class=" form-control"></td>

                            <td class="col-sm-1 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details13}? true"
                                        name="searchloan">
                                    <option th:if="${null == details13} or ${details13.dcFlg eq 'D'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.borrow"></option>
                                    <option th:if="${null != details13} and ${details13.dcFlg eq 'C'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.lend"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details13}? ${details13.txAmt}"
                                       required="required" th:disabled="${details13}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details13}? ${details13.rmk}"
                                       required="required" th:disabled="${details13}? true"
                                       class=" form-control"></td>
                        </tr>
                        <tr>
                            <td><input type="text" th:value="${details14}? ${details14.seq}"
                                       required="required" th:disabled="${details14}? true"
                                       class=" form-control"></td>
                            <td class="col-sm-2 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details14}? true"
                                        name="searchacType">
                                    <option th:if="${null == details14} or ${details14.acTyp eq 'U'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.payone"></option>
                                    <option th:if="${null != details14} and ${details14.acTyp eq 'I'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.paytwo"></option>
                                </select>
                            </td>
                            <td class="col-sm-2">
                                <select class="form-control" required="required" th:disabled="${details14}? true"
                                        name="searchamtType">
                                    <option th:if="${null == details14} or ${details14.capTyp eq '1'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.cash"></option>
                                    <option th:if="${null != details14} and ${details14.capTyp eq '8'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.beway"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details14}? ${details14.acNo}"
                                       required="required" th:disabled="${details14}? true"
                                       class="form-control"></td>
                            <td>
                                <button type="button" class="btn btn-link" data-i18n="manrecinput.queryBtn" diabled = true></button>
                            </td>
                            <td><input type="text" th:value="${details14}? ${details14.acNm}"
                                       required="required" th:disabled="${details14}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details14}? ${details14.curBal}"
                                       required="required" th:disabled="${details14}? true"
                                       class=" form-control"></td>

                            <td class="col-sm-1 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details14}? true"
                                        name="searchloan">
                                    <option th:if="${null == details14} or ${details14.dcFlg eq 'D'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.borrow"></option>
                                    <option th:if="${null != details14} and ${details14.dcFlg eq 'C'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.lend"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details14}? ${details14.txAmt}"
                                       required="required" th:disabled="${details14}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details14}? ${details14.rmk}"
                                       required="required" th:disabled="${details14}? true"
                                       class=" form-control"></td>
                        </tr>
                        <tr>
                            <td><input type="text" th:value="${details15}? ${details15.seq}"
                                       required="required" th:disabled="${details15}? true"
                                       class=" form-control"></td>
                            <td class="col-sm-2 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details15}? true"
                                        name="searchacType">
                                    <option th:if="${null == details15} or ${details15.acTyp eq 'U'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.payone"></option>
                                    <option th:if="${null != details15} and ${details15.acTyp eq 'I'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.paytwo"></option>
                                </select>
                            </td>
                            <td class="col-sm-2">
                                <select class="form-control" required="required" th:disabled="${details15}? true"
                                        name="searchamtType">
                                    <option th:if="${null == details15} or ${details15.capTyp eq '1'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.cash"></option>
                                    <option th:if="${null != details15} and ${details15.capTyp eq '8'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.beway"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details15}? ${details15.acNo}"
                                       required="required" th:disabled="${details15}? true"
                                       class="form-control"></td>
                            <td>
                                <button type="button" class="btn btn-link" data-i18n="manrecinput.queryBtn" diabled = true></button>
                            </td>
                            <td><input type="text" th:value="${details15}? ${details15.acNm}"
                                       required="required" th:disabled="${details15}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details15}? ${details15.curBal}"
                                       required="required" th:disabled="${details15}? true"
                                       class=" form-control"></td>

                            <td class="col-sm-1 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details15}? true"
                                        name="searchloan">
                                    <option th:if="${null == details15} or ${details15.dcFlg eq 'D'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.borrow"></option>
                                    <option th:if="${null != details15} and ${details15.dcFlg eq 'C'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.lend"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details15}? ${details15.txAmt}"
                                       required="required" th:disabled="${details15}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details15}? ${details15.rmk}"
                                       required="required" th:disabled="${details15}? true"
                                       class=" form-control"></td>
                        </tr>
                        <tr>
                            <td><input type="text" th:value="${details16}? ${details16.seq}"
                                       required="required" th:disabled="${details16}? true"
                                       class=" form-control"></td>
                            <td class="col-sm-2 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details16}? true"
                                        name="searchacType">
                                    <option th:if="${null == details16} or ${details16.acTyp eq 'U'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.payone"></option>
                                    <option th:if="${null != details16} and ${details16.acTyp eq 'I'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.paytwo"></option>
                                </select>
                            </td>
                            <td class="col-sm-2">
                                <select class="form-control" required="required" th:disabled="${details16}? true"
                                        name="searchamtType">
                                    <option th:if="${null == details16} or ${details16.capTyp eq '1'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.cash"></option>
                                    <option th:if="${null != details16} and ${details16.capTyp eq '8'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.beway"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details16}? ${details16.acNo}"
                                       required="required" th:disabled="${details16}? true"
                                       class="form-control"></td>
                            <td>
                                <button type="button" class="btn btn-link" data-i18n="manrecinput.queryBtn" diabled = true></button>
                            </td>
                            <td><input type="text" th:value="${details16}? ${details16.acNm}"
                                       required="required" th:disabled="${details16}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details16}? ${details16.curBal}"
                                       required="required" th:disabled="${details16}? true"
                                       class=" form-control"></td>

                            <td class="col-sm-1 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details16}? true"
                                        name="searchloan">
                                    <option th:if="${null == details16} or ${details16.dcFlg eq 'D'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.borrow"></option>
                                    <option th:if="${null != details16} and ${details16.dcFlg eq 'C'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.lend"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details16}? ${details16.txAmt}"
                                       required="required" th:disabled="${details16}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details16}? ${details16.rmk}"
                                       required="required" th:disabled="${details16}? true"
                                       class=" form-control"></td>
                        </tr>
                        <tr>
                            <td><input type="text" th:value="${details17}? ${details17.seq}"
                                       required="required" th:disabled="${details17}? true"
                                       class=" form-control"></td>
                            <td class="col-sm-2 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details17}? true"
                                        name="searchacType">
                                    <option th:if="${null == details17} or ${details17.acTyp eq 'U'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.payone"></option>
                                    <option th:if="${null != details17} and ${details17.acTyp eq 'I'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.paytwo"></option>
                                </select>
                            </td>
                            <td class="col-sm-2">
                                <select class="form-control" required="required" th:disabled="${details17}? true"
                                        name="searchamtType">
                                    <option th:if="${null == details17} or ${details17.capTyp eq '1'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.cash"></option>
                                    <option th:if="${null != details17} and ${details17.capTyp eq '8'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.beway"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details17}? ${details17.acNo}"
                                       required="required" th:disabled="${details17}? true"
                                       class="form-control"></td>
                            <td>
                                <button type="button" class="btn btn-link" data-i18n="manrecinput.queryBtn" diabled = true></button>
                            </td>
                            <td><input type="text" th:value="${details17}? ${details17.acNm}"
                                       required="required" th:disabled="${details17}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details17}? ${details17.curBal}"
                                       required="required" th:disabled="${details17}? true"
                                       class=" form-control"></td>

                            <td class="col-sm-1 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details17}? true"
                                        name="searchloan">
                                    <option th:if="${null == details17} or ${details17.dcFlg eq 'D'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.borrow"></option>
                                    <option th:if="${null != details17} and ${details17.dcFlg eq 'C'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.lend"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details17}? ${details17.txAmt}"
                                       required="required" th:disabled="${details17}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details17}? ${details17.rmk}"
                                       required="required" th:disabled="${details17}? true"
                                       class=" form-control"></td>
                        </tr>
                        <tr>
                            <td><input type="text" th:value="${details18}? ${details18.seq}"
                                       required="required" th:disabled="${details18}? true"
                                       class=" form-control"></td>
                            <td class="col-sm-2 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details18}? true"
                                        name="searchacType">
                                    <option th:if="${null == details18} or ${details18.acTyp eq 'U'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.payone"></option>
                                    <option th:if="${null != details18} and ${details18.acTyp eq 'I'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.paytwo"></option>
                                </select>
                            </td>
                            <td class="col-sm-2">
                                <select class="form-control" required="required" th:disabled="${details18}? true"
                                        name="searchamtType">
                                    <option th:if="${null == details18} or ${details18.capTyp eq '1'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.cash"></option>
                                    <option th:if="${null != details18} and ${details18.capTyp eq '8'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.beway"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details18}? ${details18.acNo}"
                                       required="required" th:disabled="${details18}? true"
                                       class="form-control"></td>
                            <td>
                                <button type="button" class="btn btn-link" data-i18n="manrecinput.queryBtn" diabled = true></button>
                            </td>
                            <td><input type="text" th:value="${details18}? ${details18.acNm}"
                                       required="required" th:disabled="${details18}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details18}? ${details18.curBal}"
                                       required="required" th:disabled="${details18}? true"
                                       class=" form-control"></td>

                            <td class="col-sm-1 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details18}? true"
                                        name="searchloan">
                                    <option th:if="${null == details18} or ${details18.dcFlg eq 'D'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.borrow"></option>
                                    <option th:if="${null != details18} and ${details18.dcFlg eq 'C'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.lend"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details18}? ${details18.txAmt}"
                                       required="required" th:disabled="${details18}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details18}? ${details18.rmk}"
                                       required="required" th:disabled="${details18}? true"
                                       class=" form-control"></td>
                        </tr>
                        <tr>
                            <td><input type="text" th:value="${details19}? ${details19.seq}"
                                       required="required" th:disabled="${details19}? true"
                                       class=" form-control"></td>
                            <td class="col-sm-2 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details19}? true"
                                        name="searchacType">
                                    <option th:if="${null == details19} or ${details19.acTyp eq 'U'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.payone"></option>
                                    <option th:if="${null != details19} and ${details19.acTyp eq 'I'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.paytwo"></option>
                                </select>
                            </td>
                            <td class="col-sm-2">
                                <select class="form-control" required="required" th:disabled="${details19}? true"
                                        name="searchamtType">
                                    <option th:if="${null == details19} or ${details19.capTyp eq '1'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.cash"></option>
                                    <option th:if="${null != details19} and ${details19.capTyp eq '8'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.beway"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details19}? ${details19.acNo}"
                                       required="required" th:disabled="${details19}? true"
                                       class="form-control"></td>
                            <td>
                                <button type="button" class="btn btn-link" data-i18n="manrecinput.queryBtn" diabled = true></button>
                            </td>
                            <td><input type="text" th:value="${details19}? ${details19.acNm}"
                                       required="required" th:disabled="${details19}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details19}? ${details19.curBal}"
                                       required="required" th:disabled="${details19}? true"
                                       class=" form-control"></td>

                            <td class="col-sm-1 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details19}? true"
                                        name="searchloan">
                                    <option th:if="${null == details19} or ${details19.dcFlg eq 'D'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.borrow"></option>
                                    <option th:if="${null != details19} and ${details19.dcFlg eq 'C'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.lend"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details19}? ${details19.txAmt}"
                                       required="required" th:disabled="${details19}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details19}? ${details19.rmk}"
                                       required="required" th:disabled="${details19}? true"
                                       class=" form-control"></td>
                        </tr>
                        <tr>
                            <td><input type="text" th:value="${details20}? ${details20.seq}"
                                       required="required" th:disabled="${details20}? true"
                                       class=" form-control"></td>
                            <td class="col-sm-2 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details20}? true"
                                        name="searchacType">
                                    <option th:if="${null == details20} or ${details20.acTyp eq 'U'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.payone"></option>
                                    <option th:if="${null != details20} and ${details20.acTyp eq 'I'}" th:selected="selected"
                                            data-i18n="manrecinput.acType.paytwo"></option>
                                </select>
                            </td>
                            <td class="col-sm-2">
                                <select class="form-control" required="required" th:disabled="${details20}? true"
                                        name="searchamtType">
                                    <option th:if="${null == details20} or ${details20.capTyp eq '1'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.cash"></option>
                                    <option th:if="${null != details20} and ${details20.capTyp eq '8'}" th:selected="selected"
                                            data-i18n="manrecinput.amtType.beway"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details20}? ${details20.acNo}"
                                       required="required" th:disabled="${details20}? true"
                                       class="form-control"></td>
                            <td>
                                <button type="button" class="btn btn-link" data-i18n="manrecinput.queryBtn" diabled = true></button>
                            </td>
                            <td><input type="text" th:value="${details20}? ${details20.acNm}"
                                       required="required" th:disabled="${details20}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details20}? ${details20.curBal}"
                                       required="required" th:disabled="${details20}? true"
                                       class=" form-control"></td>

                            <td class="col-sm-1 m-b-xs">
                                <select class="form-control" required="required" th:disabled="${details20}? true"
                                        name="searchloan">
                                    <option th:if="${null == details20} or ${details20.dcFlg eq 'D'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.borrow"></option>
                                    <option th:if="${null != details20} and ${details20.dcFlg eq 'C'}" th:selected="selected"
                                            data-i18n="manrecinput.loaned.lend"></option>
                                </select>
                            </td>
                            <td><input type="text" th:value="${details20}? ${details20.txAmt}"
                                       required="required" th:disabled="${details20}? true"
                                       class=" form-control"></td>
                            <td><input type="text" th:value="${details20}? ${details20.rmk}"
                                       required="required" th:disabled="${details20}? true"
                                       class=" form-control"></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="row pb20 pl20">
                    <button type="button" class="btn btn-primary" onclick="returnButtun()"
                            data-i18n="manrecinput.submitBtn">返回
                    </button>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>
    </div>
</div>
<div th:replace="script"></div>
<script>
</script>
</body>
</html>