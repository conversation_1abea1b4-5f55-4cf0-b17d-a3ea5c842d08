<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4"
      xmlns="http://www.w3.org/1999/html">

<head>

    <title data-i18n="nav.csmsub.adjustsub.query.title"></title>

    <div th:replace="head"></div>
    <style>
        .input-table{
            display: inline-block;
            width: 45%;
        }
        .pb20{
            padding-bottom: 20px;
        }
        .pl20{
            padding-left: 20px;
        }
    </style>
</head>

<body>
<div id="wrapper">
    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="nav.csmsub.adjustsub.query.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.csmmgr"></a>
                    </li>
                    <li class="active">
                        <a href="/acm/adjust/audit" data-i18n="nav.csmsub.adjust"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="csm.recorddetail"></strong>
                    </li>
                </ol>
            </div>
        </div>

        <div class="wrapper wrapper-content animated fadeInRight">
            <div class="ibox-content">
                <div class="row">
                    <div class="col-sm-4">
                        <span class="col-search-1 control-label" data-i18n="csm.regOpr"></span>
                        <input type="text" class="searchInput" name="regOpr" id="regOpr">
                    </div>
                    <div class="col-sm-6">
                        <span class="col-search-1 control-label" data-i18n="csm.regDt"></span>
                        <input type="text" class="searchInput" name="regDtS" id="regDtS">-
                        <input type="text" class="searchInput" name="regDtE" id="regDtE">
                    </div>
                </div>

                <div class="row">
                    <div class="col-sm-4">
                        <span class="col-search-1 control-label" data-i18n="csm.dateType"></span>
                        <select class="form-control input-table" id="dtType">
                            <option value="acDt" data-i18n="csm.dateTypeVal.acDt"></option>
                            <option value="regDt" data-i18n="csm.dateTypeVal.regDt"></option>
                        </select>
                    </div>
                    <div class="col-sm-4">
                        <span class="col-search-1 control-label" data-i18n="csm.auditSts"></span>
                        <select class="form-control input-table" id="auditSts">
                            <option value="1" data-i18n="csm.auditStsVal.one"></option>
                            <option value="0" data-i18n="csm.auditStsVal.two"></option>
                            <option value="2" data-i18n="csm.auditStsVal.three"></option>
                        </select>
                    </div>
                    <div class="col-sm-2">
                        <div class="col-sm-4 col-sm-offset-1">
                            <button type="button" id="query" class="btn btn-primary" data-i18n="csm.adjustquery"></button>
                        </div>
                    </div>
                </div>
            
                <div class="table-responsive">
                    <div class="table-responsive">
                        <table id="dataTables" class="table table-striped table-bordered table-hover ">
                            <thead>
                            <tr>
                                <th data-i18n="csm.adjustinf.regOpr"></th>
                                <th data-i18n="csm.adjustinf.jrnNo"></th>
                                <th data-i18n="csm.adjustinf.regDt"></th>
                                <th data-i18n="csm.adjustinf.acDt"></th>
                                <th data-i18n="csm.adjustinf.totTxAmt"></th>
                                <th data-i18n="csm.adjustinf.totTxNum"></th>
                                <th data-i18n="csm.adjustinf.rmk"></th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>
    </div>
</div>
<div th:replace="script"></div>
<script type="text/javascript" th:inline="javascript">
    var table;
    $(document).ready(function () {
        var languageUrl;

        switch ($.cookie('i18next')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'kh':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        var audited;
        audited = window.location.search.split("=")[1];
        i18nLoad.then(function () {
            table = $('#dataTables').DataTable({
                ajax: {
                    contentType: 'application/json',
                    url: '/acm/adjust/audit/findAll',
                    type: 'POST',
                    data: function (d) {
                        var regOpr = $("#regOpr").val();
                        var dtType = $("#dtType").val();
                        var regDtS = $("#regDtS").val();
                        var regDtE = $("#regDtE").val();
                        var auditSts = $('#auditSts').val() ? $('#auditSts').val() : audited;

                        d.extra_search = {
                            "regOpr": regOpr,
                            "regDtS": regDtS,
                            "regDtE": regDtE,
                            "dtType": dtType,
                            "auditSts" : auditSts
                        };
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                responsive: true,
                processing: true,
                searching: false,
                language: {
                    url: languageUrl
                },
                columns: [{
                    data: 'regOpr'
                }, {
                    data: 'jrnNo'
                }, {
                    data: 'regDt'
                }, {
                    data: 'acDt'
                },{
                    data: 'totTxAmt'
                }, {
                    data: 'totTxNum'
                }, {
                    data: 'rmk'
                }
                ]
            });
        });
    });
    $("#query").click(function () {
        table.ajax.reload();
    })
    /**初始化日期控件**/
    var beginTimePick = $('#regDtS').datetimepicker({
        lang: 'en',
        timepicker: false,
        validateOnBlur: false,
        format: 'Y-m-d ',
        formatDate: 'Y-m-d',
        onChangeDate: function (dateText, inst) {
            endTimePick.datetimepicker('minDate', new Date(dateText.replace("-", "-")));
        }
    });

    var endTimePick = $('#regDtE').datetimepicker({
        lang: 'en',
        timepicker: false,
        validateOnBlur: false,
        format: 'Y-m-d ',
        formatDate: 'Y-m-d',
        // maxDate:'+1970/01/01',
    });

    function ajaxClick(name, type, id) {
        var url = $("span[name='" + name + "'][id='" + id + "']").attr("data");
        if (name == "lock") {
            $.ajax({
                type: type,
                url: url,
                success: function (data) {
                    if (data) {
                        alert(data);
                    } else {
                        window.location.reload();
                    }
                }
            })
        } else if (name == "trash") {
            swal({
                title: $.i18n.t("user.swal-title"),
                text: "",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: $.i18n.t("user.swal-confirm"),
                cancelButtonText: $.i18n.t("user.swal-cancel"),
                closeOnConfirm: false
            }, function (isConfirm) {
                if (!isConfirm) return;
                $.ajax({
                    url: url,
                    type: type,
                    success: function () {
                        swal($.i18n.t("user.swal-sucess"), "", "success");
                        $('.confirm').click(function () {   //额外绑定一个事件，当确定执行之后返回成功的页面的确定按钮，点击之后刷新当前页面或者跳转其他页面
                            location.reload();
                        });
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        swal($.i18n.t("user.swal-error"), $.i18n.t("user.swal-error-tips"), "error");
                    }
                });
            });
        }
    }
</script>
</body>
</html>