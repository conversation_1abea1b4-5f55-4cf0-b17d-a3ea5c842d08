<div class="modal inmodal" id="handleModal" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content animated">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span
                        class="sr-only">Close</span></button>
                <h4 class="modal-title" data-i18n="csm.audit"></h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal" id="auditForm">


                    <div class="form-group" id='auditDiv'>
                        <label class="col-sm-4 control-label" for="auditSts" data-i18n="csm.auditSts"></label>
                        <div class="col-sm-6">
                            <select type="" class="form-control" name="auditSts"
                                    required="required" maxlength="02" id='auditSts'>
                                <option value='1' data-i18n="csm.audited"></option>
                                <option value='2' data-i18n="csm.rejected"></option>
                            </select>
                        </div>
                    </div>
                    <input id="jrnNo" name="jrnNo" value="" type="hidden"/><!--调账流水号-->

                    <div class="form-group">

                        <div class="form-group">
                            <label class="col-sm-4 control-label" for="rmk" data-i18n="csm.rmk"></label>
                            <div class="col-sm-6">
                                <textarea class="form-control" name="rmk" id="rmk"> </textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="audit" class="btn btn-white" data-i18n="csm.confirm"></button>
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>