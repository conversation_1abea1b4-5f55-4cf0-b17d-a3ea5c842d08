<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4"
      xmlns="http://www.w3.org/1999/html">

<head>

    <title data-i18n="nav.csmsub.adjustsub.auditsub.title"></title>

    <div th:replace="head"></div>
</head>

<body>
<div id="wrapper">
    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="nav.csmsub.adjustsub.audit"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.csmmgr"></a>
                    </li>
                    <li class="active">
                        <a href="/acm/adjust/audit" data-i18n="nav.csmsub.adjust"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="nav.csmsub.adjustsub.audit"></strong>
                    </li>
                </ol>
            </div>
        </div>


        <!--调账审核蒙版-->
        <div th:replace="acm/adjust/audit/auditModal"></div>

        <div class="wrapper wrapper-content animated fadeInRight">
            <div class="ibox-content">
                <div class="box-header">
                    <!-- 表头搜索栏 -->
                    <table>
                        <tr>
                            <!--操作员编号-->
                            <td align="center">
                                <label class="control-label" data-i18n="csm.regOpr"></label>
                            </td>
                            <td>&nbsp; </td>
                            <td>
                                <input type="text" class="searchInput" name="regOpr" id="regOpr">
                            </td>
                            <td>&nbsp; &nbsp; &nbsp; </td>

                            <!--日期-->
                            <td align="center">
                                <label class="control-label" data-i18n="csm.regDt"></label>
                            </td>
                            <td>&nbsp;</td>
                            <td>
                                <input type="text" class="searchInput" name="regDtS" id="regDtS">
                            </td>
                            <td >-</td>
                            <td>
                                <input type="text" class="searchInput" name="regDtE" id="regDtE">
                            </td>
                            <td>&nbsp; &nbsp; &nbsp; </td>

                            <!--提交按钮-->
                            <td>
                                <button type="button" id="query" class="btn btn-primary" data-i18n="csm.adjustquery"></button>
                            </td>
                        </tr>
                    </table>

                </div>
                <hr/>
                <div class="row">
                    <div class="box-header">
                        <div class="col-sm-4">
                                <button type="button" id="modifyBtn" class="btn btn-default" data-toggle="modal"
                                        data-i18n="csm.adjustaudit"></button>
                                <button type="button" id="queryBtn" class="btn btn-default" data-toggle="modal"
                                        data-i18n="csm.look"></button>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table id="dataTables" class="table table-striped table-bordered table-hover ">
                        <thead>
                        <tr>
                            <th data-i18n="csm.adjustinf.select"></th>
                            <th data-i18n="csm.adjustinf.regOpr"></th>
                            <th data-i18n="csm.adjustinf.jrnNo"></th>
                            <th data-i18n="csm.adjustinf.regDt"></th>
                            <th data-i18n="csm.adjustinf.totTxAmt"></th>
                            <th data-i18n="csm.adjustinf.totTxNum"></th>
                            <th data-i18n="csm.adjustinf.rmk"></th>
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>
    </div>
</div>
<div th:replace="script"></div>
<script type="text/javascript" th:inline="javascript">
    var table;
    $(document).ready(function () {
        var languageUrl;

        switch ($.cookie('i18next')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'kh':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        var audited;
        i18nLoad.then(function () {
            table = $('#dataTables').DataTable({
                ajax: {
                    contentType: 'application/json',
                    url: '/acm/adjust/audit/findAll',
                    type: 'POST',
                    data: function (d) {
                        var regOpr = $("#regOpr").val();
                        var regDtS = $("#regDtS").val();
                        var regDtE = $("#regDtE").val();

                        d.extra_search = {
                            "regOpr": regOpr,
                            "regDtS": regDtS,
                            "regDtE": regDtE,
                        };
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                responsive: true,
                processing: true,
                searching: false,
                language: {
                    url: languageUrl
                },
                columns: [
                {
                    data: 'regOpr',
                    render: function (data, type, row) {
                            return '<input type="radio" value="" name="accountNo" />';
                    }
                },
                {
                    data: 'regOpr'
                },
                {
                    data: 'jrnNo'
                }, {
                    data: 'regDt'
                }, {
                    data: 'totTxAmt'
                }, {
                    data: 'totTxNum'
                }, {
                    data: 'rmk'
                }
                ]
            });
        });
    });
    $("#query").click(function () {
        table.ajax.reload();
    })
    /**初始化日期控件**/
    var beginTimePick = $('#regDtS').datetimepicker({
        lang: 'en',
        timepicker: false,
        validateOnBlur: false,
        format: 'Y-m-d ',
        formatDate: 'Y-m-d',
        onChangeDate: function (dateText, inst) {
            endTimePick.datetimepicker('minDate', new Date(dateText.replace("-", "-")));
        }
    });

    var endTimePick = $('#regDtE').datetimepicker({
        lang: 'en',
        timepicker: false,
        validateOnBlur: false,
        format: 'Y-m-d ',
        formatDate: 'Y-m-d',
        // maxDate:'+1970/01/01',
    });

    function ajaxClick(name, type, id) {
        var url = $("span[name='" + name + "'][id='" + id + "']").attr("data");
        if (name == "lock") {
            $.ajax({
                type: type,
                url: url,
                success: function (data) {
                    if (data) {
                        alert(data);
                    } else {
                        window.location.reload();
                    }
                }
            })
        } else if (name == "trash") {
            swal({
                title: $.i18n.t("user.swal-title"),
                text: "",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: $.i18n.t("user.swal-confirm"),
                cancelButtonText: $.i18n.t("user.swal-cancel"),
                closeOnConfirm: false
            }, function (isConfirm) {
                if (!isConfirm) return;
                $.ajax({
                    url: url,
                    type: type,
                    success: function () {
                        swal($.i18n.t("user.swal-sucess"), "", "success");
                        $('.confirm').click(function () {   //额外绑定一个事件，当确定执行之后返回成功的页面的确定按钮，点击之后刷新当前页面或者跳转其他页面
                            location.reload();
                        });
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        swal($.i18n.t("user.swal-error"), $.i18n.t("user.swal-error-tips"), "error");
                    }
                });
            });
        }
    }

    //审核
    $("#modifyBtn").click(function() {
        var row = 0;
        $("input[name='accountNo']:checked").each(function(){
            row++;
        });
        if(row == 0) {
            swal($.i18n.t("csm.fail"), $.i18n.t("csm.shouldSelectOne"), "error");
            return;
        }
        $("#handleModal").modal("show");
        var jrnNo = $("input[name='accountNo']:checked").parent().parent().find('td').eq(2).text();
        var param = {
            "jrnNo": jrnNo,
            "auditSts": $('#auditSts').val(),
            "rmk": $('#rmk').val()
        };


    });

    // ajax异步调起后台服务
    $("#audit").click(function () {
        var jrnNo = $("input[name='accountNo']:checked").parent().parent().find('td').eq(2).text();
        var param = {
            "jrnNo": jrnNo,
            "auditSts": $('#auditSts').val(),
            "rmk": $('#rmk').val()
        };
        $.ajax({
            url: "/acm/adjust/audit",
            data: JSON.stringify(param),
            dataType: "json",
            contentType: 'application/json',
            type: "PUT",
            success: function (data) {
                if (data.msgCd.substring(3) != '00000') {
                    $("#handleModal").modal("hide");
                    swal($.i18n.t("csm.fail"), data.msgCd + ":" + data.msgInfo, "error");
                } else {
                    $("#handleModal").modal("hide");
                    swal($.i18n.t("csm.success"));
                    table.ajax.reload();
                }

            },
            error: function () {
                // swal($.i18n.t("csm.fail") , "error");
            }
        });
    })

    //查看
    $("#queryBtn").click(function() {
        var row = 0;
        $("input[name='accountNo']:checked").each(function(){
            row++;
        });
        if(row == 0) {
            swal($.i18n.t("csm.fail"), $.i18n.t("csm.shouldSelectOne"), "error");
            return;
        }
        var jrnNo = $("input[name='accountNo']:checked").parent().parent().find('td').eq(2).text();
        var param = {
            "jrnNo": jrnNo,
        };

        window.location.href ="/acm/adjust/record/findAll?jrnNo="+jrnNo;

        // ajax异步调起后台服务
        /*$.ajax({
            url: "/acm/adjust/record/findAll",
            data:JSON.stringify(param),
            dataType: "json",
            contentType : 'application/json',
            type:"GET",
            success:function(data){
                var result = data.result;
                if (result !='ACM00000') {
                    swal($.i18n.t("csm.fail"), result , "error");
                } else {
                    swal($.i18n.t("csm.success"));
                    table.ajax.reload();
                }

            },
            error: function(){
                // swal($.i18n.t("csm.fail") , "error");
            }
        });*/
    })
</script>
</body>
</html>