<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

    <head>

        <title data-i18n="nav.csmsub.adjustsub.title"></title>
        <div th:replace="head"></div>
        <style>
            .input-table{
                display: inline-block;
                width: 45%;
            }
            .pb20{
                padding-bottom: 20px;
            }
            .pl20{
                padding-left: 20px;
            }
        </style>
    </head>

    <body>

        <div id="wrapper" >
            <div th:replace="nav"></div>
            <div id="page-wrapper" class="gray-bg">
                <div th:replace="top"></div>
                <div class="row wrapper border-bottom white-bg page-heading">
                    <div class="col-lg-10">
                        <h2 data-i18n="nav.csmsub.adjustsub.record"></h2>
                        <ol class="breadcrumb">
                            <li>
                                <a data-i18n="nav.csmmgr"></a>
                            </li>
                            <li>
                                <a data-i18n="nav.csmsub.adjust"></a>
                            </li>
                            <li class="active">
                                <strong data-i18n="nav.csmsub.adjustsub.record"></strong>
                            </li>

                        </ol>
                    </div>
                </div>
                <div class="wrapper wrapper-content animated fadeInRight">
                    <div class="ibox-content">
                        <div class="row pb20 pl20">
                            <button type="button" class="btn btn-default" onclick="submitButtun()" data-i18n="manrecinput.submitBtn"></button>
                        </div>
                        <div class="row">
                           
                            <div class="col-sm-3">
                                <label data-i18n="manrecinput.allBwAmt"></label>
                                <!-- <label >总借记金额：</label> -->
                                <input id="allBwAmt" type="text" readonly="readonly" class="input-s-sm form-control input-table">
                            </div>

                            <div class="col-sm-3">
                                <label data-i18n="manrecinput.allloanAmt"></label><input id="allloanAmt" type="text" readonly class="input-s-sm form-control input-table">
                            </div>

                            <div class="col-sm-2">
                                <label data-i18n="manrecinput.allSum"></label><input id="countSum" type="text" readonly class="input-s-sm form-control input-table">
                            </div>

                            <div class="col-sm-2">
                                <label data-i18n="manrecinput.digest"></label>
                                <input id="digest" type="text"  class="input-s-sm form-control input-table">
                            </div>
                            
                            <div class="col-sm-1 m-b-xs">
                                <button type="button" class="btn btn-default" onclick="checkPay()" data-i18n="manrecinput.checkAmt"></button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered table-hover dataTables-example">
                                <thead>
                                    <tr>
                                        <th data-i18n="manrecinput.tableTh.number"></th>
                                        <th data-i18n="manrecinput.tableTh.acType"> </th>
                                        <th data-i18n="manrecinput.tableTh.amtType"> </th>
                                        <th data-i18n="manrecinput.tableTh.acnum"></th>
                                        <th></th>
                                        <th data-i18n="manrecinput.tableTh.usernm"></th>
                                        <th data-i18n="manrecinput.tableTh.realAmt"></th>
                                        <th data-i18n="manrecinput.tableTh.loaned"></th>
                                        <th data-i18n="manrecinput.tableTh.amt"></th>
                                        <th data-i18n="manrecinput.tableTh.notices"></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td ><input type="text" class=" form-control"></td>
                                        <td class="col-sm-2 m-b-xs">
                                            <select class="form-control" name="searchacType">
                                                <option value="U" data-i18n="manrecinput.acType.payone"></option>
                                                <option value="I" data-i18n="manrecinput.acType.paytwo"></option>
                                            </select>
                                        </td>
                                        <td class="col-sm-2">
                                            <select class="form-control" name="searchamtType">
                                                <option value="1" data-i18n="manrecinput.amtType.cash"></option>
                                                <option value="8" data-i18n="manrecinput.amtType.beway"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" class="form-control"></td>
                                        <td><button type="button" onclick="searchButton(this)" class="btn btn-link" data-i18n="manrecinput.queryBtn"></button></td>
                                        <td><input type="text" class=" form-control"></td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        
                                        <td class="col-sm-1 m-b-xs">
                                            <select class="form-control" name="searchloan">
                                                <option value="D" data-i18n="manrecinput.loaned.borrow"></option>
                                                <option value="C" data-i18n="manrecinput.loaned.lend"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        <td><input type="text" class=" form-control"></td>
                                    </tr>
                                    <tr>
                                        <td ><input type="text" class=" form-control"></td>
                                        <td class="col-sm-2 m-b-xs">
                                            <select class="form-control" name="searchacType">
                                                <option value="U" data-i18n="manrecinput.acType.payone"></option>
                                                <option value="I" data-i18n="manrecinput.acType.paytwo"></option>
                                            </select>
                                        </td>
                                        <td class="col-sm-2">
                                            <select class="form-control" name="searchamtType">
                                                <option value="1" data-i18n="manrecinput.amtType.cash"></option>
                                                <option value="8" data-i18n="manrecinput.amtType.beway"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" class="form-control"></td>
                                        <td><button type="button" onclick="searchButton(this)" class="btn btn-link" data-i18n="manrecinput.queryBtn"></button></td>
                                        <td><input type="text" class=" form-control"></td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        
                                        <td class="col-sm-1 m-b-xs">
                                            <select class="form-control" name="searchloan">
                                                <option value="D" data-i18n="manrecinput.loaned.borrow"></option>
                                                <option value="C" data-i18n="manrecinput.loaned.lend"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        <td><input type="text" class=" form-control"></td>
                                    </tr>
                                    <tr>
                                        <td ><input type="text" class=" form-control"></td>
                                        <td class="col-sm-2 m-b-xs">
                                            <select class="form-control" name="searchacType">
                                                <option value="U" data-i18n="manrecinput.acType.payone"></option>
                                                <option value="I" data-i18n="manrecinput.acType.paytwo"></option>
                                            </select>
                                        </td>
                                        <td class="col-sm-2">
                                            <select class="form-control" name="searchamtType">
                                                <option value="1" data-i18n="manrecinput.amtType.cash"></option>
                                                <option value="8" data-i18n="manrecinput.amtType.beway"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" class="form-control"></td>
                                        <td><button type="button" onclick="searchButton(this)" class="btn btn-link" data-i18n="manrecinput.queryBtn"></button></td>
                                        <td><input type="text" class=" form-control"></td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        
                                        <td class="col-sm-1 m-b-xs">
                                            <select class="form-control" name="searchloan">
                                                <option value="D" data-i18n="manrecinput.loaned.borrow"></option>
                                                <option value="C" data-i18n="manrecinput.loaned.lend"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        <td><input type="text" class=" form-control"></td>
                                    </tr>
                                    <tr>
                                        <td ><input type="text" class=" form-control"></td>
                                        <td class="col-sm-2 m-b-xs">
                                            <select class="form-control" name="searchacType">
                                                <option value="U" data-i18n="manrecinput.acType.payone"></option>
                                                <option value="I" data-i18n="manrecinput.acType.paytwo"></option>
                                            </select>
                                        </td>
                                        <td class="col-sm-2">
                                            <select class="form-control" name="searchamtType">
                                                <option value="1" data-i18n="manrecinput.amtType.cash"></option>
                                                <option value="8" data-i18n="manrecinput.amtType.beway"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" class="form-control"></td>
                                        <td><button type="button" onclick="searchButton(this)" class="btn btn-link" data-i18n="manrecinput.queryBtn"></button></td>
                                        <td><input type="text" class=" form-control"></td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        
                                        <td class="col-sm-1 m-b-xs">
                                            <select class="form-control" name="searchloan">
                                                <option value="D" data-i18n="manrecinput.loaned.borrow"></option>
                                                <option value="C" data-i18n="manrecinput.loaned.lend"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        <td><input type="text" class=" form-control"></td>
                                    </tr>
                                    <tr>
                                        <td ><input type="text" class=" form-control"></td>
                                        <td class="col-sm-2 m-b-xs">
                                            <select class="form-control" name="searchacType">
                                                <option value="U" data-i18n="manrecinput.acType.payone"></option>
                                                <option value="I" data-i18n="manrecinput.acType.paytwo"></option>
                                            </select>
                                        </td>
                                        <td class="col-sm-2">
                                            <select class="form-control" name="searchamtType">
                                                <option value="1" data-i18n="manrecinput.amtType.cash"></option>
                                                <option value="8" data-i18n="manrecinput.amtType.beway"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" class="form-control"></td>
                                        <td><button type="button" onclick="searchButton(this)" class="btn btn-link" data-i18n="manrecinput.queryBtn"></button></td>
                                        <td><input type="text" class=" form-control"></td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        
                                        <td class="col-sm-1 m-b-xs">
                                            <select class="form-control" name="searchloan">
                                                <option value="D" data-i18n="manrecinput.loaned.borrow"></option>
                                                <option value="C" data-i18n="manrecinput.loaned.lend"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        <td><input type="text" class=" form-control"></td>
                                    </tr>
                                    <tr>
                                        <td ><input type="text" class=" form-control"></td>
                                        <td class="col-sm-2 m-b-xs">
                                            <select class="form-control" name="searchacType">
                                                <option value="U" data-i18n="manrecinput.acType.payone"></option>
                                                <option value="I" data-i18n="manrecinput.acType.paytwo"></option>
                                            </select>
                                        </td>
                                        <td class="col-sm-2">
                                            <select class="form-control" name="searchamtType">
                                                <option value="1" data-i18n="manrecinput.amtType.cash"></option>
                                                <option value="8" data-i18n="manrecinput.amtType.beway"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" class="form-control"></td>
                                        <td><button type="button" onclick="searchButton(this)" class="btn btn-link" data-i18n="manrecinput.queryBtn"></button></td>
                                        <td><input type="text" class=" form-control"></td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        
                                        <td class="col-sm-1 m-b-xs">
                                            <select class="form-control" name="searchloan">
                                                <option value="D" data-i18n="manrecinput.loaned.borrow"></option>
                                                <option value="C" data-i18n="manrecinput.loaned.lend"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        <td><input type="text" class=" form-control"></td>
                                    </tr>
                                    <tr>
                                        <td ><input type="text" class=" form-control"></td>
                                        <td class="col-sm-2 m-b-xs">
                                            <select class="form-control" name="searchacType">
                                                <option value="U" data-i18n="manrecinput.acType.payone"></option>
                                                <option value="I" data-i18n="manrecinput.acType.paytwo"></option>
                                            </select>
                                        </td>
                                        <td class="col-sm-2">
                                            <select class="form-control" name="searchamtType">
                                                <option value="1" data-i18n="manrecinput.amtType.cash"></option>
                                                <option value="8" data-i18n="manrecinput.amtType.beway"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" class="form-control"></td>
                                        <td><button type="button" onclick="searchButton(this)" class="btn btn-link" data-i18n="manrecinput.queryBtn"></button></td>
                                        <td><input type="text" class=" form-control"></td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        
                                        <td class="col-sm-1 m-b-xs">
                                            <select class="form-control" name="searchloan">
                                                <option value="D" data-i18n="manrecinput.loaned.borrow"></option>
                                                <option value="C" data-i18n="manrecinput.loaned.lend"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        <td><input type="text" class=" form-control"></td>
                                    </tr>
                                    <tr>
                                        <td ><input type="text" class=" form-control"></td>
                                        <td class="col-sm-2 m-b-xs">
                                            <select class="form-control" name="searchacType">
                                                <option value="U" data-i18n="manrecinput.acType.payone"></option>
                                                <option value="I" data-i18n="manrecinput.acType.paytwo"></option>
                                            </select>
                                        </td>
                                        <td class="col-sm-2">
                                            <select class="form-control" name="searchamtType">
                                                <option value="1" data-i18n="manrecinput.amtType.cash"></option>
                                                <option value="8" data-i18n="manrecinput.amtType.beway"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" class="form-control"></td>
                                        <td><button type="button" onclick="searchButton(this)" class="btn btn-link" data-i18n="manrecinput.queryBtn"></button></td>
                                        <td><input type="text" class=" form-control"></td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        
                                        <td class="col-sm-1 m-b-xs">
                                            <select class="form-control" name="searchloan">
                                                <option value="D" data-i18n="manrecinput.loaned.borrow"></option>
                                                <option value="C" data-i18n="manrecinput.loaned.lend"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        <td><input type="text" class=" form-control"></td>
                                    </tr>
                                    <tr>
                                        <td ><input type="text" class=" form-control"></td>
                                        <td class="col-sm-2 m-b-xs">
                                            <select class="form-control" name="searchacType">
                                                <option value="U" data-i18n="manrecinput.acType.payone"></option>
                                                <option value="I" data-i18n="manrecinput.acType.paytwo"></option>
                                            </select>
                                        </td>
                                        <td class="col-sm-2">
                                            <select class="form-control" name="searchamtType">
                                                <option value="1" data-i18n="manrecinput.amtType.cash"></option>
                                                <option value="8" data-i18n="manrecinput.amtType.beway"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" class="form-control"></td>
                                        <td><button type="button" onclick="searchButton(this)" class="btn btn-link" data-i18n="manrecinput.queryBtn"></button></td>
                                        <td><input type="text" class=" form-control"></td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        
                                        <td class="col-sm-1 m-b-xs">
                                            <select class="form-control" name="searchloan">
                                                <option value="D" data-i18n="manrecinput.loaned.borrow"></option>
                                                <option value="C" data-i18n="manrecinput.loaned.lend"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        <td><input type="text" class=" form-control"></td>
                                    </tr>
                                    <tr>
                                        <td ><input type="text" class=" form-control"></td>
                                        <td class="col-sm-2 m-b-xs">
                                            <select class="form-control" name="searchacType">
                                                <option value="U" data-i18n="manrecinput.acType.payone"></option>
                                                <option value="I" data-i18n="manrecinput.acType.paytwo"></option>
                                            </select>
                                        </td>
                                        <td class="col-sm-2">
                                            <select class="form-control" name="searchamtType">
                                                <option value="1" data-i18n="manrecinput.amtType.cash"></option>
                                                <option value="8" data-i18n="manrecinput.amtType.beway"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" class="form-control"></td>
                                        <td><button type="button" onclick="searchButton(this)" class="btn btn-link" data-i18n="manrecinput.queryBtn"></button></td>
                                        <td><input type="text" class=" form-control"></td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        
                                        <td class="col-sm-1 m-b-xs">
                                            <select class="form-control" name="searchloan">
                                                <option value="D" data-i18n="manrecinput.loaned.borrow"></option>
                                                <option value="C" data-i18n="manrecinput.loaned.lend"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        <td><input type="text" class=" form-control"></td>
                                    </tr>
                                    <tr>
                                        <td ><input type="text" class=" form-control"></td>
                                        <td class="col-sm-2 m-b-xs">
                                            <select class="form-control" name="searchacType">
                                                <option value="U" data-i18n="manrecinput.acType.payone"></option>
                                                <option value="I" data-i18n="manrecinput.acType.paytwo"></option>
                                            </select>
                                        </td>
                                        <td class="col-sm-2">
                                            <select class="form-control" name="searchamtType">
                                                <option value="1" data-i18n="manrecinput.amtType.cash"></option>
                                                <option value="8" data-i18n="manrecinput.amtType.beway"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" class="form-control"></td>
                                        <td><button type="button" onclick="searchButton(this)" class="btn btn-link" data-i18n="manrecinput.queryBtn"></button></td>
                                        <td><input type="text" class=" form-control"></td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        
                                        <td class="col-sm-1 m-b-xs">
                                            <select class="form-control" name="searchloan">
                                                <option value="D" data-i18n="manrecinput.loaned.borrow"></option>
                                                <option value="C" data-i18n="manrecinput.loaned.lend"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        <td><input type="text" class=" form-control"></td>
                                    </tr>
                                    <tr>
                                        <td ><input type="text" class=" form-control"></td>
                                        <td class="col-sm-2 m-b-xs">
                                            <select class="form-control" name="searchacType">
                                                <option value="U" data-i18n="manrecinput.acType.payone"></option>
                                                <option value="I" data-i18n="manrecinput.acType.paytwo"></option>
                                            </select>
                                        </td>
                                        <td class="col-sm-2">
                                            <select class="form-control" name="searchamtType">
                                                <option value="1" data-i18n="manrecinput.amtType.cash"></option>
                                                <option value="8" data-i18n="manrecinput.amtType.beway"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" class="form-control"></td>
                                        <td><button type="button" onclick="searchButton(this)" class="btn btn-link" data-i18n="manrecinput.queryBtn"></button></td>
                                        <td><input type="text" class=" form-control"></td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        
                                        <td class="col-sm-1 m-b-xs">
                                            <select class="form-control" name="searchloan">
                                                <option value="D" data-i18n="manrecinput.loaned.borrow"></option>
                                                <option value="C" data-i18n="manrecinput.loaned.lend"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        <td><input type="text" class=" form-control"></td>
                                    </tr>
                                    <tr>
                                        <td ><input type="text" class=" form-control"></td>
                                        <td class="col-sm-2 m-b-xs">
                                            <select class="form-control" name="searchacType">
                                                <option value="U" data-i18n="manrecinput.acType.payone"></option>
                                                <option value="I" data-i18n="manrecinput.acType.paytwo"></option>
                                            </select>
                                        </td>
                                        <td class="col-sm-2">
                                            <select class="form-control" name="searchamtType">
                                                <option value="1" data-i18n="manrecinput.amtType.cash"></option>
                                                <option value="8" data-i18n="manrecinput.amtType.beway"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" class="form-control"></td>
                                        <td><button type="button" onclick="searchButton(this)" class="btn btn-link" data-i18n="manrecinput.queryBtn"></button></td>
                                        <td><input type="text" class=" form-control"></td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        
                                        <td class="col-sm-1 m-b-xs">
                                            <select class="form-control" name="searchloan">
                                                <option value="D" data-i18n="manrecinput.loaned.borrow"></option>
                                                <option value="C" data-i18n="manrecinput.loaned.lend"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        <td><input type="text" class=" form-control"></td>
                                    </tr>
                                    <tr>
                                        <td ><input type="text" class=" form-control"></td>
                                        <td class="col-sm-2 m-b-xs">
                                            <select class="form-control" name="searchacType">
                                                <option value="U" data-i18n="manrecinput.acType.payone"></option>
                                                <option value="I" data-i18n="manrecinput.acType.paytwo"></option>
                                            </select>
                                        </td>
                                        <td class="col-sm-2">
                                            <select class="form-control" name="searchamtType">
                                                <option value="1" data-i18n="manrecinput.amtType.cash"></option>
                                                <option value="8" data-i18n="manrecinput.amtType.beway"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" class="form-control"></td>
                                        <td><button type="button" onclick="searchButton(this)" class="btn btn-link" data-i18n="manrecinput.queryBtn"></button></td>
                                        <td><input type="text" class=" form-control"></td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        
                                        <td class="col-sm-1 m-b-xs">
                                            <select class="form-control" name="searchloan">
                                                <option value="D" data-i18n="manrecinput.loaned.borrow"></option>
                                                <option value="C" data-i18n="manrecinput.loaned.lend"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        <td><input type="text" class=" form-control"></td>
                                    </tr>
                                    <tr>
                                        <td ><input type="text" class=" form-control"></td>
                                        <td class="col-sm-2 m-b-xs">
                                            <select class="form-control" name="searchacType">
                                                <option value="U" data-i18n="manrecinput.acType.payone"></option>
                                                <option value="I" data-i18n="manrecinput.acType.paytwo"></option>
                                            </select>
                                        </td>
                                        <td class="col-sm-2">
                                            <select class="form-control" name="searchamtType">
                                                <option value="1" data-i18n="manrecinput.amtType.cash"></option>
                                                <option value="8" data-i18n="manrecinput.amtType.beway"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" class="form-control"></td>
                                        <td><button type="button" onclick="searchButton(this)" class="btn btn-link" data-i18n="manrecinput.queryBtn"></button></td>
                                        <td><input type="text" class=" form-control"></td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        
                                        <td class="col-sm-1 m-b-xs">
                                            <select class="form-control" name="searchloan">
                                                <option value="D" data-i18n="manrecinput.loaned.borrow"></option>
                                                <option value="C" data-i18n="manrecinput.loaned.lend"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        <td><input type="text" class=" form-control"></td>
                                    </tr>
                                    <tr>
                                        <td ><input type="text" class=" form-control"></td>
                                        <td class="col-sm-2 m-b-xs">
                                            <select class="form-control" name="searchacType">
                                                <option value="U" data-i18n="manrecinput.acType.payone"></option>
                                                <option value="I" data-i18n="manrecinput.acType.paytwo"></option>
                                            </select>
                                        </td>
                                        <td class="col-sm-2">
                                            <select class="form-control" name="searchamtType">
                                                <option value="1" data-i18n="manrecinput.amtType.cash"></option>
                                                <option value="8" data-i18n="manrecinput.amtType.beway"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" class="form-control"></td>
                                        <td><button type="button" onclick="searchButton(this)" class="btn btn-link" data-i18n="manrecinput.queryBtn"></button></td>
                                        <td><input type="text" class=" form-control"></td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        
                                        <td class="col-sm-1 m-b-xs">
                                            <select class="form-control" name="searchloan">
                                                <option value="D" data-i18n="manrecinput.loaned.borrow"></option>
                                                <option value="C" data-i18n="manrecinput.loaned.lend"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        <td><input type="text" class=" form-control"></td>
                                    </tr>
                                    <tr>
                                        <td ><input type="text" class=" form-control"></td>
                                        <td class="col-sm-2 m-b-xs">
                                            <select class="form-control" name="searchacType">
                                                <option value="U" data-i18n="manrecinput.acType.payone"></option>
                                                <option value="I" data-i18n="manrecinput.acType.paytwo"></option>
                                            </select>
                                        </td>
                                        <td class="col-sm-2">
                                            <select class="form-control" name="searchamtType">
                                                <option value="1" data-i18n="manrecinput.amtType.cash"></option>
                                                <option value="8" data-i18n="manrecinput.amtType.beway"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" class="form-control"></td>
                                        <td><button type="button" onclick="searchButton(this)" class="btn btn-link" data-i18n="manrecinput.queryBtn"></button></td>
                                        <td><input type="text" class=" form-control"></td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        
                                        <td class="col-sm-1 m-b-xs">
                                            <select class="form-control" name="searchloan">
                                                <option value="D" data-i18n="manrecinput.loaned.borrow"></option>
                                                <option value="C" data-i18n="manrecinput.loaned.lend"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        <td><input type="text" class=" form-control"></td>
                                    </tr>
                                    <tr>
                                        <td ><input type="text" class=" form-control"></td>
                                        <td class="col-sm-2 m-b-xs">
                                            <select class="form-control" name="searchacType">
                                                <option value="U" data-i18n="manrecinput.acType.payone"></option>
                                                <option value="I" data-i18n="manrecinput.acType.paytwo"></option>
                                            </select>
                                        </td>
                                        <td class="col-sm-2">
                                            <select class="form-control" name="searchamtType">
                                                <option value="1" data-i18n="manrecinput.amtType.cash"></option>
                                                <option value="8" data-i18n="manrecinput.amtType.beway"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" class="form-control"></td>
                                        <td><button type="button" onclick="searchButton(this)" class="btn btn-link" data-i18n="manrecinput.queryBtn"></button></td>
                                        <td><input type="text" class=" form-control"></td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        
                                        <td class="col-sm-1 m-b-xs">
                                            <select class="form-control" name="searchloan">
                                                <option value="D" data-i18n="manrecinput.loaned.borrow"></option>
                                                <option value="C" data-i18n="manrecinput.loaned.lend"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        <td><input type="text" class=" form-control"></td>
                                    </tr>
                                    <tr>
                                        <td ><input type="text" class=" form-control"></td>
                                        <td class="col-sm-2 m-b-xs">
                                            <select class="form-control" name="searchacType">
                                                <option value="U" data-i18n="manrecinput.acType.payone"></option>
                                                <option value="I" data-i18n="manrecinput.acType.paytwo"></option>
                                            </select>
                                        </td>
                                        <td class="col-sm-2">
                                            <select class="form-control" name="searchamtType">
                                                <option value="1" data-i18n="manrecinput.amtType.cash"></option>
                                                <option value="8" data-i18n="manrecinput.amtType.beway"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" class="form-control"></td>
                                        <td><button type="button" onclick="searchButton(this)" class="btn btn-link" data-i18n="manrecinput.queryBtn"></button></td>
                                        <td><input type="text" class=" form-control"></td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        
                                        <td class="col-sm-1 m-b-xs">
                                            <select class="form-control" name="searchloan">
                                                <option value="D" data-i18n="manrecinput.loaned.borrow"></option>
                                                <option value="C" data-i18n="manrecinput.loaned.lend"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        <td><input type="text" class=" form-control"></td>
                                    </tr>
                                    <tr>
                                        <td ><input type="text" class=" form-control"></td>
                                        <td class="col-sm-2 m-b-xs">
                                            <select class="form-control" name="searchacType">
                                                <option value="U" data-i18n="manrecinput.acType.payone"></option>
                                                <option value="I" data-i18n="manrecinput.acType.paytwo"></option>
                                            </select>
                                        </td>
                                        <td class="col-sm-2">
                                            <select class="form-control" name="searchamtType">
                                                <option value="1" data-i18n="manrecinput.amtType.cash"></option>
                                                <option value="8" data-i18n="manrecinput.amtType.beway"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" class="form-control"></td>
                                        <td><button type="button" onclick="searchButton(this)" class="btn btn-link" data-i18n="manrecinput.queryBtn"></button></td>
                                        <td><input type="text" class=" form-control"></td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        
                                        <td class="col-sm-1 m-b-xs">
                                            <select class="form-control" name="searchloan">
                                                <option value="D" data-i18n="manrecinput.loaned.borrow"></option>
                                                <option value="C" data-i18n="manrecinput.loaned.lend"></option>
                                            </select>
                                        </td>
                                        <td><input type="text" onblur="compareAmt(this)" class=" form-control"></td>
                                        <td><input type="text" class=" form-control"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
                <div th:replace="footer"></div>
            </div>
        </div>
        <div th:replace="script"></div>
       <script>
          
            function submitButtun(){
                checkPay();
                // 提交按钮
                var recData=[]; // 取页面的值提交
                $("tbody tr").each(function(i, v) {
                    var recval = {
                        seq:$.trim($(v).find("td").eq(0).find("input").val()), // 套号
                        acTyp:$.trim($(v).find("td").eq(1).find("select").val()), // 账户类型
                        capTyp:$.trim($(v).find("td").eq(2).find("select").val()), // 资金类型  账户资金属性
                        acNo:$.trim($(v).find("td").eq(3).find("input").val()), // 账号
                        acNm:$.trim($(v).find("td").eq(5).find("input").val()), // 户名
                        curBal:$.trim($(v).find("td").eq(6).find("input").val()), // 余额
                        dcFlg:$.trim($(v).find("td").eq(7).find("select").val()), // 借贷标志
                        txAmt:$.trim($(v).find("td").eq(8).find("input").val()), // 交易金额
                        rmk:$.trim($(v).find("td").eq(9).find("input").val()) // 备注
                    }; 
                    if($.trim($(v).find("td").eq(8).find("input").val()) != ''){
                        recData.push(recval);
                    }
                    
                });
               
                console.log(recData);
                var paramObj = {
                    'recData' : recData
                };
                $.ajax({
                    type : 'POST',
                    url : '/acm/adjust/record',
                    dataType : 'json',
                    contentType : 'application/json',
                    // data : $('form').serialize(),
                    data: JSON.stringify(recData),
                    beforeSend : function() {
                        // 禁用按钮防止重复提交
                        $("#submitBtn").prop('disabled',true);
                        $("#submitBtn").text('提交中');

                    },
                    error : function(msg) {
                        alert('系统忙，请稍后再试');
                    },
                    success : function(msg) {
                        if (msg.msgCd && msg.msgCd.substr(3) === '00000') {
                           // alert('操作成功');
                           var RMK = $('#digest').val(),
                               jnl = msg.jrnNo;
                           submitTwo(RMK,jnl);
                            
                        } else {
                           alert(msg.msgInfo);
                        };
                         
                    },
                    complete : function() {
                        $("#submitBtn").prop('disabled',false);
                        $("#submitBtn").text('提交');
                    }
                });
                
            }

            function submitTwo(rmk,jnl){

                // 提交
                var paramObj = {
                    rmk : rmk,
                    totTxAmt:$('#allBwAmt').val(),
                    totTxNum:$('#countSum').val(),
                    jrnNo : jnl
                };
                $.ajax({
                    type : 'POST',
                    url : '/acm/adjust/audit',
                    dataType : 'json',
                    contentType : 'application/json',
                    data: JSON.stringify(paramObj),
                    beforeSend : function() {
                        // 禁用按钮防止重复提交
                        $("#submitBtn").prop('disabled',true);
                        $("#submitBtn").text('提交中');
                    },
                    error : function(msg) {
                        alert('系统忙，请稍后再试');
                    },
                    success : function(msg) {
                        if (msg.msgCd && msg.msgCd.substr(3) === '00000') {
                           alert('操作成功');
                        } else {
                           alert('错误信息为：' + msg.msgInfo);
                        };
                         
                    },
                    complete : function() {
                        $("#submitBtn").prop('disabled',false);
                        $("#submitBtn").text('提交');
                    }
                });
                
            }

            // +
            function accMul(arg1,arg2)
            {
                return (parseFloat(arg1) * 100 + parseFloat(arg2) * 100)/100
            }

            function checkPay(){
                // 检查收支平衡
                $('#countSum').removeAttr("readonly");
                $('#allBwAmt').removeAttr("readonly");
                $('#allloanAmt').removeAttr("readonly");
                $('#countSum').val('');
                $('#allBwAmt').val('');
                $('#allloanAmt').val('');
                var count = 0,
                    allBwAmt = 0,
                    allloanAmt = 0;
                $("tbody tr").each(function(i, v) {
                    var amt = $.trim($(v).find("td").eq(8).find("input").val());
                    if( amt != ''){
                        count++;
                        if(!/^\d+\.?(\d{1,2})?$/.test(amt)) {
                            alert('只能输入数字，小数点后只能保留两位' + '错误在第' + count + '行');
                            $(v).find("td").eq(8).find("input").val('');
                            return false;
                        }
                        if($(v).find("td").eq(7).find("select").val() == 'D'){
                            allBwAmt = accMul(allBwAmt,amt);
                        }else if($(v).find("td").eq(7).find("select").val() == 'C'){
                            allloanAmt = accMul(allloanAmt,amt);
                        }
                    }
                });
                console.log(count);
                $('#countSum').val(count);
                $('#allBwAmt').val(allBwAmt);
                $('#allloanAmt').val(allloanAmt);
                $('#countSum').attr("readonly","readonly");
                $('#allBwAmt').attr("readonly","readonly");
                $('#allloanAmt').attr("readonly","readonly");
                if (allloanAmt != allBwAmt) {
                    alert("借贷不平，请检查录入数据");
                    return false;
                }
            }

            function compareAmt(obj){
                var acType = $(obj).parent().siblings().eq(1).find('select').val();
                var amt = $(obj).parent().siblings().eq(8).find('input').val();
                var realAmt = $(obj).parent().siblings().eq(6).find('input').val();

                console.log($(obj).parent().siblings().eq(1).find('select').val()); // 账户类型
                console.log($(obj).parent().siblings().eq(6).find('input').val()); // 余额
                // 当是用户账户时，校验余额大于交易金额
                if(amt != '' && realAmt != '' && acType == 'U' && (amt >  realAmt)){
                    alert("用户账户余额小于交易金额");
                    return false;
                }
                
            }

            // 查询按钮
            function searchButton(obj) {
                // 把账户号和资金类型传到后台，
                var acTyp = $(obj).parent().siblings().eq(1).find('select').val();
                var capTyp = $(obj).parent().siblings().eq(2).find('select').val();
                var acNo = $(obj).parent().siblings().eq(3).find('input').val();
                console.log($(obj).parent().siblings().eq(1).find('select').val()); // 账户类型
                console.log($(obj).parent().siblings().eq(2).find('select').val()); // 资金类型
                console.log($(obj).parent().siblings().eq(3).find('input').val()); // 账户号
                if (acTyp == '' || capTyp == '' || acNo == '') {
                    alert("账号信息不完整，查询失败");
                    return false;
                }
                var paramObj = {
                    acTyp:acTyp, // 账户类型
                    capTyp:capTyp, // 资金类型  账户资金属性
                    acNo:acNo, // 账号
                };

                $.ajax({
                    type : 'GET',
                    url : '/acm/adjust/record/bal',
                    dataType : 'json',
                    contentType : 'application/json',
                    data: paramObj,
                    beforeSend : function() {
                        // 禁用按钮防止重复提交
                        $(obj).prop('disabled',true);
                    },
                    error : function(msg) {
                        alert('系统忙，请稍后再试');
                    },
                    success : function(msg) {
                        // 查询对应的账号的余额
                        if (msg.msgCd && msg.msgCd.substr(3) === '00000') {
                            alert('查询成功');
                            // acNm
                            $(obj).parent().siblings().eq(4).find('input').val(msg.acNm);
                            $(obj).parent().siblings().eq(5).find('input').val(msg.acCurBal);
                            
                        } else {
                            if (msg.msgInfo != null) {
                                alert(msg.msgCd + ":" + msg.msgInfo);
                            } else {
                                alert(msg.msgCd + ":" + "账户不存在");
                            }
                        };
                       
                       
                    },
                    complete : function() {
                        $(obj).prop('disabled',false);
                    }
                });
                
            }
       </script>
    </body>
</html>