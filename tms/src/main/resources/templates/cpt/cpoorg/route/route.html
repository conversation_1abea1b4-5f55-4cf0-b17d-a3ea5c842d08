<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="nav.orgroute.title"></title>
    <div th:replace="head"></div>
</head>

<body>
<div id="wrapper">
    <div th:replace="nav"></div>
    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>
        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="nav.orgroute.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.cptmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.cpoorgmgr"></a>
                    </li>
                    <li>
                        <strong data-i18n="nav.orgroute.content"></strong>
                    </li>
                </ol>
            </div>
        </div>

        <div class="wrapper wrapper-content animated fadeInRight">
            <!--查询条件-->
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form id="queryForm" class="form-horizontal">
                                <div class="form-group col-sm-4">
                                    <!--合作机构编号-->
                                    <label class="col-sm-4 control-label" for="crdCorpOrg" data-i18n="cpo.crdCorpOrg"></label>
                                    <div class="col-sm-8">
                                        <select name="crdCorpOrg" id="crdCorpOrg" class="form-control">
                                        </select>
                                    </div>
                                    <!--路由机构编号-->
                                    <label class="col-sm-4 control-label" for="rutCorpOrg" data-i18n="cpo.rutCorpOrg"></label>
                                    <div class="col-sm-8">
                                        <select name="rutCorpOrg" id="rutCorpOrg" class="form-control" >
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group col-sm-4">
                                    <!--业务类型-->
                                    <label class="col-sm-4 control-label" for="corpBusTyp" data-i18n="cpo.corpBusTyp"></label>
                                    <div class="col-sm-8">
                                        <select name="corpBusTyp" id="corpBusTyp" class="form-control">
                                            <option value="" data-i18n="cpo.selectByNull" selected></option>
                                            <option value="06" data-i18n="cpo.withdraw"></option>
                                        </select>
                                    </div>
                                    <!--业务子类型-->
                                    <label class="col-sm-4 control-label" for="corpBusSubTyp" data-i18n="cpo.corpBusSubTyp"></label>
                                    <div class="col-sm-8">
                                        <select name="corpBusSubTyp" id="corpBusSubTyp" class="form-control">
                                            <option value="" data-i18n="cpo.selectByNull" selected></option>
                                            <option value="0601" data-i18n="cpo.perWithdraw"></option>
                                            <option value="0602" data-i18n="cpo.cardWithdraw"></option>
                                            <option value="0603" data-i18n="cpo.mercWithdraw"></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group col-sm-4">
                                    <!--生效标志-->
                                    <label class="col-sm-4 control-label" for="rutEffFlg" data-i18n="cpo.rutEffFlg"></label>
                                    <div class="col-sm-8">
                                        <select name="rutEffFlg" id="rutEffFlg" class="form-control">
                                            <option value="" data-i18n="cpo.selectByNull" selected></option>
                                            <option value="0" data-i18n="cpo.rutEffFlg-0"></option>
                                            <option value="1" data-i18n="cpo.rutEffFlg-1"></option>
                                        </select>
                                    </div>
                                    <!--银行卡类型-->
                                    <label class="col-sm-4 control-label" for="crdAcTyp" data-i18n="cpo.crdAcTyp"></label>
                                    <div class="col-sm-8">
                                        <select name="crdAcTyp" id="crdAcTyp" class="form-control">
                                            <option value="" data-i18n="cpo.selectByNull" selected></option>
                                            <option value="D" data-i18n="cpo.crdAcTyp-D"></option>
                                            <option value="C" data-i18n="cpo.crdAcTyp-C"></option>
                                            <option value="U" data-i18n="cpo.crdAcTyp-U"></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-4 col-sm-offset-1">
                                        <button type="button" id="searchBtn" class="btn btn-primary" onclick="search()" data-i18n="cpo.searchList"></button>
                                        <button type="button" id="addModalBtn" class="btn btn-primary" data-toggle="modal" onclick="addModal()" data-i18n="cpo.addModal"></button>
                                        <button type="button" id="modifyModalBtn" class="btn btn-primary" data-toggle="modal" onclick="modifyModal()" data-i18n="cpo.modifyModal"></button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <div th:replace="cpt/cpoorg/route/routeModal"></div>

            <!--数据表格-->
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <div class="table-responsive">
                                <table id="orgnRoute" class="table table-striped table-bordered table-hover">
                                    <thead>
                                        <tr>
                                            <th data-i18n="cpo.crdCorpOrg"></th>
                                            <th data-i18n="cpo.corpBusTyp"></th>
                                            <th data-i18n="cpo.corpBusSubTyp"></th>
                                            <th data-i18n="cpo.rutCorpOrg"></th>
                                            <th data-i18n="cpo.crdAcTyp"></th>
                                            <th data-i18n="cpo.rutEffFlg"></th>
                                            <th data-i18n="cpo.priLvl"></th>
                                            <th data-i18n="cpo.lowAmt"></th>
                                            <th data-i18n="cpo.highAmt"></th>
                                            <th data-i18n="cpo.rmk"></th>
                                            <th data-i18n="cpo.rutInfId"></th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>
    </div>
</div>

<div th:replace="script"></div>

<script>
    var editor;
    var table;

    $(document).ready(function () {
        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        i18nLoad.then(function () {
            editor = new $.fn.dataTable.Editor({
                table: "#orgnRoute",
                idSrc: 'rutInfId',
                fields: [
                    {name: "id", type: "hidden"},
                    {label: $.i18n.t("cpo.crdCorpOrg"), name: "crdCorpOrg"},
                    {label: $.i18n.t("cpo.corpBusTyp"), name: "corpBusTyp"},
                    {label: $.i18n.t("cpo.corpBusSubTyp"), name: "corpBusSubTyp"},
                    {label: $.i18n.t("cpo.rutCorpOrg"), name: "rutCorpOrg"},
                    {label: $.i18n.t("cpo.crdAcTyp"), name: "crdAcTyp"},
                    {label: $.i18n.t("cpo.rutEffFlg"), name: "rutEffFlg"},
                    {label: $.i18n.t("cpo.priLvl"), name: "priLvl"},
                    {label: $.i18n.t("cpo.lowAmt"), name: "lowAmt"},
                    {label: $.i18n.t("cpo.highAmt"), name: "highAmt"},
                    {label: $.i18n.t("cpo.rmk"), name: "rmk"},
                    {label: $.i18n.t("cpo.rutInfId"), name: "rutInfId"}
                ]
            });

            table = $('#orgnRoute').DataTable({
                dom: 'Blfrtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/cpt/cpoorg/route/findAll',
                    type: 'post',
                    data: function (d) {
                        d.extra_search = {
                            "crdCorpOrg" : $("#crdCorpOrg").val(),
                            "corpBusTyp" : $("#corpBusTyp").val(),
                            "corpBusSubTyp" : $("#corpBusSubTyp").val(),
                            "rutCorpOrg" : $("#rutCorpOrg").val(),
                            "crdAcTyp" : $("#crdAcTyp").val(),
                            "rutEffFlg" : $("#rutEffFlg").val()
                        };
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
                language: {
                    url: languageUrl
                },
                select: true,
                buttons: [
//                        {extend: "create", editor: editor},
//                        {extend: "edit", editor: editor},
//                        {extend: "remove", editor: editor},
//                        {extend: 'copyHtml5'},
//                        {extend: 'csvHtml5'},
//                        {extend: 'excelHtml5', title: '文件名'},
                    'copy',
                    'csv',
                    'excel',
//                        'pdf',
//                        'print'
                ],
                columns: [{
                    data: 'crdCorpOrg'
                },{
                    data: 'corpBusTyp',
                    render: function (data, type, row) {
                        switch (data) {
                            case "06":
                                return $.i18n.t("cpo.withdraw");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'corpBusSubTyp',
                    render: function (data, type, row) {
                        switch (data) {
                            case "0601":
                                return $.i18n.t("cpo.perWithdraw");
                            case "0602":
                                return $.i18n.t("cpo.cardWithdraw");
                            case "0603":
                                return $.i18n.t("cpo.mercWithdraw");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'rutCorpOrg'
                },{
                    data: 'crdAcTyp',
                    render: function (data, type, row) {
                        switch (data) {
                            case "D":
                                return $.i18n.t("cpo.crdAcTyp-D");
                            case "C":
                                return $.i18n.t("cpo.crdAcTyp-C");
                            case "U":
                                return $.i18n.t("cpo.crdAcTyp-U");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'rutEffFlg',
                    render: function (data, type, row) {
                        switch (data) {
                            case "0":
                                return $.i18n.t("cpo.rutEffFlg-0");
                            case "1":
                                return $.i18n.t("cpo.rutEffFlg-1");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'priLvl'
                },{
                    data: 'lowAmt',
                    render: $.fn.dataTable.render.number(',', '.', 2)
                },{
                    data: 'highAmt',
                    render: $.fn.dataTable.render.number(',', '.', 2)
                },{
                    data: 'rmk'
                },{
                    data: 'rutInfId'
                }]
            });
        });

        //查询所有的合作机构信息
        $.ajax({
            url:"/cpt/cpoorg/info/findAllOrgnInfo",
            dataType: "json",
            type:"post",
            success:function(data){
                $("#crdCorpOrg").append("<option value=''>" + $.i18n.t("cpo.selectByNull") + "</option>");
                $("#rutCorpOrg").append("<option value=''>" + $.i18n.t("cpo.selectByNull") + "</option>");
                for (var one in data)
                {
                    var orgId = data[one].corpOrgId;
                    var orgNm = data[one].corpOrgNm;
                    $("#crdCorpOrg").append("<option value='" + orgId + "'>" +orgId+" -- "+orgNm+ "</option>");
                    $("#rutCorpOrg").append("<option value='" + orgId + "'>" +orgId+" -- "+orgNm+ "</option>");
                }
            }
        });
    });

    <!--查询按钮-->
    function search() {
        table.ajax.reload();
    }

    <!--清除modal中的输入框的值-->
    function clearModalValues() {
        $("#crd_corp_org").val("");
        $("#crd_corp_org").html("");
        $("#corp_bus_typ").val("");
        $("#corp_bus_sub_typ").val("");
        $("#rut_corp_org").val("");
        $("#rut_corp_org").html("");
        $("#crd_ac_typ").val("");
        $("#rut_eff_flg").val("");
        $("#pri_lvl").val(99);
        $("#low_Amt").val(0.00);
        $("#high_amt").val(9999999.00);
        $("#rmk").val("");
        $("#rut_inf_id").val("");
    }

    //模态框，非空数据判断
    function judgeModalValues() {
        if($("#crd_corp_org").val() == null || $("#crd_corp_org").val() == "") {
            swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.corpOrgIdNull"), "error");
            return false;
        }
        if($("#corp_bus_typ").val() == null || $("#corp_bus_typ").val() == "") {
            swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.corpBusTypNull"), "error");
            return false;
        }
        if($("#corp_bus_sub_typ").val() == null || $("#corp_bus_sub_typ").val() == "") {
            swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.corpBusSubTypNull"), "error");
            return false;
        }
        if($("#rut_corp_org").val() == null || $("#rut_corp_org").val() == "") {
            swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.corpOrgIdNull"), "error");
            return false;
        }
        if($("#crd_ac_typ").val() == null || $("#crd_ac_typ").val() == "") {
            swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.crdAcTypNull"), "error");
            return false;
        }
        if($("#rut_eff_flg").val() == null || $("#rut_eff_flg").val() == "") {
            swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.busEffFlgNull"), "error");
            return false;
        }
        if($("#pri_lvl").val() == null || $("#pri_lvl").val() == "") {
            swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.priLvlNull"), "error");
            return false;
        }
        if($("#low_Amt").val() == null || $("#low_Amt").val() == "" || $("#low_Amt").val() < 0) {
            swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.lowAmtNull"), "error");
            return false;
        }
        if($("#high_amt").val() == null || $("#high_amt").val() == "" || $("#high_amt").val() < 0 || $("#high_amt").val() < $("#low_Amt").val()) {
            swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.highAmtNull"), "error");
            return false;
        }
        return true;
    }

    //模态框，获取元素值
    function getModalValues() {
        var crdCorpOrg = $("#crd_corp_org").val();
        var corpBusTyp = $("#corp_bus_typ").val();
        var corpBusSubTyp = $("#corp_bus_sub_typ").val();
        var rutCorpOrg = $("#rut_corp_org").val();
        var crdAcTyp = $("#crd_ac_typ").val();
        var rutEffFlg = $("#rut_eff_flg").val();
        var priLvl = $("#pri_lvl").val();
        var lowAmt = $("#low_Amt").val();
        var highAmt = $("#high_amt").val();
        var rmk = $("#rmk").val();
        var rutInfId = $("#rut_inf_id").val();
        var param = {
            "crdCorpOrg" : crdCorpOrg,
            "corpBusTyp" : corpBusTyp,
            "corpBusSubTyp" : corpBusSubTyp,
            "rutCorpOrg" : rutCorpOrg,
            "crdAcTyp" : crdAcTyp,
            "rutEffFlg" : rutEffFlg,
            "priLvl" : priLvl,
            "lowAmt" : lowAmt,
            "highAmt" : highAmt,
            "rmk" : rmk,
            "rutInfId" : rutInfId
        };
        return param;
    }

    //主页新增记录按钮
    function addModal() {
        clearModalValues();
        $("#addDetailBtn").show();
        $("#modifyDetailBtn").hide();
        $("#crd_corp_org").removeAttr("disabled");
        $("#corp_bus_typ").removeAttr("disabled");
        $("#corp_bus_sub_typ").removeAttr("disabled");
        $("#rut_corp_org").removeAttr("disabled");
        $("#routeModal").modal("show");

        //查询所有的合作机构信息
        $.ajax({
            url:"/cpt/cpoorg/info/findAllOrgnInfo",
            dataType: "json",
            type:"post",
            success:function(data){
                $("#crd_corp_org").append("<option value=''>" + $.i18n.t("cpo.selectByNull") + "</option>");
                $("#rut_corp_org").append("<option value=''>" + $.i18n.t("cpo.selectByNull") + "</option>");
                for (var one in data)
                {
                    var orgId = data[one].corpOrgId;
                    var orgNm = data[one].corpOrgNm;
                    $("#crd_corp_org").append("<option value='" + orgId + "'>" +orgId+" -- "+orgNm+ "</option>");
                    $("#rut_corp_org").append("<option value='" + orgId + "'>" +orgId+" -- "+orgNm+ "</option>");
                }
            }
        });
    }

    //主页修改记录按钮
    function modifyModal() {
        clearModalValues();
        $("#addDetailBtn").hide();
        $("#modifyDetailBtn").show();
        $("#crd_corp_org").attr("disabled", "disabled");
        $("#corp_bus_typ").attr("disabled", "disabled");
        $("#corp_bus_sub_typ").attr("disabled", "disabled");
        $("#rut_corp_org").removeAttr("disabled");

        <!--获取表格中选中的单行-->
        var row = table.row('.selected');
        if(row.length == 0) {
            swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.shouldSelectOne"), "error");
            return;
        }

        <!--获取选中行的内容-->
        var rowData = table.row(row).data();

        //根据机构编号，查询资金合作机构信息
        var crdCorpOrg = rowData.crdCorpOrg;
        $.ajax({
            url:"/cpt/cpoorg/info/findOrgnInfo",
            data: {
                "corpOrgId" : crdCorpOrg
            },
            dataType: "json",
            type:"post",
            success:function(data){
                var orgId = data.corpOrgId;
                var orgNm = data.corpOrgNm;
                $("#crd_corp_org").append("<option value='" + orgId + "' selected>" +orgId+" -- "+orgNm+ "</option>");
            }
        });

        //查询所有的合作机构信息
        var rutCorpOrg = rowData.rutCorpOrg;
        $.ajax({
            url:"/cpt/cpoorg/info/findAllOrgnInfo",
            dataType: "json",
            type:"post",
            success:function(data){
                $("#rut_corp_org").append("<option value=''>" + $.i18n.t("cpo.selectByNull") + "</option>");
                for (var one in data)
                {
                    var orgId = data[one].corpOrgId;
                    var orgNm = data[one].corpOrgNm;
                    $("#rut_corp_org").append("<option value='" + orgId + "'>" +orgId+" -- "+orgNm+ "</option>");
                    if(orgId==rutCorpOrg){
                        $("#rut_corp_org").find("option:contains('"+rutCorpOrg+"')").attr("selected",true);
                    }
                }
            }
        });

        //显示模态框元素值
        $("#crd_corp_org").val(rowData.crdCorpOrg);
        $("#corp_bus_typ").val(rowData.corpBusTyp);
        $("#corp_bus_sub_typ").val(rowData.corpBusSubTyp);
        $("#rut_corp_org").val(rowData.rutCorpOrg);
        $("#crd_ac_typ").val(rowData.crdAcTyp);
        $("#rut_eff_flg").val(rowData.rutEffFlg);
        $("#pri_lvl").val(rowData.priLvl);
        $("#low_Amt").val(rowData.lowAmt);
        $("#high_amt").val(rowData.highAmt);
        $("#rmk").val(rowData.rmk);
        $("#rut_inf_id").val(rowData.rutInfId);
        $("#routeModal").modal("show");
    }

    //模态框，新增信息按钮
    function addDetail() {
        if(!judgeModalValues()){
            return;
        }
        //确认增加弹出框
        swal({
            title: $.i18n.t("cpo.addConfirmTitle"),
            text: $.i18n.t("cpo.addRouteText"),
            type: "warning",
            showCancelButton: true,
            cancelButtonText: $.i18n.t("cpo.swal-cancel"),
            confirmButtonColor: "#DD6B55",
            confirmButtonText: $.i18n.t("cpo.swal-confirm"),
            closeOnConfirm: false,
            closeOnCancel: true
        }, function() {
            var param = getModalValues();
            $.ajax({
                contentType: "application/json",
                url:"/cpt/cpoorg/route/add",
                dataType: "json",
                type: "post",
                data: JSON.stringify(param),
                success:function(data){
                    var msgCd = data.msgCd;
                    if(msgCd == "CPO00000") {
                        search();
                        $("#routeModal").modal("hide");
                        swal($.i18n.t("cpo.swal-success"), $.i18n.t("cpo.addConfirmSuss"), "success");
                    } else if(msgCd == "CPO30002") {
                        swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.corpOrgInfNotExists"), "error");
                    } else if(msgCd == "CPO30004") {
                        swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.corpOrgBusNotExists"), "error");
                    } else if(msgCd == "CPO30005") {
                        swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.corpOrgRouteExists"), "error");
                    } else {
                        swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.addConfirmFail"), "error");
                    }
                },
                error: function(){
                    swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.systemException"), "error");
                }
            });
        });
    }

    //模态框，修改信息按钮
    function modifyDetail() {
        if(!judgeModalValues()){
            return;
        }
        //确认修改弹出框
        swal({
            title: $.i18n.t("cpo.modifyConfirmTitle"),
            text: $.i18n.t("cpo.modifyRouteText"),
            type: "warning",
            showCancelButton: true,
            cancelButtonText: $.i18n.t("cpo.swal-cancel"),
            confirmButtonColor: "#DD6B55",
            confirmButtonText: $.i18n.t("cpo.swal-confirm"),
            closeOnConfirm: false,
            closeOnCancel: true
        }, function() {
            var param = getModalValues();
            $.ajax({
                contentType: "application/json",
                url: "/cpt/cpoorg/route/modify",
                data: JSON.stringify(param),
                dataType: "json",
                type: "post",
                success: function(data) {
                    var msgCd = data.msgCd;
                    if(msgCd == "CPO00000") {
                        search();
                        $("#routeModal").modal("hide");
                        swal($.i18n.t("cpo.swal-success"), $.i18n.t("cpo.modifyConfirmSuss"), "success");
                    } else if(msgCd == "CPO30002") {
                        swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.corpOrgInfNotExists"), "error");
                    } else if(msgCd == "CPO30004") {
                        swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.corpOrgBusNotExists"), "error");
                    } else {
                        swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.modifyConfirmFail"), "error");
                    }
                },
                error: function() {
                    swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.modifyConfirmFail"), "error");
                }
            });
        });
    }

</script>
</body>

</html>