<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="nav.orginfo.title"></title>
    <div th:replace="head"></div>
</head>

<body>
<div id="wrapper">
    <div th:replace="nav"></div>
    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>
        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="nav.orginfo.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.cptmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.cpoorgmgr"></a>
                    </li>
                    <li>
                        <strong data-i18n="nav.orginfo.content"></strong>
                    </li>
                </ol>
            </div>
        </div>

        <div class="wrapper wrapper-content animated fadeInRight">
            <!--查询条件-->
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form id="queryForm" class="form-horizontal">
                                <div class="form-group col-sm-4">
                                    <!--合作机构编号-->
                                    <label class="col-sm-4 control-label" for="corpOrgId" data-i18n="cpo.corpOrgId"></label>
                                    <div class="col-sm-8">
                                        <input name="corpOrgId" id="corpOrgId" class="form-control" value=""/>
                                    </div>
                                </div>
                                <div class="form-group col-sm-4">
                                    <!--合作机构类型-->
                                    <label class="col-sm-4 control-label" for="corpOrgTyp" data-i18n="cpo.corpOrgTyp"></label>
                                    <div class="col-sm-8">
                                        <select name="corpOrgTyp" id="corpOrgTyp" class="form-control">
                                            <option value="" data-i18n="cpo.selectByNull" selected></option>
                                            <option value="0" data-i18n="cpo.corpOrgTyp-0"></option>
                                            <option value="1" data-i18n="cpo.corpOrgTyp-1"></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-4 col-sm-offset-1">
                                        <button type="button" id="searchBtn" class="btn btn-primary" onclick="search()" data-i18n="cpo.searchList"></button>
                                        <button type="button" id="addBtn" class="btn btn-primary" data-toggle="modal" onclick="addModal()" data-i18n="cpo.addModal"></button>
                                        <button type="button" id="modifyBtn" class="btn btn-primary" data-toggle="modal" onclick="modifyModal()" data-i18n="cpo.modifyModal"></button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!--汇款确认/退回表单-->
            <div th:replace="cpt/cpoorg/info/infoModal"></div>

            <!--数据表格-->
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <div class="table-responsive">
                                <table id="orgnInfo" class="table table-striped table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th data-i18n="cpo.corpOrgId"></th>
                                        <th data-i18n="cpo.corpOrgNm"></th>
                                        <th data-i18n="cpo.corpOrgSnm"></th>
                                        <th data-i18n="cpo.corpOrgTyp"></th>
                                        <th data-i18n="cpo.corpAccNm"></th>
                                        <th data-i18n="cpo.corpAccNo"></th>
                                        <th data-i18n="cpo.rmk"></th>
                                        <th data-i18n="cpo.orgInfId"></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>
    </div>
</div>

<div th:replace="script"></div>

<script>
    var editor;
    var table;

    $(document).ready(function () {
        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        i18nLoad.then(function () {
            editor = new $.fn.dataTable.Editor({
                table: "#orgnInfo",
                idSrc: 'orgInfId',
                fields: [
                    {name: "id", type: "hidden"},
                    {label: $.i18n.t("cpo.orgInfId"), name: "orgInfId"},
                    {label: $.i18n.t("cpo.corpOrgId"), name: "corpOrgId"},
                    {label: $.i18n.t("cpo.corpOrgNm"), name: "corpOrgNm"},
                    {label: $.i18n.t("cpo.corpOrgSnm"), name: "corpOrgSnm"},
                    {label: $.i18n.t("cpo.corpOrgTyp"), name: "corpOrgTyp"},
                    {label: $.i18n.t("cpo.corpAccNm"), name: "corpAccNm"},
                    {label: $.i18n.t("cpo.corpAccNo"), name: "corpAccNo"},
                    {label: $.i18n.t("cpo.rmk"), name: "rmk"}
                ]
            });

            table = $('#orgnInfo').DataTable({
                dom: 'Blfrtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/cpt/cpoorg/info/findAll',
                    type: 'post',
                    data: function (d) {
                        d.extra_search = {
                            "corpOrgId" : $("#corpOrgId").val(),
                            "corpOrgTyp" : $("#corpOrgTyp").val()
                        };
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
                language: {
                    url: languageUrl
                },
                select: true,
                buttons: [
//                        {extend: "create", editor: editor},
//                        {extend: "edit", editor: editor},
//                        {extend: "remove", editor: editor},
//                        {extend: 'copyHtml5'},
//                        {extend: 'csvHtml5'},
//                        {extend: 'excelHtml5', title: '文件名'},
                    'copy',
                    'csv',
                    'excel',
//                        'pdf',
//                        'print'
                ],
                columns: [{
                    data: 'corpOrgId'
                },{
                    data: 'corpOrgNm'
                },{
                    data: 'corpOrgSnm'
                },{
                    data: 'corpOrgTyp',
                    render: function (data, type, row) {
                        switch (data) {
                            case "0":
                                return $.i18n.t("cpo.corpOrgTyp-0");
                            case "1":
                                return $.i18n.t("cpo.corpOrgTyp-1");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'corpAccNm'
                },{
                    data: 'corpAccNo'
                },{
                    data: 'rmk'
                },{
                    data: 'orgInfId'
                }]
            });
        });
    });

    //查询按钮
    function search() {
        table.ajax.reload();
    }

    //模态框，清除元素值
    function clearModalValues() {
        $("#corp_org_id").val("");
        $("#corp_org_nm").val("");
        $("#corp_org_snm").val("");
        $("#corp_org_typ").val("");
        $("#corp_acc_nm").val("");
        $("#corp_acc_no").val("");
        $("#rmk").val("");
        $("#org_inf_id").val("");
    }

    //模态框，非空数据判断
    function judgeModalValues() {
        if($("#corp_org_id").val() == null || $("#corp_org_id").val() == "") {
            swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.corpOrgIdNull"), "error");
            return false;
        }
        if($("#corp_org_nm").val() == null || $("#corp_org_nm").val() == "") {
            swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.corpOrgNmNull"), "error");
            return false;
        }
        if($("#corp_org_snm").val() == null || $("#corp_org_snm").val() == "") {
            swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.corpOrgSnmNull"), "error");
            return false;
        }
        if($("#corp_org_typ").val() == null || $("#corp_org_typ").val() == "") {
            swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.corpOrgTypNull"), "error");
            return false;
        }
        return true;
    }

    //模态框，获取元素值
    function getModalValues() {
        var corpOrgId = $("#corp_org_id").val();
        var corpOrgNm = $("#corp_org_nm").val();
        var corpOrgSnm = $("#corp_org_snm").val();
        var corpOrgTyp = $("#corp_org_typ").val();
        var corpAccNm = $("#corp_acc_nm").val();
        var corpAccNo = $("#corp_acc_no").val();
        var rmk = $("#rmk").val();
        var orgInfId = $("#org_inf_id").val();
        var param = {
            "corpOrgId" : corpOrgId,
            "corpOrgNm" : corpOrgNm,
            "corpOrgSnm" : corpOrgSnm,
            "corpOrgTyp" : corpOrgTyp,
            "corpAccNm" : corpAccNm,
            "corpAccNo" : corpAccNo,
            "rmk" : rmk,
            "orgInfId" : orgInfId
        };
        return param;
    }

    //主页新增记录按钮
    function addModal() {
        clearModalValues();
        $("#addDetail").show();
        $("#modifyDetail").hide();
        $("#corp_org_id").removeAttr("disabled");
        $("#infoModal").modal("show");
    }

    //主页修改记录按钮
    function modifyModal() {
        clearModalValues();
        $("#addDetail").hide();
        $("#modifyDetail").show();
        $("#corp_org_id").attr("disabled", "disabled");

        <!--获取表格中选中的单行-->
        var row = table.row('.selected');
        if(row.length == 0) {
            swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.shouldSelectOne"), "error");
            return;
        }

        <!--获取选中行的内容-->
        $("#infoModal").modal("show");
        var rowData = table.row(row).data();
        $("#corp_org_id").val(rowData.corpOrgId);
        $("#corp_org_nm").val(rowData.corpOrgNm);
        $("#corp_org_snm").val(rowData.corpOrgSnm);
        $("#corp_org_typ").val(rowData.corpOrgTyp);
        $("#corp_acc_nm").val(rowData.corpAccNm);
        $("#corp_acc_no").val(rowData.corpAccNo);
        $("#rmk").val(rowData.rmk);
        $("#org_inf_id").val(rowData.orgInfId);
    }

    //模态框，新增信息按钮
    function addDetail() {
        if(!judgeModalValues()){
            return;
        }
        //增加机构信息
        var param = getModalValues();
        swal({
            title: $.i18n.t("cpo.addConfirmTitle"),
            text: $.i18n.t("cpo.addConfirmText"),
            type: "warning",
            showCancelButton: true,
            cancelButtonText: $.i18n.t("cpo.swal-cancel"),
            confirmButtonColor: "#DD6B55",
            confirmButtonText: $.i18n.t("cpo.swal-confirm"),
            closeOnConfirm: false,
            closeOnCancel: true
        }, function() {
            $.ajax({
                contentType: 'application/json',
                url:"/cpt/cpoorg/info/add",
                dataType: "json",
                type: "post",
                data: JSON.stringify(param),
                success:function(data) {
                    var msgCd = data.msgCd;
                    if(msgCd == "CPO00000") {
                        search();
                        swal($.i18n.t("cpo.swal-success"), $.i18n.t("cpo.addConfirmSuss"), "success");
                        $("#infoModal").modal("hide");
                    } else if(msgCd == "CPO30001") {
                        swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.corpOrgInfExists"), "error");
                    } else {
                        swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.addConfirmFail"), "error");
                    }
                },
                error: function(){
                    swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.systemException"), "error");
                }
            });
        });
    }

    //模态框，修改信息按钮
    function modifyDetail() {
        if(!judgeModalValues()){
            return;
        }
        //修改机构信息
        var param = getModalValues();
        swal({
            title: $.i18n.t("cpo.modifyConfirmTitle"),
            text: $.i18n.t("cpo.modifyConfirmText"),
            type: "warning",
            showCancelButton: true,
            cancelButtonText: $.i18n.t("cpo.swal-cancel"),
            confirmButtonColor: "#DD6B55",
            confirmButtonText: $.i18n.t("cpo.swal-confirm"),
            closeOnConfirm: false,
            closeOnCancel: true
        }, function() {
            $.ajax({
                contentType: 'application/json',
                url: "/cpt/cpoorg/info/modify",
                data: JSON.stringify(param),
                dataType: "json",
                type: "post",
                success: function(data) {
                    var msgCd = data.msgCd;
                    if(msgCd == "CPO00000") {
                        search();
                        $("#infoModal").modal("hide");
                        swal($.i18n.t("cpo.swal-success"), $.i18n.t("cpo.modifyConfirmSuss"), "success");
                    } else {
                        swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.modifyConfirmFail"), "error");
                    }
                },
                error: function() {
                    swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.systemException"), "error");
                }
            });
        });
    }

</script>
</body>

</html>