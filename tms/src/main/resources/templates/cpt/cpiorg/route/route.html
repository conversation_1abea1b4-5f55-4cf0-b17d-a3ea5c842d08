<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="nav.orgroute.title"></title>
    <div th:replace="head"></div>
</head>

<body>
<div id="wrapper">
    <div th:replace="nav"></div>
    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>
        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="nav.orgroute.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.cptmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.cpiorgmgr"></a>
                    </li>
                    <li>
                        <strong data-i18n="nav.orgroute.content"></strong>
                    </li>
                </ol>
            </div>
        </div>

        <div class="wrapper wrapper-content animated fadeInRight">
            <!--查询条件-->
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form id="queryForm" class="form-horizontal">
                                <div class="form-group col-sm-4">
                                    <!--合作机构编号-->
                                    <label class="col-sm-4 control-label" for="crdCorpOrg" data-i18n="cpi.crdCorpOrg"></label>
                                    <div class="col-sm-8">
                                        <select name="crdCorpOrg" id="crdCorpOrg" class="form-control">
                                        </select>
                                    </div>
                                    <!--路由机构编号-->
                                    <label class="col-sm-4 control-label" for="rutCorpOrg" data-i18n="cpi.rutCorpOrg"></label>
                                    <div class="col-sm-8">
                                        <select name="rutCorpOrg" id="rutCorpOrg" class="form-control">
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group col-sm-4">
                                    <!--业务类型-->
                                    <label class="col-sm-4 control-label" for="corpBusTyp" data-i18n="cpi.corpBusTyp"></label>
                                    <div class="col-sm-8">
                                        <select name="corpBusTyp" id="corpBusTyp" class="form-control">
                                            <option value="" data-i18n="cpi.selectByNull" selected></option>
                                            <option value="01" data-i18n="cpi.txTyp-sign"></option>
                                            <option value="02" data-i18n="cpi.txTyp-fastpay"></option>
                                            <option value="03" data-i18n="cpi.txTyp-ebankpay"></option>
                                            <option value="04" data-i18n="cpi.txTyp-remittance"></option>
                                        </select>
                                    </div>
                                    <!--业务子类型-->
                                    <label class="col-sm-4 control-label" for="corpBusSubTyp" data-i18n="cpi.corpBusSubTyp"></label>
                                    <div class="col-sm-8">
                                        <select name="corpBusSubTyp" id="corpBusSubTyp" class="form-control">
                                            <option value="" data-i18n="cpi.selectByNull" selected></option>
                                            <option value="0101" data-i18n="cpi.prefastSign"></option>
                                            <option value="0102" data-i18n="cpi.fastSign"></option>
                                            <option value="0103" data-i18n="cpi.fastUnsign"></option>
                                            <option value="0104" data-i18n="cpi.withdrawSign"></option>
                                            <option value="0105" data-i18n="cpi.withdrawUnsign"></option>
                                            <option value="0201" data-i18n="cpi.fastpay"></option>
                                            <option value="0202" data-i18n="cpi.fastpayConsume"></option>
                                            <option value="0301" data-i18n="cpi.ebankpay"></option>
                                            <option value="0302" data-i18n="cpi.ebankpayConsume"></option>
                                            <option value="0303" data-i18n="cpi.ebankpayMercScan"></option>
                                            <option value="0304" data-i18n="cpi.ebankpayUserScan"></option>
                                            <option value="0401" data-i18n="cpi.remittance"></option>
                                            <option value="0402" data-i18n="cpi.bussinessRemittance"></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group col-sm-4">
                                    <!--生效标志-->
                                    <label class="col-sm-4 control-label" for="rutEffFlg" data-i18n="cpi.rutEffFlg"></label>
                                    <div class="col-sm-8">
                                        <select name="rutEffFlg" id="rutEffFlg" class="form-control">
                                            <option value="" data-i18n="cpi.selectByNull" selected></option>
                                            <option value="0" data-i18n="cpi.rutEffFlg-0"></option>
                                            <option value="1" data-i18n="cpi.rutEffFlg-1"></option>
                                        </select>
                                    </div>
                                    <!--银行卡类型-->
                                    <label class="col-sm-4 control-label" for="crdAcTyp" data-i18n="cpi.crdAcTyp"></label>
                                    <div class="col-sm-8">
                                        <select name="crdAcTyp" id="crdAcTyp" class="form-control">
                                            <option value="" data-i18n="cpi.selectByNull" selected></option>
                                            <option value="D" data-i18n="cpi.crdAcTyp-D"></option>
                                            <option value="C" data-i18n="cpi.crdAcTyp-C"></option>
                                            <option value="U" data-i18n="cpi.crdAcTyp-U"></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-4 col-sm-offset-1">
                                        <button type="button" id="searchBtn" class="btn btn-primary" onclick="search()" data-i18n="cpi.searchList"></button>
                                        <button type="button" id="addModalBtn" class="btn btn-primary" data-toggle="modal" onclick="addModal()" data-i18n="cpi.addModal"></button>
                                        <button type="button" id="modifyModalBtn" class="btn btn-primary" data-toggle="modal" onclick="modifyModal()" data-i18n="cpi.modifyModal"></button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <div th:replace="cpt/cpiorg/route/routeModal"></div>

            <!--数据表格-->
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <div class="table-responsive">
                                <table id="orgnRoute" class="table table-striped table-bordered table-hover">
                                    <thead>
                                        <tr>
                                            <th data-i18n="cpi.crdCorpOrg"></th>
                                            <th data-i18n="cpi.corpBusTyp"></th>
                                            <th data-i18n="cpi.corpBusSubTyp"></th>
                                            <th data-i18n="cpi.rutCorpOrg"></th>
                                            <th data-i18n="cpi.crdAcTyp"></th>
                                            <th data-i18n="cpi.rutEffFlg"></th>
                                            <th data-i18n="cpi.priLvl"></th>
                                            <th data-i18n="cpi.lowAmt"></th>
                                            <th data-i18n="cpi.highAmt"></th>
                                            <th data-i18n="cpi.rmk"></th>
                                            <th data-i18n="cpi.rutInfId"></th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>
    </div>
</div>

<div th:replace="script"></div>

<script>
    var editor;
    var table;

    $(document).ready(function () {
        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        i18nLoad.then(function () {
            editor = new $.fn.dataTable.Editor({
                table: "#orgnRoute",
                idSrc: 'rutInfId',
                fields: [
                    {name: "id", type: "hidden"},
                    {label: $.i18n.t("cpi.crdCorpOrg"), name: "crdCorpOrg"},
                    {label: $.i18n.t("cpi.corpBusTyp"), name: "corpBusTyp"},
                    {label: $.i18n.t("cpi.corpBusSubTyp"), name: "corpBusSubTyp"},
                    {label: $.i18n.t("cpi.rutCorpOrg"), name: "rutCorpOrg"},
                    {label: $.i18n.t("cpi.crdAcTyp"), name: "crdAcTyp"},
                    {label: $.i18n.t("cpi.rutEffFlg"), name: "rutEffFlg"},
                    {label: $.i18n.t("cpi.priLvl"), name: "priLvl"},
                    {label: $.i18n.t("cpi.lowAmt"), name: "lowAmt"},
                    {label: $.i18n.t("cpi.highAmt"), name: "highAmt"},
                    {label: $.i18n.t("cpi.rmk"), name: "rmk"},
                    {label: $.i18n.t("cpi.rutInfId"), name: "rutInfId"}
                ]
            });

            table = $('#orgnRoute').DataTable({
                dom: 'Blfrtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/cpt/cpiorg/route/findAll',
                    type: 'post',
                    data: function (d) {
                        d.extra_search = {
                            "crdCorpOrg" : $("#crdCorpOrg").val(),
                            "corpBusTyp" : $("#corpBusTyp").val(),
                            "corpBusSubTyp" : $("#corpBusSubTyp").val(),
                            "rutCorpOrg" : $("#rutCorpOrg").val(),
                            "crdAcTyp" : $("#crdAcTyp").val(),
                            "rutEffFlg" : $("#rutEffFlg").val()
                        };
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
                language: {
                    url: languageUrl
                },
                select: true,
                buttons: [
//                        {extend: "create", editor: editor},
//                        {extend: "edit", editor: editor},
//                        {extend: "remove", editor: editor},
//                        {extend: 'copyHtml5'},
//                        {extend: 'csvHtml5'},
//                        {extend: 'excelHtml5', title: '文件名'},
                    'copy',
                    'csv',
                    'excel',
//                        'pdf',
//                        'print'
                ],
                columns: [{
                    data: 'crdCorpOrg'
                },{
                    data: 'corpBusTyp',
                    render: function (data, type, row) {
                        switch (data) {
                            case "01":
                                return $.i18n.t("cpi.txTyp-sign");
                            case "02":
                                return $.i18n.t("cpi.txTyp-fastpay");
                            case "03":
                                return $.i18n.t("cpi.txTyp-ebankpay");
                            case "04":
                                return $.i18n.t("cpi.txTyp-remittance");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'corpBusSubTyp',
                    render: function (data, type, row) {
                        switch (data) {
                            case "0101":
                                return $.i18n.t("cpi.prefastSign");
                            case "0102":
                                return $.i18n.t("cpi.fastSign");
                            case "0103":
                                return $.i18n.t("cpi.fastUnsign");
                            case "0104":
                                return $.i18n.t("cpi.withdrawSign");
                            case "0105":
                                return $.i18n.t("cpi.withdrawUnsign");
                            case "0201":
                                return $.i18n.t("cpi.fastpay");
                            case "0202":
                                return $.i18n.t("cpi.fastpayConsume");
                            case "0301":
                                return $.i18n.t("cpi.ebankpay");
                            case "0302":
                                return $.i18n.t("cpi.ebankpayConsume");
                            case "0303":
                                return $.i18n.t("cpi.ebankpayMercScan");
                            case "0304":
                                return $.i18n.t("cpi.ebankpayUserScan");
                            case "0401":
                                return $.i18n.t("cpi.remittance");
                            case "0402":
                                return $.i18n.t("cpi.bussinessRemittance");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'rutCorpOrg'
                },{
                    data: 'crdAcTyp',
                    render: function (data, type, row) {
                        switch (data) {
                            case "D":
                                return $.i18n.t("cpi.crdAcTyp-D");
                            case "C":
                                return $.i18n.t("cpi.crdAcTyp-C");
                            case "U":
                                return $.i18n.t("cpi.crdAcTyp-U");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'rutEffFlg',
                    render: function (data, type, row) {
                        switch (data) {
                            case "0":
                                return $.i18n.t("cpi.rutEffFlg-0");
                            case "1":
                                return $.i18n.t("cpi.rutEffFlg-1");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'priLvl'
                },{
                    data: 'lowAmt',
                    render: $.fn.dataTable.render.number(',', '.', 2)
                },{
                    data: 'highAmt',
                    render: $.fn.dataTable.render.number(',', '.', 2)
                },{
                    data: 'rmk'
                },{
                    data: 'rutInfId'
                }]
            });
        });

        //查询所有的合作机构信息
        $.ajax({
            url:"/cpt/cpiorg/info/findAllOrgnInfo",
            dataType: "json",
            type:"post",
            success:function(data){
                $("#crdCorpOrg").append("<option value=''>" + $.i18n.t("cpi.selectByNull") + "</option>");
                $("#rutCorpOrg").append("<option value=''>" + $.i18n.t("cpi.selectByNull") + "</option>");
                for (var one in data)
                {
                    var orgId = data[one].corpOrgId;
                    var orgNm = data[one].corpOrgNm;
                    $("#crdCorpOrg").append("<option value='" + orgId + "'>" +orgId+" -- "+orgNm+ "</option>");
                    $("#rutCorpOrg").append("<option value='" + orgId + "'>" +orgId+" -- "+orgNm+ "</option>");
                }
            }
        });
    });

    <!--查询按钮-->
    function search() {
        table.ajax.reload();
    }

    <!--清除modal中的输入框的值-->
    function clearModalValues() {
        $("#crd_corp_org").val("");
        $("#crd_corp_org").html("");
        $("#corp_bus_typ").val("");
        $("#corp_bus_sub_typ").val("");
        $("#rut_corp_org").val("");
        $("#rut_corp_org").html("");
        $("#crd_ac_typ").val("");
        $("#rut_eff_flg").val("");
        $("#pri_lvl").val(99);
        $("#low_Amt").val(0.00);
        $("#high_amt").val(9999999.00);
        $("#rmk").val("");
        $("#rut_inf_id").val("");
    }

    //模态框，非空数据判断
    function judgeModalValues() {
        if($("#crd_corp_org").val() == null || $("#crd_corp_org").val() == "") {
            swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.corpOrgIdNull"), "error");
            return false;
        }
        if($("#corp_bus_typ").val() == null || $("#corp_bus_typ").val() == "") {
            swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.corpBusTypNull"), "error");
            return false;
        }
        if($("#corp_bus_sub_typ").val() == null || $("#corp_bus_sub_typ").val() == "") {
            swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.corpBusSubTypNull"), "error");
            return false;
        }
        if($("#rut_corp_org").val() == null || $("#rut_corp_org").val() == "") {
            swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.corpOrgIdNull"), "error");
            return false;
        }
        if($("#crd_ac_typ").val() == null || $("#crd_ac_typ").val() == "") {
            swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.crdAcTypNull"), "error");
            return false;
        }
        if($("#rut_eff_flg").val() == null || $("#rut_eff_flg").val() == "") {
            swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.busEffFlgNull"), "error");
            return false;
        }
        if($("#pri_lvl").val() == null || $("#pri_lvl").val() == "") {
            swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.priLvlNull"), "error");
            return false;
        }
        if($("#low_Amt").val() == null || $("#low_Amt").val() == "" || $("#low_Amt").val() < 0) {
            swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.lowAmtNull"), "error");
            return false;
        }
        if($("#high_amt").val() == null || $("#high_amt").val() == "" || $("#high_amt").val() < 0 || $("#high_amt").val() < $("#low_Amt").val()) {
            swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.highAmtNull"), "error");
            return false;
        }
        return true;
    }

    //模态框，获取元素值
    function getModalValues() {
        var crdCorpOrg = $("#crd_corp_org").val();
        var corpBusTyp = $("#corp_bus_typ").val();
        var corpBusSubTyp = $("#corp_bus_sub_typ").val();
        var rutCorpOrg = $("#rut_corp_org").val();
        var crdAcTyp = $("#crd_ac_typ").val();
        var rutEffFlg = $("#rut_eff_flg").val();
        var priLvl = $("#pri_lvl").val();
        var lowAmt = $("#low_Amt").val();
        var highAmt = $("#high_amt").val();
        var rmk = $("#rmk").val();
        var rutInfId = $("#rut_inf_id").val();
        var param = {
            "crdCorpOrg" : crdCorpOrg,
            "corpBusTyp" : corpBusTyp,
            "corpBusSubTyp" : corpBusSubTyp,
            "rutCorpOrg" : rutCorpOrg,
            "crdAcTyp" : crdAcTyp,
            "rutEffFlg" : rutEffFlg,
            "priLvl" : priLvl,
            "lowAmt" : lowAmt,
            "highAmt" : highAmt,
            "rmk" : rmk,
            "rutInfId" : rutInfId
        };
        return param;
    }

    //主页新增记录按钮
    function addModal() {
        clearModalValues();
        $("#addDetailBtn").show();
        $("#modifyDetailBtn").hide();
        $("#crd_corp_org").removeAttr("disabled");
        $("#corp_bus_typ").removeAttr("disabled");
        $("#corp_bus_sub_typ").removeAttr("disabled");
        $("#rut_corp_org").removeAttr("disabled");
        $("#routeModal").modal("show");

        //查询所有的合作机构信息
        $.ajax({
            url:"/cpt/cpiorg/info/findAllOrgnInfo",
            dataType: "json",
            type:"post",
            success:function(data){
                for (var one in data)
                {
                    var orgId = data[one].corpOrgId;
                    var orgNm = data[one].corpOrgNm;
                    $("#crd_corp_org").append("<option value='" + orgId + "'>" +orgId+" -- "+orgNm+ "</option>");
                    $("#rut_corp_org").append("<option value='" + orgId + "'>" +orgId+" -- "+orgNm+ "</option>");
                }
            }
        });
    }

    //主页修改记录按钮
    function modifyModal() {
        clearModalValues();
        $("#addDetailBtn").hide();
        $("#modifyDetailBtn").show();
        $("#crd_corp_org").attr("disabled", "disabled");
        $("#corp_bus_typ").attr("disabled", "disabled");
        $("#corp_bus_sub_typ").attr("disabled", "disabled");
        $("#rut_corp_org").removeAttr("disabled");

        <!--获取表格中选中的单行-->
        var row = table.row('.selected');
        if(row.length == 0) {
            swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.shouldSelectOne"), "error");
            return;
        }

        <!--获取选中行的内容-->
        var rowData = table.row(row).data();

        //根据机构编号，查询资金合作机构信息
        var crdCorpOrg = rowData.crdCorpOrg;
        $.ajax({
            url:"/cpt/cpiorg/info/findOrgnInfo",
            data: {
                "corpOrgId" : crdCorpOrg
            },
            dataType: "json",
            type:"post",
            success:function(data){
                var orgId = data.corpOrgId;
                var orgNm = data.corpOrgNm;
                $("#crd_corp_org").append("<option value='" + orgId + "' selected>" +orgId+" -- "+orgNm+ "</option>");
            }
        });

        //查询所有的合作机构信息
        var rutCorpOrg = rowData.rutCorpOrg;
        $.ajax({
            url:"/cpt/cpiorg/info/findAllOrgnInfo",
            dataType: "json",
            type:"post",
            success:function(data){
                for (var one in data)
                {
                    var orgId = data[one].corpOrgId;
                    var orgNm = data[one].corpOrgNm;
                    $("#rut_corp_org").append("<option value='" + orgId + "'>" +orgId+" -- "+orgNm+ "</option>");
                    $("#rut_corp_org").append("<option value='" + orgId + "'>" +orgId+" -- "+orgNm+ "</option>");
                    if(orgId==rutCorpOrg){
                        $("#rut_corp_org").find("option:contains('"+rutCorpOrg+"')").attr("selected",true);
                    }
                }
            }
        });

        //显示模态框元素值
        $("#crd_corp_org").val(rowData.crdCorpOrg);
        $("#corp_bus_typ").val(rowData.corpBusTyp);
        $("#corp_bus_sub_typ").val(rowData.corpBusSubTyp);
        $("#rut_corp_org").val(rowData.rutCorpOrg);
        $("#crd_ac_typ").val(rowData.crdAcTyp);
        $("#rut_eff_flg").val(rowData.rutEffFlg);
        $("#pri_lvl").val(rowData.priLvl);
        $("#low_Amt").val(rowData.lowAmt);
        $("#high_amt").val(rowData.highAmt);
        $("#rmk").val(rowData.rmk);
        $("#rut_inf_id").val(rowData.rutInfId);
        $("#routeModal").modal("show");
    }

    //模态框，新增信息按钮
    function addDetail() {
        if(!judgeModalValues()){
            return;
        }
        //确认增加弹出框
        swal({
            title: $.i18n.t("cpi.addConfirmTitle"),
            text: $.i18n.t("cpi.addRouteText"),
            type: "warning",
            showCancelButton: true,
            cancelButtonText: $.i18n.t("cpi.swal-cancel"),
            confirmButtonColor: "#DD6B55",
            confirmButtonText: $.i18n.t("cpi.swal-confirm"),
            closeOnConfirm: false,
            closeOnCancel: true
        }, function() {
            var param = getModalValues();
            $.ajax({
                contentType: "application/json",
                url:"/cpt/cpiorg/route/add",
                dataType: "json",
                type: "post",
                data: JSON.stringify(param),
                success:function(data){
                    var msgCd = data.msgCd;
                    if(msgCd == "CPO00000") {
                        search();
                        $("#routeModal").modal("hide");
                        swal($.i18n.t("cpi.swal-success"), $.i18n.t("cpi.addConfirmSuss"), "success");
                    } else if(msgCd == "CPO30002") {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.corpOrgInfNotExists"), "error");
                    } else if(msgCd == "CPO30004") {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.corpOrgBusNotExists"), "error");
                    } else if(msgCd == "CPO30005") {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.corpOrgRouteExists"), "error");
                    } else {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.addConfirmFail"), "error");
                    }
                },
                error: function(){
                    swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.systemException"), "error");
                }
            });
        });
    }

    //模态框，修改信息按钮
    function modifyDetail() {
        if(!judgeModalValues()){
            return;
        }
        //确认修改弹出框
        swal({
            title: $.i18n.t("cpi.modifyConfirmTitle"),
            text: $.i18n.t("cpi.modifyRouteText"),
            type: "warning",
            showCancelButton: true,
            cancelButtonText: $.i18n.t("cpi.swal-cancel"),
            confirmButtonColor: "#DD6B55",
            confirmButtonText: $.i18n.t("cpi.swal-confirm"),
            closeOnConfirm: false,
            closeOnCancel: true
        }, function() {
            var param = getModalValues();
            $.ajax({
                contentType: "application/json",
                url: "/cpt/cpiorg/route/modify",
                data: JSON.stringify(param),
                dataType: "json",
                type: "post",
                success: function(data) {
                    var msgCd = data.msgCd;
                    if(msgCd == "CPO00000") {
                        search();
                        $("#routeModal").modal("hide");
                        swal($.i18n.t("cpi.swal-success"), $.i18n.t("cpi.modifyConfirmSuss"), "success");
                    } else if(msgCd == "CPO30002") {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.corpOrgInfNotExists"), "error");
                    } else if(msgCd == "CPO30004") {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.corpOrgBusNotExists"), "error");
                    } else {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.modifyConfirmFail"), "error");
                    }
                },
                error: function() {
                    swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.modifyConfirmFail"), "error");
                }
            });
        });
    }

    //根据业务类型，修改业务子类型下拉列表
    function changeCorpBusSubTyp() {
        var corp_bus_typ = $("#corp_bus_typ").val();
        $("#corp_bus_sub_typ").val("");
        $("#corp_bus_sub_typ").html("");
        $("#corp_bus_sub_typ").append("<option value=''>" + $.i18n.t("cpi.selectByNull") + "</option>");
        switch(corp_bus_typ) {
            case "01":
                $("#corp_bus_sub_typ").append("<option value='0101'>" + $.i18n.t("cpi.prefastSign") + "</option>");
                $("#corp_bus_sub_typ").append("<option value='0102'>" + $.i18n.t("cpi.fastSign") + "</option>");
                $("#corp_bus_sub_typ").append("<option value='0103'>" + $.i18n.t("cpi.fastUnsign") + "</option>");
                $("#corp_bus_sub_typ").append("<option value='0104'>" + $.i18n.t("cpi.withdrawSign") + "</option>");
                $("#corp_bus_sub_typ").append("<option value='0105'>" + $.i18n.t("cpi.withdrawUnsign") + "</option>");
                break;
            case "02":
                $("#corp_bus_sub_typ").append("<option value='0201'>" + $.i18n.t("cpi.fastpay") + "</option>");
                $("#corp_bus_sub_typ").append("<option value='0202'>" + $.i18n.t("cpi.fastpayConsume") + "</option>");
                break;
            case "03":
                $("#corp_bus_sub_typ").append("<option value='0301'>" + $.i18n.t("cpi.ebankpay") + "</option>");
                $("#corp_bus_sub_typ").append("<option value='0302'>" + $.i18n.t("cpi.ebankpayConsume") + "</option>");
                $("#corp_bus_sub_typ").append("<option value='0303'>" + $.i18n.t("cpi.ebankpayMercScan") + "</option>");
                $("#corp_bus_sub_typ").append("<option value='0304'>" + $.i18n.t("cpi.ebankpayUserScan") + "</option>");
                break;
            case "04":
                $("#corp_bus_sub_typ").append("<option value='0401'>" + $.i18n.t("cpi.remittance") + "</option>");
                $("#corp_bus_sub_typ").append("<option value='0402'>" + $.i18n.t("cpi.bussinessRemittance") + "</option>");
                break;
        }
    }

</script>
</body>

</html>