<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="nav.orgbusi.title"></title>
    <div th:replace="head"></div>
</head>

<body>
<div id="wrapper">
    <div th:replace="nav"></div>
    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>
        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="nav.orgbusi.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.cptmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.cpoorgmgr"></a>
                    </li>
                    <li>
                        <strong data-i18n="nav.orgbusi.content"></strong>
                    </li>
                </ol>
            </div>
        </div>

        <div class="wrapper wrapper-content animated fadeInRight">
            <!--查询条件-->
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form id="queryForm" class="form-horizontal">
                                <div class="form-group col-sm-4">
                                    <!--合作机构编号-->
                                    <label class="col-sm-4 control-label" for="corpOrgId" data-i18n="cpi.corpOrgId"></label>
                                    <div class="col-sm-8">
                                        <select name="corpOrgId" id="corpOrgId" class="form-control">
                                        </select>
                                    </div>
                                    <!--生效标志-->
                                    <label class="col-sm-4 control-label" for="busEffFlg" data-i18n="cpi.busEffFlg"></label>
                                    <div class="col-sm-8">
                                        <select name="busEffFlg" id="busEffFlg" class="form-control">
                                            <option value="" data-i18n="cpi.selectByNull" selected></option>
                                            <option value="0" data-i18n="cpi.busEffFlg-0"></option>
                                            <option value="1" data-i18n="cpi.busEffFlg-1"></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group col-sm-4">
                                    <!--业务类型-->
                                    <label class="col-sm-4 control-label" for="corpBusTyp" data-i18n="cpi.corpBusTyp"></label>
                                    <div class="col-sm-8">
                                        <select name="corpBusTyp" id="corpBusTyp" class="form-control">
                                            <option value="" data-i18n="cpi.selectByNull" selected></option>
                                            <option value="01" data-i18n="cpi.txTyp-sign"></option>
                                            <option value="02" data-i18n="cpi.txTyp-fastpay"></option>
                                            <option value="03" data-i18n="cpi.txTyp-ebankpay"></option>
                                            <option value="04" data-i18n="cpi.txTyp-remittance"></option>
                                        </select>
                                    </div>
                                    <!--业务子类型-->
                                    <label class="col-sm-4 control-label" for="corpBusSubTyp" data-i18n="cpi.corpBusSubTyp"></label>
                                    <div class="col-sm-8">
                                        <select name="corpBusSubTyp" id="corpBusSubTyp" class="form-control">
                                            <option value="" data-i18n="cpi.selectByNull" selected></option>
                                            <option value="0101" data-i18n="cpi.prefastSign"></option>
                                            <option value="0102" data-i18n="cpi.fastSign"></option>
                                            <option value="0103" data-i18n="cpi.fastUnsign"></option>
                                            <option value="0104" data-i18n="cpi.withdrawSign"></option>
                                            <option value="0105" data-i18n="cpi.withdrawUnsign"></option>
                                            <option value="0201" data-i18n="cpi.fastpay"></option>
                                            <option value="0202" data-i18n="cpi.fastpayConsume"></option>
                                            <option value="0301" data-i18n="cpi.ebankpay"></option>
                                            <option value="0302" data-i18n="cpi.ebankpayConsume"></option>
                                            <option value="0303" data-i18n="cpi.ebankpayMercScan"></option>
                                            <option value="0304" data-i18n="cpi.ebankpayUserScan"></option>
                                            <option value="0401" data-i18n="cpi.remittance"></option>
                                            <option value="0402" data-i18n="cpi.bussinessRemittance"></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-4 col-sm-offset-1">
                                        <button type="button" id="searchBtn" class="btn btn-primary" onclick="search()" data-i18n="cpi.searchList"></button>
                                        <button type="button" id="addBtn" class="btn btn-primary" data-toggle="modal" onclick="addModal()" data-i18n="cpi.addModal"></button>
                                        <button type="button" id="modifyBtn" class="btn btn-primary" data-toggle="modal" onclick="modifyModal()" data-i18n="cpi.modifyModal"></button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <div th:replace="cpt/cpiorg/busi/busiModal"></div>

            <!--数据表格-->
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <div class="table-responsive">
                                <table id="orgnBusi" class="table table-striped table-bordered table-hover">
                                    <thead>
                                        <tr>
                                            <th data-i18n="cpi.corpOrgId"></th>
                                            <th data-i18n="cpi.corpBusTyp"></th>
                                            <th data-i18n="cpi.corpBusSubTyp"></th>
                                            <th data-i18n="cpi.busEffFlg"></th>
                                            <th data-i18n="cpi.orgBusId"></th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>
    </div>
</div>

<div th:replace="script"></div>

<script>
    var editor;
    var table;

    $(document).ready(function () {
        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        i18nLoad.then(function () {
            editor = new $.fn.dataTable.Editor({
                table: "#orgnBusi",
                idSrc: 'orgBusId',
                fields: [
                    {name: "id", type: "hidden"},
                    {label: $.i18n.t("cpi.corpOrgId"), name: "corpOrgId"},
                    {label: $.i18n.t("cpi.corpBusTyp"), name: "corpBusTyp"},
                    {label: $.i18n.t("cpi.corpBusSubTyp"), name: "corpBusSubTyp"},
                    {label: $.i18n.t("cpi.busEffFlg"), name: "busEffFlg"},
                    {label: $.i18n.t("cpi.orgBusId"), name: "orgBusId"}
                ]
            });

            table = $('#orgnBusi').DataTable({
                dom: 'Blfrtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/cpt/cpiorg/busi/findAll',
                    type: 'post',
                    data: function (d) {
                        d.extra_search = {
                            "corpOrgId" : $("#corpOrgId").val(),
                            "corpBusTyp" : $("#corpBusTyp").val(),
                            "corpBusSubTyp" : $("#corpBusSubTyp").val(),
                            "busEffFlg" : $("#busEffFlg").val()
                        };
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
                language: {
                    url: languageUrl
                },
                select: true,
                buttons: [
//                        {extend: "create", editor: editor},
//                        {extend: "edit", editor: editor},
//                        {extend: "remove", editor: editor},
//                        {extend: 'copyHtml5'},
//                        {extend: 'csvHtml5'},
//                        {extend: 'excelHtml5', title: '文件名'},
                    'copy',
                    'csv',
                    'excel',
//                        'pdf',
//                        'print'
                ],
                columns: [{
                    data: 'corpOrgId'
                },{
                    data: 'corpBusTyp',
                    render: function (data, type, row) {
                        switch (data) {
                            case "01":
                                return $.i18n.t("cpi.txTyp-sign");
                            case "02":
                                return $.i18n.t("cpi.txTyp-fastpay");
                            case "03":
                                return $.i18n.t("cpi.txTyp-ebankpay");
                            case "04":
                                return $.i18n.t("cpi.txTyp-remittance");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'corpBusSubTyp',
                    render: function (data, type, row) {
                        switch (data) {
                            case "0101":
                                return $.i18n.t("cpi.prefastSign");
                            case "0102":
                                return $.i18n.t("cpi.fastSign");
                            case "0103":
                                return $.i18n.t("cpi.fastUnsign");
                            case "0104":
                                return $.i18n.t("cpi.withdrawSign");
                            case "0105":
                                return $.i18n.t("cpi.withdrawUnsign");
                            case "0201":
                                return $.i18n.t("cpi.fastpay");
                            case "0202":
                                return $.i18n.t("cpi.fastpayConsume");
                            case "0301":
                                return $.i18n.t("cpi.ebankpay");
                            case "0302":
                                return $.i18n.t("cpi.ebankpayConsume");
                            case "0303":
                                return $.i18n.t("cpi.ebankpayMercScan");
                            case "0304":
                                return $.i18n.t("cpi.ebankpayUserScan");
                            case "0401":
                                return $.i18n.t("cpi.remittance");
                            case "0402":
                                return $.i18n.t("cpi.bussinessRemittance");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'busEffFlg',
                    render: function (data, type, row) {
                        switch (data) {
                            case "0":
                                return $.i18n.t("cpi.busEffFlg-0");
                            case "1":
                                return $.i18n.t("cpi.busEffFlg-1");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'orgBusId'
                }]
            });
        });

        //查询所有的合作机构信息
        $.ajax({
            url:"/cpt/cpiorg/info/findAllOrgnInfo",
            dataType: "json",
            type:"post",
            success:function(data){
                $("#corpOrgId").append("<option value=''>" + $.i18n.t("cpi.selectByNull") + "</option>");
                for (var one in data)
                {
                    var orgId = data[one].corpOrgId;
                    var orgNm = data[one].corpOrgNm;
                    $("#corpOrgId").append("<option value='" + orgId + "'>" +orgId+" -- "+orgNm+ "</option>");
                }
            }
        });
    });

    <!--查询按钮-->
    function search() {
        table.ajax.reload();
    }

    <!--清除modal中的输入框的值-->
    function clearModalValues() {
        $("#corp_org_id").val("");
        $("#corp_org_id").html("");
        $("#corp_bus_typ").val("");
        $("#corp_bus_sub_typ").val("");
        $("#bus_eff_flg").val("");
        $("#org_bus_id").val("");
    }

    //模态框，非空数据判断
    function judgeModalValues() {
        if($("#corp_org_id").val() == null || $("#corp_org_id").val() == "") {
            swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.corpOrgIdNull"), "error");
            return false;
        }
        if($("#corp_bus_typ").val() == null || $("#corp_bus_typ").val() == "") {
            swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.corpBusTypNull"), "error");
            return false;
        }
        if($("#corp_bus_sub_typ").val() == null || $("#corp_bus_sub_typ").val() == "") {
            swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.corpBusSubTypNull"), "error");
            return false;
        }
        if($("#bus_eff_flg").val() == null || $("#bus_eff_flg").val() == "") {
            swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.busEffFlgNull"), "error");
            return false;
        }
        return true;
    }

    //模态框，获取元素值
    function getModalValues() {
        var corpOrgId = $("#corp_org_id").val();
        var corpBusTyp = $("#corp_bus_typ").val();
        var corpBusSubTyp = $("#corp_bus_sub_typ").val();
        var busEffFlg = $("#bus_eff_flg").val();
        var orgBusId = $("#org_bus_id").val();
        var param = {
            "corpOrgId" : corpOrgId,
            "corpBusTyp" : corpBusTyp,
            "corpBusSubTyp" : corpBusSubTyp,
            "busEffFlg" : busEffFlg,
            "orgBusId" : orgBusId
        };
        return param;
    }

    //主页新增记录按钮
    function addModal() {
        clearModalValues();
        $("#addDetail").show();
        $("#modifyDetail").hide();
        $("#corp_org_id").removeAttr("disabled");
        $("#corp_bus_typ").removeAttr("disabled");
        $("#corp_bus_sub_typ").removeAttr("disabled");
        $("#busiModal").modal("show");

        //查询所有的合作机构信息
        $.ajax({
            url:"/cpt/cpiorg/info/findAllOrgnInfo",
            dataType: "json",
            type:"post",
            success:function(data){
                for (var one in data)
                {
                    var orgId = data[one].corpOrgId;
                    var orgNm = data[one].corpOrgNm;
                    $("#corp_org_id").append("<option value='" + orgId + "'>" +orgId+" -- "+orgNm+ "</option>");
                }
            }
        });
    }

    //主页修改记录按钮
    function modifyModal() {
        clearModalValues();
        $("#addDetail").hide();
        $("#modifyDetail").show();
        $("#corp_org_id").attr("disabled", "disabled");
        $("#corp_bus_typ").attr("disabled", "disabled");
        $("#corp_bus_sub_typ").attr("disabled", "disabled");

        <!--获取表格中选中的单行-->
        var row = table.row('.selected');
        if(row.length == 0) {
            swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.shouldSelectOne"), "error");
            return;
        }

        <!--获取选中行的内容-->
        var rowData = table.row(row).data();
        $("#corp_org_id").val(rowData.corpOrgId);
        $("#corp_bus_typ").val(rowData.corpBusTyp);
        $("#corp_bus_sub_typ").val(rowData.corpBusSubTyp);
        $("#bus_eff_flg").val(rowData.busEffFlg);
        $("#org_bus_id").val(rowData.orgBusId);
        $("#busiModal").modal("show");

        //根据机构编号，查询合作机构信息
        var corpOrgId = rowData.corpOrgId;
        $.ajax({
            url:"/cpt/cpiorg/info/findOrgnInfo",
            data: {
                "corpOrgId" : corpOrgId
            },
            dataType: "json",
            type:"post",
            success:function(data){
                var orgId = data.corpOrgId;
                var orgNm = data.corpOrgNm;
                $("#corp_org_id").append("<option value='" + orgId + "' selected>" +orgId+" -- "+orgNm+ "</option>");
            }
        });
    }

    //模态框，新增信息按钮
    function addDetail() {
        if(!judgeModalValues()){
            return;
        }
        //确认增加弹出框
        swal({
            title: $.i18n.t("cpi.addConfirmTitle"),
            text: $.i18n.t("cpi.addBusinessText"),
            type: "warning",
            showCancelButton: true,
            cancelButtonText: $.i18n.t("cpi.swal-cancel"),
            confirmButtonColor: "#DD6B55",
            confirmButtonText: $.i18n.t("cpi.swal-confirm"),
            closeOnConfirm: false,
            closeOnCancel: true
        }, function() {
            var param = getModalValues();
            $.ajax({
                contentType: 'application/json',
                url:"/cpt/cpiorg/busi/add",
                dataType: "json",
                type: "post",
                data: JSON.stringify(param),
                success:function(data){
                    var msgCd = data.msgCd;
                    if(msgCd == "CPO00000") {
                        search();
                        $("#busiModal").modal("hide");
                        swal($.i18n.t("cpi.swal-success"), $.i18n.t("cpi.addConfirmSuss"), "success");
                    } else if(msgCd == "CPO30002") {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.corpOrgInfNotExists"), "error");
                    } else if(msgCd == "CPO30003") {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.corpOrgBusExists"), "error");
                    } else {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.addConfirmFail"), "error");
                    }
                },
                error: function(){
                    swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.systemException"), "error");
                }
            });
        });
    }

    //模态框，修改信息按钮
    function modifyDetail() {
        if(!judgeModalValues()){
            return;
        }
        //确认修改弹出框
        swal({
            title: $.i18n.t("cpi.modifyConfirmTitle"),
            text: $.i18n.t("cpi.modifyBusinessText"),
            type: "warning",
            showCancelButton: true,
            cancelButtonText: $.i18n.t("cpi.swal-cancel"),
            confirmButtonColor: "#DD6B55",
            confirmButtonText: $.i18n.t("cpi.swal-confirm"),
            closeOnConfirm: false,
            closeOnCancel: true
        }, function() {
            var param = getModalValues();
            $.ajax({
                contentType: 'application/json',
                url: "/cpt/cpiorg/busi/modify",
                data: JSON.stringify(param),
                dataType: "json",
                type: "post",
                success: function(data) {
                    var msgCd = data.msgCd;
                    if (msgCd == "CPO00000") {
                        search();
                        $("#busiModal").modal("hide");
                        swal($.i18n.t("cpi.swal-success"), $.i18n.t("cpi.modifyConfirmSuss"), "success");
                    } else if(msgCd == "CPO30002") {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.corpOrgInfNotExists"), "error");
                    } else {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.modifyConfirmFail"), "error");
                    }
                },
                error: function() {
                    swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.systemException"), "error");
                }
            });
        });
    }

    //根据业务类型，修改业务子类型下拉列表
    function changeCorpBusSubTyp() {
        var corp_bus_typ = $("#corp_bus_typ").val();
        $("#corp_bus_sub_typ").val("");
        $("#corp_bus_sub_typ").html("");
        $("#corp_bus_sub_typ").append("<option value=''>" + $.i18n.t("cpi.selectByNull") + "</option>");
        switch(corp_bus_typ) {
            case "01":
                $("#corp_bus_sub_typ").append("<option value='0101'>" + $.i18n.t("cpi.prefastSign") + "</option>");
                $("#corp_bus_sub_typ").append("<option value='0102'>" + $.i18n.t("cpi.fastSign") + "</option>");
                $("#corp_bus_sub_typ").append("<option value='0103'>" + $.i18n.t("cpi.fastUnsign") + "</option>");
                $("#corp_bus_sub_typ").append("<option value='0104'>" + $.i18n.t("cpi.withdrawSign") + "</option>");
                $("#corp_bus_sub_typ").append("<option value='0105'>" + $.i18n.t("cpi.withdrawUnsign") + "</option>");
                break;
            case "02":
                $("#corp_bus_sub_typ").append("<option value='0201'>" + $.i18n.t("cpi.fastpay") + "</option>");
                $("#corp_bus_sub_typ").append("<option value='0202'>" + $.i18n.t("cpi.fastpayConsume") + "</option>");
                break;
            case "03":
                $("#corp_bus_sub_typ").append("<option value='0301'>" + $.i18n.t("cpi.ebankpay") + "</option>");
                $("#corp_bus_sub_typ").append("<option value='0302'>" + $.i18n.t("cpi.ebankpayConsume") + "</option>");
                $("#corp_bus_sub_typ").append("<option value='0303'>" + $.i18n.t("cpi.ebankpayMercScan") + "</option>");
                $("#corp_bus_sub_typ").append("<option value='0304'>" + $.i18n.t("cpi.ebankpayUserScan") + "</option>");
                break;
            case "04":
                $("#corp_bus_sub_typ").append("<option value='0401'>" + $.i18n.t("cpi.remittance") + "</option>");
                $("#corp_bus_sub_typ").append("<option value='0402'>" + $.i18n.t("cpi.bussinessRemittance") + "</option>");
                break;
        }
    }

</script>
</body>

</html>