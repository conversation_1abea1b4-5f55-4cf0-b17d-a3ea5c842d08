<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="title.title"></title>
    <div th:replace="head"></div>
</head>

<body>
<div id="wrapper">
    <div th:replace="nav"></div>
    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>
        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="settle.head"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="settle.firstLevel"></a>
                    </li>
                    <li>
                        <a data-i18n="settle.secondLevel"></a>
                    </li>
                    <li>
                        <strong data-i18n="settle.thirtLevel"></strong>
                    </li>
                </ol>
            </div>
        </div>

        <div class="wrapper wrapper-content animated fadeInRight">
            <!--查询条件-->
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form id="queryForm" class="form-horizontal">
                                <div class="form-group col-sm-12">

                                    <!--结算日期-->
                                    <label class="col-sm-1 control-label" for="beginDate" data-i18n="settle.settlesub.settleDate"></label>
                                    <div class="col-sm-2">
                                        <input name="beginDate" id="beginDate" class="form-control" value=""/>
                                    </div>
                                    <div class="col-sm-2">
                                        <input name="endDate" id="endDate" class="form-control" value=""/>
                                    </div>
                                    <!--是否结算-->
                                    <label class="col-sm-1 control-label" for="stlFlg" data-i18n="settle.settlesub.stlFlg"></label>
                                    <div class="col-sm-2">
                                        <select name="stlFlg" id="stlFlg" class="form-control" value="">
                                            <option data-i18n="settle.selectByNull" selected value=""></option>
                                            <option data-i18n="settle.agrEffFlg-Y" value="1"></option>
                                            <option data-i18n="settle.agrEffFlg-N" value="2"></option>
                                        </select>
                                    </div>
                                    <!--路径机构-->
                                    <label class="col-sm-1 control-label" for="rutCorg" data-i18n="settle.settlesub.rutCorg"></label>
                                    <div class="col-sm-2">
                                        <select name="rutCorg" id="rutCorg" class="form-control" value="">
                                            <option value="" data-i18n="settle.selectByNull" selected></option>
                                            <!--<option value="ICBC" data-i18n="corg.ICBC"></option>-->
                                            <!--<option value="ABC" data-i18n="corg.ABC"></option>-->
                                            <!--<option value="CCB" data-i18n="corg.CCB"></option>-->
                                            <!--<option value="BCM" data-i18n="corg.BCM"></option>-->
                                            <!--<option value="BOC" data-i18n="corg.BOC"></option>-->
                                            <!--<option value="BEA" data-i18n="corg.BEA"></option>-->
                                            <option value="WeChat" data-i18n="corg.WeChat"></option>
                                            <!--<option value="BESTPAY" data-i18n="corg.BESTPAY"></option>-->
                                            <!--<option value="CBP" data-i18n="corg.CBP"></option>-->
                                            <!--<option value="ABA" data-i18n="corg.ABA"></option>-->
                                            <!--<option value="ACLEDA" data-i18n="corg.ACLEDA"></option>-->
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-4 col-sm-offset-1">
                                        <button type="button" id="searchBtn" onclick="search()" class="btn btn-primary" data-i18n="cpi.search"></button>
                                        <button type="button" id="refreshBtn" onclick="refresh()" class="btn btn-primary" data-i18n="cpi.refresh"></button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>



            <!--数据表格-->
            <div class="row">
                <div class="col-lg-22">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <div class="table-responsive">
                                <table id="settleInf" class="table table-striped table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th data-i18n="settle.settlesub.settleDate"></th>
                                        <th data-i18n="settle.settlesub.updateDate"></th>
                                        <th data-i18n="settle.settlesub.updateTime"></th>
                                        <th data-i18n="settle.settlesub.stlFlg"></th>
                                        <th data-i18n="settle.settlesub.rutCorg"></th>
                                       <!-- <th data-i18n="settle.settlesub.appid"></th>-->
                                        <th data-i18n="settle.settlesub.mchId"></th>
                                        <th data-i18n="settle.settlesub.settleFeeType"></th>
                                        <th data-i18n="settle.settlesub.totSettleFee"></th>
                                        <th data-i18n="settle.settlesub.totUnsettleFee"></th>

                                        <!--<th data-i18n="settle.settlesub.subMchId"></th>-->

                                       <!-- <th data-i18n="settlesettlesub..payBatchNo"></th>-->

                                       <!-- <th data-i18n="settle.settlesub.settleDatetime"></th>-->
                                       <!-- <th data-i18n="settle.settlesub.startDate"></th>
                                        <th data-i18n="settle.settlesub.endDate"></th>-->
                                        <th data-i18n="settle.settlesub.settleFee"></th>
                                        <th data-i18n="settle.settlesub.unsettleFee"></th>
                                        <th data-i18n="settle.settlesub.payFee"></th>
                                        <th data-i18n="settle.settlesub.refundFee"></th>
                                        <th data-i18n="settle.settlesub.payNetFee"></th>
                                        <th data-i18n="settle.settlesub.poundageFee"></th>


                                        <th data-i18n="settle.settlesub.payBatchNo"></th>

                                    </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

</div>

<div th:replace="script"></div>

<!-- Page-Level Scripts -->
<script>
    var editor;
    var table;
    $(document).ready(function () {
        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        i18nLoad.then(function () {
            /*editor = new $.fn.dataTable.Editor({
                table: "#settleInf",
                idSrc: 'jrnNo',
                fields: [
//                    {label: $.i18n.t("settle.settlesub.payBatchNo"), name: "payBatchNo"},
                    {label: $.i18n.t("settle.settlesub.settleDate"), name: "settleDate"},
                    {label: $.i18n.t("settle.settlesub.settleDatetime"), name: "settleDatetime"},
//                    {label: $.i18n.t("settle.settlesub.startDate"), name: "startDate"},
//                    {label: $.i18n.t("settle.settlesub.endDate"), name: "endDate"},
                    {label: $.i18n.t("settle.settlesub.settleFee"), name: "settleFee"},
                    {label: $.i18n.t("settle.settlesub.unsettleFee"), name: "unsettleFee"},
                    {label: $.i18n.t("settle.settlesub.settleFeeType"), name: "settleFeeType"},
                    {label: $.i18n.t("settle.settlesub.refundFee"), name: "refundFee"},
                    {label: $.i18n.t("settle.settlesub.payNetFee"), name: "payNetFee"},
                    {label: $.i18n.t("settle.settlesub.poundageFee"), name: "poundageFee"},
                    {label: $.i18n.t("settle.settlesub.totSettleFee"), name: "totSettleFee"},
                    {label: $.i18n.t("settle.settlesub.totUnsettleFee"), name: "totUnsettleFee"},
                    {label: $.i18n.t("settle.settlesub.appid"), name: "appid"},
                    {label: $.i18n.t("settle.settlesub.mchId"), name: "mchId"},
                    /!*{label: $.i18n.t("settle.settlesub.subMchId"), name: "subMchId"},*!/
                    {label: $.i18n.t("settle.settlesub.stlFlg"), name: "stlFlg"},
                    {label: $.i18n.t("settle.settlesub.rutCorg"), name: "rutCorg"}
                ]
            });*/

            table = $('#settleInf').DataTable({
                dom: 'Blfrtip',
                columnDefs: [{
                    targets:[0,1,2,9,10],//指定哪几列
                    render: function(data){
                        return "\u200C" + data;
                    }
                }],
                ajax: {
                    contentType: 'application/json',
                    url: '/cpt/payment/settle/findAll',
                    type: 'post',
                    data: function (d) {
                        d.extra_search = {
                            "stlFlg" : $("#stlFlg").val(),
                            "rutCorg" : $("#rutCorg").val(),
                            "beginDate" : $("#beginDate").val(),
                            "endDate" : $("#endDate").val()
                        };
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
                language: {
                    url: languageUrl
                },
                select: true,
                buttons: [
//                        {extend: "create", editor: editor},
//                        {extend: "edit", editor: editor},
//                        {extend: "remove", editor: editor},
                    {extend: 'copyHtml5'},
                    {extend: 'csvHtml5'},
                    {extend: 'excelHtml5', title: '通道结算明细列表'},
//                        'copy',
//                        'csv',
//                        'excel',
//                        'pdf',
//                        'print'
                ],
                columns: [{
                    data: 'settleDate',
                },{
                    data: 'updateDate',
                },{
                    data: 'updateTime',
                },{
                    data: 'stlFlg',
                    render: function (data, type, row) {
                        switch (data) {
                            case "1":
                                return $.i18n.t("settle.agrEffFlg-Y");
                            case "2":
                                return $.i18n.t("settle.agrEffFlg-N");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'rutCorg',
                    render: function (data, type, row) {
                        switch (data) {
                            case "ICBC":
                                return $.i18n.t("corg.ICBC");
                            case "CMBC":
                                return $.i18n.t("corg.CMBC");
                            case "CMB":
                                return $.i18n.t("corg.CMB");
                            case "BEA":
                                return $.i18n.t("corg.BEA");
                            case "BESTPAY":
                                return $.i18n.t("corg.BESTPAY");
                            case "WeChat":
                                return $.i18n.t("corg.WeChat");
                            case "CBP":
                                return $.i18n.t("corg.CBP");
                            case "ABA":
                                return $.i18n.t("corg.ABA");
                            case "ACLEDA":
                                return $.i18n.t("corg.ACLEDA");
                            default:
                                return data;
                        }
                    }
                },
//                    {
//                    data: 'appid',
//                },
                    {
                    data: 'mchId',
                },
                    {
                    data: 'settleFeeType',
                    render: function (data, type, row) {
                        switch (data) {
                            case "CNY":
                                return $.i18n.t("CCY.CNY");
                            case "USD":
                                return $.i18n.t("CCY.USD");
                            case "KHR":
                                return $.i18n.t("CCY.KHR");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'totSettleFee',
                },{
                    data: 'totUnsettleFee',
                },
//                    {
//                    data: 'subMchId',
//                },

//                    {
//                    data: 'payBatchNo',
//                },

//                    {
//                    data: 'settleDatetime',
//                },
//                    {
//                    data: 'startDate',
//                },{
//                    data: 'endDate',
//                },
                    {
                    data: 'settleFee',
                },{
                    data: 'unsettleFee',
                },{
                    data: 'payFee',
                 },{
                    data: 'refundFee',
                },{
                    data: 'payNetFee',
                },{
                    data: 'poundageFee',
                },{
                    data: 'payBatchNo',
                }]
            });
        });

        //查看汇款凭证图片，设置zIndex:9999，显示
        $('#pic_url').viewer({zIndex:9999});

        //设置模态框backdrop:static时,空白处不关闭；keyboard:false时,esc键盘不关闭
        $('#handleModal').modal({backdrop: "static", keyboard: false, show: false});
    });

    <!--查询按钮-->
    function search() {
        table.ajax.reload();
    }

    <!--初始化日期控件-->
    var beginTimePick = $('#beginDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        onChangeDate: function(dateText, inst) {
            endTimePick.datetimepicker('minDate',new Date(dateText.replace("-", "-")));
        }
    });
    var endTimePick = $('#endDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d'
        // maxDate:'+1970/01/01',
    });

    //清空 handleModal 模态框中元素的值
    function clearModalValues() {
        $("#mbl_no").val("");
        $("#user_id").val("");
        $("#ord_dt").val("");
        $("#ord_tm").val("");
        $("#ord_sts").val("");
        $("#rut_corp_corg").val("");
        $("#crd_corp_corg").val("");
        $("#handleReason").val("");
        $("#rmk").val("");
        $("#ord_amt").val("");
        $("#pic_url").removeAttr("");
    }

    //刷新，查询商户结算信息
    function refresh() {
        //确认提交弹出框
        swal({
            title: $.i18n.t(""),
            text: $.i18n.t("cpi.refreshConfirmText"),
            type: "warning",
            showCancelButton: true,
            cancelButtonText: $.i18n.t("cpi.swal-cancel"),
            confirmButtonColor: "#DD6B55",
            confirmButtonText: $.i18n.t("cpi.swal-confirm"),
            closeOnConfirm: true
        }, function () {
            <!--隐藏模态框-->
            var stlFlg = $("#stlFlg").val();
            var rutCorg = $("#rutCorg").val();
            var startDate = $("#beginDate").val();
            var endDate = $("#endDate").val();
            if(startDate != ""){
                startDate = $.trim(startDate);
            }
            if(endDate != ""){
                endDate = $.trim(endDate);
            }
            $.ajax({
                url:"/cpt/payment/settle/refresh",
                data:{
                    "stlFlg": stlFlg,
                    "rutCorg" : rutCorg,
                    "startDate" : startDate,
                    "endDate" : endDate
                },
                dataType: "json",
                type:"post",
                success: function(data) {
                    var msgCd = data.msgCd;

                    if (msgCd == "0") {
                        swal($.i18n.t("cpi.swal-success"), $.i18n.t("cpi.refreshConfirmSuss"), "success");
                        table.ajax.reload();
                    } else {
                        swal($.i18n.t("cpi.swal-fail"), msgCd + $.i18n.t("cpi.refreshConfirmFail"), "error");
                    }
                },
                error: function() {
                    swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.systemException"), "error");
                    $("#handleModal").modal("hide");
                }
            });
        });
    }


</script>
</body>

</html>