<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title>手续费查询</title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>

            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2>手续费查询</h2>
                    <ol class="breadcrumb">
                        <li>
                            <a>支付管理</a>
                        </li>
                        <li>
                            <a>参数管理</a>
                        </li>
                        <li class="active">
                            <strong>手续费查询</strong>
                        </li>
                    </ol>
                </div>
                <div class="col-lg-2">
                    <div class="btn-group pull-right" style="margin-top: 30px;">
                        <button type="button" class="btn btn-primary btn-sm" id="btnAdd">
                            <i class="fa fa-plus"></i> <span>新增</span>
                        </button>
                    </div>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!-- 查询表单 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12">
                                        <!-- 订单号 -->
                                        <label class="col-sm-2 control-label" for="searchOrderNo">订单号</label>
                                        <div class="col-sm-4">
                                            <input name="orderNo" id="searchOrderNo" class="form-control" value="" />
                                        </div>
                                        <!-- 业务订单号 -->
                                        <label class="col-sm-2 control-label" for="searchBusOrderNo">业务订单号</label>
                                        <div class="col-sm-4">
                                            <input name="busOrderNo" id="searchBusOrderNo" class="form-control"
                                                value="" />
                                        </div>
                                    </div>
                                    <div class="form-group col-sm-12">
                                        <!-- 用户ID -->
                                        <label class="col-sm-2 control-label" for="searchUserId">用户ID</label>
                                        <div class="col-sm-4">
                                            <input name="userId" id="searchUserId" class="form-control" value="" />
                                        </div>
                                        <!-- 用户名称 -->
                                        <label class="col-sm-2 control-label" for="searchUserName">用户名称</label>
                                        <div class="col-sm-4">
                                            <input name="userName" id="searchUserName" class="form-control" value="" />
                                        </div>
                                    </div>
                                    <div class="form-group col-sm-12">
                                        <!-- 业务类型 -->
                                        <label class="col-sm-2 control-label" for="searchBusType">业务类型</label>
                                        <div class="col-sm-4">
                                            <input name="busType" id="searchBusType" class="form-control" value="" />
                                        </div>
                                        <!-- 币种 -->
                                        <label class="col-sm-2 control-label" for="searchCcy">币种</label>
                                        <div class="col-sm-4">
                                            <input name="ccy" id="searchCcy" class="form-control" value="" />
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-sm-4 col-sm-offset-1">
                                            <button type="button" id="searchBtn" class="btn btn-primary"
                                                onclick="search()">查询</button>
                                            <button type="button" id="resetBtn" class="btn btn-default"
                                                onclick="resetForm()">重置</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="feeOrderTable" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th>订单号</th>
                                                <th>业务订单号</th>
                                                <th>业务类型</th>
                                                <th>用户ID</th>
                                                <th>用户名称</th>
                                                <th>币种</th>
                                                <th>手续费</th>
                                                <th>交易金额</th>
                                                <th>费率</th>
                                                <th>创建时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>
    <!-- 新
增/编辑模态框 -->
    <div class="modal fade" id="feeOrderModal" tabindex="-1" role="dialog" aria-labelledby="feeOrderModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="feeOrderModalLabel">新增手续费订单</h4>
                </div>
                <div class="modal-body">
                    <form id="feeOrderForm" class="form-horizontal">
                        <input type="hidden" id="orderNo" name="orderNo" />
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="busOrderNo">业务订单号</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="busOrderNo" name="busOrderNo" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="busType">业务类型</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="busType" name="busType" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="userId">用户ID</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="userId" name="userId" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="userName">用户名称</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="userName" name="userName" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="ccy">币种</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="ccy" name="ccy" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="fee">手续费</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="fee" name="fee" type="number" step="0.01" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="tradeAmt">交易金额</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="tradeAmt" name="tradeAmt" type="number" step="0.01"
                                    required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="rate">费率</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="rate" name="rate" type="number" step="0.0001" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="calculateMod">计算模式</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="calculateMod" name="calculateMod" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="calculateType">计算类型</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="calculateType" name="calculateType" />
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="btnSave">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 查看详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="detailModalLabel">手续费订单详情</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">订单号</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailOrderNo"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">业务订单号</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailBusOrderNo"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">业务类型</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailBusType"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">业务类型描述</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailBusTypeDesc"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">用户ID</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUserId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">用户名称</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUserName"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">币种</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailCcy"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">手续费</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailFee"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">交易金额</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailTradeAmt"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">交易总金额</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailTradeTotalAmt"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">费率</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailRate"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">计算模式</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailCalculateMod"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">计算类型</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailCalculateType"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">清算状态</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailClearStats"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">交易日期</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailTradeDate"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">交易时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailTradeTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">业务订单时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailBusOrderTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">创建时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailCreateTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">修改时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailModifyTime"></p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var table;
        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/js/Chinese.json';
                    break;
                case 'en':
                    languageUrl = '/js/English.json';
                    break;
                default:
                    languageUrl = '/js/Chinese.json';
            }

            // 初始化DataTables
            table = $('#feeOrderTable').DataTable({
                dom: 'Blfrtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/cpt/param/feeorder/findAll',
                    type: 'post',
                    data: function (d) {
                        // 添加额外的查询参数
                        d.extra_search = {
                            "orderNo": $("#searchOrderNo").val() || "",
                            "busOrderNo": $("#searchBusOrderNo").val() || "",
                            "userId": $("#searchUserId").val() || "",
                            "userName": $("#searchUserName").val() || "",
                            "busType": $("#searchBusType").val() || "",
                            "ccy": $("#searchCcy").val() || ""
                        };
                        return JSON.stringify(d);
                    },
                    dataSrc: function (json) {
                        // 确保返回的是数组
                        return json.data || [];
                    },
                    error: function (xhr, error, thrown) {
                        console.error('DataTables AJAX error:', error, thrown);
                        toastr.error('加载数据失败，请刷新页面重试');
                    }
                },
                serverSide: true,
                searching: false, // 禁用内置搜索
                searchDelay: 1000,
                responsive: true,
                processing: true,
                language: {
                    url: languageUrl
                },
                columns: [
                    { data: 'orderNo' },
                    { data: 'busOrderNo' },
                    {
                        data: 'busTypeDesc',
                        render: function (data, type, row) {
                            return data || row.busType || '';
                        }
                    },
                    { data: 'userId' },
                    { data: 'userName' },
                    { data: 'ccy' },
                    {
                        data: 'fee',
                        render: function (data) {
                            return data ? parseFloat(data).toFixed(2) : '0.00';
                        }
                    },
                    {
                        data: 'tradeAmt',
                        render: function (data) {
                            return data ? parseFloat(data).toFixed(2) : '0.00';
                        }
                    },
                    {
                        data: 'rate',
                        render: function (data) {
                            return data ? parseFloat(data).toFixed(4) : '0.0000';
                        }
                    },
                    {
                        data: 'createTime',
                        render: function (data) {
                            if (!data) return '';
                            return new Date(data).toLocaleString();
                        }
                    },
                    {
                        data: null,
                        orderable: false,
                        render: function (data, type, row) {
                            var buttons = '<button type="button" class="btn btn-xs btn-info view-detail" data-id="' + row.orderNo + '">详情</button> ';
                            buttons += '<button type="button" class="btn btn-xs btn-primary btn-edit" data-id="' + row.orderNo + '">编辑</button> ';
                            buttons += '<button type="button" class="btn btn-xs btn-danger btn-delete" data-id="' + row.orderNo + '">删除</button>';
                            return buttons;
                        }
                    }
                ],
                buttons: [
                    { extend: 'copyHtml5' },
                    { extend: 'csvHtml5' },
                    { extend: 'excelHtml5', title: '手续费订单列表' }
                ]
            });

            // 新增按钮
            $("#btnAdd").click(function () {
                $("#feeOrderModalLabel").text("新增手续费订单");
                $("#feeOrderForm")[0].reset();
                $("#orderNo").val("");
                $("#feeOrderModal").modal("show");
            });

            // 保存按钮
            $("#btnSave").click(function () {
                if (!$("#feeOrderForm").valid()) {
                    toastr.error('请填写必填项');
                    return;
                }

                var orderNo = $("#orderNo").val();
                var formData = $("#feeOrderForm").serialize();
                var url = orderNo ? "/cpt/param/feeorder/modify/" + orderNo : "/cpt/param/feeorder/add";

                $.ajax({
                    url: url,
                    type: "POST",
                    data: formData,
                    success: function (res) {
                        if (res.result === "CPT00000" || res === "CPT00000") {
                            toastr.success('操作成功');
                            $("#feeOrderModal").modal("hide");
                            table.ajax.reload();
                        } else {
                            toastr.error('操作失败：' + res.result);
                        }
                    },
                    error: function (xhr, status, error) {
                        toastr.error('操作失败：' + error);
                    }
                });
            });

            // 查看详情
            $(document).on("click", ".view-detail", function () {
                var orderNo = $(this).data("id");
                $.ajax({
                    url: "/cpt/param/feeorder/getFeeOrder",
                    type: "POST",
                    data: { orderNo: orderNo },
                    success: function (data) {
                        $("#detailOrderNo").text(data.orderNo || '');
                        $("#detailBusOrderNo").text(data.busOrderNo || '');
                        $("#detailBusType").text(data.busType || '');
                        $("#detailBusTypeDesc").text(data.busTypeDesc || '');
                        $("#detailUserId").text(data.userId || '');
                        $("#detailUserName").text(data.userName || '');
                        $("#detailCcy").text(data.ccy || '');
                        $("#detailFee").text(data.fee ? parseFloat(data.fee).toFixed(2) : '0.00');
                        $("#detailTradeAmt").text(data.tradeAmt ? parseFloat(data.tradeAmt).toFixed(2) : '0.00');
                        $("#detailTradeTotalAmt").text(data.tradeTotalAmt ? parseFloat(data.tradeTotalAmt).toFixed(2) : '0.00');
                        $("#detailRate").text(data.rate ? parseFloat(data.rate).toFixed(4) : '0.0000');
                        $("#detailCalculateMod").text(data.calculateMod || '');
                        $("#detailCalculateType").text(data.calculateType || '');
                        $("#detailClearStats").text(data.clearStats || '');
                        $("#detailTradeDate").text(data.tradeDate ? new Date(data.tradeDate).toLocaleDateString() : '');
                        $("#detailTradeTime").text(data.tradeTime || '');
                        $("#detailBusOrderTime").text(data.busOrderTime ? new Date(data.busOrderTime).toLocaleString() : '');
                        $("#detailCreateTime").text(data.createTime ? new Date(data.createTime).toLocaleString() : '');
                        $("#detailModifyTime").text(data.modifyTime ? new Date(data.modifyTime).toLocaleString() : '');
                        $("#detailModal").modal("show");
                    },
                    error: function (xhr, status, error) {
                        toastr.error('获取详情失败：' + error);
                    }
                });
            });

            // 编辑按钮
            $(document).on("click", ".btn-edit", function () {
                var orderNo = $(this).data("id");
                $.ajax({
                    url: "/cpt/param/feeorder/getFeeOrder",
                    type: "POST",
                    data: { orderNo: orderNo },
                    success: function (data) {
                        $("#feeOrderModalLabel").text("编辑手续费订单");
                        $("#orderNo").val(data.orderNo);
                        $("#busOrderNo").val(data.busOrderNo);
                        $("#busType").val(data.busType);
                        $("#userId").val(data.userId);
                        $("#userName").val(data.userName);
                        $("#ccy").val(data.ccy);
                        $("#fee").val(data.fee);
                        $("#tradeAmt").val(data.tradeAmt);
                        $("#rate").val(data.rate);
                        $("#calculateMod").val(data.calculateMod);
                        $("#calculateType").val(data.calculateType);
                        $("#feeOrderModal").modal("show");
                    },
                    error: function (xhr, status, error) {
                        toastr.error('获取详情失败：' + error);
                    }
                });
            });

            // 删除按钮
            $(document).on("click", ".btn-delete", function () {
                var orderNo = $(this).data("id");
                swal({
                    title: "确定要删除该手续费订单吗？",
                    text: "删除后将无法恢复！",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#DD6B55",
                    confirmButtonText: "确定删除",
                    cancelButtonText: "取消",
                    closeOnConfirm: false
                }, function () {
                    $.ajax({
                        url: "/cpt/param/feeorder/delete/" + orderNo,
                        type: "DELETE",
                        success: function (res) {
                            if (res.result === "CPT00000" || res === "CPT00000") {
                                swal("删除成功！", "手续费订单已被删除。", "success");
                                table.ajax.reload();
                            } else {
                                swal("删除失败", "请稍后重试", "error");
                            }
                        },
                        error: function (xhr, status, error) {
                            swal("删除失败", "错误: " + error, "error");
                        }
                    });
                });
            });

            // 表单验证
            $("#feeOrderForm").validate({
                rules: {
                    busOrderNo: {
                        required: true,
                        maxlength: 50
                    },
                    busType: {
                        required: true,
                        maxlength: 20
                    },
                    userId: {
                        required: true,
                        maxlength: 50
                    },
                    userName: {
                        required: true,
                        maxlength: 100
                    },
                    ccy: {
                        required: true,
                        maxlength: 10
                    },
                    fee: {
                        required: true,
                        number: true,
                        min: 0
                    },
                    tradeAmt: {
                        required: true,
                        number: true,
                        min: 0
                    },
                    rate: {
                        number: true,
                        min: 0
                    }
                },
                messages: {
                    busOrderNo: {
                        required: "请输入业务订单号",
                        maxlength: "业务订单号不能超过50个字符"
                    },
                    busType: {
                        required: "请输入业务类型",
                        maxlength: "业务类型不能超过20个字符"
                    },
                    userId: {
                        required: "请输入用户ID",
                        maxlength: "用户ID不能超过50个字符"
                    },
                    userName: {
                        required: "请输入用户名称",
                        maxlength: "用户名称不能超过100个字符"
                    },
                    ccy: {
                        required: "请输入币种",
                        maxlength: "币种不能超过10个字符"
                    },
                    fee: {
                        required: "请输入手续费",
                        number: "请输入有效的数字",
                        min: "手续费不能小于0"
                    },
                    tradeAmt: {
                        required: "请输入交易金额",
                        number: "请输入有效的数字",
                        min: "交易金额不能小于0"
                    },
                    rate: {
                        number: "请输入有效的数字",
                        min: "费率不能小于0"
                    }
                }
            });
        });

        // 搜索方法
        function search() {
            table.ajax.reload();
        }

        // 重置表单
        function resetForm() {
            $("#queryForm")[0].reset();
            search();
        }
    </script>
</body>

</html>