<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="nav.cardbin.title"></title>
    <div th:replace="head"></div>
</head>

<body>
<div id="wrapper">
    <div th:replace="nav"></div>
    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>
        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="nav.cardbin.constants"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.cmmmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.cmmsub.sysmgr"></a>
                    </li>
                    <li>
                        <strong data-i18n="nav.cardbin.constants"></strong>
                    </li>
                </ol>
            </div>
        </div>

        <div class="wrapper wrapper-content animated fadeInRight">
            <!--查询条件-->
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form id="queryForm" class="form-horizontal">
                                <div class="form-group col-sm-4">
                                    <!--卡bin-->
                                    <label class="col-sm-4 control-label" for="crdBin" data-i18n="cpi.crdBin"></label>
                                    <div class="col-sm-8">
                                        <input name="crdBin" id="crdBin" class="form-control" value=""/>
                                    </div>
                                    <!--卡号长度-->
                                    <label class="col-sm-4 control-label" for="crdLth" data-i18n="cpi.crdLth"></label>
                                    <div class="col-sm-8">
                                        <input name="crdLth" id="crdLth" class="form-control" value="" />
                                    </div>
                                    <!-- 归属国家 -->
                                    <label class="col-sm-4 control-label" for="belongingState" data-i18n="nav.cardbin.belongingState"></label>
                                    <div class="col-sm-8">
                                        <input name="belongingState" id="belongingState" class="form-control" value="" />
                                    </div>
                               	</div>
                                <div class="form-group col-sm-4">
                                    <!--资金机构-->
                                    <label class="col-sm-4 control-label" for="capCorg" data-i18n="cpi.capCorg"></label>
                                    <div class="col-sm-8">
                                        <select name="capCorg" id="capCorg" class="form-control">
                                        </select>
                                    </div>
                                    <!--卡类型-->
                                    <label class="col-sm-4 control-label" for="crdAcTyp" data-i18n="cpi.crdAcTyp"></label>
                                    <div class="col-sm-8">
                                        <select name="crdAcTyp" id="crdAcTyp" class="form-control">
                                            <option value="" data-i18n="cpi.selectByNull" selected></option>
                                            <option value="D" data-i18n="cpi.crdAcTyp-D"></option>
                                            <option value="C" data-i18n="cpi.crdAcTyp-C"></option>
                                        </select>
                                    </div>
                                    <!-- 归属地区 -->
                                    <label class="col-sm-4 control-label" for="ascriptionArea" data-i18n="nav.cardbin.ascriptionArea"></label>
                                    <div class="col-sm-8">
                                        <input name="ascriptionArea" id="ascriptionArea" class="form-control" value="" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-4 col-sm-offset-1">
                                        <button type="button" id="searchBtn" class="btn btn-primary" onclick="search()" data-i18n="cpi.searchList"></button>
                                        <button type="button" id="addBtn" class="btn btn-primary" data-toggle="modal" onclick="addModal()" data-i18n="cpi.addModal"></button>
                                        <button type="button" id="modifyBtn" class="btn btn-primary" data-toggle="modal" onclick="modifyModal()" data-i18n="cpi.modifyModal"></button>
                                        <button type="button" id="deleteBtn" class="btn btn-primary" data-toggle="modal" onclick="deleteModal()" data-i18n="cpi.deleteModal"></button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <div th:replace="cpt/param/cardbin/cardbinModal"></div>

            <!--数据表格-->
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <div class="table-responsive">
                                <table id="cardbin" class="table table-striped table-bordered table-hover">
                                    <thead>
                                        <tr>
                                            <th data-i18n="cpi.crdBin"></th>
                                            <th data-i18n="cpi.capCorg"></th>
                                            <th data-i18n="cpi.crdAcTyp"></th>
                                            <th data-i18n="cpi.crdLth"></th>
                                            <th data-i18n="cpi.binId"></th>
                                            <th data-i18n="nav.cardbin.belongingState"></th>
                                        	<th data-i18n="nav.cardbin.ascriptionArea"></th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>
    </div>
</div>

<div th:replace="script"></div>

<script>
    var editor;
    var table;

    $(document).ready(function () {
        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        i18nLoad.then(function () {
            editor = new $.fn.dataTable.Editor({
                table: "#cardbin",
                idSrc: 'binId',
                fields: [
                    {name: "id", type: "hidden"},
                    {label: $.i18n.t("cpi.crdBin"), name: "crdBin"},
                    {label: $.i18n.t("cpi.capCorg"), name: "capCorg"},
                    {label: $.i18n.t("cpi.crdAcTyp"), name: "crdAcTyp"},
                    {label: $.i18n.t("cpi.crdLth"), name: "crdLth"},
                    {label: $.i18n.t("cpi.binId"), name: "binId"},
                    {label: $.i18n.t("nav.cardbin.belongingState"), name: "belongingState"},
                    {label: $.i18n.t("nav.cardbin.ascriptionArea"), name: "ascriptionArea"}
                ]
            });
            
            table = $('#cardbin').DataTable({
                dom: 'Blfrtip',
                columnDefs: [{
                    targets:[4],//指定哪几列
                    render: function(data){
                        return "\u200C" + data;
                    }
                }],
                ajax: {
                    contentType: 'application/json',
                    url: '/cpt/param/cardbin/findAll',
                    type: 'post',
                    data: function (d) {
                        d.extra_search = {
                            "crdBin" : $("#crdBin").val(),
                            "capCorg" : $("#capCorg").val(),
                            "crdAcTyp" : $("#crdAcTyp").val(),
                            "crdLth" : $("#crdLth").val(),
                            "belongingState" : $("#belongingState").val(),
                            "ascriptionArea" : $("#ascriptionArea").val()
                        };
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
                language: {
                    url: languageUrl
                },
                select: true,
                buttons: [
//                        {extend: "create", editor: editor},
//                        {extend: "edit", editor: editor},
//                        {extend: "remove", editor: editor},
//                        {extend: 'copyHtml5'},
//                        {extend: 'csvHtml5'},
//                        {extend: 'excelHtml5', title: '文件名'},
                    'copy',
                    'csv',
                    'excel',
//                        'pdf',
//                        'print'
                ],
                columns: [{
                    data: 'crdBin'
                },{
                    data: 'capCorg'
                },{
                    data: 'crdAcTyp',
                    render: function (data, type, row) {
                        switch (data) {
                            case "D":
                                return $.i18n.t("cpi.crdAcTyp-D");
                            case "C":
                                return $.i18n.t("cpi.crdAcTyp-C");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'crdLth'
                },{
                    data: 'binId'
                },{
                    data: 'belongingState'
                },{
                    data: 'ascriptionArea'
                }]
            });
        });

        //查询所有的合作机构信息
        $.ajax({
            url:"/cpt/cpiorg/info/findAllOrgnInfo",
            dataType: "json",
            type:"post",
            success:function(data){
                $("#capCorg").append("<option value=''>" + $.i18n.t("cpi.selectByNull") + "</option>");
                for (var one in data)
                {
                    var orgId = data[one].corpOrgId;
                    var orgNm = data[one].corpOrgNm;
                    $("#capCorg").append("<option value='" + orgId + "'>" +orgId+" -- "+orgNm+ "</option>");
                }
            }
        });
    });

    <!--查询按钮-->
    function search() {
        table.ajax.reload();
    }

    <!--清除modal中的输入框的值-->
    function clearModalValues() {
        $("#crd_bin").val("");
        $("#cap_corg").html("");
        $("#cap_corg").val("");
        $("#crd_ac_typ").val("");
        $("#crd_lth").val("");
        $("#bin_id").val("");
        $("#belonging_state").val("");
        $("#ascription_area").val("");
    }

    //模态框，非空数据判断
    function judgeModalValues() {
        if($("#crd_bin").val() == null || $("#crd_bin").val() == "") {
            swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.crdBinNull"), "error");
            return false;
        }
        if($("#cap_corg").val() == null || $("#cap_corg").val() == "") {
            swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.capCorgNull"), "error");
            return false;
        }
        if($("#crd_ac_typ").val() == null || $("#crd_ac_typ").val() == "") {
            swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.crdAcTypNull"), "error");
            return false;
        }
        if($("#crd_lth").val() == null || $("#crd_lth").val() == "") {
            swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.crdLthNull"), "error");
            return false;
        }
        return true;
    }

    //模态框，获取元素值
    function getModalValues() {
        var crdBin = $("#crd_bin").val();
        var capCorg = $("#cap_corg").val();
        var crdAcTyp = $("#crd_ac_typ").val();
        var crdLth = $("#crd_lth").val();
        var binId = $("#bin_id").val();
        var belongingState = $("#belonging_state").val();
        var ascriptionArea = $("#ascription_area").val();
        var param = {
            "crdBin" : crdBin,
            "capCorg" : capCorg,
            "crdAcTyp" : crdAcTyp,
            "crdLth" : crdLth,
            "binId" : binId,
            "belongingState" : belongingState,
            "ascriptionArea" : ascriptionArea
        };
        return param;
    }

    //主页新增记录按钮
    function addModal() {
        clearModalValues();
        $("#addDetail").show();
        $("#deleteDetail").hide();
        $("#modifyDetail").hide();
        $("#cardbinModal").modal("show");
        $("#crd_bin").removeAttr("disabled", "disabled");
        $("#cap_corg").removeAttr("disabled", "disabled");
        $("#crd_ac_typ").removeAttr("disabled", "disabled");
        $("#crd_lth").removeAttr("disabled", "disabled");
        $("#belonging_state").removeAttr("disabled", "disabled");
        $("#ascription_area").removeAttr("disabled", "disabled");
        
        //查询所有的合作机构信息
        $.ajax({  
            url:"/cpt/cpiorg/info/findAllOrgnInfo",
            dataType: "json",
            type:"post",
            success:function(data){
                $("#cap_corg").append("<option value=''>" + $.i18n.t("cpi.selectByNull") + "</option>");
                for (var one in data)
                {
                    var orgId = data[one].corpOrgId;
                    var orgNm = data[one].corpOrgNm;
                    $("#cap_corg").append("<option value='" + orgId + "'>" +orgId+" -- "+orgNm+ "</option>");
                }
            }
        });
    }

    //主页修改记录按钮
    function modifyModal() {
        clearModalValues();
        $("#addDetail").hide();
        $("#deleteDetail").hide();
        $("#modifyDetail").show();
        $("#crd_bin").removeAttr("disabled", "disabled");
        $("#cap_corg").removeAttr("disabled", "disabled");
        $("#crd_ac_typ").removeAttr("disabled", "disabled");
        $("#crd_lth").removeAttr("disabled", "disabled");

        <!--获取表格中选中的单行-->
        var row = table.row('.selected');
        if(row.length == 0) {
            swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.shouldSelectOne"), "error");
            return;
        }

        //查询所有的合作机构信息
        var rowData = table.row(row).data();
        var capCorg = rowData.capCorg;
        $.ajax({
            url:"/cpt/cpiorg/info/findAllOrgnInfo",
            dataType: "json",
            type:"post",
            success:function(data){
                $("#cap_corg").append("<option value=''>" + $.i18n.t("cpi.selectByNull") + "</option>");
                for (var one in data)
                {
                    var orgId = data[one].corpOrgId;
                    var orgNm = data[one].corpOrgNm;
                    $("#cap_corg").append("<option value='" + orgId + "'>" +orgId+" -- "+orgNm+ "</option>");
                    if(orgId==capCorg){
                        $("#cap_corg").find("option:contains('"+capCorg+"')").attr("selected",true);
                    }
                }
            }
        });

        <!--获取选中行的内容-->
        $("#cardbinModal").modal("show");
        var rowData = table.row(row).data();
        $("#crd_bin").val(rowData.crdBin);
        $("#cap_corg").val(rowData.capCorg);
        $("#crd_ac_typ").val(rowData.crdAcTyp);
        $("#crd_lth").val(rowData.crdLth);
        $("#bin_id").val(rowData.binId);
        $("#belonging_state").val(rowData.belongingState);
        $("#ascription_area").val(rowData.ascriptionArea);
    }

    //主页删除记录按钮
    function deleteModal() {
        clearModalValues();
        $("#addDetail").hide();
        $("#modifyDetail").hide();
        $("#deleteDetail").show();
        $("#crd_bin").attr("disabled", "disabled");
        $("#cap_corg").attr("disabled", "disabled");
        $("#crd_ac_typ").attr("disabled", "disabled");
        $("#crd_lth").attr("disabled", "disabled");
        $("#belonging_state").attr("disabled", "disabled");
        $("#ascription_area").attr("disabled", "disabled");

        <!--获取表格中选中的单行-->
        var row = table.row('.selected');
        if(row.length == 0) {
            swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.shouldSelectOne"), "error");
            return;
        }

        //查询所有的合作机构信息
        var rowData = table.row(row).data();

        var capCorg = rowData.capCorg;
        $.ajax({
            url:"/cpt/cpoorg/info/findOrgnInfo",
            data: {
                "corpOrgId" : capCorg
            },
            dataType: "json",
            type:"post",
            success:function(data){
                var orgId = data.corpOrgId;
                var orgNm = data.corpOrgNm;
                $("#cap_corg").append("<option value='" + orgId + "' selected>" +orgId+" -- "+orgNm+ "</option>");
            }
        });

        <!--获取选中行的内容-->
        $("#cardbinModal").modal("show");
        var rowData = table.row(row).data();
        $("#crd_bin").val(rowData.crdBin);
        $("#cap_corg").val(rowData.capCorg);
        $("#crd_ac_typ").val(rowData.crdAcTyp);
        $("#crd_lth").val(rowData.crdLth);
        $("#bin_id").val(rowData.binId);
        $("#belonging_state").val(rowData.belongingState);
        $("#ascription_area").val(rowData.ascriptionArea);
    }

    //模态框，新增信息按钮
    function addDetail() {
        if(!judgeModalValues()){
            return;
        }
        //确认增加弹出框
        swal({
            title: $.i18n.t("cpi.addConfirmTitle"),
            text: $.i18n.t("cpi.addCardBinText"),
            type: "warning",
            showCancelButton: true,
            cancelButtonText: $.i18n.t("cpi.swal-cancel"),
            confirmButtonColor: "#DD6B55",
            confirmButtonText: $.i18n.t("cpi.swal-confirm"),
            closeOnConfirm: false,
            closeOnCancel: true
        }, function() {
            var param = getModalValues();
            $.ajax({
                contentType: 'application/json',
                url:"/cpt/param/cardbin/add",
                dataType: "json",
                type: "post",
                data: JSON.stringify(param),
                success:function(data){
                    var msgCd = data.msgCd;
                    if(msgCd == "CPO00000") {
                        search();
                        $("#cardbinModal").modal("hide");
                        swal($.i18n.t("cpi.swal-success"), $.i18n.t("cpi.addConfirmSuss"), "success");
                    } else if(msgCd == "CPO30006") {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.cardBinExists"), "error");
                    } else {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.addConfirmFail"), "error");
                    }
                },
                error: function(){
                    swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.systemException"), "error");
                }
            });
        });
    }

    //模态框，修改信息按钮
    function modifyDetail() {
        if(!judgeModalValues()){
            return;
        }
        //确认修改弹出框
        swal({
            title: $.i18n.t("cpi.modifyConfirmTitle"),
            text: $.i18n.t("cpi.modifyCardBinText"),
            type: "warning",
            showCancelButton: true,
            cancelButtonText: $.i18n.t("cpi.swal-cancel"),
            confirmButtonColor: "#DD6B55",
            confirmButtonText: $.i18n.t("cpi.swal-confirm"),
            closeOnConfirm: false,
            closeOnCancel: true
        }, function() {
            var param = getModalValues();
            $.ajax({
                contentType: 'application/json',
                url: "/cpt/param/cardbin/modify",
                data: JSON.stringify(param),
                dataType: "json",
                type: "post",
                success: function(data) {
                    var msgCd = data.msgCd;
                    if (msgCd == "CPO00000") {
                        search();
                        $("#cardbinModal").modal("hide");
                        swal($.i18n.t("cpi.swal-success"), $.i18n.t("cpi.modifyConfirmSuss"), "success");
                    } else if(msgCd == "CPO30007") {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.cardBinNotExists"), "error");
                    } else {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.modifyConfirmFail"), "error");
                    }
                },
                error: function() {
                    swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.systemException"), "error");
                }
            });
        });
    }

    //模态框，修改信息按钮
    function deleteDetail() {
        if(!judgeModalValues()){
            return;
        }
        //确认修改弹出框
        swal({
            title: $.i18n.t("cpi.deleteConfirmTitle"),
            text: $.i18n.t("cpi.deleteConfirmText"),
            type: "warning",
            showCancelButton: true,
            cancelButtonText: $.i18n.t("cpi.swal-cancel"),
            confirmButtonColor: "#DD6B55",
            confirmButtonText: $.i18n.t("cpi.swal-confirm"),
            closeOnConfirm: false,
            closeOnCancel: true
        }, function() {
            var param = getModalValues();
            $.ajax({
                contentType: 'application/json',
                url: "/cpt/param/cardbin/delete",
                data: JSON.stringify(param),
                dataType: "json",
                type: "post",
                success: function(data) {
                    var msgCd = data.msgCd;
                    if (msgCd == "CPO00000") {
                        search();
                        $("#cardbinModal").modal("hide");
                        swal($.i18n.t("cpi.swal-success"), $.i18n.t("cpi.deleteConfirmSuss"), "success");
                    } else {
                        swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.deleteConfirmFail"), "error");
                    }
                },
                error: function() {
                    swal($.i18n.t("cpi.swal-fail"), $.i18n.t("cpi.systemException"), "error");
                }
            });
        });
    }

</script>
</body>

</html>