<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="cpi.title"></title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>
            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="cpi.head"></h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="cpi.firstLevel"></a>
                        </li>
                        <li>
                            <a data-i18n="cpi.secondLevelOrderMgr"></a>
                        </li>
                        <li>
                            <strong data-i18n="cpi.head"></strong>
                        </li>
                    </ol>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!--查询条件-->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-4">
                                        <!--手机号/商户号-->
                                        <label class="col-sm-4 control-label" for="user_id" data-i18n="cpi.userId"></label>
                                        <div class="col-sm-8">
                                            <input name="user_id" id="user_id" class="form-control" value=""/>
                                        </div>
                                        <!--退款类型-->
                                        <label class="col-sm-4 control-label" for="corp_bus_sub_typ" data-i18n="cpi.corpBusSubTyp"></label>
                                        <div class="col-sm-8">
                                            <select name="corp_bus_sub_typ" id="corp_bus_sub_typ" class="form-control" value="">
                                                <option value="" data-i18n="cpo.selectByNull" selected></option>
                                                <option value="0501" data-i18n="cpi.fastRefund"></option>
                                                <option value="0502" data-i18n="cpi.ebankRefund"></option>
                                                <option value="0503" data-i18n="cpi.remittanceRefund"></option>
                                            </select>
                                        </div>
                                        <!--银行流水号-->
                                        <label class="col-sm-4 control-label" for="org_jrn_no" data-i18n="cpi.orgJrnNo"></label>
                                        <div class="col-sm-8">
                                            <input name="org_jrn_no" id="org_jrn_no" class="form-control" value=""/>
                                        </div>
                                    </div>
                                    <div class="form-group col-sm-4">
                                        <!--充值订单号-->
                                        <label class="col-sm-4 control-label" for="fnd_ord_no" data-i18n="cpi.fndOrdNo"></label>
                                        <div class="col-sm-8">
                                            <input name="fnd_ord_no" id="fnd_ord_no" class="form-control" value=""/>
                                        </div>
                                        <!--退款银行-->
                                        <label class="col-sm-4 control-label" for="rut_corp_org" data-i18n="cpi.rutCorpOrg"></label>
                                        <div class="col-sm-8">
                                            <select name="rut_corp_org" id="rut_corp_org" class="form-control" value="">
                                                <option value="" data-i18n="cpi.selectByNull" selected></option>
                                                <option value="ICBC" data-i18n="corg.ICBC"></option>
                                                <option value="ABC" data-i18n="corg.ABC"></option>
                                                <option value="CCB" data-i18n="corg.CCB"></option>
                                                <option value="BCM" data-i18n="corg.BCM"></option>
                                                <option value="BOC" data-i18n="corg.BOC"></option>
                                                <option value="WeChat" data-i18n="corg.WeChat"></option>
                                                <option value="BESTPAY" data-i18n="corg.BESTPAY"></option>
                                                <option value="CBP" data-i18n="corg.CBP"></option>
                                                <option value="ABA" data-i18n="corg.ABA"></option>
                                                <option value="ACLEDA" data-i18n="corg.ACLEDA"></option>
                                            </select>
                                        </div>
                                        <!--提交日期-->
                                        <label class="col-sm-4 control-label" for="beginDate" data-i18n="cpi.submitDate"></label>
                                        <div class="col-sm-4">
                                            <input name="beginDate" id="beginDate" class="form-control" value=""/>
                                        </div>
                                        <div class="col-sm-4">
                                            <input name="endDate" id="endDate" class="form-control" value=""/>
                                        </div>
                                    </div>
                                    <div class="form-group col-sm-4">
                                        <!--退款订单号-->
                                        <label class="col-sm-4 control-label" for="rfd_ord_no" data-i18n="cpi.rfdOrdNo"></label>
                                        <div class="col-sm-8">
                                            <input name="rfd_ord_no" id="rfd_ord_no" class="form-control" value=""/>
                                        </div>
                                        <!--退款订单状态-->
                                        <label class="col-sm-4 control-label" for="ord_sts" data-i18n="cpi.ordSts"></label>
                                        <div class="col-sm-8">
                                            <select name="ord_sts" id="ord_sts" class="form-control" value="">
                                                <option value="" data-i18n="cpi.selectByNull" selected></option>
                                                <option value="S1" data-i18n="cpi.refund-S1"></option>
                                                <option value="F1" data-i18n="cpi.refund-F1"></option>
                                                <option value="W1" data-i18n="cpi.refund-W1"></option>
                                                <option value="W2" data-i18n="cpi.refund-W2"></option>
                                                <option value="E1" data-i18n="cpi.refund-E1"></option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-sm-4 col-sm-offset-1">
                                            <button type="button" id="searchBtn" class="btn btn-primary" onclick="search()" data-i18n="cpi.search"></button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!--数据表格-->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="orderInf" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="cpi.userId"></th>
                                                <th data-i18n="cpi.ordDt"></th>
                                                <th data-i18n="cpi.ordTm"></th>
                                                <th data-i18n="cpi.corpBusSubTyp"></th>
                                                <th data-i18n="cpi.rutCorpOrg"></th>
                                                <th data-i18n="cpi.ordAmt"></th>
                                                <th data-i18n="cpi.ordSts"></th>
                                                <th data-i18n="cpi.fndOrdNo"></th>
                                                <th data-i18n="cpi.rfdOrdNo"></th>
                                                <th data-i18n="cpi.orgJrnNo"></th>
                                                <th data-i18n="cpi.orgRspMsg"></th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var editor;
        var table;
        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }

            i18nLoad.then(function () {
                editor = new $.fn.dataTable.Editor({
                    table: "#orderInf",
                    idSrc: 'rfdOrdNo',
                    fields: [
                        {name: "id", type: "hidden"},
                        {label: $.i18n.t("cpi.userId"), name: "userId"},
                        {label: $.i18n.t("cpi.ordDt"), name: "ordDt"},
                        {label: $.i18n.t("cpi.ordTm"), name: "ordTm"},
                        {label: $.i18n.t("cpi.corpBusSubTyp"), name: "corpBusSubTyp"},
                        {label: $.i18n.t("cpi.rutCorpOrg"), name: "rutCorpOrg"},
                        {label: $.i18n.t("cpi.ordAmt"), name: "ordAmt"},
                        {label: $.i18n.t("cpi.ordSts"), name: "ordSts"},
                        {label: $.i18n.t("cpi.fndOrdNo"), name: "fndOrdNo"},
                        {label: $.i18n.t("cpi.rfdOrdNo"), name: "rfdOrdNo"},
                        {label: $.i18n.t("cpi.orgJrnNo"), name: "orgJrnNo"},
                        {label: $.i18n.t("cpi.orgRspMsg"), name: "orgRspMsg"}
                    ]
                });

                table = $('#orderInf').DataTable({
                    dom: 'Blfrtip',
                    columnDefs: [{
                        targets:[0,7,8,9],//指定哪几列
                        render: function(data){
                            return "\u200C" + data;
                        }
                    }],
                    ajax: {
                        contentType: 'application/json',
                        url: '/cpt/order/refund/findAll',
                        type: 'post',
                        data: function (d) {
                            d.extra_search = {
                                "userId" : $("#user_id").val(),
                                "fndOrdNo" : $("#fnd_ord_no").val(),
                                "rfdOrdNo" : $("#rfd_ord_no").val(),
                                "corpBusSubTyp" : $("#corp_bus_sub_typ").val(),
                                "rutCorpOrg" : $("#rut_corp_org").val(),
                                "orgJrnNo" : $("#org_jrn_no").val(),
                                "ordSts" : $("#ord_sts").val(),
                                "beginDate" : $("#beginDate").val(),
                                "endDate" : $("#endDate").val()
                            };
                            return JSON.stringify(d);
                        }
                    },
                    serverSide: true,
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    select: true,
                    buttons: [
//                        {extend: "create", editor: editor},
//                        {extend: "edit", editor: editor},
//                        {extend: "remove", editor: editor},
                        {extend: 'copyHtml5'},
                        {extend: 'csvHtml5'},
                        {extend: 'excelHtml5', title: '退款订单列表'},
//                        'copy',
//                        'csv',
//                        'excel',
//                        'pdf',
//                        'print'
                    ],
                    columns: [{
                        data: 'userId'
                    },{
                        data: 'ordDt'
                    },{
                        data: 'ordTm'
                    },{
                        data: 'corpBusSubTyp',
                        render: function (data, type, row) {
                            switch (data) {
                                case "0501":
                                    return $.i18n.t("cpi.fastRefund");
                                case "0502":
                                    return $.i18n.t("cpi.ebankRefund");
                                case "0503":
                                    return $.i18n.t("cpi.remittanceRefund");
                                default:
                                    return data;
                            }
                        }
                    },{
                        data: 'rutCorpOrg',
                        render: function (data, type, row) {
                            switch (data) {
                                case "ICBC":
                                    return $.i18n.t("corg.ICBC");
                                case "CMBC":
                                    return $.i18n.t("corg.CMBC");
                                case "CMB":
                                    return $.i18n.t("corg.CMB");
                                case "BEA":
                                    return $.i18n.t("corg.BEA");
                                case "WeChat":
                                    return $.i18n.t("corg.WeChat");
                                case "BESTPAY":
                                    return $.i18n.t("corg.BESTPAY");
                                case "CBP":
                                    return $.i18n.t("corg.CBP");
                                case "ABA":
                                    return $.i18n.t("corg.ABA");
                                case "ACLEDA":
                                    return $.i18n.t("corg.ACLEDA");
                                default:
                                    return data;
                            }
                        }
                    },{
                        data: 'ordAmt'
                    },{
                        data: 'ordSts',
                        render: function (data, type, row) {
                            switch (data) {
                                case "S1":
                                    return $.i18n.t("cpi.refund-S1");
                                case "F1":
                                    return $.i18n.t("cpi.refund-F1");
                                case "E1":
                                    return $.i18n.t("cpi.refund-E1");
                                case "W1":
                                    return $.i18n.t("cpi.refund-W1");
                                case "W2":
                                    return $.i18n.t("cpi.refund-W2");
                                default:
                                    return data;
                            }
                        }
                    },{
                        data: 'fndOrdNo'
                    },{
                        data: 'rfdOrdNo'
                    },{
                        data: 'orgJrnNo'
                    },{
                        data: 'orgRspMsg'
                    }]
                });
            });
        });

        <!--查询按钮-->
        function search() {
            table.ajax.reload();
        }

        <!--初始化日期控件-->
        var beginTimePick = $('#beginDate').datetimepicker({
            lang:'en',
            timepicker:false,
            validateOnBlur: false,
            format:'Y-m-d ',
            formatDate:'Y-m-d',
            onChangeDate: function(dateText, inst) {
                endTimePick.datetimepicker('minDate',new Date(dateText.replace("-", "-")));
            }
        });
        var endTimePick = $('#endDate').datetimepicker({
            lang:'en',
            timepicker:false,
            validateOnBlur: false,
            format:'Y-m-d ',
            formatDate:'Y-m-d'
            // maxDate:'+1970/01/01',
        });
    </script>
</body>

</html>