<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="tran.title"></title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>
        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>
            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="tran.head"></h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="tran.firstLevel"></a>
                        </li>
                        <li>
                            <a data-i18n="tran.secondLevelOrderMgr"></a>
                        </li>
                        <li>
                            <strong data-i18n="tran.head"></strong>
                        </li>
                    </ol>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!--查询条件-->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-4">
                                        <!--订单号-->
                                        <label class="col-sm-4 control-label" for="orderNo" data-i18n="tran.orderNo"></label>
                                        <div class="col-sm-8">
                                            <input name="orderNo" id="orderNo" class="form-control" value=""/>
                                        </div>
                                    </div>
                                    <div class="form-group col-sm-4">
                                        <!--外围订单号-->
                                        <label class="col-sm-4 control-label" for="busOrderNo" data-i18n="tran.busOrderNo"></label>
                                        <div class="col-sm-8">
                                            <input name="busOrderNo" id="busOrderNo" class="form-control" value=""/>
                                        </div>
                                    </div>
                                    <div class="form-group col-sm-4">
                                        <!--付款人-->
                                        <label class="col-sm-4 control-label" for="payerId" data-i18n="tran.payerId"></label>
                                        <div class="col-sm-8">
                                            <input name="payerId" id="payerId" class="form-control" value=""/>
                                        </div>
                                    </div>
                                    <div class="form-group col-sm-4">
                                        <!--订单状态-->
                                        <label class="col-sm-4 control-label" for="orderStatus" data-i18n="tran.orderStatus"></label>
                                        <div class="col-sm-8">
                                            <select name="orderStatus" id="orderStatus" class="form-control" value="">
                                                <option value="" data-i18n="cpo.selectByNull" selected></option>
                                                <option value="S" data-i18n="tran.tranSucc"></option>
                                                <option value="F" data-i18n="tran.tranFail"></option>
                                                <option value="P" data-i18n="tran.waitPay"></option>
                                                <option value="A1" data-i18n="tran.waitReview"></option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group col-sm-4">
                                        <!--收款人-->
                                        <label class="col-sm-4 control-label" for="payeeId" data-i18n="tran.payeeId"></label>
                                        <div class="col-sm-8">
                                            <input name="payeeId" id="payeeId" class="form-control" value=""/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-sm-4 col-sm-offset-1">
                                            <button type="button" id="searchBtn" class="btn btn-primary" onclick="search()" data-i18n="tran.searchList"></button>
                                            <button type="button" id="viewDetailBtn" class="btn btn-primary" data-toggle="modal" onclick="viewDetail()" data-i18n="tran.viewModal"></button>
                                            <button type="button" id="payHandleBtn" class="btn btn-primary" data-toggle="modal" onclick="payHandle()" data-i18n="tran.handleModal"></button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!--转账订单详细信息弹窗-->
                <div th:replace="cpt/order/transfer/transferModal"></div>

                <!--数据表格-->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="orderInf" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="tran.orderNo"></th>
                                                <th data-i18n="tran.txType"></th>
                                                <th data-i18n="tran.busType"></th>
                                                <th data-i18n="tran.payerId"></th>
                                                <th data-i18n="tran.payeeId"></th>
                                                <th data-i18n="tran.busOrderNo"></th>
                                                <th data-i18n="tran.orderAmt"></th>
                                                <th data-i18n="tran.fee"></th>
                                                <th data-i18n="tran.orderStatus"></th>
                                                <th data-i18n="tran.orderTm"></th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <div th:replace="script"></div>

    <script>
        var editor;
        var table;

        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }

            i18nLoad.then(function () {
                editor = new $.fn.dataTable.Editor({
                    table: "#orderInf",
                    idSrc: 'busOrderNo',
                    fields: [
                        {label: $.i18n.t("tran.orderNo"), name: "orderNo"},
                        {label: $.i18n.t("tran.txType"), name: "txType"},
                        {label: $.i18n.t("tran.busType"), name: "busType"},
                        {label: $.i18n.t("tran.payerId"), name: "payerId"},
                        {label: $.i18n.t("tran.payeeId"), name: "payeeId"},
                        {label: $.i18n.t("tran.busOrderNo"), name: "busOrderNo"},
                        {label: $.i18n.t("tran.orderAmt"), name: "orderAmt"},
                        {label: $.i18n.t("tran.fee"), name: "fee"},
                        {label: $.i18n.t("tran.orderStatus"), name: "orderStatus"},
                        {label: $.i18n.t("tran.orderTm"), name: "orderTm"}
                    ]
                });

                table = $('#orderInf').DataTable({
                    dom: 'Blfrtip',
                    columnDefs: [{
                        targets:[0,1,2,3,4,5],//指定哪几列
                        render: function(data){
                            return "\u200C" + data;
                        }
                    }],
                    ajax: {
                        contentType: 'application/json',
                        url: '/cpt/order/transfer/findAll',
                        type: 'post',
                        data: function (d) {
                            d.extra_search = {
                                "orderNo" : $("#orderNo").val(),
                                "payerId" : $("#payerId").val(),
                                "payeeId" : $("#payeeId").val(),
                                "busOrderNo" : $("#busOrderNo").val(),
                                "orderStatus" : $("#orderStatus").val()
                            };
                            return JSON.stringify(d);
                        },
                        dataSrc: function(json) {
                            console.log('Response data:', json);
                            // 处理DataTablesOutput格式
                            if (json && json.data) {
                                return json.data;
                            } else {
                                console.error('Invalid response format:', json);
                                return [];
                            }
                        },
                        error: function(xhr, error, thrown) {
                            console.error('Ajax error:', error);
                            console.error('Response:', xhr.responseText);
                        }
                    },
                    serverSide: true,
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    select: true,
                    buttons: [
//                        {extend: "create", editor: editor},
//                        {extend: "edit", editor: editor},
//                        {extend: "remove", editor: editor},
                        {extend: 'copyHtml5'},
                        {extend: 'csvHtml5'},
                        {extend: 'excelHtml5', title: '付款订单列表'},
//                        'copy',
//                        'csv',
//                        'excel',
//                        'pdf',
//                        'print'
                    ],
                    columns: [{
                        data: 'orderNo'
                    },{
                        data: 'txType'
                    },{
                        data: 'busType',
                        render: function (data, type, row) {
                            switch (data) {
                                case "0301":
                                    return $.i18n.t("tran.accountTransfer");
                                case "0302":
                                    return $.i18n.t("tran.cardTransfer");
                                case "0303":
                                    return $.i18n.t("tran.faceTransfer");
                                default:
                                    return data;
                            }
                        }
                    },{
                        data: 'payerId'
                    },{
                        data: 'payeeId'
                    },{
                        data: 'busOrderNo'
                    },{
                        data: 'orderAmt'
                    },{
                        data: 'fee'
                    },{
                        data: 'orderStatus',
                        render: function (data, type, row) {
                            switch (data) {
                                case "S":
                                    return $.i18n.t("tran.tranSucc");
                                case "F":
                                    return $.i18n.t("tran.tranFail");
                                case "P":
                                    return $.i18n.t("tran.waitPay");
                                case "A1":
                                    return $.i18n.t("tran.waitReview");
                                default:
                                    return data;
                            }
                        }
                    },{
                        data: 'orderTm',
                        render: function(data) {
                            return formatDateTime(data);
                        }
                    }]
                });

            });

            //modal模态框隐藏后处理
            $('#handleModal').on('hide.bs.modal',
                function () {
                }
            );
            //modal模态框隐藏后处理
            $('#handleModal').on('shown.bs.modal',
                function () {
                    var row = table.row('.selected');
                    if(row.length == 0) {
                        $("#handleModal").modal("hide");
                    }
                }
            );
        });

        // 查询按钮
        function search() {
            table.ajax.reload();
        }

        // 清除modal中的输入框的值
        function clearModalValues() {
            $("#order_no").val("");
            $("#tx_type").val("");
            $("#bus_type").val("");
            $("#payer_id").val("");
            $("#payee_id").val("");
            $("#bus_order_no").val("");
            $("#order_amt").val("");
            $("#fee").val("");
            $("#order_status").val("");
            $("#order_tm").val("");
            $("#rmk").val("");
        }

        // 时间格式化工具函数
        function formatDateTime(val) {
            return val ? val.replace('T', ' ') : '';
        }

        // 查看明细
        function viewDetail() {
            clearModalValues();
            $("#muilRowHide").show() ;
            $("#payConfirm").hide();
            $("#payCancel").hide();

            // 获取表格中选中的单行
            var row = table.row('.selected');
            if(row.length == 0) {
                swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.shouldSelectOne"), "error");
                return;
            }

            // 获取选中行的内容
            var rowData = table.row(row).data();
            var orderNo = rowData["orderNo"];
            $.ajax({
                url:"/cpt/order/transfer/findOrderInfo",
                data:{
                    "orderNo": orderNo
                },
                dataType: "json",
                type:"post",
                success:function(data) {
                    if (data != null) {
                        $("#handleModal").modal("show");
                        $(".modal-backdrop").remove();//删除class值为modal-backdrop的标签，可去除阴影
                        // 获取选中行的内容
                        $("#order_no").val( data.orderNo );
                        $("#tx_type").val( data.txType );
                        $("#bus_type").val( convertCorpBusSubTyp(data.busType) );
                        $("#payer_id").val( data.payerId );
                        $("#payee_id").val( data.payeeId );
                        $("#bus_order_no").val( data.busOrderNo );
                        $("#order_amt").val( data.orderAmt );
                        $("#fee").val( data.fee );
                        $("#order_status").val( convertOrdSts(data.orderStatus) );
                        $("#order_tm").val( formatDateTime(data.orderTm) );
                        $("#rmk").val( data.rmk );
                    } else {
                        // 隐藏模态框
                        $("#handleModal").modal("hide");
                        swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.detailNull"), "error");
                    }
                },
                error: function() {
                    $("#handleModal").modal("hide");
                    swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.systemException"), "error");
                }
            });
        }

        // 处理汇款订单
        function payHandle() {
            $(".modal-backdrop").remove();//删除class值为modal-backdrop的标签，可去除阴影
            clearModalValues();
            $("#rmk").removeAttr("readonly");
            $("#rmk").attr("placeholder", $.i18n.t("cpo.lessThan20words"));
            $("#payConfirm").show();
            $("#payCancel").show();

            // 获取表格中选中的多行
            var row = table.rows('.selected').data();
            console.log(table.rows('.selected').data().length);
            if(row.length == 0) {
                swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.shouldSelectOne"), "error");
                return;
            } else  if (row.length == 1 ) {
                // 获取选中行的内容
                $("#muilRowHide").show() ;
                var rowData = row[0];
                var orderNo = rowData["orderNo"];
                var orderStatus = rowData["orderStatus"];
                if (orderStatus == "S" || orderStatus == "F") {
                    //订单状态为 S-交易成功 、F-交易失败，则不能处理汇款订单
                    swal($.i18n.t("cpo.swal-fail"), $.i18n.t("tran.ordStsError"), "error");
                    return;
                }

                //异步调起后台服务
                $.ajax({
                    url: "/cpt/order/transfer/findOrderInfo",
                    data: {
                        "orderNo": orderNo
                    },
                    dataType: "json",
                    type: "post",
                    success: function (data) {
                        if (data != null) {
                            $("#handleModal").modal("show");
                            $(".modal-backdrop").remove();//删除class值为modal-backdrop的标签，可去除阴影
                            // 获取选中行的内容
                            $("#order_no").val( data.orderNo );
                            $("#tx_type").val( data.txType );
                            $("#bus_type").val( convertCorpBusSubTyp(data.busType) );
                            $("#payer_id").val( data.payerId );
                            $("#payee_id").val( data.payeeId );
                            $("#bus_order_no").val( data.busOrderNo );
                            $("#order_amt").val( data.orderAmt );
                            $("#fee").val( data.fee );
                            $("#order_status").val( convertOrdSts(data.orderStatus) );
                            $("#order_tm").val( formatDateTime(data.orderTm) );
                            $("#rmk").val( data.rmk );
                        } else {
                            // 隐藏模态框
                            $("#handleModal").modal("hide");
                            swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.detailNull"), "error");
                        }
                    },
                    error: function () {
                        $("#handleModal").modal("hide");
                        swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.systemException"), "error");
                    }
                });
            } else if (row.length > 1) {
                for (var i = 0 ; i < row.length ; i++ ) {
                    var rowData = row[i] ;
                    var orderStatus = rowData["orderStatus"];
                    console.log("hshs"+orderStatus)
                    if ( orderStatus == "S" || orderStatus == "F" ) {
                        //订单状态为 S-交易成功 、F-交易失败，则不能处理汇款订单
                        swal($.i18n.t("cpo.swal-fail"), $.i18n.t("tran.ordStsError"), "error");
                        return;
                    }
                }
                $("#muilRowHide").hide() ;
                $("#handleModal").modal("show");

            }


        }
        function getSelectOrderNo() {
            var row = table.rows('.selected').data();
            var busOrderNos = [];
            for (var i=0 ; i < row.length ; i++ ) {
                var rowData = row[i] ;
                var busOrderNo = rowData["busOrderNo"];
                busOrderNos.push(busOrderNo);
            }
            return busOrderNos ;
        }
        // 审核通过
        function payConfirm() {
            var handleReason = $.trim($("#rmk").val());
            if(handleReason == null || handleReason == "") {
                swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.reasonNull"), "error");
                return;
            }
            //确认提交弹出框
            swal({
                title: $.i18n.t("cpo.payConfirmTitle"),
                // text: $.i18n.t("tran.payConfirmText"),
                type: "warning",
                showCancelButton: true,
                cancelButtonText: $.i18n.t("cpo.swal-cancel"),
                confirmButtonColor: "#DD6B55",
                confirmButtonText: $.i18n.t("cpo.swal-confirm"),
                closeOnConfirm: true
            }, function () {
                // 隐藏模态框
                $("#handleModal").modal("hide");

                //ajax异步调起后台服务
                var ordSts = "A1";
                var orderNo = $("#order_no").val();
                $.ajax({
                    url:"/cpt/order/transfer/auditHandler",
                    data:{
                        "orderNo": orderNo,
                        "reason" : handleReason,
                        "ordSts" : ordSts
                    },
                    dataType: "json",
                    type:"post",
                    success:function(data){
                        var msgCd = data.msgCd;
                        if (msgCd == "CSH00000") {
                            swal($.i18n.t("cpo.swal-success"), $.i18n.t("cpo.payConfirmSuss"), "success");
                            table.ajax.reload();
                        } else if (msgCd == "1") {
                            swal($.i18n.t("cpo.swal-success"),  $.i18n.t("cpo.paymentNum")+data.successNum, "success");
                        } else {
                            swal($.i18n.t("cpo.swal-fail"),  $.i18n.t("cpo.payConfirmFail"), "error");
                        }
                    },
                    error: function(){
                        swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.systemException"), "error");
                    }
                });
            });
        }

        // 付款退回
        function payCancel() {
            var handleReason = $.trim($("#rmk").val());
            if(handleReason == null || handleReason == "") {
                swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.reasonNull"), "error");
                return;
            }
            swal({
                title: $.i18n.t("cpo.payCancelTitle"),
                // text: $.i18n.t("cpo.payCancelText"),
                type: "warning",
                showCancelButton: true,
                cancelButtonText: $.i18n.t("cpo.swal-cancel"),
                confirmButtonColor: "#DD6B55",
                confirmButtonText: $.i18n.t("cpo.swal-confirm"),
                closeOnConfirm: true
            }, function () {
                // 隐藏模态框
                $("#handleModal").modal("hide");

                //ajax异步调起后台服务
                var ordSts = "F";
                var orderNo = $("#order_no").val();
                $.ajax({
                    url:"/cpt/order/transfer/auditHandler",
                    data:{
                        "orderNo": orderNo,
                        "reason" : handleReason,
                        "ordSts" : ordSts
                    },
                    dataType: "json",
                    type:"post",
                    success:function(data){
                        var msgCd = data.msgCd;
                        if (msgCd == "CSH00000") {
                            swal($.i18n.t("cpo.swal-success"), $.i18n.t("cpo.payCancelSucc"), "success");
                            table.ajax.reload();
                        } else if (msgCd == "1") {
                            swal($.i18n.t("cpo.swal-success"),  $.i18n.t("cpo.paymentNum")+data.successNum, "success");
                        } else {
                            swal($.i18n.t("cpo.swal-fail"),  $.i18n.t("cpo.payCancelFail"), "error");
                        }
                        var msgCd = data.msgCd;
                        if (msgCd == "CSH00000") {
                            swal($.i18n.t("cpo.swal-success"), $.i18n.t("cpo.payCancelSucc"), "success");
                            table.ajax.reload();
                        } else {
                            swal($.i18n.t("cpo.swal-fail"), msgCd + $.i18n.t("cpo.payCancelFail"), "error");
                        }
                    },
                    error: function() {
                        swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.systemException"), "error");
                    }
                });
            });
        }

        // 初始化日期控件
        var beginTimePick = $('#beginDate').datetimepicker({
            lang:'en',
            timepicker:false,
            validateOnBlur: false,
            format:'Y-m-d ',
            formatDate:'Y-m-d',
            onChangeDate: function(dateText, inst) {
                endTimePick.datetimepicker('minDate',new Date(dateText.replace("-", "-")));
            }
        });
        var endTimePick = $('#endDate').datetimepicker({
            lang:'en',
            timepicker:false,
            validateOnBlur: false,
            format:'Y-m-d ',
            formatDate:'Y-m-d'
            // maxDate:'+1970/01/01',
        });
        var agrPayDtPick = $('#agrPayDt').datetimepicker({
            lang:'en',
            timepicker:false,
            validateOnBlur: false,
            format:'Y-m-d ',
            formatDate:'Y-m-d'
            // maxDate:'+1970/01/01',
        });

        // 付款类型转换
        function convertCorpBusSubTyp(busType) {
            switch (busType) {
                case "0301":
                    return $.i18n.t("tran.accountTransfer");
                case "0302":
                    return $.i18n.t("tran.cardTransfer");
                case "0303":
                    return $.i18n.t("tran.faceTransfer");
                default:
                    return corpBusSubTyp;
            }
        }

        // 订单状态转换
        function convertOrdSts(ordSts) {
            switch (ordSts) {
                case "S":
                    return $.i18n.t("tran.tranSucc");
                case "F":
                    return $.i18n.t("tran.tranFail");
                case "P":
                    return $.i18n.t("tran.waitPay");
                case "A1":
                    return $.i18n.t("tran.waitReview");
                default:
                    return ordSts;
            }
        }

    </script>
</body>

</html>