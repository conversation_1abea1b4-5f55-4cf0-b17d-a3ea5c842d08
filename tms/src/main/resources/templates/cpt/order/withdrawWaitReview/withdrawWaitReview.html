<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="cpo.title1"></title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>
        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>
            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="cpo.head1"></h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="cpo.firstLevel"></a>
                        </li>
                        <li>
                            <a data-i18n="cpo.secondLevelOrderMgr"></a>
                        </li>
                        <li>
                            <strong data-i18n="cpo.head1"></strong>
                        </li>
                    </ol>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!--查询条件-->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-4">
                                        <!--请求订单号-->
                                        <label class="col-sm-4 control-label" for="reqOrdNo" data-i18n="cpo.reqOrdNo"></label>
                                        <div class="col-sm-8">
                                            <input name="reqOrdNo" id="reqOrdNo" class="form-control" value=""/>
                                        </div>
                                        <!--付款银行-->
                                        <label class="col-sm-4 control-label" for="rutCorg" data-i18n="cpo.rutCorg"></label>
                                        <div class="col-sm-8">
                                            <select name="rutCorg" id="rutCorg" class="form-control">
                                                <option value="" data-i18n="cpo.selectByNull" selected></option>
                                                <option value="ICBC" data-i18n="corg.ICBC"></option>
                                                <option value="ABC" data-i18n="corg.ABC"></option>
                                                <option value="CCB" data-i18n="corg.CCB"></option>
                                                <option value="BCM" data-i18n="corg.BCM"></option>
                                                <option value="BOC" data-i18n="corg.BOC"></option>
                                                <option value="CBP" data-i18n="corg.CBP"></option>
                                                <option value="ABA" data-i18n="corg.ABA"></option>
                                                <option value="ACLEDA" data-i18n="corg.ACLEDA"></option>
                                            </select>
                                        </div>
                                        <!--手机号/商户号-->
                                        <label class="col-sm-4 control-label" for="userId" data-i18n="cpo.userId"></label>
                                        <div class="col-sm-8">
                                            <input name="userId" id="userId" class="form-control" value=""/>
                                        </div>
                                    </div>
                                    <div class="form-group col-sm-4">
                                        <!--付款订单号-->
                                        <label class="col-sm-4 control-label" for="wcOrdNo" data-i18n="cpo.wcOrdNo"></label>
                                        <div class="col-sm-8">
                                            <input name="wcOrdNo" id="wcOrdNo" class="form-control" value=""/>
                                        </div>
                                        <!--付款类型-->
                                        <label class="col-sm-4 control-label" for="corpBusSubTyp" data-i18n="cpo.corpBusSubTyp"></label>
                                        <div class="col-sm-8">
                                            <select name="corpBusSubTyp" id="corpBusSubTyp" class="form-control" value="">
                                                <option value="" data-i18n="cpo.selectByNull" selected></option>
                                                <option value="0601" data-i18n="cpo.perWithdraw" ></option>
                                                <option value="0602" data-i18n="cpo.cardWithdraw" ></option>
                                                <option value="0603" data-i18n="cpo.mercWithdraw" ></option>
                                                <option value="0604" data-i18n="cpo.perHallWithdraw" ></option>
                                                <option value="0605" data-i18n="cpo.mercHallWithdraw" ></option>
                                            </select>
                                        </div>
                                        <!--提交日期-->
                                        <label class="col-sm-4 control-label" for="beginDate" data-i18n="cpo.submitDate"></label>
                                        <div class="col-sm-4">
                                            <input name="beginDate" id="beginDate" class="form-control" value=""/>
                                        </div>
                                        <div class="col-sm-4">
                                            <input name="endDate" id="endDate" class="form-control" value=""/>
                                        </div>
                                    </div>
                                    <div class="form-group col-sm-4">
                                        <!--银行流水号-->
                                        <label class="col-sm-4 control-label" for="rutCorgJrn" data-i18n="cpo.rutCorgJrn"></label>
                                        <div class="col-sm-8">
                                            <input name="rutCorgJrn" id="rutCorgJrn" class="form-control" value=""/>
                                        </div>
                                        <!--订单状态-->
                                        <label class="col-sm-4 control-label" for="ordSts" data-i18n="cpo.ordSts"></label>
                                        <div class="col-sm-8">
                                            <select name="ordSts" id="ordSts" class="form-control" value="">
                                                <option value="" data-i18n="cpo.selectByNull" selected></option>
                                                <option value="S1" data-i18n="cpo.paymentSucc"></option>
                                                <option value="F1" data-i18n="cpo.paymentFail"></option>
                                                <option value="W3" data-i18n="cpo.paymentProcessing"></option>
                                                <option value="R1" data-i18n="cpo.partRefund"></option>
                                                <option value="R2" data-i18n="cpo.totalRefund"></option>
                                            </select>
                                        </div>
                                        <!--提交日期-->
                                        <label class="col-sm-4 control-label" for="agrPayDt" data-i18n="cpo.agrPayDt"></label>
                                        <div class="col-sm-8">
                                            <input name="agrPayDt" id="agrPayDt" class="form-control" value=""/>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-sm-4 col-sm-offset-1">
                                            <button type="button" id="searchBtn" class="btn btn-primary" onclick="search()" data-i18n="cpo.searchList"></button>
                                            <button type="button" id="viewDetailBtn" class="btn btn-primary" data-toggle="modal" onclick="viewDetail()" data-i18n="cpo.viewModal"></button>
                                            <button type="button" id="payHandleBtn" class="btn btn-primary" data-toggle="modal" onclick="payHandle()" data-i18n="cpo.handleModal1"></button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!--汇款确认/退回表单-->
                <div th:replace="cpt/order/withdrawWaitReview/withdrawModal"></div>

                <!--数据表格-->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="orderInf" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="cpo.reqOrdNo"></th>
                                                <th data-i18n="cpo.wcOrdNo"></th>
                                                <th data-i18n="cpo.userId"></th>
                                                <th data-i18n="cpo.userNm"></th>
                                                <th data-i18n="cpo.agrPayDt"></th>
                                                <th data-i18n="cpo.ordDt"></th>
                                                <th data-i18n="cpo.ordTm"></th>
                                                <th data-i18n="cpo.ordSuccDt"></th>
                                                <th data-i18n="cpo.ordSts"></th>
                                                <th data-i18n="cpo.corpBusSubTyp"></th>
                                                <th data-i18n="cpo.rutCorg"></th>
                                                <th data-i18n="cpo.wcAplAmt"></th>
                                                <th data-i18n="cpo.capCorg"></th>
                                                <th data-i18n="cpo.capCrdNm"></th>
                                                <th data-i18n="cpo.subBranch"></th>
                                                <th data-i18n="cpo.crdNo"></th>
                                                <th data-i18n="cpo.reason"></th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <div th:replace="script"></div>

    <script>
        var editor;
        var table;

        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }

            i18nLoad.then(function () {
                editor = new $.fn.dataTable.Editor({
                    table: "#orderInf",
                    idSrc: 'wcOrdNo',
                    fields: [
                        {name: "id", type: "hidden"},
                        {label: $.i18n.t("cpo.reqOrdNo"), name: "reqOrdNo"},
                        {label: $.i18n.t("cpo.wcOrdNo"), name: "wcOrdNo"},
                        {label: $.i18n.t("cpo.userId"), name: "userId"},
                        {label: $.i18n.t("cpo.userNm"), name: "userNm"},
                        {label: $.i18n.t("cpo.agrPayDt"), name: "agrPayDt"},
                        {label: $.i18n.t("cpo.ordDt"), name: "ordDt"},
                        {label: $.i18n.t("cpo.ordTm"), name: "ordTm"},
                        {label: $.i18n.t("cpo.ordSts"), name: "ordSts"},
                        {label: $.i18n.t("cpo.corpBusSubTyp"), name: "corpBusSubTyp"},
                        {label: $.i18n.t("cpo.rutCorg"), name: "rutCorg"},
                        {label: $.i18n.t("cpo.wcAplAmt"), name: "wcAplAmt"},
                        {label: $.i18n.t("cpo.capCorg"), name: "capCorg"},
                        {label: $.i18n.t("cpo.capCrdNm"), name: "capCrdNm"},
                        {label: $.i18n.t("cpo.subBranch"), name: "subBranch"},
                        {label: $.i18n.t("cpo.crdNo"), name: "crdNo"},
                        {label: $.i18n.t("cpo.reason"), name: "rmk"}
                    ]
                });

                table = $('#orderInf').DataTable({
                    dom: 'Blfrtip',
                    columnDefs: [{
                        targets:[0,1,2,12,13,14],//指定哪几列
                        render: function(data){
                            return "\u200C" + data;
                        }
                    }],
                    ajax: {
                        contentType: 'application/json',
                        url: '/cpt/order/withdraw/findAll',
                        type: 'post',
                        data: function (d) {
                            d.extra_search = {
                                "reqOrdNo" : $("#reqOrdNo").val(),
                                "rutCorg" : $("#rutCorg").val(),
                                "userId" : $("#userId").val(),
                                "wcOrdNo" : $("#wcOrdNo").val(),
                                "corpBusSubTyp" : $("#corpBusSubTyp").val(),
                                "beginDate" : $("#beginDate").val(),
                                "endDate" : $("#endDate").val(),
                                "agrPayDt" : $("#agrPayDt").val(),
                                "rutCorgJrn" : $("#rutCorgJrn").val(),
                                "reviewFlag" : '0',
                                "ordSts" : $("#ordSts").val()
                            };
                            return JSON.stringify(d);
                        }
                    },
                    serverSide: true,
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    select: true,
                    buttons: [
//                        {extend: "create", editor: editor},
//                        {extend: "edit", editor: editor},
//                        {extend: "remove", editor: editor},
                        {extend: 'copyHtml5'},
                        {extend: 'csvHtml5'},
                        {extend: 'excelHtml5', title: '付款订单列表'},
//                        'copy',
//                        'csv',
//                        'excel',
//                        'pdf',
//                        'print'
                    ],
                    columns: [{
                        data: 'reqOrdNo'
                    },{
                        data: 'wcOrdNo'
                    },{
                        data: 'userId'
                    },{
                        data: 'userNm'
                    },{
                        data: 'agrPayDt'
                    },{
                        data: 'ordDt'
                    },{
                        data: 'ordTm'
                    },{
                        data: 'ordSuccDt'
                    },{
                        data: 'ordSts',
                        render: function (data, type, row) {
                            switch (data) {
                                case "S1":
                                    return $.i18n.t("cpo.paymentSucc1");
                                case "F1":
                                    return $.i18n.t("cpo.paymentFail1");
                                case "A1":
                                    return $.i18n.t("cpo.waitReview");
                                case "W3":
                                    return $.i18n.t("cpo.paymentProcessing1");
                                default:
                                    return data;
                            }
                        }
                    },{
                        data: 'corpBusSubTyp',
                        render: function (data, type, row) {
                            switch (data) {
                                case "0601":
                                    return $.i18n.t("cpo.perWithdraw");
                                case "0602":
                                    return $.i18n.t("cpo.cardWithdraw");
                                case "0603":
                                    return $.i18n.t("cpo.mercWithdraw");
                                case "0604":
                                    return $.i18n.t("cpo.perHallWithdraw");
                                case "0605":
                                    return $.i18n.t("cpo.mercHallWithdraw");
                                default:
                                    return data;
                            }
                        }
                    },{
                        data: 'rutCorg',
                        render: function (data, type, row) {
                            switch (data) {
                                case "ICBC":
                                    return $.i18n.t("corg.ICBC");
                                case "CMBC":
                                    return $.i18n.t("corg.CMBC");
                                case "CMB":
                                    return $.i18n.t("corg.CMB");
                                case "ABC":
                                    return $.i18n.t("corg.ABC");
                                case "BEA":
                                    return $.i18n.t("corg.BEA");
                                case "CBP":
                                    return $.i18n.t("corg.CBP");
                                case "ABA":
                                    return $.i18n.t("corg.ABA");
                                case "ACLEDA":
                                    return $.i18n.t("corg.ACLEDA");
                                case "HALL":
                                    return $.i18n.t("corg.HALL");
                                default:
                                    return data;
                            }
                        }
                    },{
                        data: 'wcAplAmt'
                    },{
                        data: 'capCorg',
                        render: function (data, type, row) {
                            switch (data) {
                                case "ICBC":
                                    return $.i18n.t("corg.ICBC");
                                case "CMBC":
                                    return $.i18n.t("corg.CMBC");
                                case "CMB":
                                    return $.i18n.t("corg.CMB");
                                case "CBP":
                                    return $.i18n.t("corg.CBP");
                                case "ABC":
                                    return $.i18n.t("corg.ABC");
                                case "BEA":
                                    return $.i18n.t("corg.BEA");
                                case "ABA":
                                    return $.i18n.t("corg.ABA");
                                case "ACLEDA":
                                    return $.i18n.t("corg.ACLEDA");
                                default:
                                    return data;
                            }
                        }
                    },{
                        data: 'capCrdNm'
                    },{
                        data: 'subBranch'
                    },{
                        data: 'crdNo'
                    },{
                        data: 'rmk'
                    }]
                });

            });

            //modal模态框隐藏后处理
            $('#handleModal').on('hide.bs.modal',
                function () {
                    $("#handleReason").val("");
                }
            );
            //modal模态框隐藏后处理
            $('#handleModal').on('shown.bs.modal',
                function () {
                    var row = table.row('.selected');
                    if(row.length == 0) {
                        $("#handleModal").modal("hide");
                    }
                }
            );
        });

        <!--查询按钮-->
        function search() {
            table.ajax.reload();
        }

        <!--清楚modal中的输入框的值-->
        function clearModalValues() {
            $("#user_id").val("");
            $("#corp_bus_sub_typ").val("");
            $("#rut_corg").val("");
            $("#wc_apl_amt").val("");
            $("#cap_corg").val("");
            $("#cap_crd_nm").val("");
            $("#crd_no").val("");
            $("#ord_sts").val("");
            $("#handleReason").val("");
            $("#wc_ord_no").val("");
        }

        <!--查看明细-->
        function viewDetail() {
            clearModalValues();
            $("#muilRowHide").show() ;
            $("#payConfirm").hide();
            $("#payCancel").hide();
            $("#handleReason").attr("readonly", "readonly");
            $("#handleReason").removeAttr("placeholder");

            <!--获取表格中选中的单行-->
            var row = table.row('.selected');
            if(row.length == 0) {
                swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.shouldSelectOne"), "error");
                return;
            }

            <!--获取选中行的内容-->
            var rowData = table.row(row).data();
            var wcOrdNo = rowData["wcOrdNo"];
            $.ajax({
                url:"/cpt/order/withdraw/findOrderInfo",
                data:{
                    "wcOrdNo": wcOrdNo
                },
                dataType: "json",
                type:"post",
                success:function(data) {
                    if (data != null) {
                        $("#handleModal").modal("show");
                        $(".modal-backdrop").remove();//删除class值为modal-backdrop的标签，可去除阴影
                        <!--获取选中行的内容-->
                        $("#user_id").val( data.userId );
                        $("#ord_dt_tm").val( data.createTime );
                        $("#corp_bus_sub_typ").val( convertCorpBusSubTyp(data.corpBusSubTyp) );
                        $("#rut_corg").val( convertOrng(data.rutCorg) );
                        $("#wc_apl_amt").val( data.wcAplAmt );
                        $("#cap_corg").val( convertOrng(data.capCorg) );
                        $("#cap_crd_nm").val( data.capCrdNm );
                        $("#crd_no").val( data.crdNo );
                        $("#ord_sts").val( convertOrdSts(data.ordSts) );
                        $("#handleReason").val( data.rmk );
                        $("#wc_ord_no").val( data.wcOrdNo );
                    } else {
                        <!--隐藏模态框-->
                        $("#handleModal").modal("hide");
                        swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.detailNull"), "error");
                    }
                },
                error: function() {
                    $("#handleModal").modal("hide");
                    swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.systemException"), "error");
                }
            });
        }

        <!--处理汇款订单-->
        function payHandle() {
            $(".modal-backdrop").remove();//删除class值为modal-backdrop的标签，可去除阴影
            clearModalValues();
            $("#handleReason").removeAttr("readonly");
            $("#handleReason").attr("placeholder", $.i18n.t("cpo.lessThan20words"));
            $("#payConfirm").show();
            $("#payCancel").show();

            <!--获取表格中选中的多行-->
            var row = table.rows('.selected').data();
            console.log(table.rows('.selected').data().length);
            if(row.length == 0) {
                swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.shouldSelectOne"), "error");
                return;
            } else  if (row.length == 1 ) {
                <!--获取选中行的内容-->
                $("#muilRowHide").show() ;
                var rowData = row[0];
                var wcOrdNo = rowData["wcOrdNo"];
                var ordSts = rowData["ordSts"];
                if (ordSts == "S1" || ordSts == "F1") {
                    //订单状态为 S1-交易成功 、F1-交易失败，则不能处理汇款订单
                    swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.ordStsError"), "error");
                    return;
                }

                //异步调起后台服务
                $.ajax({
                    url: "/cpt/order/withdraw/findOrderInfo",
                    data: {
                        "wcOrdNo": wcOrdNo
                    },
                    dataType: "json",
                    type: "post",
                    success: function (data) {
                        if (data != null) {
                            $("#handleModal").modal("show");
                            $(".modal-backdrop").remove();//删除class值为modal-backdrop的标签，可去除阴影
                            <!--获取选中行的内容-->
                            $("#user_id").val(data.userId);
                            $("#ord_dt_tm").val(data.createTime);
                            $("#corp_bus_sub_typ").val(convertCorpBusSubTyp(data.corpBusSubTyp));
                            $("#rut_corg").val(convertOrng(data.rutCorg));
                            $("#wc_apl_amt").val(data.wcAplAmt);
                            $("#cap_corg").val(convertOrng(data.capCorg));
                            $("#cap_crd_nm").val(data.capCrdNm);
                            $("#crd_no").val(data.crdNo);
                            $("#ord_sts").val(convertOrdSts(data.ordSts));
                            $("#wc_ord_no").val(data.wcOrdNo);
                        } else {
                            <!--隐藏模态框-->
                            $("#handleModal").modal("hide");
                            swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.detailNull"), "error");
                        }
                    },
                    error: function () {
                        $("#handleModal").modal("hide");
                        swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.systemException"), "error");
                    }
                });
            } else if (row.length > 1) {
                for (var i = 0 ; i < row.length ; i++ ) {
                    var rowData = row[i] ;
                    var ordSts = rowData["ordSts"];
                    console.log("hshs"+ordSts)
                    if ( ordSts == "S1" || ordSts == "F1" ) {
                        //订单状态为 S1-交易成功 、F1-交易失败，则不能处理汇款订单
                        swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.ordStsError"), "error");
                        return;
                    }
                }
                $("#muilRowHide").hide() ;
                $("#handleModal").modal("show");

            }


        }
        function getSelectOrderNo() {
            var row = table.rows('.selected').data();
            var wcOrdNos = [];
            for (var i=0 ; i < row.length ; i++ ) {
                var rowData = row[i] ;
                var wcOrdNo = rowData["wcOrdNo"];
                wcOrdNos.push(wcOrdNo);
            }
            return wcOrdNos ;
        }
        <!--付款确认-->
        function payConfirm() {
            var handleReason = $.trim($("#handleReason").val());
            if(handleReason == null || handleReason == "") {
                swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.reasonNull"), "error");
                return;
            }
            //确认提交弹出框
            swal({
                title: $.i18n.t("cpo.payConfirmTitle1"),
                text: $.i18n.t("cpo.payConfirmText"),
                type: "warning",
                showCancelButton: true,
                cancelButtonText: $.i18n.t("cpo.swal-cancel"),
                confirmButtonColor: "#DD6B55",
                confirmButtonText: $.i18n.t("cpo.swal-confirm"),
                closeOnConfirm: true
            }, function () {
                <!--隐藏模态框-->
                $("#handleModal").modal("hide");

                <!--ajax异步调起后台服务-->
                var ordSts = "S1";
                var wcOrdNo = $("#wc_ord_no").val();
                var wcOrdNos = getSelectOrderNo();
                console.log(wcOrdNos)
                $.ajax({
                    url:"/cpt/order/withdraw/payHandlerBatch",
                    data:{
                        "wcOrdNos": wcOrdNos,
                        "reason" : handleReason,
                        "ordSts" : ordSts
                    },
                    dataType: "json",
                    type:"post",
                    success:function(data){
                        var msgCd = data.msgCd;
                        if (msgCd == "CPO00000") {
                            swal($.i18n.t("cpo.swal-success"), $.i18n.t("cpo.payConfirmSuss"), "success");
                            table.ajax.reload();
                        } else if (msgCd == "1") {
                            swal($.i18n.t("cpo.swal-success"),  $.i18n.t("cpo.paymentNum")+data.successNum, "success");
                        } else {
                            swal($.i18n.t("cpo.swal-fail"),  $.i18n.t("cpo.payConfirmFail"), "error");
                        }
                    },
                    error: function(){
                        swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.systemException"), "error");
                    }
                });
            });
        }

        <!--付款退回-->
        function payCancel() {
            var handleReason = $.trim($("#handleReason").val());
            if(handleReason == null || handleReason == "") {
                swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.reasonNull"), "error");
                return;
            }
            swal({
                title: $.i18n.t("cpo.payConfirmTitle1"),
                text: $.i18n.t("cpo.payCancelText"),
                type: "warning",
                showCancelButton: true,
                cancelButtonText: $.i18n.t("cpo.swal-cancel"),
                confirmButtonColor: "#DD6B55",
                confirmButtonText: $.i18n.t("cpo.swal-confirm"),
                closeOnConfirm: true
            }, function () {
                <!--隐藏模态框-->
                $("#handleModal").modal("hide");

                <!--ajax异步调起后台服务-->
                var ordSts = "F1";
                var wcOrdNo = $("#wc_ord_no").val();
                var wcOrdNos = getSelectOrderNo();
                $.ajax({
                    url:"/cpt/order/withdraw/payHandlerBatch",
                    data:{
                        "wcOrdNos": wcOrdNos,
                        "reason" : handleReason,
                        "ordSts" : ordSts
                    },
                    dataType: "json",
                    type:"post",
                    success:function(data){
                        var msgCd = data.msgCd;
                        if (msgCd == "CPO00000") {
                            swal($.i18n.t("cpo.swal-success"), $.i18n.t("cpo.payCancelSucc"), "success");
                            table.ajax.reload();
                        } else if (msgCd == "1") {
                            swal($.i18n.t("cpo.swal-success"),  $.i18n.t("cpo.paymentNum")+data.successNum, "success");
                        } else {
                            swal($.i18n.t("cpo.swal-fail"),  $.i18n.t("cpo.payCancelFail"), "error");
                        }
                        var msgCd = data.msgCd;
                        if (msgCd == "CPO00000") {
                            swal($.i18n.t("cpo.swal-success"), $.i18n.t("cpo.payCancelSucc"), "success");
                            table.ajax.reload();
                        } else {
                            swal($.i18n.t("cpo.swal-fail"), msgCd + $.i18n.t("cpo.payCancelFail"), "error");
                        }
                    },
                    error: function() {
                        swal($.i18n.t("cpo.swal-fail"), $.i18n.t("cpo.systemException"), "error");
                    }
                });
            });
        }

        <!--初始化日期控件-->
        var beginTimePick = $('#beginDate').datetimepicker({
            lang:'en',
            timepicker:false,
            validateOnBlur: false,
            format:'Y-m-d ',
            formatDate:'Y-m-d',
            onChangeDate: function(dateText, inst) {
                endTimePick.datetimepicker('minDate',new Date(dateText.replace("-", "-")));
            }
        });
        var endTimePick = $('#endDate').datetimepicker({
            lang:'en',
            timepicker:false,
            validateOnBlur: false,
            format:'Y-m-d ',
            formatDate:'Y-m-d'
            // maxDate:'+1970/01/01',
        });
        var agrPayDtPick = $('#agrPayDt').datetimepicker({
            lang:'en',
            timepicker:false,
            validateOnBlur: false,
            format:'Y-m-d ',
            formatDate:'Y-m-d'
            // maxDate:'+1970/01/01',
        });

        <!--付款类型转换-->
        function convertCorpBusSubTyp(corpBusSubTyp) {
            switch (corpBusSubTyp) {
                case "0601":
                    return $.i18n.t("cpo.perWithdraw");
                case "0602":
                    return $.i18n.t("cpo.cardWithdraw");
                case "0603":
                    return $.i18n.t("cpo.mercWithdraw");
                case "0604":
                    return $.i18n.t("cpo.perHallWithdraw");
                case "0605":
                    return $.i18n.t("cpo.mercHallWithdraw");
                default:
                    return corpBusSubTyp;
            }
        }

        <!--银行名称转换-->
        function convertOrng(corgId) {
            switch (corgId) {
                case "ICBC":
                    return $.i18n.t("corg.ICBC");
                case "CMBC":
                    return $.i18n.t("corg.CMBC");
                case "CMB":
                    return $.i18n.t("corg.CMB");
                case "CBP":
                    return $.i18n.t("corg.CBP");
                case "BEA":
                    return $.i18n.t("corg.BEA");
                case "ABC":
                    return $.i18n.t("corg.ABC");
                case "ABA":
                    return $.i18n.t("corg.ABA");
                case "ACLEDA":
                    return $.i18n.t("corg.ACLEDA");
                default:
                    return corgId;
            }
        }

        <!--订单状态转换-->
        function convertOrdSts(ordSts) {
            switch (ordSts) {
                case "S1":
                    return $.i18n.t("cpo.paymentSucc");
                case "F1":
                    return $.i18n.t("cpo.paymentFail");
                case "W3":
                    return $.i18n.t("cpo.paymentProcessing");
                default:
                    return ordSts;
            }
        }

    </script>
</body>

</html>