<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="rpt.rptRun.mgrOpr.title"></title>
    <div th:replace="head"></div>
    <style>
        div.floatright {
            display: inline;
            float: right;
            padding-left: 20px;
            padding-right: 20px;
        }

        ._right {
            display: inline;
            float: right;
            padding-right: 15px;
        }
    </style>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>
        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>
            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="rpt.rptRun.mgrOpr.content"></h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="nav.rpt"></a>
                        </li>
                        <li class="active">
                            <strong data-i18n="rpt.rptRun.mgrOpr.content"></strong>
                        </li>
                    </ol>
                </div>
            </div>
            <div class="wrapper wrapper-content  animated fadeInRight">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">

                                <div class="box-header">
                                    <!-- 表头搜索栏 -->
                                    <table style="width: 80%">
                                        <tr>
                                            <td align="center">
                                                <label class="control-label"
                                                    data-i18n="rpt.rptRun.mgrDef.rptId"></label>
                                            </td>
                                            <td>&nbsp; </td>
                                            <td>
                                                <input class="form-control" name="searchId" id="searchId" />
                                            </td>
                                            <td>&nbsp;</td>
                                            <td align="center">
                                                <label class="control-label"
                                                    data-i18n="rpt.rptRun.mgrDef.rptMd"></label>
                                            </td>
                                            <td>&nbsp;</td>
                                            <td>
                                                <input class="form-control" name="searchMd" id="searchMd" />
                                            </td>
                                            <td>&nbsp;</td>
                                            <td align="center">
                                                <label class="control-label"
                                                    data-i18n="rpt.rptRun.mgrDef.rptPeriod"></label>
                                            </td>
                                            <td>&nbsp;</td>
                                            <td>
                                                <select class="form-control" name="searchPeriod" id="searchPeriod">
                                                    <option value="" data-i18n="rpt.constant.all"></option>
                                                    <option value="D" data-i18n="rpt.constant.period.date"></option>
                                                    <option value="W" data-i18n="rpt.constant.period.week"></option>
                                                    <option value="M" data-i18n="rpt.constant.period.month"></option>
                                                    <option value="Q" data-i18n="rpt.constant.period.season"></option>
                                                    <option value="H" data-i18n="rpt.constant.period.halfYear"></option>
                                                    <option value="Y" data-i18n="rpt.constant.period.year"></option>
                                                    <option value="FM" data-i18n="rpt.constant.period.fixMonth">
                                                    </option>
                                                    <option value="FY" data-i18n="rpt.constant.period.fixYear"></option>
                                                    <option value="R" data-i18n="rpt.constant.period.fixDate"></option>
                                                    <option value="O" data-i18n="rpt.constant.period.other"></option>
                                                </select>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="center">
                                                <label class="control-label" for="sBeginTime"
                                                    data-i18n="rpt.rptRun.mgrOpera.startTm"></label>
                                            </td>
                                            <td>&nbsp;</td>
                                            <td>
                                                <input type="text" class="form-control" name="sBeginTime"
                                                    id="sBeginTime">
                                            </td>
                                            <td align="right">—</td>
                                            <td align="center">
                                                <label class="control-label" for="sEndTime"
                                                    data-i18n="rpt.rptRun.mgrOpera.endTm"></label>
                                            </td>
                                            <td>&nbsp;</td>
                                            <td>
                                                <input type="text" class="form-control" name="sBeginTime" id="sEndTime">
                                            </td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>
                                                <button type="button" class="btn btn-w-m btn-primary _right"
                                                    data-i18n="rpt.search" onclick="searchButton()">
                                                </button>
                                            </td>
                                        </tr>


                                    </table>
                                </div>
                                <hr />

                                <div class="table-responsive">
                                    <table id="example" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="rpt.rptRun.mgrDef.rptId"></th>
                                                <th data-i18n="rpt.rptRun.mgrDef.rptNm"></th>
                                                <th data-i18n="rpt.rptRun.mgrDef.rptMd"></th>
                                                <th data-i18n="rpt.rptRun.mgrDef.rptPeriod"></th>
                                                <th data-i18n="rpt.rptRun.mgrDef.rptFileName"></th>
                                                <th data-i18n="rpt.rptRun.mgrDef.rptRunDt"></th>
                                                <th data-i18n="rpt.rptRun.mgrDef.rptRunRs"></th>
                                                <th data-i18n="rpt.rptRun.mgrDef.rptOpera"></th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>
    <div th:replace="script"></div>
    <script>
        var table;
        var valSuccess;
        var valRun;
        var valFail;
        var order = [1, 'asc'];
        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }

            i18nLoad.then(function () {
                valSuccess = $.i18n.t("rpt.constant.operaSts.download");
                valRun = $.i18n.t("rpt.constant.operaSts.running");
                valFail = $.i18n.t("rpt.constant.operaSts.run");
                table = $('#example').DataTable({
                    dom: 'B<"floatright"l>rtip',
                    ajax: {
                        contentType: 'application/json',
                        url: '/rpt/mgr/opera/findAll',
                        type: 'POST',
                        data: function (d) {
                            var reportId = $("#searchId").val();
                            var md = $("#searchMd").val();
                            var pd = $("#searchPeriod").val();
                            var endTime = $("#sEndTime").val();
                            var beginTime = $("#sBeginTime").val();

                            console.info('endTime : ' + endTime);
                            console.info('beginTime : ' + beginTime);

                            d.extra_search = {
                                "reportId": reportId,
                                "md": md,
                                "period": pd,
                                "endTime": endTime,
                                "beginTime": beginTime
                            };
                            return JSON.stringify(d);
                        }
                    },
                    searching: true,
                    serverSide: true,
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    select: true,
                    buttons: [
                    ],
                    columns: [
                        {
                            data: 'reportId'
                        }, {
                            data: 'reportName'
                        }, {
                            data: 'reportModule',
                        }, {
                            data: 'reportPeriod',
                            render: function (data, type, row) {
                                switch (data) {
                                    case 'D':
                                        return $.i18n.t("rpt.constant.period.date");
                                    case 'W':
                                        return $.i18n.t("rpt.constant.period.week");
                                    case 'M':
                                        return $.i18n.t("rpt.constant.period.month");
                                    case 'Q':
                                        return $.i18n.t("rpt.constant.period.season");
                                    case 'H':
                                        return $.i18n.t("rpt.constant.period.halfYear");
                                    case 'Y':
                                        return $.i18n.t("rpt.constant.period.year");
                                    case 'FM':
                                        return $.i18n.t("rpt.constant.period.fixMonth");
                                    case 'FY':
                                        return $.i18n.t("rpt.constant.period.fixYear");
                                    case 'R':
                                        return $.i18n.t("rpt.constant.period.fixDate");
                                    case 'O':
                                        return $.i18n.t("rpt.constant.period.other");
                                }
                            }
                        }, {
                            data: 'reportFilename',
                            render: function (data, type, row) {
                                return '<span  id="f' + row.id + '">' + row.reportFilename + '</span>';
                            }
                        }, {
                            data: 'runDt'
                        }, {
                            data: 'runRs',
                            render: function (data, type, row) {
                                switch (data) {
                                    case '0':
                                        return '<span  id="s' + row.id + '">' + $.i18n.t("rpt.constant.runSts.success") + '</span>';
                                    case '1':
                                        return '<span  id="s' + row.id + '">' + $.i18n.t("rpt.constant.runSts.fail") + '</span>';
                                }
                            }
                        }, {
                            data: 'runRs',
                            render: function (data, type, row) {
                                switch (data) {
                                    case '0':
                                        return '<input type="button" class="btn btn-w-m btn-primary" id="' + row.id + '" value=' + $.i18n.t("rpt.constant.operaSts.download") + ' onclick="run(' + row.id + ', \'' + row.runRs + '\', \'' + 1 + '\',\' ' + 1 + '\')"/>';
                                    //return '<button type="button" data-i18n="' + $.i18n.t("rpt.constant.runSts.success") + '" onclick="aa()"></button>';
                                    case '1':
                                        return '<input type="button" class="btn btn-w-m btn-primary" id="' + row.id + '" value=' + $.i18n.t("rpt.constant.operaSts.run") + ' onclick="run(' + row.id + ', \'' + row.runRs + '\', \'' + row.reportPath.replace(/\\/g, '\\\\') + '\',\'' + row.runDt + '\')"/>';

                                    //return  '<button type="button" data-i18n="' + $.i18n.t("rpt.constant.runSts.fail") + '" onclick="aa()"></button>';
                                }
                            }
                        }
                    ]
                });
            });

        });

        function run(bid, sts, path, runDt) {
            if (sts == '0') {
                window.location.href = "/rpt/mgr/opera/getRsFile?id=" + bid;

            } else {
                //alert((runDt).length);
                $("input[id='" + bid + "']").val(valRun);
                $.ajax({
                    url: "/rpt/mgr/opera/run",
                    data: {
                        "id": bid,
                        "runDt": $.trim(runDt)
                    },
                    dataType: "json",
                    type: "post",
                    success: function (data) {
                        console.info(data.data[0].fileName);
                        console.info(data.data[0].sts);
                        if (data.data[0].sts == "0") {
                            $("input[id='" + bid + "']").val(valSuccess);
                            $("#f" + bid).html(data.data[0].fileName);
                            $("#s" + bid).html($.i18n.t("rpt.constant.runSts.success"));
                            document.getElementById(bid).onclick = function () {
                                run(bid, '0', '1', '1');
                            };
                        } else {
                            $("input[id='" + bid + "']").val(valFail);
                            $("#s" + bid).html($.i18n.t("rpt.constant.runSts.fail"));

                        }
                    },
                    error: function () {
                        $("input[id='" + bid + "']").val(valFail);
                        //$("#f" + bid).html(data.data[0].fileName);
                        $("#s" + bid).html($.i18n.t("rpt.constant.runSts.fail"));
                    }
                });
            }

        }


        /**初始化日期控件**/

        var sbeginTimePick = $('#sBeginTime').datetimepicker({
            lang: 'en',
            timepicker: false,
            validateOnBlur: false,
            format: 'Y-m-d ',
            formatDate: 'Y-m-d',
            onChangeDate: function (dateText, inst) {
                endTimePick.datetimepicker('minDate', new Date(dateText.replace("-", "-")));
            }
        });

        var sendTimePick = $('#sEndTime').datetimepicker({
            lang: 'en',
            timepicker: false,
            validateOnBlur: false,
            format: 'Y-m-d ',
            formatDate: 'Y-m-d',
            // maxDate:'+1970/01/01',
        });
        /**初始化日期控件**/
        var beginTimePick = $('#beginTime').datetimepicker({
            lang: 'en',
            timepicker: true,
            validateOnBlur: false,
            format: 'Y-m-d H:i:00',
            formatDate: 'Y-m-d',
            closeOnInputClick: '',
            onChangeDate: function (dateText, inst) {
                endTimePick.datetimepicker('minDate', new Date(dateText.replace("-", "-")));
            }
        });

        var endTimePick = $('#endTime').datetimepicker({
            lang: 'en',
            timepicker: true,
            validateOnBlur: false,
            format: 'Y-m-d H:i:00',
            formatDate: 'Y-m-d',
            // maxDate:'+1970/01/01',
        });

        function searchButton() {
            var searchId = $("input[name='searchId']").val();
            var searchMd = $("input[name='searchMd']").val();
            var searchPeriod = $("select[name='searchPeriod']").val();
            console.log(searchId);
            console.log(searchMd);
            console.log(searchPeriod);

            table.column(0).search(searchId)
                .column(2).search(searchMd)
                .column(3).search(searchPeriod)
                .draw();
        }
    </script>
</body>

</html>