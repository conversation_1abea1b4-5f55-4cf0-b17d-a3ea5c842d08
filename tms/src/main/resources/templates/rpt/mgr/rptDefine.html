<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="rpt.rptRun.mgrDef.title"></title>
    <div th:replace="head"></div>
    <style>
        div.floatright {
            display: inline;
            float: right;
            padding-left: 20px;
            padding-right: 20px;
        }

        ._right {
            display: inline;
            float: right;
            padding-right: 15px;
        }
    </style>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>
        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>
            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="rpt.rptRun.mgrDef.content"></h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="nav.rpt"></a>
                        </li>
                        <li class="active">
                            <strong data-i18n="rpt.rptRun.mgrDef.content"></strong>
                        </li>
                    </ol>
                </div>
            </div>
            <div class="wrapper wrapper-content  animated fadeInRight">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">

                                <div class="box-header">
                                    <!-- 表头搜索栏 -->
                                    <table style="width: 80%">
                                        <tr>
                                            <td align="center">
                                                <label class="control-label"
                                                    data-i18n="rpt.rptRun.mgrDef.rptId"></label>
                                            </td>
                                            <td>&nbsp; </td>
                                            <td>
                                                <input class="form-control" name="searchId" />
                                            </td>
                                            <td>&nbsp;</td>
                                            <td align="center">
                                                <label class="control-label"
                                                    data-i18n="rpt.rptRun.mgrDef.rptMd"></label>
                                            </td>
                                            <td>&nbsp;</td>
                                            <td>
                                                <input class="form-control" name="searchMd" />
                                            </td>
                                            <td>&nbsp;</td>
                                            <td align="center">
                                                <label class="control-label"
                                                    data-i18n="rpt.rptRun.mgrDef.rptPeriod"></label>
                                            </td>
                                            <td>&nbsp;</td>
                                            <td>
                                                <select class="form-control" name="searchPeriod">
                                                    <option value="" data-i18n="rpt.constant.all"></option>
                                                    <option value="D" data-i18n="rpt.constant.period.date"></option>
                                                    <option value="W" data-i18n="rpt.constant.period.week"></option>
                                                    <option value="M" data-i18n="rpt.constant.period.month"></option>
                                                    <option value="Q" data-i18n="rpt.constant.period.season"></option>
                                                    <option value="H" data-i18n="rpt.constant.period.halfYear"></option>
                                                    <option value="Y" data-i18n="rpt.constant.period.year"></option>
                                                    <option value="FM" data-i18n="rpt.constant.period.fixMonth">
                                                    </option>
                                                    <option value="FY" data-i18n="rpt.constant.period.fixYear"></option>
                                                    <option value="R" data-i18n="rpt.constant.period.fixDate"></option>
                                                    <option value="O" data-i18n="rpt.constant.period.other"></option>
                                                </select>
                                            </td>
                                            <td>&nbsp;</td>
                                            <td>
                                                <button type="button" class="btn btn-w-m btn-primary _right"
                                                    data-i18n="rpt.search" onclick="searchButton()">
                                                </button>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <hr />

                                <div class="table-responsive">
                                    <table id="example" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="rpt.rptRun.mgrDef.rptId"></th>
                                                <th data-i18n="rpt.rptRun.mgrDef.rptNm"></th>
                                                <th data-i18n="rpt.rptRun.mgrDef.rptMd"></th>
                                                <th data-i18n="rpt.rptRun.mgrDef.rptPeriod"></th>
                                                <th data-i18n="rpt.rptRun.mgrDef.rptDate"></th>
                                                <th data-i18n="rpt.rptRun.mgrDef.rptClass"></th>
                                                <th data-i18n="rpt.rptRun.mgrDef.rptMathon"></th>
                                                <th data-i18n="rpt.rptRun.mgrDef.rptPam"></th>
                                                <th data-i18n="rpt.rptRun.mgrDef.rptPath"></th>
                                                <th data-i18n="rpt.rptRun.mgrDef.rptFileName"></th>
                                                <th data-i18n="rpt.rptRun.mgrDef.level"></th>
                                                <th data-i18n="rpt.rptRun.mgrDef.memo"></th>
                                                <th data-i18n="rpt.rptRun.mgrDef.sts"></th>
                                                <th data-i18n="rpt.rptRun.createUserId"></th>
                                                <th data-i18n="rpt.rptRun.createTime"></th>
                                                <th data-i18n="rpt.rptRun.modifyUserId"></th>
                                                <th data-i18n="rpt.rptRun.modifyTime"></th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>
    <div th:replace="script"></div>
    <script>
        var table;
        var editor;
        var order = [1, 'asc'];
        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }

            i18nLoad.then(function () {

                editor = new $.fn.dataTable.Editor({
                    ajax: {
                        create: {
                            type: 'POST',
                            url: '/rpt/mgr/define/add',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        },
                        edit: {
                            type: 'POST',
                            url: '/rpt/mgr/define/edit',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        }
                    },
                    table: "#example",
                    idSrc: 'reportId',
                    fields: [
                        { label: $.i18n.t("rpt.rptRun.mgrDef.rptId"), name: "reportId" },
                        { label: $.i18n.t("rpt.rptRun.mgrDef.rptNm"), name: "reportName" },
                        { label: $.i18n.t("rpt.rptRun.mgrDef.rptMd"), name: "reportModule" },
                        {
                            label: $.i18n.t("rpt.rptRun.mgrDef.rptPeriod"), name: "reportPeriod",
                            type: "select",
                            id: "atd",
                            def: function () {

                                $(this).on('selected', function () {
                                    alert("sdf");
                                });

                            },
                            options: [
                                { label: $.i18n.t("rpt.constant.period.date"), value: "D" },
                                { label: $.i18n.t("rpt.constant.period.week"), value: "W" },
                                { label: $.i18n.t("rpt.constant.period.month"), value: "M" },
                                { label: $.i18n.t("rpt.constant.period.season"), value: "Q" },
                                { label: $.i18n.t("rpt.constant.period.halfYear"), value: "H" },
                                { label: $.i18n.t("rpt.constant.period.year"), value: "Y" },
                                { label: $.i18n.t("rpt.constant.period.fixMonth"), value: "FM" },
                                { label: $.i18n.t("rpt.constant.period.fixYear"), value: "FY" },
                                { label: $.i18n.t("rpt.constant.period.fixDate"), value: "R" },
                                { label: $.i18n.t("rpt.constant.period.other"), value: "O" }
                            ]
                        },
                        {
                            label: $.i18n.t("rpt.rptRun.mgrDef.rptDate"), name: "reportDate",
                            type: "datetime",
                            def: function () { return new Date(); }
                        },
                        { label: $.i18n.t("rpt.rptRun.mgrDef.rptClass"), name: "reportClass" },
                        { label: $.i18n.t("rpt.rptRun.mgrDef.rptMathon"), name: "reportMathon" },
                        { label: $.i18n.t("rpt.rptRun.mgrDef.rptPam"), name: "reportPam" },
                        { label: $.i18n.t("rpt.rptRun.mgrDef.rptPath"), name: "reportPath" },
                        { label: $.i18n.t("rpt.rptRun.mgrDef.rptFileName"), name: "reportFilename" },
                        {
                            label: $.i18n.t("rpt.rptRun.mgrDef.level"), name: "denseLevel",
                            type: "select",
                            options: [
                                { label: $.i18n.t("rpt.constant.level.high"), value: "H" },
                                { label: $.i18n.t("rpt.constant.level.middle"), value: "M" },
                                { label: $.i18n.t("rpt.constant.level.low"), value: "L" }
                            ]
                        },
                        { label: $.i18n.t("rpt.rptRun.mgrDef.memo"), name: "memo" },
                        {
                            label: $.i18n.t("rpt.rptRun.mgrDef.sts"), name: "reportSts",
                            type: "select",
                            options: [
                                { label: $.i18n.t("rpt.constant.sts.effective"), value: "0" },
                                { label: $.i18n.t("rpt.constant.sts.invalid"), value: "1" }
                            ]
                        }
                    ],
                    i18n: {
                        create: {
                            button: $.i18n.t("rpt.add"),
                            title: $.i18n.t("rpt.rptRun.mgrDef.add"),
                            submit: $.i18n.t("rpt.add")
                        },
                        edit: {
                            button: $.i18n.t("rpt.edit"),
                            title: $.i18n.t("rpt.rptRun.mgrDef.edit"),
                            submit: $.i18n.t("rpt.edit")
                        }
                    }
                });
                editor.on('preSubmit', function (e, o, action) {
                    var id = editor.field('id');
                    //                        o.id = id.val();
                });
                /*不允许修改编号*/
                editor.on('open', function (e, o, action) {
                    if (action === 'edit') {
                        this.field('reportId').disable();
                    } else {
                        this.field('reportId').enable();
                    }

                });

                table = $('#example').DataTable({
                    dom: 'B<"floatright"l>rtip',
                    ajax: {
                        contentType: 'application/json',
                        url: '/rpt/mgr/define/findAll',
                        type: 'POST',
                        data: function (d) {
                            return JSON.stringify(d);
                        }
                    },
                    searching: true,
                    serverSide: true,
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    select: true,
                    buttons: [
                        { extend: "create", editor: editor },
                        { extend: "edit", editor: editor }
                    ],
                    columns: [
                        {
                            data: 'reportId'
                        }, {
                            data: 'reportName'
                        }, {
                            data: 'reportModule',
                        }, {
                            data: 'reportPeriod',
                            render: function (data, type, row) {
                                switch (data) {
                                    case 'D':
                                        return $.i18n.t("rpt.constant.period.date");
                                    case 'W':
                                        return $.i18n.t("rpt.constant.period.week");
                                    case 'M':
                                        return $.i18n.t("rpt.constant.period.month");
                                    case 'Q':
                                        return $.i18n.t("rpt.constant.period.season");
                                    case 'H':
                                        return $.i18n.t("rpt.constant.period.halfYear");
                                    case 'Y':
                                        return $.i18n.t("rpt.constant.period.year");
                                    case 'FM':
                                        return $.i18n.t("rpt.constant.period.fixMonth");
                                    case 'FY':
                                        return $.i18n.t("rpt.constant.period.fixYear");
                                    case 'R':
                                        return $.i18n.t("rpt.constant.period.fixDate");
                                    case 'O':
                                        return $.i18n.t("rpt.constant.period.other");
                                }
                            }
                        }, {
                            data: 'reportDate'
                        }, {
                            data: 'reportClass'
                        }, {
                            data: 'reportMathon'
                        }, {
                            data: 'reportPam'
                        }, {
                            data: 'reportPath'
                        }, {
                            data: 'reportFilename'
                        }, {
                            data: 'denseLevel',
                            render: function (data, type, row) {
                                switch (data) {
                                    case 'H':
                                        return $.i18n.t("rpt.constant.level.high");
                                    case 'M':
                                        return $.i18n.t("rpt.constant.period.middle");
                                    case 'L':
                                        return $.i18n.t("rpt.constant.period.low");
                                }
                            }
                        }, {
                            data: 'memo'
                        }, {
                            data: 'reportSts',
                            render: function (data, type, row) {
                                switch (data) {
                                    case '0':
                                        return $.i18n.t("rpt.constant.sts.effective");

                                    //$.i18n.t("rpt.constant.sts.effective");
                                    case '1':
                                        return $.i18n.t("rpt.constant.sts.invalid");

                                    //return $.i18n.t("rpt.constant.sts.invalid");
                                }
                            }
                        }, {
                            data: 'createUser'
                        }, {
                            data: 'createTime'
                        }, {
                            data: 'updateUser'
                        }, {
                            data: 'modifyTime'
                        }
                    ]
                });
            });
            $(":selected").change(function () {
                alert("asda");
            });
            $("#atd").change(function () {
                alert("asda");
            });

        });
        function aa() {
            alert("aaa");
        }

        function searchButton() {
            var searchId = $("input[name='searchId']").val();
            var searchMd = $("input[name='searchMd']").val();
            var searchPeriod = $("select[name='searchPeriod']").val();

            table.column(0).search(searchId)
                .column(2).search(searchMd)
                .column(3).search(searchPeriod)
                .draw();
        }
    </script>
</body>

</html>