<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title data-i18n="rsm.riskList.whiteList.title"></title>
    <div th:replace="head"></div>
    <style>
        div.floatright{
            display:inline;
            float:right;
            padding-left:20px;
            padding-right:20px;
        }

        ._right{
            display:inline;
            float:right;
            padding-right:15px;
        }
    </style>
</head>
<body>
<div id="wrapper">
    <div th:replace="nav"></div>
    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>
        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="rsm.riskList.whiteList.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.rsm"></a>
                    </li>
                    <li>
                        <a data-i18n="rsm.riskList.title"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="rsm.riskList.whiteList.content"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">

                            <div class="box-header">
                                <table style="width: 80%">
                                    <tr>
                                        <td align="center">
                                            <label class="control-label" data-i18n="rsm.riskList.idTyp"></label>
                                        </td>
                                        <td>
                                            <select class="form-control" name="searchIdTyp">
                                                <option value="" data-i18n="rsm.constant.all"></option>
                                                <option value="01" data-i18n="rsm.constant.idTyp.userId"></option>
                                                <option value="02" data-i18n="rsm.constant.idTyp.card"></option>
                                                <option value="03" data-i18n="rsm.constant.idTyp.idNo"></option>
                                            </select>
                                        </td>
                                        <td align="center">
                                            <label class="control-label" data-i18n="rsm.riskList.id"></label>
                                        </td>
                                        <td>
                                            <input class="form-control" name="searchId" />
                                        </td>
                                    </tr>
                                    <tr style="padding-top: 10px;">
                                        <td align="center">
                                            <label class="control-label" data-i18n="rsm.riskList.txTyp"></label>
                                        </td>
                                        <td>
                                            <select class="form-control" name="searchTxTyp">
                                                <option value="" data-i18n="rsm.constant.all"></option>
                                                <option value="00" data-i18n="rsm.constant.txTyp.all"></option>
                                                <option value="01" data-i18n="rsm.constant.txTyp.recharge"></option>
                                                <option value="02" data-i18n="rsm.constant.txTyp.consume"></option>
                                                <option value="03" data-i18n="rsm.constant.txTyp.transfer"></option>
                                                <option value="04" data-i18n="rsm.constant.txTyp.withdraw"></option>
                                                <option value="05" data-i18n="rsm.constant.txTyp.seatel"></option>
                                                <option value="06" data-i18n="rsm.constant.txTyp.refunds"></option>
                                                <option value="07" data-i18n="rsm.constant.txTyp.interest"></option>
                                                <option value="08" data-i18n="rsm.constant.txTyp.payment"></option>
                                            </select>
                                        </td>
                                        <td></td>
                                        <td>
                                            <button type="button" class="btn btn-w-m btn-primary _right" data-i18n="rsm.search" onclick="searchButton()">
                                            </button>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <hr/>

                            <div class="table-responsive">
                                <table id="example" class="table table-striped table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th data-i18n="rsm.riskList.liskId"></th>
                                        <th data-i18n="rsm.riskList.id"></th>
                                        <th data-i18n="rsm.riskList.idTyp"></th>
                                        <th data-i18n="rsm.riskList.txTyp"></th>
                                        <th data-i18n="rsm.riskList.beginDt"></th>
                                        <th data-i18n="rsm.riskList.endDt"></th>
                                        <th data-i18n="rsm.riskList.effFlg"></th>
                                        <th data-i18n="rsm.riskList.listRsn"></th>
                                        <th data-i18n="rsm.updOprId"></th>
                                        <th></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>
    </div>
</div>
<div th:replace="script"></div>
<script>
    var table;
    var editor;
    var editor1;
    var order = [1, 'asc'];

    $(document).ready(function () {

        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        i18nLoad.then(function () {
            editor = new $.fn.dataTable.Editor({
                ajax: {
                    create: {
                        type: 'POST',
                        url: '/rsm/risklist/white/add',
                        contentType: 'application/json',
                        data: function (d) {
                            return JSON.stringify(d);
                        }
                    }
                },
                table: "#example",
                idSrc: 'whtListId',
                fields: [
                    {name: "whtListId", type: "hidden"},
                    {
                        label: $.i18n.t("rsm.riskList.idTyp"), name: "idTyp",
                        type:  "select",
                        options: [
                            { label: $.i18n.t("rsm.constant.idTyp.userId"), value: "01" },
                            { label: $.i18n.t("rsm.constant.idTyp.card"), value: "02" },
                            { label: $.i18n.t("rsm.constant.idTyp.idNo"), value: "03" }
                        ]
                    },
                    {label: $.i18n.t("rsm.riskList.id"), name: "id"},
                    {
                        label: $.i18n.t("rsm.riskList.txTyp"), name: "txTyp",
                        type:  "select",
                        options: [
                            { label: $.i18n.t("rsm.constant.txTyp.all"), value: "00" },
                            { label: $.i18n.t("rsm.constant.txTyp.recharge"), value: "01" },
                            { label: $.i18n.t("rsm.constant.txTyp.consume"), value: "02" },
                            { label: $.i18n.t("rsm.constant.txTyp.transfer"), value: "03" },
                            { label: $.i18n.t("rsm.constant.txTyp.withdraw"), value: "04" },
                            { label: $.i18n.t("rsm.constant.txTyp.seatel"), value: "05" },
                            { label: $.i18n.t("rsm.constant.txTyp.refunds"), value: "06" },
                            { label: $.i18n.t("rsm.constant.txTyp.interest"), value: "07" },
                            { label: $.i18n.t("rsm.constant.txTyp.payment"), value: "08" }
                        ]
                    },
                    {
                        label: $.i18n.t("rsm.riskList.beginDt"),
                        name: "beginDt",
                        type: "datetime",
                        def: function () {
                            return new Date();
                        },
                        format: "YYYY-MM-DD HH:mm:ss"
                    },
                    {
                        label: $.i18n.t("rsm.riskList.endDt"),
                        name: "endDt",
                        type: "datetime",
                        def: function () {
                            return new Date();
                        },
                        format: "YYYY-MM-DD HH:mm:ss"
                    },
                    {label: $.i18n.t("rsm.riskList.listRsn"), name: "listRsn"}
                ],
                i18n: {
                    create: {
                        button: $.i18n.t("rsm.add"),
                        title: $.i18n.t("rsm.riskList.addWhite"),
                        submit: $.i18n.t("rsm.add")
                    }
                }
            });

            editor.on('preSubmit', function (e, o, action) {
                var id = editor.field('id');
                o.id = id.val();
            });

            editor1 = new $.fn.dataTable.Editor({
                ajax: {
                    edit: {
                        type: 'POST',
                        url: '/rsm/risklist/white/del',
                        contentType: 'application/json',
                        data: function (d) {
                            return JSON.stringify(d);
                        }
                    }
                },
                table: "#example",
                idSrc: 'whtListId',
                fields: [
                    {name: "whtListId", type: "hidden"},
                    {label: $.i18n.t("rsm.riskList.listRsn"), name: "listRsn"}
                ],
                i18n: {
                    edit: {
                        button: $.i18n.t("rsm.cancer"),
                        title: $.i18n.t("rsm.riskList.delWhite"),
                        submit: $.i18n.t("rsm.cancer")
                    }
                }
            });

            editor1.on('preSubmit', function (e, o, action) {
                var id = editor.field('id');
                o.id = id.val();
            });

            table = $('#example').DataTable({
                dom: 'B<"floatright"l>rtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/rsm/risklist/white/findAll',
                    type: 'POST',
                    data: function (d) {
                        return JSON.stringify(d);
                    }
                },
                searching: true,
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
                language: {
                    url: languageUrl
                },
                select: true,
                buttons: [
                    {extend: "create", editor: editor},
                    {extend: "edit", editor: editor1}
                ],
                order: [ 9, "desc" ],
                columns: [
                    {
                        data: 'whtListId',
                        visible: false
                    }, {
                        data: 'idHid'
                    }, {
                        data: 'idTyp',
                        render: function (data, type, row) {
                            switch (data) {
                                case '01':
                                    return $.i18n.t("rsm.constant.idTyp.userId");
                                case '02':
                                    return $.i18n.t("rsm.constant.idTyp.card");
                                case '03':
                                    return $.i18n.t("rsm.constant.idTyp.idNo");
                            }
                        }
                    }, {
                        data: 'txTyp',
                        render: function (data, type, row) {
                            switch (data) {
                                case '00':
                                    return $.i18n.t("rsm.constant.txTyp.all");
                                case '01':
                                    return $.i18n.t("rsm.constant.txTyp.recharge");
                                case '02':
                                    return $.i18n.t("rsm.constant.txTyp.consume");
                                case '03':
                                    return $.i18n.t("rsm.constant.txTyp.transfer");
                                case '04':
                                    return $.i18n.t("rsm.constant.txTyp.withdraw");
                                case '05':
                                    return $.i18n.t("rsm.constant.txTyp.seatel");
                                case '06':
                                    return $.i18n.t("rsm.constant.txTyp.refunds");
                                case '07':
                                    return $.i18n.t("rsm.constant.txTyp.interest");
                                case '08':
                                    return $.i18n.t("rsm.constant.txTyp.payment");
                            }
                        }
                    }, {
                        data: 'beginDt',
                        type: "datetime",
                        def: function () {
                            return new Date();
                        },
                        format: "YYYY-MM-DD HH:mm:ss"
                    }, {
                        data: 'endDt',
                        type: "datetime",
                        def: function () {
                            return new Date();
                        },
                        format: "YYYY-MM-DD HH:mm:ss"
                    }, {
                        data: 'effFlg',
                        render: function (data, type, row) {
                            switch (data) {
                                case '0':
                                    return $.i18n.t("rsm.constant.effective");
                                case '1':
                                    return $.i18n.t("rsm.constant.invalid");
                            }
                        }
                    }, {
                        data: 'listRsn'
                    }, {
                        data: 'updOprId'
                    }, {
                        data: 'modifyTime',
                        visible: false
                    }
                ]
            });
        });

    });

    function searchButton() {
        var id = $("input[name='searchId']").val();
        var idTyp = $("select[name='searchIdTyp']").val();
        var txTyp = $("select[name='searchTxTyp']").val();
        console.log(id);
        console.log(idTyp);
        console.log(txTyp);

        table.column(1).search(id)
            .column(2).search(idTyp)
            .column(3).search(txTyp)
            .draw();
    }
</script>
</body>
</html>