<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
    <head>
        <title data-i18n="rsm.highrisk.title"></title>
        <div th:replace="head"></div>
        <style>
            div.floatright{
                display:inline;
                float:right;
                padding-left:20px;
                padding-right:20px;
            }

            ._right{
                display:inline;
                float:right;
                padding-right:15px;
            }
        </style>
    </head>
    <body>
        <div id="wrapper">
            <div th:replace="nav"></div>
            <div id="page-wrapper" class="gray-bg">
                <div th:replace="top"></div>
                <div class="row wrapper border-bottom white-bg page-heading">
                    <div class="col-lg-10">
                        <h2 data-i18n="rsm.highrisk.content"></h2>
                        <ol class="breadcrumb">
                            <li>
                                <a data-i18n="nav.rsm"></a>
                            </li>
                            <li class="active">
                                <strong data-i18n="rsm.highrisk.content"></strong>
                            </li>
                        </ol>
                    </div>
                </div>
                <div class="wrapper wrapper-content  animated fadeInRight">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="ibox float-e-margins">
                                <div class="ibox-content">

                                    <div class="box-header">
                                        <table width="100%">
                                            <tr>
                                                <td align="center">
                                                    <label class="control-label" data-i18n="rsm.highrisk.recId"></label>
                                                </td>
                                                <td>
                                                    <input class="form-control" name="searchRecId" />
                                                </td>
                                                <td align="center">
                                                    <label class="control-label" data-i18n="rsm.highrisk.txTyp"></label>
                                                </td>
                                                <td>
                                                    <select class="form-control" name="searchTxTyp">
                                                        <option value="" data-i18n="rsm.constant.all"></option>
                                                        <option value="01" data-i18n="rsm.constant.txTyp.recharge"></option>
                                                        <option value="02" data-i18n="rsm.constant.txTyp.consume"></option>
                                                        <option value="03" data-i18n="rsm.constant.txTyp.transfer"></option>
                                                        <option value="04" data-i18n="rsm.constant.txTyp.withdraw"></option>
                                                        <option value="05" data-i18n="rsm.constant.txTyp.seatel"></option>
                                                        <option value="06" data-i18n="rsm.constant.txTyp.refunds"></option>
                                                        <option value="07" data-i18n="rsm.constant.txTyp.interest"></option>
                                                        <option value="08" data-i18n="rsm.constant.txTyp.payment"></option>
                                                    </select>
                                                </td>
                                                <td align="center">
                                                    <label class="control-label" data-i18n="rsm.highrisk.userId"></label>
                                                </td>
                                                <td>
                                                    <input class="form-control" name="searchUserId" />
                                                </td>
                                                <td align="center">
                                                    <label class="control-label" data-i18n="rsm.highrisk.riskOpr"></label>
                                                </td>
                                                <td>
                                                    <select class="form-control" name="searchRiskOpr">
                                                        <option value="" data-i18n="rsm.constant.all"></option>
                                                        <option value="00" data-i18n="rsm.highrisk.riskOprAmt"></option>
                                                        <option value="01" data-i18n="rsm.highrisk.riskOprCnt"></option>
                                                        <option value="02" data-i18n="rsm.highrisk.riskOprSps"></option>
                                                    </select>
                                                </td>
                                                <td></td>
                                                <td>
                                                    <button type="button" class="btn btn-w-m btn-primary _right" data-i18n="rsm.search" onclick="searchButton()">
                                                    </button>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <hr/>

                                    <div class="table-responsive">
                                        <table id="example" class="table table-striped table-bordered table-hover">
                                            <thead>
                                                <tr>
                                                    <th data-i18n="rsm.highrisk.recId"></th>
                                                    <th data-i18n="rsm.highrisk.txTyp"></th>
                                                    <th data-i18n="rsm.highrisk.userId"></th>
                                                    <th data-i18n="rsm.highrisk.riskOpr"></th>
                                                    <th data-i18n="rsm.highrisk.riskDesc"></th>
                                                    <th data-i18n="rsm.highrisk.oprSts"></th>
                                                    <th data-i18n="rsm.highrisk.risk_sorc"></th>
                                                    <th data-i18n="rsm.highrisk.createTime"></th>
                                                </tr>
                                            </thead>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div th:replace="footer"></div>
            </div>
        </div>
        <div th:replace="script"></div>
        <script>
            var table;
            var order = [1, 'asc'];

            $(document).ready(function () {

                var languageUrl;
                switch ($.cookie('lang')) {
                    case 'zh':
                        languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                        break;
                    case 'en':
                        languageUrl = '/datatables/plugins/i18n/English.lang';
                        break;
                    case 'km':
                        languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                        break;
                }

                i18nLoad.then(function () {
                    table = $('#example').DataTable({
                        dom: 'B<"floatright"l>rtip',
                        ajax: {
                            contentType: 'application/json',
                            url: '/rsm/highrisk/findAll',
                            type: 'POST',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        },
                        buttons: [
                            {
                                text: $.i18n.t("rsm.highrisk.riskParam"),
                                action: function ( e, dt, node, config ) {
                                    window.location.href="/rsm/highrisk/edit";
                                }
                            }
                        ],
                        searching: true,
                        serverSide: true,
                        searchDelay: 1000,
                        responsive: true,
                        processing: true,
                        language: {
                            url: languageUrl
                        },
                        select: true,
                        columns: [
                            {
                                data: 'recId'
                            }, {
                                data: 'txTyp',
                                render: function (data, type, row) {
                                    switch (data) {
                                        case '00':
                                            return $.i18n.t("rsm.constant.txTyp.all");
                                        case '01':
                                            return $.i18n.t("rsm.constant.txTyp.recharge");
                                        case '02':
                                            return $.i18n.t("rsm.constant.txTyp.consume");
                                        case '03':
                                            return $.i18n.t("rsm.constant.txTyp.transfer");
                                        case '04':
                                            return $.i18n.t("rsm.constant.txTyp.withdraw");
                                        case '05':
                                            return $.i18n.t("rsm.constant.txTyp.seatel");
                                        case '06':
                                            return $.i18n.t("rsm.constant.txTyp.refunds");
                                        case '07':
                                            return $.i18n.t("rsm.constant.txTyp.interest");
                                        case '08':
                                            return $.i18n.t("rsm.constant.txTyp.payment");
                                    }
                                }
                            }, {
                                data: 'userId'
                            }, {
                                data: 'riskOpr',
                                render: function (data, type, row) {
                                    switch (data) {
                                        case '1':
                                            return $.i18n.t("rsm.highrisk.riskOprAmt");
                                        case '2':
                                            return $.i18n.t("rsm.highrisk.riskOprCnt");
                                        case '3':
                                            return $.i18n.t("rsm.highrisk.riskOprSps");
                                    }
                                }
                            }, {
                                data: 'riskDesc'
                            }, {
                                data: 'oprSts',
                                render: function (data, type, row) {
                                    switch (data) {
                                        case '1':
                                            return $.i18n.t("rsm.highrisk.oprStsRisk");
                                    }
                                }
                            }, {
                                data: 'risk_sorc'
                            }, {
                                data: 'createTime'
                            }
                        ]
                    });
                });

            });

            function searchButton() {
                var recId = $("input[name='searchRecId']").val();
                var txTyp = $("select[name='searchTxTyp']").val();
                var userId = $("input[name='searchUserId']").val();
                var riskOpr = $("select[name='searchRiskOpr']").val();

                table.column(0).search(recId)
                    .column(1).search(txTyp)
                    .column(2).search(userId)
                    .column(3).search(riskOpr)
                    .draw();
            }
        </script>
    </body>
</html>