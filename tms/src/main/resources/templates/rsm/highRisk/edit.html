<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
    <head>
        <title data-i18n="rsm.highrisk.title"></title>
        <div th:replace="head"></div>
        <style>
            table{
                border-collapse: separate;
                border-spacing: 0;
            }
            tr{
                border: 1px solid #FFF;
            }
            td{
                border: 1px solid rgba(1, 1, 2, 0.43);
                padding: 10px 30px;
                background-color: #FFF;
            }

            td:first-child{
                border-top-left-radius: 10px;
                border-bottom-left-radius: 10px;
            }

            td:last-child{
                border-top-right-radius: 10px;
                border-bottom-right-radius: 10px;
            }

        </style>

        <script language="javascript">
            function TestBlack(TagName) {
                $("#" + TagName).toggle();
                reload(TagName.substr(2));
            }

            function reload(name) {
                $.ajax({
                    url:"/rsm/highrisk/edit/reload",
                    data: {"no" : name},
                    type:"get",
                    success:function(data) {
                        var td = $("#th" + name).children().children();
                        var d = data.split("|");
                        td.eq(0).val(d[0]);
                        td.eq(2).val(d[1]);
                        td.eq(4).val(d[2]);
                        td.eq(6).val(d[3]);
                    }
                });
            }

            function onlyNum() {
                if(!(event.keyCode==46)&&!(event.keyCode==8)&&!(event.keyCode==37)&&!(event.keyCode==39))
                    if(!((event.keyCode>=48&&event.keyCode<=57)||(event.keyCode>=96&&event.keyCode<=105)))
                        event.returnValue=false;
            }

            window.onload = function () {
                TestBlack('th1');
                TestBlack('th2');
                TestBlack('th3');
                TestBlack('th4');
                TestBlack('th5');
            };

            function submit(name) {
                var td = $("#" + name).children().children();
                var d = {
                    "arg1" : td.eq(0).val(),
                    "arg2" : td.eq(2).val(),
                    "arg3" : td.eq(4).val(),
                    "arg4" : td.eq(6).val(),
                    "no": name.substr(2)
                };
                $.ajax({
                    url:"/rsm/highrisk/edit/upload",
                    data: d,
                    type:"post",
                    success:function(data) {
                        swal($.i18n.t("urm.success"), "", "success");
                    },
                    error:function (data) {
                        swal($.i18n.t("urm.mermgr.failed"),  "error");
                    }
                });
            }

        </script>
    </head>
    <body>
        <div id="wrapper">
            <div th:replace="nav"></div>
            <div id="page-wrapper" class="gray-bg">
                <div th:replace="top"></div>
                <div class="row wrapper border-bottom white-bg page-heading">
                    <div class="col-lg-10">
                        <h2 data-i18n="rsm.highrisk.content"></h2>
                        <ol class="breadcrumb">
                            <li>
                                <a data-i18n="nav.rsm"></a>
                            </li>
                            <li>
                                <a data-i18n="rsm.highrisk.content"></a>
                            </li>
                            <li class="active">
                                <strong data-i18n="rsm.highrisk.riskParam"></strong>
                            </li>
                        </ol>
                    </div>
                </div>

                <div class="wrapper wrapper-content  animated fadeInRight">
                    <table width="90%" border="0" cellspacing="0" cellpadding="0">
                        <tr onclick="TestBlack('th1');">
                            <td width="760" height="20">
                                <span data-i18n="rsm.highrisk.rule1"></span>
                                <span style="float: right" class="oc">+</span>
                            </td>
                        </tr>
                        <tr id="th1">
                            <td>
                                1.<input name="arg1" onkeydown="onlyNum();"/><span data-i18n="rsm.highrisk.days"></span>&nbsp;
                                2.<input name="arg2" onkeydown="onlyNum();"/><span data-i18n="rsm.highrisk.times"></span>&nbsp;
                                3.<input name="arg3" onkeydown="onlyNum();"/><span data-i18n="rsm.highrisk.tThou"></span>&nbsp;
                                4.<input name="arg4" onkeydown="onlyNum();"/><span data-i18n="rsm.highrisk.psc"></span>&nbsp;
                                <button style="border:1px #000000 dashed;background-color:#FFF;"
                                        onclick="submit('th1')" data-i18n="rsm.submit"></button>
                            </td>
                        </tr>
                    </table>

                    <br/>

                    <table width="90%" border="0" cellspacing="0" cellpadding="0">
                        <tr onclick="TestBlack('th2');">
                            <td width="760" height="20">
                                <span data-i18n="rsm.highrisk.rule2"></span>
                                <span style="float: right" class="oc">+</span>
                            </td>
                        </tr>
                        <tr id="th2">
                            <td>
                                1.<input name="arg1" onkeydown="onlyNum();"/><span data-i18n="rsm.highrisk.days"></span>&nbsp;
                                2.<input name="arg2" onkeydown="onlyNum();"/><span data-i18n="rsm.highrisk.psc"></span>&nbsp;
                                3.<input name="arg3" onkeydown="onlyNum();"/><span data-i18n="rsm.highrisk.tThou"></span>&nbsp;
                                <button style="border:1px #000000 dashed;background-color:#FFF;"
                                        onclick="submit('th2')" data-i18n="rsm.submit"></button>
                            </td>
                        </tr>
                    </table>

                    <br/>

                    <table width="90%" border="0" cellspacing="0" cellpadding="0">
                        <tr onclick="TestBlack('th3');">
                            <td width="760" height="20">
                                <span data-i18n="rsm.highrisk.rule3"></span>
                                <span style="float: right" class="oc">+</span>
                            </td>
                        </tr>
                        <tr id="th3">
                            <td>
                                1.<input name="arg1" onkeydown="onlyNum();"/><span data-i18n="rsm.highrisk.days"></span>&nbsp;
                                2.<input name="arg2" onkeydown="onlyNum();"/><span data-i18n="rsm.highrisk.times"></span>&nbsp;
                                3.<input name="arg3" onkeydown="onlyNum();"/><span data-i18n="rsm.highrisk.tThou"></span>&nbsp;
                                <button style="border:1px #000000 dashed;background-color:#FFF;"
                                        onclick="submit('th3')" data-i18n="rsm.submit"></button>
                            </td>
                        </tr>
                    </table>

                    <br/>

                    <table width="90%" border="0" cellspacing="0" cellpadding="0">
                        <tr onclick="TestBlack('th4');">
                            <td width="760" height="20">
                                <span data-i18n="rsm.highrisk.rule4"></span>
                                <span style="float: right" class="oc">+</span>
                            </td>
                        </tr>
                        <tr id="th4">
                            <td>
                                1.<input name="arg1" onkeydown="onlyNum();"/><span data-i18n="rsm.highrisk.count"></span>&nbsp;
                                2.<input name="arg2" onkeydown="onlyNum();"/><span data-i18n="rsm.highrisk.tThou"></span>&nbsp;
                                <button style="border:1px #000000 dashed;background-color:#FFF;"
                                        onclick="submit('th4')" data-i18n="rsm.submit"></button>
                            </td>
                        </tr>
                    </table>

                    <br/>

                    <table width="90%" border="0" cellspacing="0" cellpadding="0">
                        <tr onclick="TestBlack('th5');">
                            <td width="760" height="20">
                                <span data-i18n="rsm.highrisk.rule5"></span>
                                <span style="float: right" class="oc">+</span>
                            </td>
                        </tr>
                        <tr id="th5">
                            <td>
                                1.<input name="arg1" onkeydown="onlyNum();"/><span data-i18n="rsm.highrisk.times"></span>&nbsp;
                                2.<input name="arg2" onkeydown="onlyNum();"/>%&nbsp;
                                3.<input name="arg3" onkeydown="onlyNum();"/><span data-i18n="rsm.highrisk.tThou"></span>&nbsp;
                                <button style="border:1px #000000 dashed;background-color:#FFF;"
                                        onclick="submit('th5')" data-i18n="rsm.submit"></button>
                            </td>
                        </tr>
                    </table>
                </div>


                <div th:replace="footer"></div>
            </div>
        </div>
        <div th:replace="script"></div>
    </body>
</html>