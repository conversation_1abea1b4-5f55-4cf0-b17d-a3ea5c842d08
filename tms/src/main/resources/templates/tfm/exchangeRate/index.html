<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title>汇率查询</title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>

            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2>汇率查询</h2>
                    <ol class="breadcrumb">
                        <li>
                            <a>交易管理</a>
                        </li>
                        <li>
                            <a>汇率管理</a>
                        </li>
                        <li class="active">
                            <strong>汇率查询</strong>
                        </li>
                    </ol>
                </div>
                <div class="col-lg-2">
                    <div class="btn-group pull-right" style="margin-top: 30px;">
                        <button type="button" class="btn btn-primary btn-sm" id="btnAdd">
                            <i class="fa fa-plus"></i> <span>新增</span>
                        </button>
                    </div>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!-- 查询表单 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12">
                                        <!-- 汇率类型 -->
                                        <label class="col-sm-2 control-label" for="searchRateType">汇率类型</label>
                                        <div class="col-sm-4">
                                            <input name="rateType" id="searchRateType" class="form-control" value="" />
                                        </div>
                                        <!-- 源币种代码 -->
                                        <label class="col-sm-2 control-label" for="searchSourceCode">源币种代码</label>
                                        <div class="col-sm-4">
                                            <input name="sourceCode" id="searchSourceCode" class="form-control"
                                                value="" />
                                        </div>
                                    </div>
                                    <div class="form-group col-sm-12">
                                        <!-- 目标币种代码 -->
                                        <label class="col-sm-2 control-label" for="searchTargetCode">目标币种代码</label>
                                        <div class="col-sm-4">
                                            <input name="targetCode" id="searchTargetCode" class="form-control"
                                                value="" />
                                        </div>
                                        <!-- 网络 -->
                                        <label class="col-sm-2 control-label" for="searchNetwork">网络</label>
                                        <div class="col-sm-4">
                                            <input name="network" id="searchNetwork" class="form-control" value="" />
                                        </div>
                                    </div>
                                    <div class="form-group col-sm-12">
                                        <!-- 汇率来源 -->
                                        <label class="col-sm-2 control-label" for="searchRateSource">汇率来源</label>
                                        <div class="col-sm-4">
                                            <input name="rateSource" id="searchRateSource" class="form-control"
                                                value="" />
                                        </div>
                                        <!-- 调整人 -->
                                        <label class="col-sm-2 control-label" for="searchUpdateBy">调整人</label>
                                        <div class="col-sm-4">
                                            <input name="updateBy" id="searchUpdateBy" class="form-control" value="" />
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-sm-4 col-sm-offset-1">
                                            <button type="button" id="searchBtn" class="btn btn-primary"
                                                onclick="search()">查询</button>
                                            <button type="button" id="resetBtn" class="btn btn-default"
                                                onclick="resetForm()">重置</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="exchangeRateTable"
                                        class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>汇率类型</th>
                                                <th>源币种代码</th>
                                                <th>目标币种代码</th>
                                                <th>网络</th>
                                                <th>基准汇率</th>
                                                <th>人工调整点数</th>
                                                <th>最终生效汇率</th>
                                                <th>汇率来源</th>
                                                <th>生效时间</th>
                                                <th>过期时间</th>
                                                <th>调整人</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <!-- 新增/编辑模态框 -->
    <div class="modal fade" id="exchangeRateModal" tabindex="-1" role="dialog" aria-labelledby="exchangeRateModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="exchangeRateModalLabel">新增汇率信息</h4>
                </div>
                <div class="modal-body">
                    <form id="exchangeRateForm" class="form-horizontal">
                        <input type="hidden" id="id" name="id" />
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="rateType">汇率类型</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="rateType" name="rateType" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="sourceCode">源币种代码</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="sourceCode" name="sourceCode" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="targetCode">目标币种代码</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="targetCode" name="targetCode" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="network">网络</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="network" name="network" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="baseRate">基准汇率</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="baseRate" name="baseRate" type="number" step="0.000001"
                                    required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="manualPoint">人工调整点数</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="manualPoint" name="manualPoint" type="number"
                                    step="0.000001" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="finalRate">最终生效汇率</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="finalRate" name="finalRate" type="number"
                                    step="0.000001" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="rateSource">汇率来源</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="rateSource" name="rateSource" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="effectiveTime">生效时间</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="effectiveTime" name="effectiveTime"
                                    type="datetime-local" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="expireTime">过期时间</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="expireTime" name="expireTime" type="datetime-local" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="updateBy">调整人</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="updateBy" name="updateBy" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="approvedBy">审批人</label>
                            <div class="col-sm-9">
                                <input class="form-control" id="approvedBy" name="approvedBy" />
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="btnSave">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 查看详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="detailModalLabel">汇率信息详情</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">ID</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">汇率类型</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailRateType"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">源币种代码</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailSourceCode"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">目标币种代码</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailTargetCode"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">网络</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailNetwork"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">基准汇率</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailBaseRate"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">人工调整点数</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailManualPoint"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">最终生效汇率</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailFinalRate"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">汇率来源</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailRateSource"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">生效时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailEffectiveTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">过期时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailExpireTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">调整人</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUpdateBy"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">审批人</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailApprovedBy"></p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var table;
        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/js/Chinese.json';
                    break;
                case 'en':
                    languageUrl = '/js/English.json';
                    break;
                default:
                    languageUrl = '/js/Chinese.json';
            }

            // 初始化DataTables
            table = $('#exchangeRateTable').DataTable({
                dom: 'Blfrtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/tfm/exchangeRate/findAll',
                    type: 'post',
                    data: function (d) {
                        // 添加额外的查询参数
                        d.extra_search = {
                            "rateType": $("#searchRateType").val() || "",
                            "sourceCode": $("#searchSourceCode").val() || "",
                            "targetCode": $("#searchTargetCode").val() || "",
                            "network": $("#searchNetwork").val() || "",
                            "rateSource": $("#searchRateSource").val() || "",
                            "updateBy": $("#searchUpdateBy").val() || ""
                        };
                        return JSON.stringify(d);
                    },
                    dataSrc: function (json) {
                        // 确保返回的是数组
                        return json.data || [];
                    },
                    error: function (xhr, error, thrown) {
                        console.error('DataTables AJAX error:', error, thrown);
                        toastr.error('加载数据失败，请刷新页面重试');
                    }
                },
                serverSide: true,
                searching: false, // 禁用内置搜索
                searchDelay: 1000,
                responsive: true,
                processing: true,
                language: {
                    url: languageUrl
                },
                columns: [
                    { data: 'id' },
                    { data: 'rateType' },
                    { data: 'sourceCode' },
                    { data: 'targetCode' },
                    { data: 'network' },
                    {
                        data: 'baseRate',
                        render: function (data) {
                            return data ? parseFloat(data).toFixed(6) : '0.000000';
                        }
                    },
                    {
                        data: 'manualPoint',
                        render: function (data) {
                            return data ? parseFloat(data).toFixed(6) : '0.000000';
                        }
                    },
                    {
                        data: 'finalRate',
                        render: function (data) {
                            return data ? parseFloat(data).toFixed(6) : '0.000000';
                        }
                    },
                    { data: 'rateSource' },
                    {
                        data: 'effectiveTime',
                        render: function (data) {
                            if (!data) return '';
                            return new Date(data).toLocaleString();
                        }
                    },
                    {
                        data: 'expireTime',
                        render: function (data) {
                            if (!data) return '';
                            return new Date(data).toLocaleString();
                        }
                    },
                    { data: 'updateBy' },
                    {
                        data: null,
                        orderable: false,
                        render: function (data, type, row) {
                            var buttons = '<button type="button" class="btn btn-xs btn-info view-detail" data-id="' + row.id + '">详情</button> ';
                            buttons += '<button type="button" class="btn btn-xs btn-primary btn-edit" data-id="' + row.id + '">编辑</button> ';
                            buttons += '<button type="button" class="btn btn-xs btn-danger btn-delete" data-id="' + row.id + '">删除</button>';
                            return buttons;
                        }
                    }
                ],
                buttons: [
                    { extend: 'copyHtml5' },
                    { extend: 'csvHtml5' },
                    { extend: 'excelHtml5', title: '汇率信息列表' }
                ]
            });

            // 新增按钮
            $("#btnAdd").click(function () {
                $("#exchangeRateModalLabel").text("新增汇率信息");
                $("#exchangeRateForm")[0].reset();
                $("#id").val("");
                $("#exchangeRateModal").modal("show");
            });

            // 保存按钮
            $("#btnSave").click(function () {
                if (!$("#exchangeRateForm").valid()) {
                    toastr.error('请填写必填项');
                    return;
                }

                var id = $("#id").val();
                var formData = $("#exchangeRateForm").serialize();
                var url = id ? "/tfm/exchangeRate/modify/" + id : "/tfm/exchangeRate/add";

                $.ajax({
                    url: url,
                    type: "POST",
                    data: formData,
                    success: function (res) {
                        if (res.result === "TFM00000") {
                            toastr.success('操作成功');
                            $("#exchangeRateModal").modal("hide");
                            table.ajax.reload();
                        } else {
                            toastr.error('操作失败：' + res.result);
                        }
                    },
                    error: function (xhr, status, error) {
                        toastr.error('操作失败：' + error);
                    }
                });
            });

            // 查看详情
            $(document).on("click", ".view-detail", function () {
                var id = $(this).data("id");
                $.ajax({
                    url: "/tfm/exchangeRate/getExchangeRate",
                    type: "POST",
                    data: { id: id },
                    success: function (data) {
                        $("#detailId").text(data.id || '');
                        $("#detailRateType").text(data.rateType || '');
                        $("#detailSourceCode").text(data.sourceCode || '');
                        $("#detailTargetCode").text(data.targetCode || '');
                        $("#detailNetwork").text(data.network || '');
                        $("#detailBaseRate").text(data.baseRate ? parseFloat(data.baseRate).toFixed(6) : '0.000000');
                        $("#detailManualPoint").text(data.manualPoint ? parseFloat(data.manualPoint).toFixed(6) : '0.000000');
                        $("#detailFinalRate").text(data.finalRate ? parseFloat(data.finalRate).toFixed(6) : '0.000000');
                        $("#detailRateSource").text(data.rateSource || '');
                        $("#detailEffectiveTime").text(data.effectiveTime ? new Date(data.effectiveTime).toLocaleString() : '');
                        $("#detailExpireTime").text(data.expireTime ? new Date(data.expireTime).toLocaleString() : '');
                        $("#detailUpdateBy").text(data.updateBy || '');
                        $("#detailApprovedBy").text(data.approvedBy || '');
                        $("#detailModal").modal("show");
                    },
                    error: function (xhr, status, error) {
                        toastr.error('获取详情失败：' + error);
                    }
                });
            });

            // 编辑按钮
            $(document).on("click", ".btn-edit", function () {
                var id = $(this).data("id");
                $.ajax({
                    url: "/tfm/exchangeRate/getExchangeRate",
                    type: "POST",
                    data: { id: id },
                    success: function (data) {
                        $("#exchangeRateModalLabel").text("编辑汇率信息");
                        $("#id").val(data.id);
                        $("#rateType").val(data.rateType);
                        $("#sourceCode").val(data.sourceCode);
                        $("#targetCode").val(data.targetCode);
                        $("#network").val(data.network);
                        $("#baseRate").val(data.baseRate);
                        $("#manualPoint").val(data.manualPoint);
                        $("#finalRate").val(data.finalRate);
                        $("#rateSource").val(data.rateSource);

                        // 处理日期时间格式
                        if (data.effectiveTime) {
                            var effectiveDate = new Date(data.effectiveTime);
                            $("#effectiveTime").val(effectiveDate.toISOString().slice(0, 16));
                        }
                        if (data.expireTime) {
                            var expireDate = new Date(data.expireTime);
                            $("#expireTime").val(expireDate.toISOString().slice(0, 16));
                        }

                        $("#updateBy").val(data.updateBy);
                        $("#approvedBy").val(data.approvedBy);
                        $("#exchangeRateModal").modal("show");
                    },
                    error: function (xhr, status, error) {
                        toastr.error('获取详情失败：' + error);
                    }
                });
            });

            // 删除按钮
            $(document).on("click", ".btn-delete", function () {
                var id = $(this).data("id");
                swal({
                    title: "确定要删除该汇率信息吗？",
                    text: "删除后将无法恢复！",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#DD6B55",
                    confirmButtonText: "确定删除",
                    cancelButtonText: "取消",
                    closeOnConfirm: false
                }, function () {
                    $.ajax({
                        url: "/tfm/exchangeRate/delete/" + id,
                        type: "DELETE",
                        success: function (res) {
                            if (res === "1" || res.result === "TFM00000") {
                                swal("删除成功！", "汇率信息已被删除。", "success");
                                table.ajax.reload();
                            } else {
                                swal("删除失败", "请稍后重试", "error");
                            }
                        },
                        error: function (xhr, status, error) {
                            swal("删除失败", "错误: " + error, "error");
                        }
                    });
                });
            });

            // 表单验证
            $("#exchangeRateForm").validate({
                rules: {
                    rateType: {
                        required: true,
                        maxlength: 50
                    },
                    sourceCode: {
                        required: true,
                        maxlength: 10
                    },
                    targetCode: {
                        required: true,
                        maxlength: 10
                    },
                    baseRate: {
                        required: true,
                        number: true,
                        min: 0
                    },
                    finalRate: {
                        required: true,
                        number: true,
                        min: 0
                    },
                    manualPoint: {
                        number: true
                    }
                },
                messages: {
                    rateType: {
                        required: "请输入汇率类型",
                        maxlength: "汇率类型不能超过50个字符"
                    },
                    sourceCode: {
                        required: "请输入源币种代码",
                        maxlength: "源币种代码不能超过10个字符"
                    },
                    targetCode: {
                        required: "请输入目标币种代码",
                        maxlength: "目标币种代码不能超过10个字符"
                    },
                    baseRate: {
                        required: "请输入基准汇率",
                        number: "请输入有效的数字",
                        min: "基准汇率不能为负数"
                    },
                    finalRate: {
                        required: "请输入最终生效汇率",
                        number: "请输入有效的数字",
                        min: "最终生效汇率不能为负数"
                    },
                    manualPoint: {
                        number: "请输入有效的数字"
                    }
                }
            });
        });

        // 搜索方法
        function search() {
            table.ajax.reload();
        }

        // 重置表单
        function resetForm() {
            $("#queryForm")[0].reset();
            search();
        }
    </script>
</body>

</html>