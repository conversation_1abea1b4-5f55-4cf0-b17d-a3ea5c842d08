<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>

    <title data-i18n="nav.disqueryctrl.title"></title>

    <div th:replace="head"></div>
</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="nav.disqueryctrl.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.csmmgr"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="nav.disqueryctrl.content"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form id="queryForm" class="form-horizontal">

                                <!-- 表头搜索栏 -->
                                <table>
                                    <tr>
                                        <td align="center">
                                            <!--手机号/商户号-->
                                            <label class="control-label" for="userId" data-i18n="tfm.userId"></label>
                                        </td>
                                        <td>&nbsp; </td>
                                        <td>
                                            <input name="userId" id="userId" class="form-control" value=""/>
                                        </td>
                                        <td>&nbsp; &nbsp; &nbsp; </td>

                                        <td align="center">
                                            <label class="control-label" for="busType" data-i18n="tfm.busTypeDesc"></label>
                                        </td>
                                        <td>&nbsp; </td>
                                        <td>
                                            <!--业务类型-->
                                            <select name="busType" id="busType" class="form-control" >
                                            </select>
                                        </td>
                                        <td>&nbsp; &nbsp; &nbsp; </td>

                                        <td align="center">
                                            <!--提交日期-->
                                            <label class="control-label" for="beginDate" data-i18n="tfm.tradeDate"></label>
                                        </td>
                                        <td>&nbsp; </td>
                                        <td>
                                            <input name="beginDate" id="beginDate" class="form-control" value=""/>
                                        </td>
                                        <td>-</td>
                                        <td>
                                            <input name="endDate" id="endDate" class="form-control" value=""/>
                                        </td>
                                        <td>&nbsp; &nbsp; &nbsp; </td>

                                        <!--提交按钮-->
                                        <td>
                                            <button type="button" id="searchBtn" class="btn btn-w-m btn-primary _right" onclick="search()">查询</button>
                                        </td>
                                    </tr>
                                </table>
                                <hr/>
                            </form>
                            <div class="table-responsive">
                                <table id="orderInf" class="table table-striped table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th data-i18n="tfm.userId"></th>
                                        <th data-i18n="tfm.busTypeDesc"></th>
                                        <th data-i18n="tfm.orderNo"></th>
                                        <th data-i18n="tfm.busOrderNo"></th>
                                        <th data-i18n="tfm.tradeDate"></th>
                                        <th data-i18n="tfm.tradeTime"></th>
                                        <th data-i18n="tfm.tradeTotalAmt"></th>
                                        <th data-i18n="tfm.fee"></th>
                                        <th data-i18n="tfm.tradeAmt"></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>

    </div>
</div>
<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script>
    var editor;
    //A 2D array in which the first array is used to define the value options and the second array the displayed options (useful for language strings such as 'All').
    //    var array1 = [10, 25, 50, -1];
    //    var array2 = [10, 25, 50, "All"];
    var table;
    
    <!--查询按钮-->
    function search() {
        table.ajax.reload();
    }

    $(document).ready(function () {

        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        i18nLoad.then(function () {
            editor = new $.fn.dataTable.Editor({
                ajax: {
                 //  create: {
                 //      type: 'POST',
                 //      url: '/tfm/transferctrl/add',
                 //      contentType: 'application/json',
                 //      data: function (d) {
                 //          return JSON.stringify(d);
                 //      }
                 //  },
                 //  edit: {
                 //      type: 'POST',
                 //      url: '/tfm/mercinfctrl/modify',
                 //      contentType: 'application/json',
                 //      data: function (d) {
                 //          return JSON.stringify(d);
                 //      }
                 //  },
                 //  remove: {
                 //      type: 'POST',
                 //      url: '/tfm/mercinfctrl/delete',
                 //      contentType: 'application/json',
                 //      data: function (d) {
                 //          return JSON.stringify(d);
                 //      }
                 //  }
                },
                table: "#orderInf",
                idSrc: 'orderNo',
             //   fields: [
                  //  {name: "id", type: "hidden"},
                  //  {label: $.i18n.t("tfm.tfmOrderDt"),name: "tfmOrderDt"},
                  //  {label: $.i18n.t("tfm.tfmOrderNo"), name: "tfmOrderNo"},
                  //  {label: $.i18n.t("tfm.userNo"), name: "userNo"},
                  //  {label: $.i18n.t("tfm.mblNo"), name: "mblNo"},
                  //  {label: $.i18n.t("tfm.bossCopType"), name: "bossCopType"},
                  //  {label: $.i18n.t("tfm.orderType"), name: "orderType"},
                  //  {label: $.i18n.t("tfm.orderAmt"), name: "orderAmt"},
                  //  {label: $.i18n.t("tfm.orderSts"), name: "orderSts"},
                  //  {label: $.i18n.t("tfm.payType"), name: "payType"},
                  //  {label: $.i18n.t("tfm.payAmt"), name: "payAmt"}
                    //{
                        //label: $.i18n.t("demo.startDate"),
                        //name: "startDate",
                        //type: "datetime",
                        //def: function () {
                            //return new Date();
                       // },
                       // format: "YYYY-MM-DD HH:mm:ss"
                   // },
                    //{label: $.i18n.t("demo.salary"), name: "salary"}
              //  ],
                //i18n: {
                  // create: {button: $.i18n.t("tfm.add"), title: $.i18n.t("tfm.add"), submit: $.i18n.t("tfm.create")},
                  // edit: {button: $.i18n.t("tfm.modify"), title: $.i18n.t("tfm.modify"), submit: $.i18n.t("tfm.update")},
                  // remove: {
                  //     button: $.i18n.t("tfm.delete"), title: $.i18n.t("tfm.delete"), submit: $.i18n.t("tfm.delete"),
                  //     confirm: {
                  //         _: $.i18n.t("tfm.multi-delete"),
                  //         1: $.i18n.t("tfm.single-delete")
                  //     }
                  // }
                //}
            });

            //editor.on('preSubmit', function (e, o, action) {
            //    var id = editor.field('tfmOrderNo');
            //    o.id = id.val();  // create a new parameter to pass over to the server called entityId
            //});

            table = $('#orderInf').DataTable({
                dom: 'Blfrtip',
                columnDefs: [{
                    targets:[0,2 , 3],//指定哪几列
                    render: function(data){
                        return "\u200C" + data;
                    }
                }],
                ajax: {
                    contentType: 'application/json',
                    url: '/tfm/disqueryctrl/findAll',
                    type: 'POST',
                    data: function (d) {
                    	d.extra_search = {
                            "userId" : $("#userId").val(),
                            "busType" : $("#busType").val(),
                            "beginDate" : $("#beginDate").val(),
                            "endDate" : $("#endDate").val()
                            };
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
//            "lengthMenu": [array1, array2],
                language: {
//                "decimal": ",",
//                "thousands": ".",
//                    url: '/datatables/plugins/i18n/Chinese.lang'
                    url: languageUrl
                    //url: '//cdn.datatables.net/plug-ins/1.10.15/i18n/Chinese.json'
                },
                select: true,
                buttons: [
                    //{extend: "create", editor: editor},
                    //{extend: "edit", editor: editor},
                    //{extend: "remove", editor: editor},
                {extend: 'copyHtml5'},
                {extend: 'csvHtml5'},
                {extend: 'excelHtml5', title: '交易清分列表'},
//                    'copy',
//                    'csv',
//                    'excel'
//                'pdf',
//                'print'
                ],
                columns: [{
                    data: 'userId'
                },{
                    data: 'busTypeDesc'
                },{
                    data: 'orderNo'
                },{
                    data: 'busOrderNo'
                },{
                    data: 'tradeDate'
                },{
                    data: 'tradeTime'
                },{
                    data: 'tradeTotalAmt',
                    render: $.fn.dataTable.render.number(',', '.', 2, '$')
                },{
                    data: 'fee',
                    render: $.fn.dataTable.render.number(',', '.', 2, '$')
                },{
                    data: 'tradeAmt',
                    render: $.fn.dataTable.render.number(',', '.', 2, '$')
                }
                ]
            });
        });
        initBusType();
    });
 // 初始化busType
    function initBusType() {
        $.getJSON('/tfm/disqueryctrl/findBusType', {
            ajax : 'true'
        }, function(data) {
            var html = '<option value="">' + $.i18n.t("tfm.select") + '</option>';
            var len = data.length;
            for ( var i = 0; i < len; i++) {
                    html += '<option value="' + data[i].busType + '">' + data[i].busTypeDesc + '</option>';
            }
            $('#busType').html(html);
        });
    };
    /**初始化日期控件**/
    var beginTimePick = $('#beginDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        onChangeDate: function(dateText, inst) {
            endTimePick.datetimepicker('minDate',new Date(dateText.replace("-", "-")));
        }
    });

    var endTimePick = $('#endDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d'
        // maxDate:'+1970/01/01',
    });
</script>
</body>

</html>