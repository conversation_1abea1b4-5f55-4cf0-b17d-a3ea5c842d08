<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="nav.cptmgr.param.feerule.title">手续费参数管理</title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>

            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="nav.cptmgr.param.feerule.content">手续费参数管理</h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="nav.cptmgr">参数管理</a>
                        </li>
                        <li class="active">
                            <strong data-i18n="nav.cptmgr.param.feerule.content">手续费参数管理</strong>
                        </li>
                    </ol>
                </div>
                <div class="col-lg-2">
                    <div class="btn-group pull-right" style="margin-top: 30px;">
                        <button type="button" class="btn btn-primary btn-sm" id="btnAdd">
                            <i class="fa fa-plus"></i> <span data-i18n="tfm.feerule.add">新增</span>
                        </button>
                    </div>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!-- 查询表单 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12">
                                        <!-- 业务类型 -->
                                        <label class="col-sm-2 control-label" for="searchBusType"
                                            data-i18n="tfm.feerule.busType">业务类型</label>
                                        <div class="col-sm-4">
                                            <input name="busType" id="searchBusType" class="form-control" value="" />
                                        </div>
                                        <!-- 业务类型描述 -->
                                        <label class="col-sm-2 control-label" for="searchBusTypeDesc"
                                            data-i18n="tfm.feerule.busTypeDesc">业务类型描述</label>
                                        <div class="col-sm-4">
                                            <input name="busTypeDesc" id="searchBusTypeDesc" class="form-control" value="" />
                                        </div>
                                    </div>
                                    <div class="form-group col-sm-12">
                                        <!-- 币种 -->
                                        <label class="col-sm-2 control-label" for="searchCcy"
                                            data-i18n="tfm.feerule.ccy">币种</label>
                                        <div class="col-sm-4">
                                            <input name="ccy" id="searchCcy" class="form-control" value="" />
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-sm-4 col-sm-offset-1">
                                            <button type="button" id="searchBtn" class="btn btn-primary"
                                                onclick="search()" data-i18n="tfm.feerule.search">查询</button>
                                            <button type="button" id="resetBtn" class="btn btn-default"
                                                onclick="resetForm()" data-i18n="tfm.feerule.reset">重置</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="feeRuleTable" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="tfm.feerule.busType">业务类型</th>
                                                <th data-i18n="tfm.feerule.busTypeDesc">业务类型描述</th>
                                                <th data-i18n="tfm.feerule.ccy">币种</th>
                                                <th data-i18n="tfm.feerule.calculateMod">计费模式</th>
                                                <th data-i18n="tfm.feerule.calculateType">计费方式</th>
                                                <th data-i18n="tfm.feerule.rate">费率</th>
                                                <th data-i18n="tfm.feerule.fixFee">固定手续费</th>
                                                <th data-i18n="tfm.feerule.stats">状态</th>
                                                <th data-i18n="tfm.feerule.operations">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <!-- 新增/编辑模态框 -->
    <div class="modal fade" id="feeRuleModal" tabindex="-1" role="dialog" aria-labelledby="feeRuleModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="feeRuleModalLabel" data-i18n="tfm.feerule.addTitle">新增手续费参数</h4>
                </div>
                <div class="modal-body">
                    <form id="feeRuleForm" class="form-horizontal">
                        <input type="hidden" id="busType" name="busType" />
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="busTypeDesc"
                                data-i18n="tfm.feerule.busTypeDesc">业务类型描述</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="busTypeDesc" name="busTypeDesc" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="ccy"
                                data-i18n="tfm.feerule.ccy">币种</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="ccy" name="ccy" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="calculateMod"
                                data-i18n="tfm.feerule.calculateMod">计费模式</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="calculateMod" name="calculateMod" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="calculateType"
                                data-i18n="tfm.feerule.calculateType">计费方式</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="calculateType" name="calculateType" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="rate"
                                data-i18n="tfm.feerule.rate">费率</label>
                            <div class="col-sm-9">
                                <input type="number" step="0.0001" class="form-control" id="rate" name="rate" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="fixFee"
                                data-i18n="tfm.feerule.fixFee">固定手续费</label>
                            <div class="col-sm-9">
                                <input type="number" step="0.01" class="form-control" id="fixFee" name="fixFee" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="chargeType"
                                data-i18n="tfm.feerule.chargeType">收费类型</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="chargeType" name="chargeType" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="calculateMinAmt"
                                data-i18n="tfm.feerule.calculateMinAmt">计费起始金额</label>
                            <div class="col-sm-9">
                                <input type="number" step="0.01" class="form-control" id="calculateMinAmt" name="calculateMinAmt" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="minFee"
                                data-i18n="tfm.feerule.minFee">最低收费</label>
                            <div class="col-sm-9">
                                <input type="number" step="0.01" class="form-control" id="minFee" name="minFee" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="maxFee"
                                data-i18n="tfm.feerule.maxFee">最高收费</label>
                            <div class="col-sm-9">
                                <input type="number" step="0.01" class="form-control" id="maxFee" name="maxFee" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="stats"
                                data-i18n="tfm.feerule.stats">状态</label>
                            <div class="col-sm-9">
                                <select class="form-control" id="stats" name="stats" required>
                                    <option value="1" data-i18n="tfm.feerule.stats.active">生效</option>
                                    <option value="0" data-i18n="tfm.feerule.stats.inactive">失效</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="effDate"
                                data-i18n="tfm.feerule.effDate">生效日期</label>
                            <div class="col-sm-9">
                                <input type="date" class="form-control" id="effDate" name="effDate" required />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="expDate"
                                data-i18n="tfm.feerule.expDate">失效日期</label>
                            <div class="col-sm-9">
                                <input type="date" class="form-control" id="expDate" name="expDate" required />
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="tfm.feerule.cancel">取消</button>
                    <button type="button" class="btn btn-primary" id="btnSave" data-i18n="tfm.feerule.save">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 查看详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="detailModalLabel" data-i18n="tfm.feerule.detailTitle">手续费参数详情</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tfm.feerule.busType">业务类型</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailBusType"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tfm.feerule.busTypeDesc">业务类型描述</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailBusTypeDesc"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tfm.feerule.ccy">币种</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailCcy"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tfm.feerule.calculateMod">计费模式</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailCalculateMod"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tfm.feerule.calculateType">计费方式</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailCalculateType"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tfm.feerule.rate">费率</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailRate"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tfm.feerule.fixFee">固定手续费</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailFixFee"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tfm.feerule.chargeType">收费类型</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailChargeType"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tfm.feerule.calculateMinAmt">计费起始金额</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailCalculateMinAmt"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tfm.feerule.minFee">最低收费</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailMinFee"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tfm.feerule.maxFee">最高收费</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailMaxFee"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tfm.feerule.stats">状态</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailStats"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tfm.feerule.effDate">生效日期</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailEffDate"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tfm.feerule.expDate">失效日期</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailExpDate"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tfm.feerule.oprId">操作员</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailOprId"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tfm.feerule.createTime">创建时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailCreateTime"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="tfm.feerule.modifyTime">修改时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailModifyTime"></p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="tfm.feerule.close">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var table;
        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/js/Chinese.json';
                    break;
                case 'en':
                    languageUrl = '/js/English.json';
                    break;
                default:
                    languageUrl = '/js/Chinese.json';
            }

            // 初始化DataTables
            table = $('#feeRuleTable').DataTable({
                dom: 'Blfrtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/tfm/feerule/findAll',
                    type: 'post',
                    data: function (d) {
                        // 添加额外的查询参数
                        d.extra_search = {
                            "busType": $("#searchBusType").val() || "",
                            "busTypeDesc": $("#searchBusTypeDesc").val() || "",
                            "ccy": $("#searchCcy").val() || ""
                        };
                        return JSON.stringify(d);
                    },
                    dataSrc: function (json) {
                        // 确保返回的是数组
                        return json.data || [];
                    },
                    error: function (xhr, error, thrown) {
                        console.error('DataTables AJAX error:', error, thrown);
                        toastr.error('加载数据失败，请刷新页面重试');
                    }
                },
                serverSide: true,
                searching: false, // 禁用内置搜索
                searchDelay: 1000,
                responsive: true,
                processing: true,
                language: {
                    url: languageUrl
                },
                columns: [
                    { data: 'busType' },
                    { data: 'busTypeDesc' },
                    { data: 'ccy' },
                    { data: 'calculateMod' },
                    { data: 'calculateType' },
                    { data: 'rate' },
                    { data: 'fixFee' },
                    {
                        data: 'stats',
                        render: function (data) {
                            return data === '1' ? '生效' : '失效';
                        }
                    },
                    {
                        data: null,
                        orderable: false,
                        render: function (data, type, row) {
                            var buttons = '<button type="button" class="btn btn-xs btn-info view-detail" data-bustype="' + row.busType + '">详情</button> ';
                            buttons += '<button type="button" class="btn btn-xs btn-primary btn-edit" data-bustype="' + row.busType + '">编辑</button> ';
                            buttons += '<button type="button" class="btn btn-xs btn-danger btn-delete" data-bustype="' + row.busType + '">删除</button>';
                            return buttons;
                        }
                    }
                ],
                buttons: [
                    { extend: 'copyHtml5' },
                    { extend: 'csvHtml5' },
                    { extend: 'excelHtml5', title: '手续费参数列表' }
                ]
            });

            // 新增按钮
            $("#btnAdd").click(function () {
                $("#feeRuleModalLabel").text("新增手续费参数");
                $("#feeRuleForm")[0].reset();
                $("#busType").val("");
                $("#feeRuleModal").modal("show");
            });

            // 保存按钮
            $("#btnSave").click(function () {
                if (!$("#feeRuleForm").valid()) {
                    toastr.error('请填写必填项');
                    return;
                }

                var busType = $("#busType").val();
                var formData = $("#feeRuleForm").serialize();
                var url = busType ? "/tfm/feerule/modify/" + busType : "/tfm/feerule/add";

                $.ajax({
                    url: url,
                    type: "POST",
                    data: formData,
                    success: function (res) {
                        if (res.result === "TFM00000") {
                            toastr.success('操作成功');
                            $("#feeRuleModal").modal("hide");
                            table.ajax.reload();
                        } else {
                            toastr.error('操作失败：' + res.result);
                        }
                    },
                    error: function (xhr, status, error) {
                        toastr.error('操作失败：' + error);
                    }
                });
            });

            // 查看详情
            $(document).on("click", ".view-detail", function () {
                var busType = $(this).data("bustype");
                $.ajax({
                    url: "/tfm/feerule/getFeeRule",
                    type: "POST",
                    data: { busType: busType },
                    success: function (data) {
                        $("#detailBusType").text(data.busType || '');
                        $("#detailBusTypeDesc").text(data.busTypeDesc || '');
                        $("#detailCcy").text(data.ccy || '');
                        $("#detailCalculateMod").text(data.calculateMod || '');
                        $("#detailCalculateType").text(data.calculateType || '');
                        $("#detailRate").text(data.rate || '');
                        $("#detailFixFee").text(data.fixFee || '');
                        $("#detailChargeType").text(data.chargeType || '');
                        $("#detailCalculateMinAmt").text(data.calculateMinAmt || '');
                        $("#detailMinFee").text(data.minFee || '');
                        $("#detailMaxFee").text(data.maxFee || '');
                        $("#detailStats").text(data.stats === '1' ? '生效' : '失效');
                        $("#detailEffDate").text(data.effDate ? new Date(data.effDate).toLocaleDateString() : '');
                        $("#detailExpDate").text(data.expDate ? new Date(data.expDate).toLocaleDateString() : '');
                        $("#detailOprId").text(data.oprId || '');
                        $("#detailCreateTime").text(data.createTime ? new Date(data.createTime).toLocaleString() : '');
                        $("#detailModifyTime").text(data.modifyTime ? new Date(data.modifyTime).toLocaleString() : '');
                        $("#detailModal").modal("show");
                    },
                    error: function (xhr, status, error) {
                        toastr.error('获取详情失败：' + error);
                    }
                });
            });

            // 编辑按钮
            $(document).on("click", ".btn-edit", function () {
                var busType = $(this).data("bustype");
                $.ajax({
                    url: "/tfm/feerule/getFeeRule",
                    type: "POST",
                    data: { busType: busType },
                    success: function (data) {
                        $("#feeRuleModalLabel").text("编辑手续费参数");
                        $("#busType").val(data.busType);
                        $("#busTypeDesc").val(data.busTypeDesc);
                        $("#ccy").val(data.ccy);
                        $("#calculateMod").val(data.calculateMod);
                        $("#calculateType").val(data.calculateType);
                        $("#rate").val(data.rate);
                        $("#fixFee").val(data.fixFee);
                        $("#chargeType").val(data.chargeType);
                        $("#calculateMinAmt").val(data.calculateMinAmt);
                        $("#minFee").val(data.minFee);
                        $("#maxFee").val(data.maxFee);
                        $("#stats").val(data.stats);
                        if (data.effDate) {
                            $("#effDate").val(new Date(data.effDate).toISOString().split('T')[0]);
                        }
                        if (data.expDate) {
                            $("#expDate").val(new Date(data.expDate).toISOString().split('T')[0]);
                        }
                        $("#feeRuleModal").modal("show");
                    },
                    error: function (xhr, status, error) {
                        toastr.error('获取详情失败：' + error);
                    }
                });
            });

            // 删除按钮
            $(document).on("click", ".btn-delete", function () {
                var busType = $(this).data("bustype");
                swal({
                    title: "确定要删除该手续费参数吗？",
                    text: "删除后将无法恢复！",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#DD6B55",
                    confirmButtonText: "确定删除",
                    cancelButtonText: "取消",
                    closeOnConfirm: false
                }, function () {
                    $.ajax({
                        url: "/tfm/feerule/delete/" + busType,
                        type: "DELETE",
                        success: function (res) {
                            if (res === "1") {
                                swal("删除成功！", "手续费参数已被删除。", "success");
                                table.ajax.reload();
                            } else {
                                swal("删除失败", "请稍后重试", "error");
                            }
                        },
                        error: function (xhr, status, error) {
                            swal("删除失败", "错误: " + error, "error");
                        }
                    });
                });
            });

            // 表单验证
            $("#feeRuleForm").validate({
                rules: {
                    busTypeDesc: {
                        required: true,
                        maxlength: 100
                    },
                    ccy: {
                        required: true,
                        maxlength: 3
                    },
                    calculateMod: {
                        required: true,
                        maxlength: 20
                    },
                    calculateType: {
                        required: true,
                        maxlength: 20
                    },
                    rate: {
                        required: true,
                        number: true,
                        min: 0
                    },
                    fixFee: {
                        required: true,
                        number: true,
                        min: 0
                    },
                    chargeType: {
                        required: true,
                        maxlength: 20
                    },
                    calculateMinAmt: {
                        required: true,
                        number: true,
                        min: 0
                    },
                    minFee: {
                        required: true,
                        number: true,
                        min: 0
                    },
                    maxFee: {
                        required: true,
                        number: true,
                        min: 0
                    },
                    stats: {
                        required: true
                    },
                    effDate: {
                        required: true
                    },
                    expDate: {
                        required: true
                    }
                },
                messages: {
                    busTypeDesc: {
                        required: "请输入业务类型描述",
                        maxlength: "业务类型描述不能超过100个字符"
                    },
                    ccy: {
                        required: "请输入币种",
                        maxlength: "币种不能超过3个字符"
                    },
                    calculateMod: {
                        required: "请输入计费模式",
                        maxlength: "计费模式不能超过20个字符"
                    },
                    calculateType: {
                        required: "请输入计费方式",
                        maxlength: "计费方式不能超过20个字符"
                    },
                    rate: {
                        required: "请输入费率",
                        number: "请输入有效的数字",
                        min: "费率不能小于0"
                    },
                    fixFee: {
                        required: "请输入固定手续费",
                        number: "请输入有效的数字",
                        min: "固定手续费不能小于0"
                    },
                    chargeType: {
                        required: "请输入收费类型",
                        maxlength: "收费类型不能超过20个字符"
                    },
                    calculateMinAmt: {
                        required: "请输入计费起始金额",
                        number: "请输入有效的数字",
                        min: "计费起始金额不能小于0"
                    },
                    minFee: {
                        required: "请输入最低收费",
                        number: "请输入有效的数字",
                        min: "最低收费不能小于0"
                    },
                    maxFee: {
                        required: "请输入最高收费",
                        number: "请输入有效的数字",
                        min: "最高收费不能小于0"
                    },
                    stats: {
                        required: "请选择状态"
                    },
                    effDate: {
                        required: "请选择生效日期"
                    },
                    expDate: {
                        required: "请选择失效日期"
                    }
                }
            });
        });

        // 搜索方法
        function search() {
            table.ajax.reload();
        }

        // 重置表单
        function resetForm() {
            $("#queryForm")[0].reset();
            search();
        }
    </script>
</body>

</html>