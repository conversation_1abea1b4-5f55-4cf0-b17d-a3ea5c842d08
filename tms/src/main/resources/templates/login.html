<!DOCTYPE html>
<html xmlns:th = "http://www.thymeleaf.org">

    <head id = "login-head">

        <meta charset = "utf-8" />
        <meta name = "viewport" content = "width=device-width, initial-scale=1.0" />

        <title data-i18n = "login.title"></title>

        <link th:href = "@{/css/bootstrap.min.css}" rel = "stylesheet" />
        <link th:href = "@{/font-awesome/css/font-awesome.css}" rel = "stylesheet" />

        <link th:href = "@{/css/animate.css}" rel = "stylesheet" />
        <!-- <link th:href = "@{/css/style.css}" rel = "stylesheet" /> -->
        <link th:href = "@{/css/login-web3.css}" rel = "stylesheet" />

    </head>

    <body class = "login-page">
        <!-- Background with decorative elements -->
        <div class = "login-background">
            <img th:src = "@{/figma-assets/Bg-x1.png}" alt = "Background" class = "bg-image" />
            <div class = "decorative-elements">
                <img th:src = "@{/figma-assets/Tree-x1.png}" alt = "Tree" class = "tree-decoration" />
                <img th:src = "@{/figma-assets/Mask-x1.png}" alt = "Mask" class = "mask-decoration" />
            </div>
        </div>

        <!-- Main login container -->
        <div class = "login-container">
            <!-- Login Card -->
            <div class = "login-card" id = "login-body">
                <!-- Logo Section -->
                <div class = "logo-section">
                    <h2 class = "logo-name" data-i18n = "login.logo">TMS</h2>
                </div>

                <!-- Login Form -->
                <form class = "login-form" role = "form" th:action = "@{/login}" method = "post">
                    <!-- Username Field -->
                    <div class = "form-field">
                        <label class = "field-label" data-i18n = "login.username">Username</label>
                        <div class = "input-wrapper">
                            <input
                                    type = "text"
                                    name = "username"
                                    class = "form-input"
                                    data-i18n = "[placeholder]login.username"
                                    required
                            />
                            <i class = "fa fa-user input-icon"></i>
                        </div>
                    </div>

                    <!-- Password Field -->
                    <div class = "form-field">
                        <label class = "field-label" data-i18n = "login.password">Password</label>
                        <div class = "input-wrapper">
                            <input
                                    type = "password"
                                    name = "password"
                                    class = "form-input"
                                    data-i18n = "[placeholder]login.password"
                                    required
                            />
                            <i class = "fa fa-lock input-icon"></i>
                        </div>
                    </div>

                    <!-- Error Message -->
                    <div class = "form-field" th:if = "${param.error}">
                        <div class = "alert alert-danger" role = "alert" data-i18n = "login.error">Invalid credentials</div>
                    </div>

                    <!-- Remember & Forgot Password -->
                    <div class = "form-options">
                        <label class = "checkbox-wrapper">
                            <input type = "checkbox" class = "checkbox-input" />
                            <span class = "checkbox-label" data-i18n = "login.remember">Remember me</span>
                        </label>
                        <a href = "#" class = "forgot-link" data-i18n = "login.forgot">Forgot Password?</a>
                    </div>

                    <!-- Login Button -->
                    <button type = "submit" class = "login-button" data-i18n = "login.button">Sign In</button>
                </form>
            </div>

            <!-- Language Switcher -->
            <div class = "language-switcher">
                <div class = "language-selector">
                    <i class = "fa fa-globe language-icon"></i>
                    <span class = "language-text">English</span>
                    <div class = "language-dropdown">
                        <a class = "language-option set_zh active" data-lang = "zh">
                            <img th:src = "@{/img/flags/32/China.png}" alt = "中文" />
                            <span>中文</span>
                        </a>
                        <a class = "language-option set_en" data-lang = "en">
                            <img th:src = "@{/img/flags/32/United-States.png}" alt = "English" />
                            <span>English</span>
                        </a>
                        <!-- <a class="language-option set_km" data-lang="km">
                            <img th:src="@{/img/flags/32/Cambodia.png}" alt="ខ្មែរ" />
                            <span>ខ្មែរ</span>
                        </a> -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Mainly scripts -->
        <script th:src = "@{/js/jquery-2.1.1.js}"></script>
        <script th:src = "@{/js/bootstrap.min.js}"></script>

        <!-- i18next -->
        <script th:src = "@{/js/plugins/i18next/i18next.min.js}"></script>
        <script>
            $(document).ready(function () {
                $.i18n.init({
                    resGetPath: '/locales/__lng__.json',
                    load: 'unspecific',
                    fallbackLng: false,
                    cookieName: 'lang'
                }, function (t) {
                    $('#login-head').i18n();
                    $('#login-body').i18n();
                });

                // Enhanced language switching with visual feedback
                $('.set_en').on('click', function (e) {
                    e.preventDefault();
                    i18n.setLng('en', function () {
                        $('#login-head').i18n();
                        $('#login-body').i18n();

                        $('.language-option').removeClass('active');
                        $('.set_en').addClass('active');
                        $('.language-text').text('English');
                    });
                });

                $('.set_zh').on('click', function (e) {
                    e.preventDefault();
                    i18n.setLng('zh', function () {
                        $('#login-head').i18n();
                        $('#login-body').i18n();

                        $('.language-option').removeClass('active');
                        $('.set_zh').addClass('active');
                        $('.language-text').text('中文');
                    });
                });

                $('.set_km').on('click', function (e) {
                    e.preventDefault();
                    i18n.setLng('km', function () {
                        $('#login-head').i18n();
                        $('#login-body').i18n();

                        $('.language-option').removeClass('active');
                        $('.set_km').addClass('active');
                        $('.language-text').text('ខ្មែរ');
                    });
                });

                // Form validation and enhancement
                $('.form-input').on('focus', function () {
                    $(this).closest('.form-field').addClass('focused');
                });

                $('.form-input').on('blur', function () {
                    $(this).closest('.form-field').removeClass('focused');
                });

                // Add loading state to login button
                $('.login-form').on('submit', function () {
                    $('.login-button').prop('disabled', true).text('Signing in...');
                });
            });
        </script>

    </body>

</html>
