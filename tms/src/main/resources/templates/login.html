<!DOCTYPE html>
<html xmlns:th = "http://www.thymeleaf.org">

    <head id = "login-head">

        <meta charset = "utf-8" />
        <meta name = "viewport" content = "width=device-width, initial-scale=1.0" />

        <title data-i18n = "login.title"></title>

        <link th:href = "@{/css/bootstrap.min.css}" rel = "stylesheet" />
        <link th:href = "@{/font-awesome/css/font-awesome.css}" rel = "stylesheet" />

        <link th:href = "@{/css/animate.css}" rel = "stylesheet" />
        <link th:href = "@{/css/style.css}" rel = "stylesheet" />

    </head>

    <body class = "gray-bg">

        <div class = "middle-box text-center loginscreen animated fadeInDown">
            <div id = "login-body">
                <div>
                    <a class = "btn btn-white set_zh active"><img th:src = "@{/img/flags/32/China.png}"></a>
                    <a class = "btn btn-white set_en"><img th:src = "@{/img/flags/32/United-States.png}"></a>
                    <!-- <a class = "btn btn-white set_km"><img th:src = "@{/img/flags/32/Cambodia.png}"></a> -->
                </div>
                <div>

                    <h2 class = "logo-name" data-i18n = "login.logo"></h2>

                </div>
                <!--        <h3 data-i18n="login.welcome"></h3>-->
                <form class = "m-t" role = "form" th:action = "@{/login}" method = "post">
                    <div class = "form-group">
                        <input type = "text" name = "username" class = "form-control" data-i18n = "[placeholder]login.username" required = "" />
                    </div>
                    <div class = "form-group">
                        <input type = "password" name = "password" class = "form-control" data-i18n = "[placeholder]login.password" required = "" />
                    </div>
                    <div class = "form-group" th:if = "${param.error}">
                        <div class = "alert alert-danger" role = "alert" data-i18n = "login.error"></div>
                    </div>
                    <button type = "submit" class = "btn btn-primary block full-width m-b" data-i18n = "login.button"></button>
                </form>
                <p class = "m-t">
                    <!--            <small data-i18n="login.copyright">&copy;</small>-->
                </p>
            </div>
        </div>

        <!-- Mainly scripts -->
        <script th:src = "@{/js/jquery-2.1.1.js}"></script>
        <script th:src = "@{/js/bootstrap.min.js}"></script>

        <!-- i18next -->
        <script th:src = "@{/js/plugins/i18next/i18next.min.js}"></script>
        <script>
            $(document).ready(function () {
                $.i18n.init({
                    resGetPath: '/locales/__lng__.json',
                    load: 'unspecific',
                    fallbackLng: false,
                    cookieName: 'lang'
                }, function (t) {
                    $('#login-head').i18n();
                    $('#login-body').i18n();
                });

                $('.set_en').on('click', function () {
                    i18n.setLng('en', function () {
                        $('#login-head').i18n();
                        $('#login-body').i18n();

                        $('.set_en').addClass('active');
                        $('.set_zh').removeClass('active');
                        $('.set_km').removeClass('active');
                    });
                });

                $('.set_zh').on('click', function () {
                    i18n.setLng('zh', function () {
                        $('#login-head').i18n();
                        $('#login-body').i18n();

                        $('.set_zh').addClass('active');
                        $('.set_en').removeClass('active');
                        $('.set_km').removeClass('active');
                    });
                })

                $('.set_km').on('click', function () {
                    i18n.setLng('km', function () {
                        $('#login-head').i18n();
                        $('#login-body').i18n();

                        $('.set_km').addClass('active');
                        $('.set_zh').removeClass('active');
                        $('.set_en').removeClass('active');
                    });
                })
            });
        </script>

    </body>

</html>
