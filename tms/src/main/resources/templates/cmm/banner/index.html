<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
<title data-i18n="nav.bussub.oprsub.bannermgr.title"></title>
<div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>
        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>
            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="nav.bussub.oprsub.bannermgr.content"></h2>
                    <ol class="breadcrumb">
                        <li><a data-i18n="nav.busmgr"></a></li>
                        <li><a data-i18n="nav.bussub.oprmgr"></a></li>
                        <li class="active"><strong data-i18n="nav.bussub.oprsub.bannermgr.content"></strong>
                        </li>
                    </ol>
                </div>
            </div>
            <div class="wrapper wrapper-content animated fadeInRight">
                <!--查询条件-->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="box-header">
                                    <div class="form-horizontal">
                                        <table class="table table-striped table-bordered table-hover">
                                            <tr>
                                                <!-- banner主题-->
                                                <td align="center"><label class="control-label"
                                                    data-i18n="cmm.label.title"></label></td>
                                                <td><input class="form-control" id="title" name="title" />
                                                </td>
                                                <!--提交日期-->
                                                <td align="center"><label class="control-label"
                                                    data-i18n="cmm.label.date"></label></td>
                                                <td><input class="form-control" id="beginDate"
                                                    name="beginDate" /></td>
                                                <td><input class="form-control" id="endDate"
                                                    name="endDate" /></td>
                                                <td align="center">
                                                    <button class="btn btn-primary" id="searchBtn"
                                                        data-i18n="cmm.button.query" onclick="search()"></button>
                                                <td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                                <hr />
                                <div class="table-responsive">
                                    <table id="bannerInf"
                                        class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="cmm.banner.title"></th>
                                                <th data-i18n="cmm.banner.effDate"></th>
                                                <th data-i18n="cmm.banner.expDate"></th>
                                                <th data-i18n="cmm.banner.channel"></th>
                                                <th data-i18n="cmm.banner.stats"></th>
                                                <th data-i18n="cmm.banner.tmSmp"></th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>
    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var editor;
        var table;

        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }

            i18nLoad.then(function () {
                editor = new $.fn.dataTable.Editor({
                    ajax:{
                        upload: {
                            type: 'POST',
                            url: '/cmm/bannerctrl/upload'
                        },
                        create: {
                            type: 'POST',
                            url: '/cmm/bannerctrl/add',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        },
                        edit: {
                            type: 'POST',
                            url: '/cmm/bannerctrl/modify',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        },
                        remove: {
                            type: 'POST',
                            url: '/cmm/bannerctrl/delete',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        }
                    },
                    table: "#bannerInf",
                    idSrc: "id",
                    fields: [ {
                            label: "id",
                            name: "id",
                            type: "hidden"
                        },{
                            label: $.i18n.t("cmm.banner.bannerUrlKh"),
                            name: "bannerUrlKh",
                            type: "upload",
                            display: function ( file_id ) {
                                var temp = editor;
                                return '<img src="'+editor.file( 'files', file_id ).web_path+'"/>';
                            },
                            clearText: "Clear",
                            noImageText: 'No image'
                        },{
                            label: $.i18n.t("cmm.banner.bannerUrlCn"),
                            name: "bannerUrlCn",
                            type: "upload",
                            display: function ( file_id ) {
                                return '<img src="'+editor.file( 'files', file_id ).web_path+'"/>';
                            },
                            clearText: "Clear",
                            noImageText: 'No image'
                        },{
                            label: $.i18n.t("cmm.banner.bannerUrlEn"),
                            name: "bannerUrlEn",
                            type: "upload",
                            display: function ( file_id ) {
                                return '<img src="'+editor.file( 'files', file_id ).web_path+'"/>';
                            },
                            clearText: "Clear",
                            noImageText: 'No image'
                        },{
                            label: $.i18n.t("cmm.banner.title"),
                            name: "title"
                        },{
                            label: $.i18n.t("cmm.banner.detailUrlKh"),
                            name: "detailUrlKh"
                        },{
                            label: $.i18n.t("cmm.banner.detailUrlCn"),
                            name: "detailUrlCn"
                        },{
                            label: $.i18n.t("cmm.banner.detailUrlEn"),
                            name: "detailUrlEn"
                        },{
                            label: $.i18n.t("cmm.banner.effDate"),
                            name: "effDate",
                            type: "datetime",
                            def:   function () { return new Date();}
                        },{
                            label: $.i18n.t("cmm.banner.expDate"),
                            name: "expDate",
                            type: "datetime",
                            def:   function () { return new Date();}
                        },{
                            label: $.i18n.t("cmm.banner.channel"),
                            name: "channel",
                            type:  "select",
                            options: [
                                { label: $.i18n.t("cmm.channelEnum.portal"), value: "portal" },
                                { label: $.i18n.t("cmm.channelEnum.app"), value: "app" },
                                { label: $.i18n.t("cmm.channelEnum.mportal"), value: "mportal" },
                                { label: $.i18n.t("cmm.channelEnum.mapp"), value: "mapp" }
                            ]
                        },{
                            label: $.i18n.t("cmm.banner.stats"),
                            name: "stats",
                            type:  "select",
                            options: [
                                { label: $.i18n.t("cmm.statsEnum.online"), value: "1" },
                                { label: $.i18n.t("cmm.statsEnum.offline"), value: "0" }
                                
                            ]
                        }],
                        i18n: {
                            create: {
                                button: $.i18n.t("cmm.button.add"),
                                title: $.i18n.t("cmm.button.add"),
                                submit: $.i18n.t("cmm.button.add")
                            },
                            edit: {
                                button: $.i18n.t("cmm.button.modify"),
                                title: $.i18n.t("cmm.button.modify"),
                                submit: $.i18n.t("cmm.button.modify")
                            },
                            remove: {
                                button: $.i18n.t("cmm.button.delete"),
                                title: $.i18n.t("cmm.button.delete"),
                                submit: $.i18n.t("cmm.button.delete"),
                                confirm: {
                                    _: $.i18n.t("cmm.action.cofirm.delete"),
                                    1: $.i18n.t("cmm.action.cofirm.delete")
                                }
                            }
                        }
                    });
                    
                    editor.on( 'preSubmit', function ( e, o, action ) {
                        if ( action !== 'remove' ) {
                            var title = this.field( 'title' );
                            var bannerUrlKh = this.field( 'bannerUrlKh' );
                            var bannerUrlCn = this.field( 'bannerUrlCn' );
                            var bannerUrlEn = this.field( 'bannerUrlEn' );
                            var detailUrlKh = this.field( 'detailUrlKh' );
                            var detailUrlCn = this.field( 'detailUrlCn' );
                            var detailUrlEn = this.field( 'detailUrlEn' );
                            var effDate = this.field( 'effDate' );
                            var expDate = this.field( 'expDate' );
                            // Only validate user input values - different values indicate that
                            // the end user has not entered a value
                            
                            if ( ! title.isMultiValue() ) {
                                if ( ! title.val() ) {
                                    title.error( $.i18n.t("cmm.msg.banner.null.title") );
                                }
                                 
                                if ( title.val().length > 64 ) {
                                    title.error( $.i18n.t("cmm.msg.banner.length.title") );
                                }
                            }
                            
                            if ( ! bannerUrlKh.isMultiValue() ) {
                                if ( ! bannerUrlKh.val() ) {
                                    bannerUrlKh.error( $.i18n.t("cmm.msg.banner.null.bannerUrlKh") );
                                }
                                 
                                if ( bannerUrlKh.val().length > 256 ) {
                                    bannerUrlKh.error( $.i18n.t("cmm.msg.banner.length.bannerUrlKh") );
                                }
                            }
                            
                            if ( ! bannerUrlCn.isMultiValue() ) {
                                if ( ! bannerUrlCn.val() ) {
                                    bannerUrlCn.error( $.i18n.t("cmm.msg.banner.null.bannerUrlCn") );
                                }
                                 
                                if ( bannerUrlCn.val().length > 256 ) {
                                    bannerUrlCn.error( $.i18n.t("cmm.msg.banner.length.bannerUrlCn") );
                                }
                            }
                            
                            if ( ! bannerUrlEn.isMultiValue() ) {
                                if ( ! bannerUrlEn.val() ) {
                                    bannerUrlEn.error( $.i18n.t("cmm.msg.banner.null.bannerUrlEn") );
                                }
                                 
                                if ( bannerUrlEn.val().length > 1024 ) {
                                    bannerUrlEn.error( $.i18n.t("cmm.msg.banner.length.bannerUrlEn") );
                                }
                            }
                            
                            if ( ! detailUrlKh.isMultiValue() ) {
                                if ( ! detailUrlKh.val() ) {
                                    detailUrlKh.error( $.i18n.t("cmm.msg.banner.null.detailUrlKh") );
                                }
                                 
                                if ( detailUrlKh.val().length > 256 ) {
                                    detailUrlKh.error( $.i18n.t("cmm.msg.banner.length.detailUrlKh") );
                                }
                            }
                            
                            if ( ! detailUrlCn.isMultiValue() ) {
                                if ( ! detailUrlCn.val() ) {
                                    detailUrlCn.error( $.i18n.t("cmm.msg.banner.null.detailUrlCn") );
                                }
                                 
                                if ( detailUrlCn.val().length > 256 ) {
                                    detailUrlCn.error( $.i18n.t("cmm.msg.banner.length.detailUrlCn") );
                                }
                            }
                            
                            if ( ! detailUrlEn.isMultiValue() ) {
                                if ( ! detailUrlEn.val() ) {
                                    detailUrlEn.error( $.i18n.t("cmm.msg.banner.null.detailUrlEn") );
                                }
                                 
                                if ( detailUrlEn.val().length > 1024 ) {
                                    detailUrlEn.error( $.i18n.t("cmm.msg.banner.length.detailUrlEn") );
                                }
                            }
                            
                            if ( ! effDate.isMultiValue() ) {
                                if ( ! effDate.val() ) {
                                    effDate.error( $.i18n.t("cmm.msg.banner.null.effDate") );
                                }
                            }
                            
                            if ( ! expDate.isMultiValue() ) {
                                if ( ! expDate.val() ) {
                                    expDate.error( $.i18n.t("cmm.msg.banner.null.expDate") );
                                }
                            }
                 
                            // If any error was reported, cancel the submission so it can be corrected
                            if ( this.inError() ) {
                                return false;
                            }
                        }
                    } );
                    
                    editor.on( 'open', function ( e, o, action ) {
                        if ( action === 'edit' ) {
                            this.field( 'id' ).disable();
                        }
                    } );
    
                    table = $('#bannerInf').DataTable({
                        dom: "Blfrtip",
                        ajax: {
                            contentType: 'application/json',
                            url: '/cmm/bannerctrl/findAll',
                            type: 'post',
                            data: function (d) {
                                d.extra_search = {
                                    "title" : $("#title").val(),
                                    "beginDate" : $("#beginDate").val(),
                                    "endDate" : $("#endDate").val()
                                };
                                return JSON.stringify(d);
                            }
                        },
                        serverSide: true,
                        searchDelay: 1000,
                        responsive: true,
                        processing: true,
                        language: {
                            url: languageUrl
                        },
                        select: true,
                        buttons: [
                            {extend: "create", editor: editor},
                            {extend: "edit", editor: editor},
                            {extend: "remove", editor: editor},
                        ],
                        columns: [{
                            data: 'title',
                            render: $.fn.dataTable.render.ellipsis( 10 )
                        },{
                            data: 'effDate'
                        },{
                            data: 'expDate'
                        },{
                            data: 'channel',
                            render: function (data, type, row) {
                                   switch (data) {
                                       case "portal":
                                           return $.i18n.t("cmm.channelEnum.portal");
                                       case "app":
                                           return $.i18n.t("cmm.channelEnum.app");
                                       case "mportal":
                                           return $.i18n.t("cmm.channelEnum.mportal");
                                       case "mapp":
                                           return $.i18n.t("cmm.channelEnum.mapp");
                                       default:
                                           return data;
                                   }
                             }
                        },{
                            data: 'stats',
                            render: function (data, type, row) {
                                   switch (data) {
                                       case "0":
                                           return $.i18n.t("cmm.statsEnum.offline");
                                       case "1":
                                           return $.i18n.t("cmm.statsEnum.online");
                                       default:
                                           return data;
                                   }
                             }
                        },{
                            data: 'tmSmp'
                        }]
                    });
                });
            });
    
            // 初始化日期控件
            $('#beginDate').datetimepicker({
                lang:'en',
                timepicker:false,
                validateOnBlur: false,
                format:'Y-m-d',
                formatDate:'Y-m-d'
            });
    
            $('#endDate').datetimepicker({
                lang:'en',
                timepicker:false,
                validateOnBlur: false,
                format:'Y-m-d ',
                formatDate:'Y-m-d',
            });
    
            // 查询按钮
            function search() {
                table.ajax.reload();
            }
            
            <!-- ellipsis插件 -->
            $.fn.dataTable.render.ellipsis = function ( cutoff, wordbreak, escapeHtml ) {
                var esc = function ( t ) {
                    return t
                        .replace( /&/g, '&amp;' )
                        .replace( /</g, '&lt;' )
                        .replace( />/g, '&gt;' )
                        .replace( /"/g, '&quot;' );
                };
             
                return function ( d, type, row ) {
                    // Order, search and type get the original data
                    if ( type !== 'display' ) {
                        return d;
                    }
             
                    if ( typeof d !== 'number' && typeof d !== 'string' ) {
                        return d;
                    }
             
                    d = d.toString(); // cast numbers
             
                    if ( d.length < cutoff ) {
                        return d;
                    }
             
                    var shortened = d.substr(0, cutoff-1);
             
                    // Find the last white space character in the string
                    if ( wordbreak ) {
                        shortened = shortened.replace(/\s([^\s]*)$/, '');
                    }
             
                    // Protect against uncontrolled HTML input
                    if ( escapeHtml ) {
                        shortened = esc( shortened );
                    }
             
                    return '<span class="ellipsis" title="'+esc(d)+'">'+shortened+'&#8230;</span>';
                };
            };
        </script>
</body>
</html>