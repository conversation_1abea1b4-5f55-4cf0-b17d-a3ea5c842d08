<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

    <head>
        <title>邮件模板管理</title>
        <div th:replace="head"></div>
    </head>

    <body>
        <div id="wrapper">
            <div th:replace="nav"></div>
    
            <div id="page-wrapper" class="gray-bg">
                <div th:replace="top"></div>
                <div class="row wrapper border-bottom white-bg page-heading">
                    <div class="col-lg-10">
                        <h2>邮件模板管理</h2>
                        <ol class="breadcrumb">
                            <li>
                                <a data-i18n="nav.busmgr"></a>
                            </li>
                            <li>
                                <a data-i18n="nav.bussub.oprmgr"></a>
                            </li>
                            <li>
                                <strong>邮件模板管理</strong>
                            </li>
                        </ol>
                    </div>
                </div>
                
                <div class="wrapper wrapper-content animated fadeInRight">
                    <!--查询条件-->
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="ibox float-e-margins">
                                <div class="ibox-content">
                                   <div class="box-header">
                                       <div class="form-horizontal">
                                            <table class="table table-striped table-bordered table-hover">
                                                 <tr>
                                                     <!-- 模板ID-->
                                                     <td align="center">
                                                         <label class="control-label">模板ID</label>
                                                     </td>
                                                     <td>
                                                         <input class="form-control" id="id" name="id" />
                                                     </td>
                                                     <!-- 模板内容-->
                                                     <td align="center">
                                                         <label class="control-label">模板内容</label>
                                                     </td>
                                                     <td>
                                                         <input class="form-control" id="content" name="content" />
                                                     </td>
                                                     <!--日期范围-->
                                                     <td align="center">
                                                         <label class="control-label">日期范围</label>
                                                     </td>
                                                     <td>
                                                         <input class="form-control" id="beginDate" name="beginDate" />
                                                     </td>
                                                     <td>
                                                         <input class="form-control" id="endDate" name="endDate" />
                                                     </td>
                                                     <td align="center">
                                                         <button class="btn btn-primary" id="searchBtn" data-i18n="cmm.button.query" onclick="search()"></button>
                                                     </td>
                                                 </tr>
                                            </table>
                                        </div>
                                    </div>
                                    <hr/>
                                    <div class="table-responsive">
                                        <table id="emailTemplateTable" class="table table-striped table-bordered table-hover">
                                            <thead>
                                                <tr>
                                                    <th>模板ID</th>
                                                    <th>邮件类型</th>
                                                    <th>邮件主题</th>
                                                    <th>生效日期</th>
                                                    <th>失效日期</th>
                                                    <th>状态</th>
                                                    <th>操作时间</th>
                                                </tr>
                                            </thead>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div th:replace="footer"></div>
            </div>
        </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var editor;
        var table;

        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }

            i18nLoad.then(function () {
                editor = new $.fn.dataTable.Editor({
                    ajax: {
                        create: {
                            type: 'POST',
                            url: '/cmm/emailtemplatectrl/add',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        },
                        edit: {
                            type: 'POST',
                            url: '/cmm/emailtemplatectrl/modify',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        },
                        remove: {
                            type: 'POST',
                            url: '/cmm/emailtemplatectrl/delete',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        }
                    },
                    table: "#emailTemplateTable",
                    idSrc: "id",
                    fields: [ {
                            label: "模板ID", 
                            name: "id"
                        },{
                            label: "邮件类型", 
                            name: "type"
                        },{
                            label: "邮件主题", 
                            name: "subject"
                        },{
                            label: "替换变量", 
                            name: "replaceField"
                        },{
                            label: "中文邮件模板", 
                            name: "templateContentCn",
                            type: "textarea"
                        },{
                            label: "英文邮件模板", 
                            name: "templateContentEn",
                            type: "textarea"
                        },{
                            label: "生效日期", 
                            name: "effDate",
                            type: "datetime",
                            def: function () { return new Date();}
                        },{
                            label: "失效日期", 
                            name: "expDate",
                            type: "datetime",
                            def: function () { return new Date();}
                        },{
                            label: "状态", 
                            name: "stats",
                            type: "select",
                            options: [
                                { label: "有效", value: "1" },
                                { label: "无效", value: "0" }
                            ]
                        }],
                        i18n: {
                            create: {
                                button: $.i18n.t("cmm.button.add"),
                                title: $.i18n.t("cmm.button.add"),
                                submit: $.i18n.t("cmm.button.add")
                            },
                            edit: {
                                button: $.i18n.t("cmm.button.modify"),
                                title: $.i18n.t("cmm.button.modify"),
                                submit: $.i18n.t("cmm.button.modify")
                            },
                            remove: {
                                button: $.i18n.t("cmm.button.delete"),
                                title: $.i18n.t("cmm.button.delete"),
                                submit: $.i18n.t("cmm.button.delete"),
                                confirm: {
                                    _: "确认删除所选记录?",
                                    1: "确认删除所选记录?"
                                }
                            }
                        }
                    });
                    
                    editor.on('preSubmit', function (e, o, action) {
                        if (action !== 'remove') {
                            var id = this.field('id');
                            var type = this.field('type');
                            var subject = this.field('subject');
                            var templateContentCn = this.field('templateContentCn');
                            var templateContentEn = this.field('templateContentEn');
                            var effDate = this.field('effDate');
                            var expDate = this.field('expDate');
                            
                            if (!id.isMultiValue()) {
                                if (!id.val()) {
                                    id.error('模板ID不能为空');
                                }
                                
                                if (id.val().length > 64) {
                                    id.error('模板ID长度不能超过64个字符');
                                }
                            }
                            
                            if (!type.isMultiValue()) {
                                if (!type.val()) {
                                    type.error('邮件类型不能为空');
                                }
                            }
                            
                            if (!subject.isMultiValue()) {
                                if (!subject.val()) {
                                    subject.error('邮件主题不能为空');
                                }
                                
                                if (subject.val().length > 128) {
                                    subject.error('邮件主题长度不能超过128个字符');
                                }
                            }
                            
                            if (!templateContentCn.isMultiValue()) {
                                if (!templateContentCn.val()) {
                                    templateContentCn.error('中文邮件模板不能为空');
                                }
                            }
                            
                            if (!templateContentEn.isMultiValue()) {
                                if (!templateContentEn.val()) {
                                    templateContentEn.error('英文邮件模板不能为空');
                                }
                            }
                            
                            if (!effDate.isMultiValue()) {
                                if (!effDate.val()) {
                                    effDate.error('生效日期不能为空');
                                }
                            }
                            
                            if (!expDate.isMultiValue()) {
                                if (!expDate.val()) {
                                    expDate.error('失效日期不能为空');
                                }
                            }
                            
                            // If any error was reported, cancel the submission so it can be corrected
                            if (this.inError()) {
                                return false;
                            }
                        }
                    });
                    
                    editor.on('open', function (e, o, action) {
                        if (action === 'edit') {
                            this.field('id').disable();
                        }
                    });
    
                    table = $('#emailTemplateTable').DataTable({
                        dom: "Blfrtip",
                        ajax: {
                            contentType: 'application/json',
                            url: '/cmm/emailtemplatectrl/findAll',
                            type: 'post',
                            data: function (d) {
                                d.extra_search = {
                                    "id": $("#id").val(),
                                    "content": $("#content").val(),
                                    "beginDate": $("#beginDate").val(),
                                    "endDate": $("#endDate").val(),
                                };
                                return JSON.stringify(d);
                            },
                            error: function(d) {
                                alert(JSON.stringify(d));
                            },
                        },
                        serverSide: true,
                        searchDelay: 1000,
                        responsive: true,
                        processing: true,
                        language: {
                            url: languageUrl
                        },
                        select: true,
                        buttons: [
                            {extend: "create", editor: editor},
                            {extend: "edit", editor: editor},
                            {extend: "remove", editor: editor},
                        ],
                        columns: [{
                            data: 'id'
                        },{
                            data: 'type'
                        },{
                            data: 'subject',
                            render: $.fn.dataTable.render.ellipsis(20)
                        },{
                            data: 'effDate'
                        },{
                            data: 'expDate'
                        },{
                            data: 'stats',
                            render: function (data, type, row) {
                                switch (data) {
                                    case "0":
                                        return "无效";
                                    case "1":
                                        return "有效";
                                    default:
                                        return data;
                                }
                            }
                        },{
                            data: 'tmSmp'
                        }]
                    });
                });
            });
            
            // 初始化日期控件
            $('#beginDate').datetimepicker({
                lang: 'en',
                timepicker: false,
                validateOnBlur: false,
                format: 'Y-m-d',
                formatDate: 'Y-m-d'
            });
            
            $('#endDate').datetimepicker({
                lang: 'en',
                timepicker: false,
                validateOnBlur: false,
                format: 'Y-m-d',
                formatDate: 'Y-m-d',
            });
            
            // 查询按钮
            function search() {
                table.ajax.reload();
            }
            
            // ellipsis插件
            $.fn.dataTable.render.ellipsis = function (cutoff, wordbreak, escapeHtml) {
                var esc = function (t) {
                    return t
                        .replace(/&/g, '&amp;')
                        .replace(/</g, '&lt;')
                        .replace(/>/g, '&gt;')
                        .replace(/"/g, '&quot;');
                };
             
                return function (d, type, row) {
                    // Order, search and type get the original data
                    if (type !== 'display') {
                        return d;
                    }
             
                    if (typeof d !== 'number' && typeof d !== 'string') {
                        return d;
                    }
             
                    d = d.toString(); // cast numbers
             
                    if (d.length < cutoff) {
                        return d;
                    }
             
                    var shortened = d.substr(0, cutoff-1);
             
                    // Find the last white space character in the string
                    if (wordbreak) {
                        shortened = shortened.replace(/\s([^\s]*)$/, '');
                    }
             
                    // Protect against uncontrolled HTML input
                    if (escapeHtml) {
                        shortened = esc(shortened);
                    }
             
                    return '<span class="ellipsis" title="'+esc(d)+'">'+shortened+'&#8230;</span>';
                };
            };
        </script>
    </body>
</html>
