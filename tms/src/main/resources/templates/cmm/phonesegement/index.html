<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

    <head>
        <title data-i18n="nav.cmmsub.syssub.title"></title>
        <div th:replace="head"></div>
    </head>

    <body>
        <div id="wrapper">
            <div th:replace="nav"></div>
    
            <div id="page-wrapper" class="gray-bg">
                <div th:replace="top"></div>
                <div class="row wrapper border-bottom white-bg page-heading">
                    <div class="col-lg-10">
                        <h2 data-i18n="nav.cmmsub.syssub.phsmgr"></h2>
                        <ol class="breadcrumb">
                            <li>
                                <a data-i18n="nav.cmmmgr"></a>
                            </li>
                            <li>
                                <a data-i18n="nav.cmmsub.sysmgr"></a>
                            </li>
                            <li class="active">
                                <strong data-i18n="nav.cmmsub.syssub.phsmgr"></strong>
                            </li>
                        </ol>
                    </div>
                </div>
                
                <div class="wrapper wrapper-content animated fadeInRight">
                    <!--查询条件-->
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="ibox float-e-margins">
                                <div class="ibox-content">
                                    <div class="box-header">
                                        <div class="form-horizontal">
                                            <table class="table table-striped table-bordered table-hover">
                                                <tr>
                                                    <!-- 手机号段-->
                                                    <td>
                                                        <label class="control-label" data-i18n="cmm.phonesegement.prefixNumber"></label>
                                                    </td>
                                                    <td>
                                                        <input class="form-control" id="prefixNumber" name="prefixNumber"/>
                                                    </td>
                                                    <!-- 归属运营商-->
                                                    <td>
                                                        <label class="control-label" data-i18n="cmm.phonesegement.carrier"></label>
                                                    </td>
                                                    <td colspan="2">
                                                        <select class="form-control" id="carrier" name="carrier">
                                                            <option value="" data-i18n="cmm.ddl.choose" />
                                                            <option value="seatel" data-i18n="cmm.ddl.carrier.seatel" />
                                                            <option value="cellcard" data-i18n="cmm.ddl.carrier.cellcard" />
                                                            <option value="Smart" data-i18n="cmm.ddl.carrier.Smart" />
                                                            <option value="metfene" data-i18n="cmm.ddl.carrier.metfene" />
                                                            <option value="qb" data-i18n="cmm.ddl.carrier.qb" />
                                                            <option value="CooTel" data-i18n="cmm.ddl.carrier.CooTel" />
                                                            <option value="Mobile" data-i18n="cmm.ddl.carrier.Mobile" />
                                                            <option value="Unicom" data-i18n="cmm.ddl.carrier.Unicom" />
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <!-- 归属国家 -->
                                                    <td>
                                                        <label class="control-label" data-i18n="cmm.phonesegement.country" ></label>
                                                    </td>
                                                    <td>
                                                        <select class="form-control" id="countryCode" name="countryCode">
                                                            <option value="" data-i18n="cmm.ddl.choose" />
                                                            <option value="1" data-i18n="cmm.ddl.country.cambodia" />
                                                            <option value="0" data-i18n="cmm.ddl.country.china" />
                                                        </select>
                                                    </td>
                                                    <!-- 归属省份 -->
                                                    <td>
                                                        <label class="control-label" data-i18n="cmm.phonesegement.province"></label>
                                                    </td>
                                                    <td>
                                                        <select class="form-control" id="provinceCode" name="provinceCode">
                                                            <option value="" data-i18n="cmm.ddl.choose" />
                                                        </select>
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-primary" id="searchBtn" data-i18n="cmm.button.query" onclick="search()" ></button>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                    <hr/>
                                    <div class="table-responsive">
                                        <table id="phoneSegementInf" class="table table-striped table-bordered table-hover">
                                            <thead>
                                                <tr>
                                                    <th data-i18n="cmm.phonesegement.prefixNumber"></th>
                                                    <th data-i18n="cmm.phonesegement.numberLength"></th>
                                                    <th data-i18n="cmm.phonesegement.carrier"></th>
                                                    <th data-i18n="cmm.phonesegement.country"></th>
                                                    <th data-i18n="cmm.phonesegement.province"></th>
                                                    <th data-i18n="cmm.phonesegement.city"></th>
                                                    <th data-i18n="cmm.phonesegement.stats"></th>
                                                    <th data-i18n="cmm.phonesegement.tmSmp"></th>
                                                </tr>
                                            </thead>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <div th:replace="footer"></div>
            </div>
        </div>
    
        <div th:replace="script"></div>
    
        <!-- Page-Level Scripts -->
        <script>
            var editor;
            var table;
    
            $(document).ready(function () {
                var languageUrl;
                switch ($.cookie('lang')) {
                    case 'zh':
                        languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                        break;
                    case 'en':
                        languageUrl = '/datatables/plugins/i18n/English.lang';
                        break;
                    case 'kh':
                        languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                        break;
                }
                
                i18nLoad.then(function () {
                    editor = new $.fn.dataTable.Editor({
                        ajax: {
                            create: {
                                type: 'POST',
                                url: '/cmm/phsctrl/add',
                                contentType: 'application/json',
                                data: function (d) {
                                    return JSON.stringify(d);
                                }
                            },
                            edit: {
                                type: 'POST',
                                url: '/cmm/phsctrl/modify',
                                contentType: 'application/json',
                                data: function (d) {
                                    return JSON.stringify(d);
                                }
                            },
                            remove: {
                                type: 'POST',
                                url: '/cmm/phsctrl/delete',
                                contentType: 'application/json',
                                data: function (d) {
                                    return JSON.stringify(d);
                                }
                            }
                        },
                        table: "#phoneSegementInf",
                        idSrc: "prefixNumber",
                        fields: [ {
                                label: $.i18n.t("cmm.phonesegement.prefixNumber"), 
                                name: "prefixNumber"
                            },{
                                label: $.i18n.t("cmm.phonesegement.numberLength"), 
                                name: "numberLength"
                            },{
                                label: $.i18n.t("cmm.phonesegement.carrier"), 
                                name: "carrier",
                                type:  "select",
                                options: [
                                    { label: $.i18n.t("cmm.ddl.carrier.seatel"), value: "seatel" },
                                    { label: $.i18n.t("cmm.ddl.carrier.cellcard"), value: "cellcard" },
                                    { label: $.i18n.t("cmm.ddl.carrier.Smart"), value: "Smart" },
                                    { label: $.i18n.t("cmm.ddl.carrier.metfene"), value: "metfene" },
                                    { label: $.i18n.t("cmm.ddl.carrier.qb"), value: "qb" },
                                    { label: $.i18n.t("cmm.ddl.carrier.CooTel"), value: "CooTel" },
                                    { label: $.i18n.t("cmm.ddl.carrier.Mobile"), value: "Mobile" },
                                    { label: $.i18n.t("cmm.ddl.carrier.Unicom"), value: "Unicom" },
                                ]
                            },{
                                label: $.i18n.t("cmm.phonesegement.areaCode"), 
                                name: "areaCode",
                                type:  "select",
                                options: [
                                    { label: $.i18n.t("cmm.ddl.area.cambodia"), value: "855" },
                                    { label: $.i18n.t("cmm.ddl.area.china"), value: "86" }
                                ]
                            },{
                                label: $.i18n.t("cmm.phonesegement.country"), 
                                name: "countryCode",
                                type:  "select",
                                options: [
                                    { label: $.i18n.t("cmm.ddl.country.cambodia"), value: "1" },
                                    { label: $.i18n.t("cmm.ddl.country.china"), value: "0" }
                                ]
                            },{
                                label: 'countryKh', 
                                name: "countryKh",
                                type:  "hidden"
                            },{
                                label: 'countryZh', 
                                name: "countryZh",
                                type:  "hidden"
                            },{
                                label: 'countryEn', 
                                name: "countryEn",
                                type:  "hidden"
                            },{
                                label: $.i18n.t("cmm.phonesegement.province"), 
                                name: "provinceCode",
                                type:  "select"
                            },{
                                label: 'provinceKh', 
                                name: "provinceKh",
                                type:  "hidden"
                            },{
                                label: 'provinceZh', 
                                name: "provinceZh",
                                type:  "hidden"
                            },{
                                label: 'provinceEn', 
                                name: "provinceEn",
                                type:  "hidden"
                            },{
                                label: $.i18n.t("cmm.phonesegement.city"), 
                                name: "cityCode",
                                type:  "select"
                            },{
                                label: 'cityKh', 
                                name: "cityKh",
                                type:  "hidden"
                            },{
                                label: 'cityZh', 
                                name: "cityZh",
                                type:  "hidden"
                            },{
                                label: 'cityEn', 
                                name: "cityEn",
                                type:  "hidden"
                            },{
                                label: $.i18n.t("cmm.phonesegement.effDate"), 
                                name: "effDate",
                                type: "datetime",
                                def:   function () { return new Date();}
                            },{
                                label: $.i18n.t("cmm.phonesegement.expDate"), 
                                name: "expDate",
                                type: "datetime",
                                def:   function () { return new Date();}
                            },{
                                label: $.i18n.t("cmm.phonesegement.stats"), 
                                name: "stats",
                                type:  "select",
                                options: [
                                    { label: $.i18n.t("cmm.statsEnum.valid"), value: "1" },
                                    { label: $.i18n.t("cmm.statsEnum.invalid"), value: "0" }
                                ]
                            }
                        ],
                        i18n: {
                            create: {
                                button: $.i18n.t("cmm.button.add"),
                                title: $.i18n.t("cmm.button.add"),
                                submit: $.i18n.t("cmm.button.add")
                            },
                            edit: {
                                button: $.i18n.t("cmm.button.modify"),
                                title: $.i18n.t("cmm.button.modify"),
                                submit: $.i18n.t("cmm.button.modify")
                            },
                            remove: {
                                button: $.i18n.t("cmm.button.delete"),
                                title: $.i18n.t("cmm.button.delete"),
                                submit: $.i18n.t("cmm.button.delete"),
                                confirm: {
                                    _: $.i18n.t("cmm.action.cofirm.delete"),
                                    1: $.i18n.t("cmm.action.cofirm.delete")
                                }
                            }
                        }
                    });
                    
                    editor.on( 'open', function ( e, o, action ) {
                        if ( action !== 'remove' ) {
                             // 初始化省份
                             var country = this.get('countryCode');
                             editorInitProvince(country);
                             
                             editor.dependent( 'countryCode', function ( val, data, callback ) {
                                 editorInitProvince(val); 
                                 editorFillCountry(val);
                             });
                             
                             // 初始化地市
                             var province = this.get('provinceCode');
                             editorInitCity(province);
                             
                             editor.dependent( 'provinceCode', function ( val, data, callback ) {
                                 editorInitCity(val);
                                 editorFillProvince(val);
                             });
                             
                             editor.dependent( 'cityCode', function ( val, data, callback ) {
                                 editorFillCity(val);
                             });
                        }
                    } );
                    
                    editor.on( 'preSubmit', function ( e, o, action ) {
                        if ( action !== 'remove' ) {
                            var prefixNumber = this.field( 'prefixNumber' );
                            var numberLength = this.field( 'numberLength' );
                            var carrier = this.field( 'carrier' );
                            var areaCode = this.field( 'areaCode' );
                            var countryCode = this.field( 'countryCode' );
                            var provinceCode = this.field( 'provinceCode' );
                            var cityCode = this.field( 'cityCode' );
                            var effDate = this.field( 'effDate' );
                            var expDate = this.field( 'expDate' );
                            // Only validate user input values - different values indicate that
                            // the end user has not entered a value
                            
                            if ( ! prefixNumber.isMultiValue() ) {
                                if ( ! prefixNumber.val() ) {
                                    prefixNumber.error( $.i18n.t("cmm.msg.phs.null.prefixNumber") );
                                }
                                 
                                if ( prefixNumber.val().length > 8 ) {
                                    prefixNumber.error( $.i18n.t("cmm.msg.phs.length.prefixNumber") );
                                }
                            }
                            
                            if ( ! numberLength.isMultiValue() ) {
                                if ( ! numberLength.val() ) {
                                	numberLength.error( $.i18n.t("cmm.msg.phs.null.numberLength") );
                                }
                                 
                                if ( numberLength.val().length > 20 ) {
                                	numberLength.error( $.i18n.t("cmm.msg.phs.length.numberLength") );
                                }
                            }
                            
                            if ( ! carrier.isMultiValue() ) {
                                if ( ! carrier.val() ) {
                                    carrier.error( $.i18n.t("cmm.msg.phs.null.carrier") );
                                }
                            }
                            
                            if ( ! areaCode.isMultiValue() ) {
                                if ( ! areaCode.val() ) {
                                    areaCode.error( $.i18n.t("cmm.msg.phs.null.areaCode") );
                                }
                            }
                            
                            if ( ! countryCode.isMultiValue() ) {
                                if ( ! countryCode.val() ) {
                                    countryCode.error( $.i18n.t("cmm.msg.phs.null.country") );
                                }
                            }
                            
                            if ( ! provinceCode.isMultiValue() ) {
                                if ( ! provinceCode.val() ) {
                                    provinceCode.error( $.i18n.t("cmm.msg.phs.null.province") );
                                }
                            }
                            
                            if ( ! cityCode.isMultiValue() ) {
                                if ( ! cityCode.val() ) {
                                    cityCode.error( $.i18n.t("cmm.msg.phs.null.city") );
                                }
                            }
                            
                            if ( ! effDate.isMultiValue() ) {
                                if ( ! effDate.val() ) {
                                    effDate.error( $.i18n.t("cmm.msg.phs.null.effDate") );
                                }
                            }
                            
                            if ( ! expDate.isMultiValue() ) {
                                if ( ! expDate.val() ) {
                                    expDate.error( $.i18n.t("cmm.msg.phs.null.expDate") );
                                }
                            }
                            // ... additional validation rules
                 
                            // If any error was reported, cancel the submission so it can be corrected
                            if ( this.inError() ) {
                                return false;
                            }
                        }
                    } );
                    
                    editor.on( 'open', function ( e, o, action ) {
                        if ( action === 'edit' ) {
                            this.field( 'prefixNumber' ).disable();
                        }
                        if ( action === 'create' ) {
                            this.field( 'prefixNumber' ).enable();
                        }
                    } );
    
                    table = $('#phoneSegementInf').DataTable({
                        dom: "Blfrtip",
                        ajax: {
                            contentType: 'application/json',
                            url: '/cmm/phsctrl/findAll',
                            type: 'post',
                            data: function (d) {
                                d.extra_search = {
                                    "prefixNumber" : $("#prefixNumber").val(),
                                    "carrier" : $("#carrier").val(),
                                    "countryCode" : $("#countryCode").val(),
                                    "provinceCode" : $("#provinceCode").val(),
                                };
                                return JSON.stringify(d);
                            }
                        },
                        serverSide: true,
                        searchDelay: 1000,
                        responsive: true,
                        processing: true,
                        language: {
                            url: languageUrl
                        },
                        select: true,
                        buttons: [
                            {extend: "create", editor: editor},
                            {extend: "edit", editor: editor},
                            {extend: "remove", editor: editor},
                        ],
                        columns: [{
                            data: 'prefixNumber'
                        },{
                            data: 'numberLength'
                        },{
                            data: 'carrier'
                        },{
                            data: 'country'
                        },{
                            data: 'province'
                        },{
                            data: 'city'
                        },{
                            data: 'stats',
                            render: function (data, type, row) {
                                  switch (data) {
                                      case "0":
                                          return $.i18n.t("cmm.statsEnum.invalid");
                                      case "1":
                                          return $.i18n.t("cmm.statsEnum.valid");
                                      default:
                                          return data;
                                }
                            }
                        },{
                            data: 'tmSmp'
                        }]
                    });
                });
            });
            
            <!--查询按钮-->
            function search() {
                table.ajax.reload();
            }
            
            // editor获取省份
            function editorInitProvince(country) {
                $.getJSON('/cmm/areactrl/findAll', {
                    parentId: country,
                    ajax : 'true'
                }, function(data) {
                    var obj = new Object();
                    var len = data.length;
                    for ( var i = 0; i < len; i++) {
                        switch ($.cookie('lang')) {
                        case 'zh':
                            obj[data[i].areaNameZh] = data[i].id;
                            break;
                        case 'en':
                            obj[data[i].areaNameEn] = data[i].id;
                            break;
                        case 'kh':
                            obj[data[i].areaNameKh] = data[i].id;
                            break;
                        }
                    }
                    editor.field('provinceCode').update(obj);
                });
            }
            
            // editor获取地市
            function editorInitCity(province) {
                $.getJSON('/cmm/areactrl/findAll', {
                    parentId: province,
                    ajax : 'true'
                }, function(data) {
                    var obj = new Object();
                    var len = data.length;
                    for ( var i = 0; i < len; i++) {
                        switch ($.cookie('lang')) {
                        case 'zh':
                            obj[data[i].areaNameZh] = data[i].id;
                            break;
                        case 'en':
                            obj[data[i].areaNameEn] = data[i].id;
                            break;
                        case 'kh':
                            obj[data[i].areaNameKh] = data[i].id;
                            break;
                        }
                    }
                    editor.field('cityCode').update(obj);
                });
            }
            
            // 填充国家信息
            function editorFillCountry(country) {
                $.getJSON('/cmm/areactrl/get', {
                    parentId: country,
                    ajax : 'true'
                }, function(data) {
                    editor.field('countryKh').val(data.areaNameKh);
                    editor.field('countryZh').val(data.areaNameZh);
                    editor.field('countryEn').val(data.areaNameEn);
                });
            }
            
            // 填充省份信息
            function editorFillProvince(province) {
                $.getJSON('/cmm/areactrl/get', {
                    parentId: province,
                    ajax : 'true'
                }, function(data) {
                    var obj = data.areaNameKh;
                    editor.field('provinceKh').val(obj);
                    obj = data.areaNameZh;
                    editor.field('provinceZh').val(obj);
                    obj = data.areaNameEn;
                    editor.field('provinceEn').val(obj);
                });
            }
            
            // 填充地市信息
            function editorFillCity(city) {
                $.getJSON('/cmm/areactrl/get', {
                    parentId: city,
                    ajax : 'true'
                }, function(data) {
                    var obj = data.areaNameKh;
                    editor.field('cityKh').val(obj);
                    obj = data.areaNameZh;
                    editor.field('cityZh').val(obj);
                    obj = data.areaNameEn;
                    editor.field('cityEn').val(obj);
                });
            }
            
            
            // 获取省份
            $('#countryCode').change(
                function() {
                    var parentId = $(this).val();
                    if (parentId.length == 0) {
                        $('#provinceCode').html('<option value="">' + $.i18n.t("cmm.ddl.choose") + '</option>');
                    } else {
                        $.getJSON('/cmm/areactrl/findAll', {
                            parentId : parentId,
                            ajax : 'true'
                        }, function(data) {
                            var html = '';
                            var len = data.length;
                            for ( var i = 0; i < len; i++) {
                                switch ($.cookie('lang')) {
                                case 'zh':
                                    html += '<option value="' + data[i].id + '">' + data[i].areaNameZh + '</option>';
                                    break;
                                case 'en':
                                    html += '<option value="' + data[i].id + '">' + data[i].areaNameEn + '</option>';
                                    break;
                                case 'kh':
                                    html += '<option value="' + data[i].id + '">' + data[i].areaNameKh + '</option>';
                                    break;
                                }
                            }
                            $('#provinceCode').html(html);
                        });
                    }
              });
        </script>
    </body>
</html>