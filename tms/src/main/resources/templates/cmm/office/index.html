<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="nav.cmmsub.orgsub.title"></title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>
            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="nav.cmmsub.orgsub.officemgr"></h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="nav.cmmmgr"></a>
                        </li>
                        <li>
                            <a data-i18n="nav.cmmsub.orgmgr"></a>
                        </li>
                        <li class="active">
                            <strong data-i18n="nav.cmmsub.orgsub.officemgr"></strong>
                        </li>
                    </ol>
                </div>
            </div>
            
            <div class="wrapper wrapper-content animated fadeInRight">
                <!--查询条件-->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="box-header">
                                        <!-- 表头搜索栏 -->
                                        <table style="width: 80%">
                                            <tr>
                                                <!--分公司编号-->
                                                <td align="center">
                                                    <label class="control-label" for="officeId" data-i18n="nav.cmmsub.orgsub.offsub.officeid"></label>
                                                </td>
                                                <td>&nbsp; </td>
                                                <td>
                                                    <input name="officeId" id="officeId" class="form-control" value=""/>
                                                </td>
                                                <td>&nbsp;</td>

                                                <!--分公司名称-->
                                                <td align="center">
                                                    <label class="control-label" for="officeNm" data-i18n="nav.cmmsub.orgsub.offsub.officenm"></label>
                                                </td>
                                                <td>&nbsp;</td>
                                                <td>
                                                    <input name="officeNm" id="officeNm" class="form-control" value=""/>
                                                </td>
                                                <td>&nbsp;&nbsp;</td>

                                                <!--提交按钮-->
                                                <td>
                                                    <button type="button" id="searchBtn" class="btn btn-primary" onclick="search()" data-i18n="cmm.search"></button>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!--数据表格-->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="officeInf" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="nav.cmmsub.orgsub.offsub.officeid"></th>
                                                <th data-i18n="nav.cmmsub.orgsub.offsub.officenm"></th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var editor;
        var table;

        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }
            
            i18nLoad.then(function () {
                editor = new $.fn.dataTable.Editor({
                    ajax: {
                        create: {
                            type: 'POST',
                            url: '/cmm/officectrl/add',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            },
                            success:function(o){
                                handleErr(o);
                            },
                            error:function(e){

                            }
                        },
                        edit: {
                            type: 'POST',
                            url: '/cmm/officectrl/modify',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        },
                        remove: {
                            type: 'POST',
                            url: '/cmm/officectrl/delete',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            },
                            success: function (data) {
                            	var prebra=data.PREBRA;
                            	if(prebra=="YES"){
                            		swal($.i18n.t("nav.cmmsub.orgsub.offsub.deletefailed"), $.i18n.t("nav.cmmsub.orgsub.offsub.exitbra"), "error");  
                            	}
                            },
                            error : function (data) {
                            	swal($.i18n.t("nav.cmmsub.orgsub.offsub.deletefailed"), $.i18n.t("nav.cmmsub.orgsub.offsub.exitbra"), "error");  
                            },
                        }
                    },
                    table: "#officeInf",
                    idSrc: "id",
                    fields: [ {
	                        label: "id",
	                        name: "id",
	                        type: "hidden"
	                    },{
                            label: $.i18n.t("nav.cmmsub.orgsub.offsub.officeid"), 
                            name: "officeId"
                        },{
                        	label: $.i18n.t("nav.cmmsub.orgsub.offsub.officenm"), 
                            name: "officeNm"
                        }
                    ],
                    i18n: {
                        create: {button: $.i18n.t("cmm.button.add"), title: $.i18n.t("cmm.button.add"), submit: $.i18n.t("cmm.button.create")},
                        edit: {button: $.i18n.t("cmm.button.modify"), title: $.i18n.t("cmm.button.modify"), submit: $.i18n.t("cmm.button.update")},
                        remove: {
                            button: $.i18n.t("cmm.button.delete"), title: $.i18n.t("cmm.button.delete"), submit: $.i18n.t("cmm.button.delete"),
                            confirm: {
                                _: $.i18n.t("cmm.button.multi-delete"),
                                1: $.i18n.t("cmm.button.single-delete")
                            }
                        }
                    }
                });
                editor.on( 'open', function ( e, o, action ) {
                    if ( action === 'edit' ) {
                        this.field( 'officeId' ).disable();
                    }
                } );
                editor.on( 'preSubmit', function ( e, o, action ) {
                    if ( action !== 'remove' ) {
                        var officeId = this.field( 'officeId' );
                        var officeNm = this.field( 'officeNm' );
                        
                        if ( ! officeId.isMultiValue() ) {
                            if ( ! officeId.val() ) {
                            	officeId.error( $.i18n.t("cmm.msg.office.null.officeId") );
                            }
                        }
                        
                        if ( ! officeNm.isMultiValue() ) {
                            if ( ! officeNm.val() ) {
                            	officeNm.error( $.i18n.t("cmm.msg.office.null.officeNm") );
                            }
                             
                        }
                        if ( this.inError() ) {
                            return false;
                        }
                    }
                } );
                table = $('#officeInf').DataTable({
                    dom: "Blfrtip",
                    ajax: {
                        contentType: 'application/json',
                        url: '/cmm/officectrl/findAll',
                        type: 'post',
                        data: function (d) {
                            d.extra_search = {
                                "officeId" : $("#officeId").val(),
                                "officeNm" : $("#officeNm").val()
                            };
                            return JSON.stringify(d);
                        }
                    },
                    serverSide: true,
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    select: true,
                    buttons: [
                        {extend: "create", editor: editor},
                        {extend: "edit", editor: editor},
                        {extend: "remove", editor: editor},
                    ],
                    columns: [{
                        data: 'officeId'
                    },{
                        data: 'officeNm'
                    }]
                });
            });
            
            // 初始化省份
        });
        
        <!--查询按钮-->
        function search() {
            table.ajax.reload();
        };
        
        function handleErr(o){
            if(o.code && o.message){
            	swal($.i18n.t("nav.cmmsub.orgsub.offsub.addfailed"), $.i18n.t("nav.cmmsub.orgsub.offsub.exitofficeid"), "error");
            }
        }
    </script>
</body>
</html>