<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="nav.cmmsub.orgsub.brasub.title"></title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>
            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="nav.cmmsub.orgsub.branchmgr"></h2>
                    <ol class="breadcrumb">
                        <li>
	                        <a data-i18n="nav.cmmmgr"></a>
	                    </li>
	                    <li>
	                        <a data-i18n="nav.cmmsub.orgmgr"></a>
	                    </li>
	                    <li class="active">
	                        <strong data-i18n="nav.cmmsub.orgsub.branchmgr"></strong>
	                    </li>
                    </ol>
                </div>
            </div>
            
            <div class="wrapper wrapper-content animated fadeInRight">
                <!--查询条件-->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="box-header">
                                        <!-- 表头搜索栏 -->
                                        <table style="width: 80%">
                                            <tr>
                                                <!--部门编号-->
                                                <td align="center">
                                                    <label class="control-label" for="braId" data-i18n="nav.cmmsub.orgsub.brasub.braid"></label>
                                                </td>
                                                <td>&nbsp; </td>
                                                <td>
                                                    <input name="braId" id="braId" class="form-control" value=""/>
                                                </td>
                                                <td>&nbsp;</td>

                                                <!--部门名称-->
                                                <td align="center">
                                                    <label class="control-label" for="braNm" data-i18n="nav.cmmsub.orgsub.brasub.branm"></label>
                                                </td>
                                                <td>&nbsp;</td>
                                                <td>
                                                    <input name="braNm" id="braNm" class="form-control" value=""/>
                                                </td>
                                                <td>&nbsp;</td>

                                                <!--归属公司-->
                                                <td align="center">
                                                    <label class="control-label" for="officeId" data-i18n="nav.cmmsub.orgsub.brasub.officenm"></label>
                                                </td>
                                                <td>
                                                    <select id="officeId" name="officeId" class="form-control">
                                                    </select>
                                                </td>
                                                <td>&nbsp;&nbsp;</td>

                                                <!--提交按钮-->
                                                <td>
                                                    <button type="button" id="searchBtn" class="btn btn-primary" onclick="search()" data-i18n="cmm.search"></button>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!--数据表格-->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="branchInf" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="nav.cmmsub.orgsub.brasub.braid"></th>
                                                <th data-i18n="nav.cmmsub.orgsub.brasub.branm"></th>
                                                <th data-i18n="nav.cmmsub.orgsub.brasub.officenm"></th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
            </div>
    </div>
    </div>
    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var editor;
        var table;

        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }
            
            i18nLoad.then(function () {
                editor = new $.fn.dataTable.Editor({
                    ajax: {
                        create: {
                            type: 'POST',
                            url: '/cmm/branchctrl/add',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            },
                            success:function(o){
                                handleErr(o);
                            },
                            error:function(e){

                            }
                        },
                        edit: {
                            type: 'POST',
                            url: '/cmm/branchctrl/modify',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        },
                        remove: {
                            type: 'POST',
                            url: '/cmm/branchctrl/delete',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            },
                            success: function (data) {
                            	var preuser=data.PREUSER;
                            	if(preuser=="YES"){
                            		swal($.i18n.t("nav.cmmsub.orgsub.brasub.deletefailed"), $.i18n.t("nav.cmmsub.orgsub.brasub.exituser"), "error");  
                            	}
                            },
                            error : function (data) {
                            	swal($.i18n.t("nav.cmmsub.orgsub.brasub.deletefailed"), $.i18n.t("nav.cmmsub.orgsub.brasub.exituser"), "error");  
                            },
                        }
                    },
                    table: "#branchInf",
                    idSrc: "id",
                    fields: [ {
	                        label: "id",
	                        name: "id",
	                        type: "hidden"
	                    },{
                            label: $.i18n.t("nav.cmmsub.orgsub.brasub.braid"),
                            name: "braId"
                        },{
                            label: $.i18n.t("nav.cmmsub.orgsub.brasub.branm"), 
                            name: "braNm"
                        },{
                            label: $.i18n.t("nav.cmmsub.orgsub.brasub.officenm"), 
                            name: "officeId",
                            type:  "select"
                        },{
                            label: $.i18n.t("nav.cmmsub.orgsub.brasub.officenm"), 
                            name: "officeNm",
                            type: "hidden"
                        }
                    ],
                    i18n: {
                        create: {button: $.i18n.t("cmm.button.add"), title: $.i18n.t("cmm.button.add"), submit: $.i18n.t("cmm.button.create")},
                        edit: {button: $.i18n.t("cmm.button.modify"), title: $.i18n.t("cmm.button.modify"), submit: $.i18n.t("cmm.button.update")},
                        remove: {
                            button: $.i18n.t("cmm.button.delete"), title: $.i18n.t("cmm.button.delete"), submit: $.i18n.t("cmm.button.delete"),
                            confirm: {
                                _: $.i18n.t("cmm.button.multi-delete"),
                                1: $.i18n.t("cmm.button.single-delete")
                            }
                        }
                    }
                });
                editor.on( 'open', function ( e, o, action ) {
                	if ( action === 'edit' ) {
                		this.field( 'braId' ).disable();
                    }
                    if ( action !== 'remove' ) {
                         // 初始化分公司
                         editorInitProvince();
                         editor.dependent( 'officeId', function ( val, data, callback ) {
                        	// alert(val);
                        	 //var s=JSON.stringify(data);
                        	// alert(s);
                        	var myselect=document.getElementById("DTE_Field_officeId");
                        	var index=myselect.selectedIndex;
                        	var officeNm=myselect.options[index].text;
                             editorFillOfficeNm(officeNm);
                         });
                    }
                } );
                editor.on( 'preSubmit', function ( e, o, action ) {
                    if ( action !== 'remove' ) {
                        var braId = this.field( 'braId' );
                        var braNm = this.field( 'braNm' );
                        
                        if ( ! braId.isMultiValue() ) {
                            if ( ! braId.val() ) {
                            	braId.error( $.i18n.t("cmm.msg.branch.null.braId") );
                            }
                        }
                        
                        if ( ! braNm.isMultiValue() ) {
                            if ( ! braNm.val() ) {
                            	braNm.error( $.i18n.t("cmm.msg.branch.null.braNm") );
                            }
                             
                        }
                        if ( this.inError() ) {
                            return false;
                        }
                    }
                } );
                table = $('#branchInf').DataTable({
                    dom: "Blfrtip",
                    ajax: {
                        contentType: 'application/json',
                        url: '/cmm/branchctrl/findAll',
                        type: 'post',
                        data: function (d) {
                            d.extra_search = {
                                "braId" : $("#braId").val(),
                                "braNm" : $("#braNm").val(),
                                "officeId" : $("#officeId").val()
                            };
                            return JSON.stringify(d);
                        }
                    },
                    serverSide: true,
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    select: true,
                    buttons: [
                        {extend: "create", editor: editor},
                        {extend: "edit", editor: editor},
                        {extend: "remove", editor: editor},
                    ],
                    columns: [{
                        data: 'braId'
                    },{
                        data: 'braNm'
                    },{
                        data: 'officeNm'
                    }]
                });
            });
            
            // 初始化公司
            initProvince();
        });
        
        <!--查询按钮-->
        function search() {
            table.ajax.reload();
        }
        
        // 初始分公司
        function initProvince() {
            $.getJSON('/cmm/officectrl/findAll', {
                ajax : 'true'
            }, function(data) {
                var html = '<option value="">' + $.i18n.t("cmm.ddl.choose") + '</option>';
                var len = data.length;
                for ( var i = 0; i < len; i++) {
                        html += '<option value="' + data[i].officeId + '">' + data[i].officeNm + '</option>';
                }
                $('#officeId').html(html);
            });
        };
     // editor获取公司
        function editorInitProvince() {
            $.getJSON('/cmm/officectrl/findAll', {
                ajax : 'true'
            }, function(data) {
                var obj = new Object();
                var len = data.length;
                for ( var i = 0; i < len; i++) {
                    obj[data[i].officeNm] = data[i].officeId;
                }
                editor.field('officeId').update(obj);
                });
        };
     // 填充公司信息
        function editorFillOfficeNm(officeNm) {
                editor.field('officeNm').val(officeNm);
        }
        function handleErr(o){
            if(o.code && o.message){
            	swal($.i18n.t("nav.cmmsub.orgsub.brasub.addfailed"), $.i18n.t("nav.cmmsub.orgsub.brasub.exitbraid"), "error");
            }
        }
    </script>
</body>
</html>