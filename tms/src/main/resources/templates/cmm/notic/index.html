<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

    <head>
        <title data-i18n="nav.bussub.oprsub.noticmgr.title"></title>
        <div th:replace="head"></div>
    </head>

    <body>
        <div id="wrapper">
            <div th:replace="nav"></div>
    
            <div id="page-wrapper" class="gray-bg">
                <div th:replace="top"></div>
                <div class="row wrapper border-bottom white-bg page-heading">
                    <div class="col-lg-10">
                        <h2 data-i18n="nav.bussub.oprsub.noticmgr.content"></h2>
                        <ol class="breadcrumb">
                            <li>
                                <a data-i18n="nav.busmgr"></a>
                            </li>
                            <li>
                                <a data-i18n="nav.bussub.oprmgr"></a>
                            </li>
                            <li>
                                <strong data-i18n="nav.bussub.oprsub.noticmgr.content"></strong>
                            </li>
                        </ol>
                    </div>
                </div>
                
                <div class="wrapper wrapper-content animated fadeInRight">
                    <!--查询条件-->
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="ibox float-e-margins">
                                <div class="ibox-content">
                                   <div class="box-header">
                                       <div class="form-horizontal">
                                            <table class="table table-striped table-bordered table-hover">
                                                 <tr>
                                                     <!-- 公告主题-->
                                                     <td align="center">
                                                         <label class="control-label" data-i18n="cmm.notice.title"></label>
                                                     </td>
                                                     <td>
                                                         <input class="form-control"  id="title" name="title" />
                                                     </td>
                                                     <!--提交日期-->
                                                     <td align="center">
                                                         <label class="control-label" data-i18n="cmm.label.date"></label>
                                                     </td>
                                                     <td>
                                                         <input class="form-control" id="beginDate" name="beginDate" />
                                                     </td>
                                                     <td>
                                                         <input class="form-control" id="endDate" name="endDate" />
                                                     </td>
                                                     <td align="center">
                                                         <button class="btn btn-primary" id="searchBtn" data-i18n="cmm.button.query" onclick="search()" ></button>
                                                     </td>
                                                 </tr>
                                            </table>
                                        </div>
                                    </div>
                                    <hr/>
                                    <div class="table-responsive">
                                        <table id="noticeInf" class="table table-striped table-bordered table-hover">
                                            <thead>
                                                <tr>
                                                    <th data-i18n="cmm.notice.title"></th>
                                                    <th data-i18n="cmm.notice.effDate"></th>
                                                    <th data-i18n="cmm.notice.expDate"></th>
                                                    <th data-i18n="cmm.notice.channel"></th>
                                                    <th data-i18n="cmm.notice.stats"></th>
                                                    <th data-i18n="cmm.notice.tmSmp"></th>
                                                </tr>
                                            </thead>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div th:replace="footer"></div>
            </div>
        </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var editor;
        var table;

        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                    break;
                case 'en':
                    languageUrl = '/datatables/plugins/i18n/English.lang';
                    break;
                case 'km':
                    languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                    break;
            }

            i18nLoad.then(function () {
                editor = new $.fn.dataTable.Editor({
                    ajax: {
                        create: {
                            type: 'POST',
                            url: '/cmm/noticctrl/add',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        },
                        edit: {
                            type: 'POST',
                            url: '/cmm/noticctrl/modify',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        },
                        remove: {
                            type: 'POST',
                            url: '/cmm/noticctrl/delete',
                            contentType: 'application/json',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        }
                    },
                    table: "#noticeInf",
                    idSrc: "id",
                    fields: [ {
                            label: "id", 
                            name: "id",
                            type: "hidden"
                        },{
                            label: $.i18n.t("cmm.notice.noticeTitleKh"), 
                            name: "noticeTitleKh"
                        },{
                            label: $.i18n.t("cmm.notice.noticeTitleCn"), 
                            name: "noticeTitleCn"
                        },{
                            label: $.i18n.t("cmm.notice.noticeTitleEn"), 
                            name: "noticeTitleEn"
                        },{
                            label: $.i18n.t("cmm.notice.noticeContentKh"), 
                            name: "noticeContentKh",
                            type: "textarea"
                        },{
                            label: $.i18n.t("cmm.notice.noticeContentCn"), 
                            name: "noticeContentCn",
                            type: "textarea"
                        },{
                            label: $.i18n.t("cmm.notice.noticeContentEn"), 
                            name: "noticeContentEn",
                            type: "textarea"
                        },{
                            label: $.i18n.t("cmm.notice.effDate"), 
                            name: "effDate",
                            type: "datetime",
                            def:   function () { return new Date();}
                        },{
                            label: $.i18n.t("cmm.notice.expDate"), 
                            name: "expDate",
                            type: "datetime",
                            def:   function () { return new Date();}
                        },{
                            label: $.i18n.t("cmm.notice.channel"), 
                            name: "channel",
                            type:  "select",
                            options: [
                                { label: $.i18n.t("cmm.channelEnum.portal"), value: "portal" },
                                { label: $.i18n.t("cmm.channelEnum.app"), value: "app" },
                                { label: $.i18n.t("cmm.channelEnum.mportal"), value: "mportal" },
                                { label: $.i18n.t("cmm.channelEnum.mapp"), value: "mapp" }
                            ]
                        },{
                            label: $.i18n.t("cmm.notice.stats"), 
                            name: "stats",
                            type:  "select",
                            options: [
                                { label: $.i18n.t("cmm.statsEnum.online"), value: "1" },
                                { label: $.i18n.t("cmm.statsEnum.offline"), value: "0" }
                            ]
                        }],
                        i18n: {
                            create: {
                                button: $.i18n.t("cmm.button.add"),
                                title: $.i18n.t("cmm.button.add"),
                                submit: $.i18n.t("cmm.button.add")
                            },
                            edit: {
                                button: $.i18n.t("cmm.button.modify"),
                                title: $.i18n.t("cmm.button.modify"),
                                submit: $.i18n.t("cmm.button.modify")
                            },
                            remove: {
                                button: $.i18n.t("cmm.button.delete"),
                                title: $.i18n.t("cmm.button.delete"),
                                submit: $.i18n.t("cmm.button.delete"),
                                confirm: {
                                    _: $.i18n.t("cmm.action.cofirm.delete"),
                                    1: $.i18n.t("cmm.action.cofirm.delete")
                                }
                            }
                        }
                    });
                    
                    editor.on( 'preSubmit', function ( e, o, action ) {
                        if ( action !== 'remove' ) {
                            var noticeTitleKh = this.field( 'noticeTitleKh' );
                            var noticeTitleCn = this.field( 'noticeTitleCn' );
                            var noticeTitleEn = this.field( 'noticeTitleEn' );
                            var noticeContentKh = this.field( 'noticeContentKh' );
                            var noticeContentCn = this.field( 'noticeContentCn' );
                            var noticeContentEn = this.field( 'noticeContentEn' );
                            var effDate = this.field( 'effDate' );
                            var expDate = this.field( 'expDate' );
                            // Only validate user input values - different values indicate that
                            // the end user has not entered a value
                            
                            if ( ! noticeTitleKh.isMultiValue() ) {
                                if ( ! noticeTitleKh.val() ) {
                                    noticeTitleKh.error( $.i18n.t("cmm.msg.notice.null.noticeTitleKh") );
                                }
                                 
                                if ( noticeTitleKh.val().length > 64 ) {
                                    noticeTitleKh.error( $.i18n.t("cmm.msg.notice.length.noticeTitleKh") );
                                }
                            }
                            
                            if ( ! noticeTitleCn.isMultiValue() ) {
                                if ( ! noticeTitleCn.val() ) {
                                    noticeTitleCn.error( $.i18n.t("cmm.msg.notice.null.noticeTitleCn") );
                                }
                                 
                                if ( noticeTitleCn.val().length > 64 ) {
                                    noticeTitleCn.error( $.i18n.t("cmm.msg.notice.length.noticeTitleCn") );
                                }
                            }
                            
                            if ( ! noticeTitleEn.isMultiValue() ) {
                                if ( ! noticeTitleEn.val() ) {
                                    noticeTitleEn.error( $.i18n.t("cmm.msg.notice.null.noticeTitleEn") );
                                }
                                 
                                if ( noticeTitleEn.val().length > 64 ) {
                                    noticeTitleEn.error( $.i18n.t("cmm.msg.notice.length.noticeTitleEn") );
                                }
                            }
                            
                            if ( ! noticeContentKh.isMultiValue() ) {
                                if ( ! noticeContentKh.val() ) {
                                    noticeContentKh.error( $.i18n.t("cmm.msg.notice.null.noticeContentKh") );
                                }
                                 
                                if ( noticeContentKh.val().length > 1024 ) {
                                    noticeContentKh.error( $.i18n.t("cmm.msg.notice.length.noticeContentKh") );
                                }
                            }
                            
                            if ( ! noticeContentCn.isMultiValue() ) {
                                if ( ! noticeContentCn.val() ) {
                                    noticeContentCn.error( $.i18n.t("cmm.msg.notice.null.noticeContentCn") );
                                }
                                 
                                if ( noticeContentCn.val().length > 1024 ) {
                                    noticeContentCn.error( $.i18n.t("cmm.msg.notice.length.noticeContentCn") );
                                }
                            }
                            
                            if ( ! noticeContentEn.isMultiValue() ) {
                                if ( ! noticeContentEn.val() ) {
                                    noticeContentEn.error( $.i18n.t("cmm.msg.notice.null.noticeContentEn") );
                                }
                                 
                                if ( noticeContentEn.val().length > 1024 ) {
                                    noticeContentEn.error( $.i18n.t("cmm.msg.notice.length.noticeContentEn") );
                                }
                            }
                            
                            if ( ! effDate.isMultiValue() ) {
                                if ( ! effDate.val() ) {
                                    effDate.error( $.i18n.t("cmm.msg.notice.null.effDate") );
                                }
                            }
                            
                            if ( ! expDate.isMultiValue() ) {
                                if ( ! expDate.val() ) {
                                    expDate.error( $.i18n.t("cmm.msg.notice.null.expDate") );
                                }
                            }
                            // ... additional validation rules
                 
                            // If any error was reported, cancel the submission so it can be corrected
                            if ( this.inError() ) {
                                return false;
                            }
                        }
                    } );
                    
                    editor.on( 'open', function ( e, o, action ) {
                        if ( action === 'edit' ) {
                            this.field( 'id' ).disable();
                        }
                    } );
    
                    table = $('#noticeInf').DataTable({
                        dom: "Blfrtip",
                        ajax: {
                            contentType: 'application/json',
                            url: '/cmm/noticctrl/findAll',
                            type: 'post',
                            data: function (d) {
                                d.extra_search = {
                                    "title" : $("#title").val(),
                                    "beginDate" : $("#beginDate").val(),
                                    "endDate" : $("#endDate").val(),
                                };
                                return JSON.stringify(d);
                            },
                            error:function(d){
                                alert(JSON.stringify(d));
                            },
                        },
                        serverSide: true,
                        searchDelay: 1000,
                        responsive: true,
                        processing: true,
                        language: {
                            url: languageUrl
                        },
                        select: true,
                        buttons: [
                            {extend: "create", editor: editor},
                            {extend: "edit", editor: editor},
                            {extend: "remove", editor: editor},
                        ],
                        columns: [{
                            data: 'noticeTitleCn',
                            render: $.fn.dataTable.render.ellipsis( 10 )
                        },{
                            data: 'effDate'
                        },{
                            data: 'expDate'
                        },{
                            data: 'channel',
                            render: function (data, type, row) {
                                   switch (data) {
                                       case "portal":
                                           return $.i18n.t("cmm.channelEnum.portal");
                                       case "app":
                                           return $.i18n.t("cmm.channelEnum.app");
                                       case "mportal":
                                           return $.i18n.t("cmm.channelEnum.mportal");
                                       case "mapp":
                                           return $.i18n.t("cmm.channelEnum.mapp");
                                       default:
                                           return data;
                                   }
                             }
                        },{
                            data: 'stats',
                            render: function (data, type, row) {
                                  switch (data) {
                                      case "0":
                                          return $.i18n.t("cmm.statsEnum.offline");
                                      case "1":
                                          return $.i18n.t("cmm.statsEnum.online");
                                      default:
                                          return data;
                                }
                            }
                        },{
                            data: 'tmSmp'
                        }]
                    });
                });
            });
            
            <!--初始化日期控件-->
            $('#beginDate').datetimepicker({
                lang:'en',
                timepicker:false,
                validateOnBlur: false,
                format:'Y-m-d',
                formatDate:'Y-m-d'
            });
            
            $('#endDate').datetimepicker({
                lang:'en',
                timepicker:false,
                validateOnBlur: false,
                format:'Y-m-d ',
                formatDate:'Y-m-d',
            });
            
            <!--查询按钮-->
            function search() {
                table.ajax.reload();
            }
            
            <!-- ellipsis插件 -->
            $.fn.dataTable.render.ellipsis = function ( cutoff, wordbreak, escapeHtml ) {
                var esc = function ( t ) {
                    return t
                        .replace( /&/g, '&amp;' )
                        .replace( /</g, '&lt;' )
                        .replace( />/g, '&gt;' )
                        .replace( /"/g, '&quot;' );
                };
             
                return function ( d, type, row ) {
                    // Order, search and type get the original data
                    if ( type !== 'display' ) {
                        return d;
                    }
             
                    if ( typeof d !== 'number' && typeof d !== 'string' ) {
                        return d;
                    }
             
                    d = d.toString(); // cast numbers
             
                    if ( d.length < cutoff ) {
                        return d;
                    }
             
                    var shortened = d.substr(0, cutoff-1);
             
                    // Find the last white space character in the string
                    if ( wordbreak ) {
                        shortened = shortened.replace(/\s([^\s]*)$/, '');
                    }
             
                    // Protect against uncontrolled HTML input
                    if ( escapeHtml ) {
                        shortened = esc( shortened );
                    }
             
                    return '<span class="ellipsis" title="'+esc(d)+'">'+shortened+'&#8230;</span>';
                };
            };
        </script>
    </body>
</html>