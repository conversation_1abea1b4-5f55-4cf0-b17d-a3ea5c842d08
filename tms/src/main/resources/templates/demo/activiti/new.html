<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>

    <title data-i18n="activiti.title"></title>

    <div th:replace="head"></div>
</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="activiti.head"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="activiti.firstLevel"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="activiti.secondLevel"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content animated fadeInRight">

            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form method="post" class="form-horizontal" id="form" th:action="@{${action}}">
                                <div class="hr-line-dashed"></div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">选择审核人</label>
                                    <div class="col-sm-4">
                                        <select class="form-control m-b" name="reviewer_id" required>
                                            <option value="">请选择</option>
                                            <option th:each="user:${users}" th:value="${user.id}" th:text="${user.name}">
                                        </select>
                                    </div>
                                </div>
                                <div class="hr-line-dashed"></div>
                                <div class="form-group">
                                    <div class="col-sm-4 col-sm-offset-2">
                                        <button class="btn btn-w-m btn-white" type="reset">重置</button>
                                        <button class="btn btn-w-m btn-primary" type="submit">提交</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <div th:replace="footer"></div>

    </div>
</div>
<div th:replace="script"></div>
</body>

</html>