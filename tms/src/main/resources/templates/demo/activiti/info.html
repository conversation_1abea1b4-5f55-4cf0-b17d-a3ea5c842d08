<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">

<head>

    <title data-i18n="activiti.title"></title>

    <div th:replace="head"></div>
</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="activiti.head"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="activiti.firstLevel"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="activiti.secondLevel"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content animated fadeInRight">

            <form class="form-horizontal" role="form" id="form" th:object="${approval}">
                <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">当前执行:</label>
                                <div class="col-sm-4">
                                    <p class="form-control-static" th:text="${approval.assign}"></p>
                                </div>

                                <label class="col-sm-2 control-label">状态:</label>
                                <div class="col-sm-4">
                                    <p class="form-control-static"><span class="label label-info" th:text="${approval.approval_status}"></span></p>
                                </div>
                                <label class="col-sm-2 control-label">编号:</label>
                                <div class="col-sm-4">
                                    <input type="hidden" id="id" name="approvalId" th:value="${approval.id}">
                                    <input type="hidden" id="processInstanceId" name="processInstanceId" th:value="${approval.processInstanceId}">
                                    <p class="form-control-static" th:text="${approval.approvalNo}"></p>
                                </div>
                            </div>
                            <div class="hr-line-dashed"></div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">审核人:</label>
                                <div class="col-sm-4">
                                    <p class="form-control-static" th:text="${approval.reviewer}"></p>
                                </div>
                                <label class="col-sm-2 control-label">审核时间:</label>
                                <div class="col-sm-4">
                                    <p class="form-control-static" th:text="${approval.reviewTime}"></p>
                                </div>
                                <label class="col-sm-2 control-label">创建者:</label>
                                <div class="col-sm-4">
                                    <p class="form-control-static" th:text="${approval.createdBy}"></p>
                                </div>
                                <label class="col-sm-2 control-label">创建时间:</label>
                                <div class="col-sm-4">
                                    <p class="form-control-static" th:text="${approval.createTime}"></p>
                                </div>
                                <label class="col-sm-2 control-label">修改者:</label>
                                <div class="col-sm-4">
                                    <p class="form-control-static" th:text="${approval.lastModifier}"></p>
                                </div>
                                <label class="col-sm-2 control-label">修改时间:</label>
                                <div class="col-sm-4">
                                    <p class="form-control-static" th:text="${approval.lastModifyTime}"></p>
                                </div>
                            </div>
                            <div class="hr-line-dashed"></div>
                            <div sec:authorize="hasPermission('','/demo/activiti:adult')"
                                 th:if="${(#strings.toString(approval.approval_status) == '等待审核')
                                and (#authentication.principal.username == approval.reviewer)}">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">驳回原因</label>
                                    <div class="col-sm-10">
                                        <textarea class="form-control" rows="5" name="reason" id="reason" maxlength="1024" required></textarea>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-6 col-sm-offset-2">
                                        <input style="display:none;" type="checkbox" id="reviewResult" name="reviewResult" checked>
                                        <input class="btn btn-w-m btn-default" type="button" id="reject" value="驳回">
                                        <input class="btn btn-w-m btn-primary" type="button" id="audit" value="审核通过">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </form>

        </div>

        <div th:replace="footer"></div>

    </div>
</div>
<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script>
    $(document).ready(function(){
        function validform() {
            return $("#form").validate({ignore:"hidden"});
        }

        $("#reject").click(function(){
            $("#reviewResult").prop("checked", false);
            validform();
            if(validform().form()) {
                $.ajax({
                    type : "post",
                    url : "/demo/activiti/reviewApproval",
                    data : $("#form").serialize(),
                    dataType : "text",
                    success: function(text) {
                        console.log(text);
                        swal({
                            title: "成功!",
                            text: "已成功驳回!",
                            type: "success",
                            closeOnConfirm: true
                        }, function() {
                            window.location.href = "/demo/activiti";
                        });
                    }
                });
            }
        });

        $("#audit").click(function(){
            $("#reviewResult").prop("checked", true);
            $.ajax({
                type : "post",
                url : "/demo/activiti/reviewApproval",
                data : $("#form").serialize(),
                dataType : "text",
                success: function(text) {
                    console.log(text);
                    swal({
                        title: "成功!",
                        text: "审核成功!",
                        type: "success",
                        closeOnConfirm: true
                    }, function() {
                        window.location.href = "/demo/activiti";
                    });
                }
            });
        });

//        $.ajax({
//            type : 'get',
//            url : '/demo/activiti/users',
//            dataType : 'json',
//            success : function (users) {
//                console.log(users);
//                $.ajax({
//                    type : 'get',
//                    url : getRealPath() + '/history/historic-activity-instances?order=desc&size=9999&processInstanceId=' + $("#processInstanceId").val(),
//                    dataType : 'json',
//                    success : function (json) {
//                        console.log(json);
//                        var array = json.data;
//                        var count = 0;
//                        for (var k = 0, length = array.length; k < length; k++) {
//                            if (chReg.test(array[k].activityName) && !hiddens.contains(array[k].activityName)) {
//                                var activitiInfo = getActivitiInfo(array[k].activityId, array[k].activityName, array[k].endTime, array[k].id, array[k].endTime);
//                                if (activitiInfo) {
//                                    if (count < 5) {
//                                        if (array[k].endTime) {
//                                            $("#last-activities").append("<li class='list-group-item" +
//                                                (count == 0 ? " fist-item'>" : "'>") +
//                                                "<span class='pull-right'> " + array[k].endTime + " </span>" +
//                                                "<a href=\"javascript:showHistory(" + array[k].id + ", '" + activitiInfo.name + "', " + activitiInfo.feedBackId + ", " + activitiInfo.replyId + ")\">" + activitiInfo.name + "</a> 完成于" +
//                                                "</li>");
//                                        } else {
//                                            $("#last-activities").append("<li class='list-group-item" +
//                                                (count == 0 ? " fist-item'>" : "'>") +
//                                                "<span class='pull-right'> " + array[k].startTime + " </span>" +
//                                                activitiInfo.name + " 开始于" +
//                                                "</li>");
//                                        }
//                                    }
//                                    count ++;
//
//                                    var username = 'CRM';
//                                    if (array[k].assignee != null) {
//                                        for (var i = 0; i < users.length; i++) {
//                                            if (users[i].id == array[k].assignee) {
//                                                if (chReg.test(users[i].first))
//                                                    username = users[i].last + users[i].first;
//                                                else
//                                                    username = users[i].first + ' ' + users[i].last;
//                                                break;
//                                            }
//                                        }
//                                    }
//
//                                    if (array[k].endTime) {
//                                        $("#vertical-timeline").append("<div class=\"vertical-timeline-block\">" +
//                                            "<div class=\"vertical-timeline-icon " + activitiInfo.color + "\">" +
//                                            "<i class=\"fa " + activitiInfo.fa + "\"></i>" +
//                                            "</div>" +
//                                            "<div class=\"vertical-timeline-content\">" +
//                                            "<p>" + username + " <a href=\"javascript:showHistory(" + array[k].id + ", '" + activitiInfo.name + "', " + activitiInfo.feedBackId + ", " + activitiInfo.replyId + ")\">" + activitiInfo.opr + "</a>. " +
//                                            "<br>此过程共耗时 " + (array[k].durationInMillis / 1000 / 60 / 60).toFixed(2) + " 小时</p>" +
//                                            "<span class=\"vertical-date small text-muted\"><br>开始于<br>" + array[k].startTime + "<br>完成于<br>" + array[k].endTime + "</span>" +
//                                            "</div>" +
//                                            "</div>");
//                                    } else {
//                                        $("#vertical-timeline").append("<div class=\"vertical-timeline-block\">" +
//                                            "<div class=\"vertical-timeline-icon " + activitiInfo.color + "\">" +
//                                            "<i class=\"fa " + activitiInfo.fa + "\"></i>" +
//                                            "</div>" +
//                                            "<div class=\"vertical-timeline-content\">" +
//                                            "<p>当前 " + activitiInfo.opr + ".</p>" +
//                                            "<span class=\"vertical-date small text-muted\"><br>开始于<br>" + array[k].startTime + "</span>" +
//                                            "</div>" +
//                                            "</div>");
//                                    }
//                                }
//                            }
//                        }
//                    }
//                });
//            }
//        });
    });
</script>
</body>

</html>