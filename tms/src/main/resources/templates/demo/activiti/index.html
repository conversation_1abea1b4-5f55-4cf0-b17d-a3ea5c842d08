<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>

    <title data-i18n="activiti.title"></title>

    <div th:replace="head"></div>
</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="activiti.head"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="activiti.firstLevel"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="activiti.secondLevel"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content animated fadeInRight">

            <div class="ibox-content m-b-sm border-bottom">
                <form id="queryForm" class="form-horizontal">
                    <div class="form-group col-sm-4">
                        <label class="col-sm-2 control-label" for="approval_status"
                               data-i18n="activiti.status"></label>
                        <div class="col-sm-10">
                            <select name="approval_status" id="approval_status" class="form-control">
                                <option value="等待审核">等待审核</option>
                                <option value="审核未通过">审核未通过</option>
                                <option value="审核通过">审核通过</option>
                            </select>
                        </div>
                    </div>
                    <div class="text-center">
                        <button type="button" id="refreshBtn" class="btn btn-w-m btn-primary">查询</button>
                    </div>
                </form>
            </div>

            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">

                            <div class="table-responsive">
                                <table class="table table-striped table-bordered table-hover" style="display:none;"
                                       id="dataTable"></table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal inmodal fade" id="authorityModal" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content"></div>
            </div>
        </div>
        <div th:replace="footer"></div>

    </div>
</div>
<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script>
    $(document).ready(function () {

        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        $("#authorityModal").on("hidden.bs.modal", function (e) {
            $(this).removeData();
        });

        var oTable = null;
        var search = function () {
            if (oTable == null) {
                $("#dataTable").show();
                oTable = $("#dataTable").DataTable({
                    dom: '<"html5buttons"B><"top"i<"clear">>rt<"bottom"lp<"clear">>',
                    ajax: {
                        contentType: 'application/json',
                        url: '/demo/activiti/findAll',
                        type: 'POST',
                        data: function (d) {
                            return JSON.stringify(d);
                        }
                    },
                    serverSide: true,
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
                    language: {
                        url: languageUrl
                    },
                    select: true,
                    buttons: [
                        {
                            text: '新建', action: function (e, dt, node, config) {
                            add();
                        }
                        }
                    ],
                    columns: [
                        {data: 'approvalNo', title: '审批编号'},
                        {data: 'approval_status', title: '审批状态'},
                        {data: 'createTime', title: '创建时间'},
                        {
                            data: 'id',
                            title: '操作',
                            className: 'text-center',
                            render: function (data, type, full, meta) {
                                switch (full.approval_status) {
                                    case '等待审核' :
                                        return '<a href="javascript:info(' + data + ')">审核</a>';
                                    default :
                                        return '<a href="javascript:info(' + data + ')">查看</a>';
                                }
                            }
                        }
                    ]
                });
            }
            oTable.draw();
        };

        $("#refreshBtn").click(function () {
            search();
        });

        search();
    });

    function add() {
        window.location.href = '/demo/activiti/new';
    }

    function info(id) {
        window.location.href = "/demo/activiti/" + id;
    }

</script>
</body>

</html>