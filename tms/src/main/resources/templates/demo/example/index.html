<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>

    <title data-i18n="demo.title"></title>

    <div th:replace="head"></div>
</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="demo.head"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="demo.firstLevel"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="demo.secondLevel"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">

                            <div class="table-responsive">
                                <table id="example" class="table table-striped table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th data-i18n="demo.username"></th>
                                        <th data-i18n="demo.position"></th>
                                        <th data-i18n="demo.office"></th>
                                        <th data-i18n="demo.startDate"></th>
                                        <th data-i18n="demo.salary"></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>

    </div>
</div>
<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script>
    var editor;
    //A 2D array in which the first array is used to define the value options and the second array the displayed options (useful for language strings such as 'All').
    //    var array1 = [10, 25, 50, -1];
    //    var array2 = [10, 25, 50, "All"];
    var order = [1, 'asc'];

    $(document).ready(function () {

        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        i18nLoad.then(function () {
            editor = new $.fn.dataTable.Editor({
                ajax: {
                    create: {
                        type: 'POST',
                        url: '/demo/example/add',
                        contentType: 'application/json',
                        data: function (d) {
                            return JSON.stringify(d);
                        }
                    },
                    edit: {
                        type: 'POST',
                        url: '/demo/example/modify',
                        contentType: 'application/json',
                        data: function (d) {
                            return JSON.stringify(d);
                        }
                    },
                    remove: {
                        type: 'POST',
                        url: '/demo/example/delete',
                        contentType: 'application/json',
                        data: function (d) {
                            return JSON.stringify(d);
                        }
                    }
                },
                table: "#example",
                idSrc: 'id',
                fields: [
                    {name: "id", type: "hidden"},
                    {label: $.i18n.t("demo.username"), name: "name"},
                    {label: $.i18n.t("demo.position"), name: "position"},
                    {label: $.i18n.t("demo.office"), name: "office"},
                    {
                        label: $.i18n.t("demo.startDate"),
                        name: "startDate",
                        type: "datetime",
                        def: function () {
                            return new Date();
                        },
                        format: "YYYY-MM-DD HH:mm:ss"
                    },
                    {label: $.i18n.t("demo.salary"), name: "salary"}
                ],
                i18n: {
                    create: {button: $.i18n.t("demo.add"), title: $.i18n.t("demo.add"), submit: $.i18n.t("demo.create")},
                    edit: {button: $.i18n.t("demo.modify"), title: $.i18n.t("demo.modify"), submit: $.i18n.t("demo.update")},
                    remove: {
                        button: $.i18n.t("demo.delete"), title: $.i18n.t("demo.delete"), submit: $.i18n.t("demo.delete"),
                        confirm: {
                            _: $.i18n.t("demo.multi-delete"),
                            1: $.i18n.t("demo.single-delete")
                        }
                    }
                }
            });

            editor.on('preSubmit', function (e, o, action) {
                var id = editor.field('id');
                o.id = id.val();  // create a new parameter to pass over to the server called entityId
            });

            var table = $('#example').DataTable({
                dom: 'Blfrtip',
//                dom: '<"html5buttons"B>lTfgitp',
                ajax: {
                    contentType: 'application/json',
                    url: '/demo/example/findAll',
                    type: 'POST',
                    data: function (d) {
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
//            "lengthMenu": [array1, array2],
                language: {
//                "decimal": ",",
//                "thousands": ".",
//                    url: '/datatables/plugins/i18n/Chinese.lang'
                    url: languageUrl
                    //url: '//cdn.datatables.net/plug-ins/1.10.15/i18n/Chinese.json'
                },
                select: true,
                buttons: [
                    {extend: "create", editor: editor},
                    {extend: "edit", editor: editor},
                    {extend: "remove", editor: editor},
//                {extend: 'copyHtml5'},
//                {extend: 'csvHtml5'},
//                {extend: 'excelHtml5', title: '文件名'},
                    'copy',
                    'csv',
                    'excel'
//                'pdf',
//                'print'
                ],
                columns: [{
                    data: 'name'
                }, {
                    data: 'position'
                }, {
                    data: 'office'
                }, {
                    data: 'startDate'
//                orderable: false
                }, {
                    data: 'salary',
                    render: $.fn.dataTable.render.number(',', '.', 2, '$')
                }
                ]
            });
        });
    });
</script>
</body>

</html>