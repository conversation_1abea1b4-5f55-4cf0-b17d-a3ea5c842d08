<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>

    <title data-i18n="csh.paytype.title"></title>

    <div th:replace="head"></div>
</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="csh.paytype.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.busmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.bussub.trdmgr"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="csh.paytype.content"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">

                            <div class="table-responsive">
                                <table id="paytypeList" class="table table-striped table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th data-i18n="csh.paytype.id"></th>
                                        <th data-i18n="csh.paytype.appCnl"></th>
                                        <th data-i18n="csh.paytype.busType"></th>
                                        <th data-i18n="csh.paytype.mercId"></th>
                                        <th data-i18n="csh.paytype.payTypes"></th>
                                        <th data-i18n="csh.paytype.status"></th>
                                        <th data-i18n="csh.paytype.modifyOpr"></th>
                                        <th data-i18n="csh.paytype.lastmodifiedTime"></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>

                        </div>
                    </div>

                    <!-- 支付方式查看蒙板 -->
                    <div class="modal inmodal" id="ptDetailModel" tabindex="-1" role="dialog" aria-hidden="true"
                         style="display: none;" data-backdrop="static">
                        <div class="modal-dialog">
                            <div class="modal-content animated flipInY">
                                <div class="modal-body">
                                    <div class="ibox float-e-margins">

                                        <div class="ibox-title">
                                            <h5 data-i18n="csh.pttitle"></h5>
                                        </div>

                                        <div class="ibox-content">
                                            <div id="ptDetail"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-white" data-dismiss="modal"
                                            data-i18n="csh.button.close"></button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!--支付方式查看蒙板-->



                </div>
            </div>
        </div>
        <div th:replace="footer"></div>

    </div>
</div>
<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script>
    var editor;

    var addEditor;
    var payTypeArr=[];
       /* =[
        {label:'线下支付-线下汇款',value:0},
        {label:'线下支付-现金',value:1},
        {label:'现金',value:2},
        {label:'海币',value:3},
        {label:'借记卡快捷',value:4},
        {label:'贷记卡快捷',value:5},
        {label:'借记卡网银',value:6},
        {label:'贷记卡网银',value:7},
        {label:'支付宝-手机网页',value:8},
        {label:'支付宝-APP支付',value:9},
        {label:'支付宝-扫码支付',value:10},
        {label:'支付宝-条码支付',value:11},
        {label:'支付宝-即时到账',value:12},
        {label:'微信-公众号支付',value:13},
        {label:'微信-扫码支付',value:14},
        {label:'微信-条码支付',value:15},
        {label:'微信支付-APP支付',value:16},
    ];*/


    var bustypeKV={};
        /*'0101':'个人快捷充值',
        '0102':'个人线下充值',
        '0103':'个人营业厅充值',
        '0104':'企业网银充值',

        '0201':'条码消费',
        '0202':'扫码付消费',
        '0203':'APP消费',
        '0204':'POS消费',
        '0205':'银行卡收单',

        '0301':'转到账户',
        '0302':'转银行卡',
        '0303':'当面收款',

        '0501':'充海币',
        '0701':'理财买入',

        '0801':'充流量',
        '0802':'充话费'
    };*/

    var busTypeSelect={};
        /*'个人快捷充值':'0101',
        '个人线下充值':'0102',
        '个人营业厅充值':'0103',
        '企业网银充值':'0104',

        '条码消费':'0201',
        '扫码付消费':'0202',
        'APP消费':'0203',
        'POS消费':'0204',
        '银行卡收单':'0205',

        '转到账户':'0301',
        '转银行卡':'0302',
        '当面收款':'0303',

        '充海币':'0501',
        '理财买入':'0701',

        '充流量':'0801',
        '充话费':'0802'
    }*/

    var appCnlKV={
        PWM:'充提',
        TAM:'转账',
        ONR:'收单',
        INV:'理财',
        CPM:'缴费'
    }

    var appCnlSelect={
        '充提':'PWM',
        '转账':'TAM',
        '收单':'ONR',
        '理财':'INV',
        '缴费':'CPM'
    }

    var statusSelect={
        '生效':'1',
        '失效':'0'
    }
    var table;
    var order = [8, 'desc'];
    $(document).ready(function () {

        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        $.ajax({
            url : '/csh/paytype/init',
            data: {"type" : "PAY_TYPE"},
            dataType : "json",
            type : "get",
            async: false,
            success:function(data){
                //payTypeArr=new Array(17);
                for(var i=0;i<data.length;i++) {
                    //console.info(data[i].parmDispNm+"--"+data[i].parmVal);
                    var json ={};
                    json.label = data[i].parmDispNm;
                    json.value = data[i].parmVal;
                    payTypeArr.push(json);
                }
            },
            error: function(){
                swal($.i18n.t("csh.payType-init-fail") , "error");
            }
        });

        $.ajax({
            url : '/csh/paytype/init',
            data: {"type" : "BUS_TYPE"},
            dataType : "json",
            type : "get",
            async: false,
            success:function(data){
                //console.info(data);
                for(var i=0;i<data.length;i++) {
                    //console.info(data[i].parmDispNm+"--"+data[i].parmVal);
                    bustypeKV[data[i].parmVal] = data[i].parmDispNm;
                    busTypeSelect[data[i].parmDispNm] = data[i].parmVal;
                }
            },
            error: function(){
                swal($.i18n.t("csh.busType-init-fail") , "error");
            }
        });

        i18nLoad.then(function () {
            editor = new $.fn.dataTable.Editor({
                ajax: {
                    create: {
                        type: 'POST',
                        url: '/csh/paytype/add',
                        contentType: 'application/json',
                        data: function (d) {
                            return JSON.stringify(d);
                        },
                        success:function(o){
                            handleErr(o);
                        },
                        error:function(e){

                        }
                    },
                      edit: {
                          type: 'POST',
                          url: '/csh/paytype/modify',
                          contentType: 'application/json',
                          data: function (d) {
                              return JSON.stringify(d);
                          },
                          success:function(o){
                              handleErr(o);
                          },
                          error:function(e){

                          }
                      },
                      remove: {
                          type: 'POST',
                          url: '/csh/paytype/status',
                          contentType: 'application/json',
                          data: function (d) {
                              var selectRowData=table.rows('.selected').data()[0];
                              var sts=d.data[selectRowData.id]["status"];
                              var data={};
                              data.id=selectRowData.id;
                              if(sts=='0'){
                                  data.status='1';
                              }else{
                                  data.status='0';
                              }
                              return JSON.stringify(data);
                          }
                      }
                },
                table: "#paytypeList",
                idSrc: 'id',
                fields: [
                        {name: "id", type: "hidden"},
                        {label: $.i18n.t("csh.paytype.appCnl"), name: "appCnl",type:"select", options:appCnlSelect},
                        {label: $.i18n.t("csh.paytype.busType"),name: "busType",type:"select", options:busTypeSelect},
                        {label: $.i18n.t("csh.paytype.mercId"), name: "mercId"},
                        {label: $.i18n.t("csh.paytype.payTypes"), name: "checkbox_pt",type:"checkbox",options: payTypeArr},
                        {label: $.i18n.t("csh.paytype.status"), name: "status" ,type:"select", options:statusSelect}
                ],
                i18n: {
                    create: {button: $.i18n.t("csh.button.add"), title: $.i18n.t("csh.button.add"), submit: $.i18n.t("csh.operator.create")},
                    edit: {button: $.i18n.t("csh.button.modify"), title: $.i18n.t("csh.button.modify"), submit: $.i18n.t("csh.operator.update")},
                    remove: {
                         button: $.i18n.t("csh.button.del"), title: $.i18n.t("csh.button.del"), submit: $.i18n.t("csh.operator.delete"),
                         confirm: {
                             _: $.i18n.t("csh.operator.multi-delete"),
                             1: $.i18n.t("csh.operator.single-delete")
                         }
                    }
                }
            });

            editor.on('preSubmit', function (e, o, action) {
                var selectRowData=table.rows('.selected').data()[0];
                if(action=='edit' || action=='create'||action=='remove'){
                    if(action=='remove'){
                        o.id=selectRowData.id;
                    }else{
                        var id = editor.field('id');
                        o.id = id.val();
                    }

                    var index;
                    if(action=='create'){
                        index=0;
                    }else if(action=='remove'){
                        index=selectRowData.id;
                    }else{
                        index=id.val();
                    }
                    //将支付方式转换为01串
                    var checkbox_pt=o.data[index]['checkbox_pt'];
                    var busTypes=buildPaytype(checkbox_pt);
                    o.data[index]['payTypes']=busTypes;

                    //换回商户号
                    var merDesc=o.data[index]['mercId'];
                    if(merDesc==null ||merDesc==''){
                        alert('请填写商户号');
                        return false;
                    }
                    if(merDesc=='默认'){
                        o.data[index]['mercId']='*';
                    }

                    //网关支付方式
                    o.data[index]['gwPayTypes']='00000000';

                }

            });

            editor.on('preOpen', function (e, o, action) {

                if(table.rows('.selected').data().length<1){
                    if(action!='create'){
                        return false;
                    }
                }
                editor.field('status').disable();
                if(action=='edit'){
                    editor.field('appCnl').disable();
                    editor.field('busType').disable();
                    editor.field('mercId').disable();
                }else if(action=='create'){
                    editor.field('appCnl').enable();
                    editor.field('busType').enable();
                    editor.field('mercId').enable();
                }

            });

            editor.on('open', function (e, o, action) {
            	var d_url=document.location.href;
                var d_url1=d_url.split("id=")[1];
                if (d_url1 != null) {
                    var userId=d_url1;
                    editor.field('mercId').val(userId);
                }
            });
            editor.on('postEdit', function (e, o, action) {

            });
            var d_url=document.location.href;
            var d_url1=d_url.split("id=")[1];
            if (d_url1 != null) {
                var userId=d_url1;
            }
            table = $('#paytypeList').DataTable({
                dom: 'Blfrtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/csh/paytype/findAll',
                    type: 'POST',
                    data: function (d) {
                        d.extra_search = {
                            "userId":userId
                        };
                        return JSON.stringify(d);
                    },
                    dataSrc: function(o){
                        var arr= o.data;
                        for(var i=0;i<arr.length;i++){
                            arr[i].checkbox_pt=payTypeToCheckBox(arr[i].payTypes);
                        }
                        return arr;
                    }
                },
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
//            "lengthMenu": [array1, array2],
                language: {
                    url: languageUrl
                },
                select: true,
                buttons: [
                    {extend: "create", editor: editor},
                    {extend: "edit", editor: editor},
                    {extend: "remove", editor: editor}
                ],
                columns: [{
                    data: 'id'
                },{
                    data: 'appCnl',
                    render: function (data, type, row) {
                        return getApp(data);
                    }
                },{
                    data: 'busType',
                    render: function (data, type, row) {
                        return getBusName(data);
                    }
                },{
                    data: 'mercId',
                    render: function (data, type, row) {
                        return getMerchant(data);
                    }
                },{
                    data: 'payTypes',
                    render: function (data, type, row) {
                        var payTypeDesc="";
                        var nums=0;
                        var payTypeAll='';
                        for(var i=0;i<data.length,i<payTypeArr.length;i++){
                            if(data.charAt(i)=='1'){
                                if(nums<4){
                                    payTypeDesc+=payTypeArr[i].label+", ";
                                }
                                payTypeAll+=payTypeArr[i].label+"，";
                                nums++;
                            }
                        }
                        var lastIndex=(payTypeDesc.length-2)>0?payTypeDesc.length-2:0;
                        if(nums<4){
                            return payTypeDesc.substr(0,lastIndex);
                        }
                        return payTypeDesc.substr(0,lastIndex)
                                +'&nbsp;&nbsp;<th style="text-align: right"><a href="#ptDetailModel" style="cursor: pointer" class="fa fa-list-ul" title="' + $.i18n.t("csh.operator.lookDetail") + '" data-toggle="modal" data-role="' + payTypeAll + '"></a></th>';
                    }

                },{
                    data: 'status',
                    render: function (data, type, row) {
                        if(data=='1'){
                            return '生效';
                        }
                        if(data=='0'){
                            return '失效';
                        }
                    }
                },{
                    data: 'modifyOpr'
                },{
                    data: 'lastmodifiedTime',
                    render: function (data, type, row) {
                        if(null==data){
                            return "";
                        }
                        return data.replace('T',' ');
                    }
                }
                ]
            });


            $('#paytypeList tbody').on( 'click', 'tr', function () {
//                var buttons=table.buttons();
                var selectCount=table.$('tr.selected').length;
                if(selectCount>0){
                    table.row('tr.selected').deselect();
                }
                table.rows(this).select();

                //没有找到好办法恢复按钮的使用，datatable的方法好像不起作用，暂时这么干了。
                $('.btn.btn-default.disabled').removeClass('disabled');

                if(table.row(this).data().status==1){
                    $(table.buttons()[2].node).text('禁用');
                }else{
                    $(table.buttons()[2].node).text('启用');
                }
            } );
        });
    });

    function payTypeToCheckBox(payTypes){
        var curPaytypeCheckBox=[];
        for(var i=0;i<payTypes.length;i++){
            if(payTypes[i]==1){
                curPaytypeCheckBox.push(i);
            }
        }
        return curPaytypeCheckBox;
    }


    function getApp(appCnl){
        var app=appCnlKV[appCnl];
        if(null==app||app===''){
            return appCnl;
        }
        return app;
    }

    function getBusName(busType){
        var name=bustypeKV[busType];
        if(name==null || name==''){
            return busType;
        }
        return name;
    }

    function buildPaytype(recPaytypeArr){
        var paytype="";
        for(var i=0;i<payTypeArr.length;i++){
            var match=false;
            for(var j=0;j<recPaytypeArr.length;j++){
                if(recPaytypeArr[j]==payTypeArr[i].value){
                    paytype+='1';
                    match=true;
                    break;
                }
            }

            if(!match){
                paytype+='0';
            }
        }
        return paytype;
    }

    function getMerchant(id){
        if(id=='*'){
            return '默认';
        }
        return id;
    }

    function handleErr(o){
        if(o.code && o.message){
            alert(o.message);
        }
    }

    $('#ptDetailModel').on('show.bs.modal',
            function (event) {
                var a = $(event.relatedTarget);
                var recipient = a.data('role');
                var lastIndex=(recipient.length-1)>0?recipient.length-1:0;
                $("#ptDetail").html(recipient.substr(0,lastIndex)) ;
            });

    $('#ptDetailModel').on('hide.bs.modal',
            function () {
               ;
            });
</script>
</body>

</html>