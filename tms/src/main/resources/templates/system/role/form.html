<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>

    <title data-i18n="role.title"></title>

    <div th:replace="head"></div>

</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="role.head"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="role.sysmanage"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="role.usermanage"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form method="post" class="form-horizontal" th:action="@{${action}}" id="form">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label" data-i18n="role.role"></label>
                                    <div class="col-sm-5">
                                        <input type="text" class="form-control" name="role" data-i18n="[placeholder]role.placeholder.role" required>
                                    </div>
                                </div>
                                <div class="hr-line-dashed"></div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label" data-i18n="role.rolename"></label>
                                    <div class="col-sm-5">
                                        <input type="text" class="form-control" name="roleName" data-i18n="[placeholder]role.placeholder.rolename" required>
                                    </div>
                                </div>
                                <div class="hr-line-dashed"></div>
                                <div class="form-group">
                                    <div class="col-sm-4 col-sm-offset-3">
                                        <button class="btn btn-primary" type="submit" data-i18n="role.save"></button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>

    </div>
</div>


<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script>
    $(document).ready(function() {
        switch ($.cookie('lang')) {
            case 'zh':
                $.extend($.validator.messages, window.validate_zh);
                break;
            case 'en':
                $.extend($.validator.messages, window.validate_en);
                break;
            case 'km':
                $.extend($.validator.messages, window.validate_km);
                break;
        }

        $('#form').validate();
    });

</script>
</body>

</html>
