<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>

    <title data-i18n="user.title"></title>

    <div th:replace="head"></div>

</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="user.head"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="user.sysmanage"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="user.usermanage"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <p data-i18n="user.assign"></p>
                            <form id="form" th:action="@{${api}}" method="post" class="wizard-big">
                                <select class="form-control dual_select" multiple name="rid">
                                    <option th:each="item:${list}" th:value="${item.rid}"
                                            th:selected="${item.selected}"
                                            th:text="${item.name}"></option>
                                </select>
                                <div class="form-group">
                                    <div class="col-sm-4 col-sm-offset-4">
                                        <button class="btn btn-primary btn-block" type="submit"><i
                                                class="fa fa-check"></i>&nbsp;<span data-i18n="user.save"></span>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>

    </div>
</div>


<div th:replace="script"></div>
<script>
    $(function () {
        i18nLoad.then(function () {
            $('.dual_select').bootstrapDualListbox({
                nonSelectedListLabel: $.i18n.t("user.nonSelectedListLabel"),
                selectedListLabel: $.i18n.t("user.selectedListLabel"),
                selectorMinimalHeight: 160
            });
        });
    });
</script>
</body>

</html>
