<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>

    <title data-i18n="user.title"></title>

    <div th:replace="head"></div>

</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="user.head"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="user.sysmanage"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="user.usermanage"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form method="post" class="form-horizontal" th:action="@{${api}}" id="form">
                                <div class="form-group" th:if="${user==null}">
                                    <label class="col-sm-3 control-label" data-i18n="user.username"></label>
                                    <div class="col-sm-5">
                                        <input type="text" class="form-control" name="name" th:value="${user}? ${user.name}"
                                               data-i18n="[placeholder]user.placeholder.username" required="required" maxlength="10" id = 'name'>
                                    </div>
                                </div>
                                <div class="hr-line-dashed"></div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label" data-i18n="user.password"></label>
                                    <div class="col-sm-5">
                                        <input type="password" class="form-control" name="password" id="password"
                                               data-i18n="[placeholder]user.placeholder.password" required="required" maxlength="20">
                                    </div>
                                </div>
                                <div class="hr-line-dashed"></div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label" data-i18n="user.password"></label>
                                    <div class="col-sm-5">
                                        <input type="password" class="form-control" name="password_again" id = "password_again"
                                               data-i18n="[placeholder]user.placeholder.again" required maxlength="20">
                                    </div>
                                </div>
                                <div class="hr-line-dashed"></div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label" data-i18n="user.email"></label>
                                    <div class="col-sm-5">
                                        <input type="email" class="form-control" name="email"
                                               th:value="${user}? ${user.email}" required data-i18n="[placeholder]user.placeholder.email">
                                    </div>
                                </div>
                                <div class="hr-line-dashed"></div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label" data-i18n="user.mblNo"></label>
                                    <div class="col-sm-5">
                                        <input type="tel" class="form-control" name="mblNo" id="mblNo"
                                               th:value="${user}? ${user.mblNo}" required data-i18n="[placeholder]user.placeholder.mblNo">
                                    </div>
                                </div>
                                <div class="hr-line-dashed"></div>
                                <input type="hidden" id="mblNohid" value="0">
                                <input type="hidden" id="ofId" th:value="${user}? ${user.officeId}">
                                <input type="hidden" id="brId" th:value="${user}? ${user.braId}">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label" data-i18n="user.officeNm"></label>
                                    <div class="col-sm-5">
                                       <select name="officeId" id="officeId" required class="form-control">
                                        </select>
                                    </div>
                                </div>
                                <div class="hr-line-dashed"></div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label" data-i18n="user.braNm"></label>
                                    <div class="col-sm-5">
                                        <select name="braId" id="braId" required class="form-control">
                                        </select>
                                    </div>
                                </div>
                                <div class="hr-line-dashed"></div>
                                <div class="form-group">
                                    <div class="col-sm-4 col-sm-offset-3">
                                        <button class="btn btn-primary" type="submit" data-i18n="user.save" onsubmit="return false;"></button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>

    </div>
</div>


<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script th:src="@{/js/myJs.js}"></script>
<script>
    $(document).ready(function() {
    	initOfficeNm();
        switch ($.cookie('lang')) {
            case 'zh':
                $.extend($.validator.messages, window.validate_zh);
                break;
            case 'en':
                $.extend($.validator.messages, window.validate_en);
                break;
            case 'km':
                $.extend($.validator.messages, window.validate_km);
                break;
        }

        $('#form').validate({
            rules: {
                password: "required",
                password_again: {
                    equalTo: "#password"
                }
            }
        });
        $('#form').submit(function() {
        	checkMblNo();

        	if($("#mblNohid").val()==0){
        		return false;
        	}
            var password = myjs($("#password").val());
            $("#password").val(password);
            var password_again = myjs($("#password_again").val());
            $("#password_again").val(password_again);
        });

        $("#name").change(function () {
            checkUser();
        })
    });
    //检查是否有同名用户
    function checkUser(){
        $name=$("#name");
        var name=$name.val();
        $.ajax({
            url:"/system/user/checkUserName",
            type:"post",
            data :{
                "name": name,
            },
            success:function(data) {
                if (data != 'success') {
                    $("#name").val("");
                    swal("", $.i18n.t("user.repeatUserName"), "error");
                }
            },
            error: function() {
                swal($.i18n.t("urm.mermgr.failed"),  "error");
            }
        });
    };
    function checkMblNo(){
    	$mobile=$("#mblNo");
        var mobile=$mobile.val();
  //检查手机号的合法性
        $.ajax({
            url:"/urm/mermgr/info/checkMobile",
            dataType: "json",
            type:"post",
            async : false,
            data :{
                "mobile": mobile,
            },
            success:function(data) {
                if (data.result != '1') {
                    swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkMoblieFormat"), "error");
                }else{
                	$("#mblNohid").val(1);
                }
            },
            error: function() {
                swal($.i18n.t("urm.mermgr.failed"),  "error");
            }
        });
    };
    function setofficeinfo(){
    	var checkValue=document.getElementById("ofId").value;
    	var select = document.getElementById("officeId");
        for (var i = 0; i < select.options.length; i++){  
            if (select.options[i].value == checkValue){  
                select.options[i].selected = true;  
                break;  
            }  
        }  
    };
 // 初始化公司
    function initOfficeNm() {
            $.getJSON('/cmm/officectrl/findAll', {
                ajax : 'true'
            }, function(data) {
                var html = '<option value="" selected="selected">' + $.i18n.t("cmm.ddl.choose") + '</option>';
                var len = data.length;
                for ( var i = 0; i < len; i++) {
                        html += '<option value="' + data[i].officeId + '">' + data[i].officeNm + '</option>';
                }
                $('#officeId').html(html);
                setofficeinfo();
                initbranch();
                setbranchinfo();
            });
        };
        function setbranchinfo(){
            var checkValue=document.getElementById("brId").value;
            var select = document.getElementById("braId");
            for (var i = 0; i < select.options.length; i++){  
                if (select.options[i].value == checkValue){  
                    select.options[i].selected = true;  
                    break;  
                }  
            }  
        };
 //根据选择的公司确定部门
     $('#officeId').change(
             function() {
                 var officeId = $(this).val();
                 if (officeId.length == 0) {
                     $('#braId').html('<option value="" selected="selected">' + $.i18n.t("cmm.ddl.choose") + '</option>');
                 } else {
                     $.getJSON('/cmm/branchctrl/findAll', {
                         officeId : officeId,
                         ajax : 'true'
                     }, function(data) {
                    	 var html ='';
                         var len = data.length;
                         for ( var i = 0; i < len; i++) {
                                 html += '<option value="' + data[i].braId + '">' + data[i].braNm + '</option>';
                         }
                         $('#braId').html(html);
                     });
                 }
           });
 //根据选择的公司加载出部门
 function initbranch(){
	 var officeId = document.getElementById("ofId").value;
     if (officeId.length == 0) {
         $('#braId').html('<option value="">' + $.i18n.t("cmm.ddl.choose") + '</option>');
     } else {
         $.getJSON('/cmm/branchctrl/findAll', {
             officeId : officeId,
             ajax : 'true'
         }, function(data) {
             var html ='';
             var len = data.length;
             for ( var i = 0; i < len; i++) {
                     html += '<option value="' + data[i].braId + '">' + data[i].braNm + '</option>';
             }
             $('#braId').html(html);
         });
     }
 };
</script>
</body>

</html>
