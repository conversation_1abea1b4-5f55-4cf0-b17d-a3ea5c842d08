<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>

    <title data-i18n="activity.title"></title>

    <div th:replace="head"></div>

</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="onr.refund"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.busmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.bussub.trdmgr"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="onr.refund"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form method="post" id = "form" class="form-horizontal" th:action="@{${api}}">
                                <div class="form-group" >
                                    <label class="col-sm-2 control-label" data-i18n="onr.cshOrderNo"></label>
                                    <div class="col-sm-4"><input type="text" class="form-control" name="cshOrderNo" id="cshOrderNo"
                                                                  th:value="${rfdActivity}? ${rfdActivity.cshOrderNo}"
                                                                  required="required" th:disabled = "${rfdActivity}? true" ></div>
                                    <label class="col-sm-2 control-label" data-i18n="onr.mercId"></label>
                                    <div class="col-sm-4"><input type="text" class="form-control" name="mercId" id="mercId"
                                                                  th:value="${rfdActivity}? ${rfdActivity.mercId}"
                                                                  required="required" th:disabled = "${rfdActivity}? true" ></div>
                                </div>
                                <div class="hr-line-dashed" ></div>
                                <div class="form-group" >
                                	<label class="col-sm-2 control-label" data-i18n="onr.ccy"></label>
                                    <div class="col-sm-4"><input type="text" class="form-control" name="ccy" id="ccy"
                                                                 th:value="${rfdActivity}? ${rfdActivity.ccy}"
                                                                 required="required" th:disabled = "${rfdActivity}? true">
                                                                 <input type="hidden" name="ccyEn" id="ccyEn" th:value="${rfdActivity}? ${rfdActivity.ccy}"
                                                                 required="required" th:disabled = "${rfdActivity}? true">
                                    </div>
                                    <label class="col-sm-2 control-label" data-i18n="onr.orderAmt"></label>
                                    <div class="col-sm-4"><input type="text" class="form-control" name="orderAmt"
                                                                 required="required" id="orderAmt" 
                                                                 th:value="${rfdActivity}? ${rfdActivity.orderAmt}"
                                                                 th:disabled = "${rfdActivity}? true">
                                    </div>
                                </div>
                                <div class="hr-line-dashed"></div>
                                <div class="form-group" >
                                    <label class="col-sm-2 control-label" data-i18n="onr.payAmt"></label>
                                    <div class="col-sm-4"><input type="text" class="form-control" name="payAmt" id="payAmt"
                                                                 th:value="${rfdActivity}? ${rfdActivity.payAmt}"
                                                                 required="required" th:disabled = "${rfdActivity}? true">
                                    </div>
	                                <label class="col-sm-2 control-label" data-i18n="onr.refAmt"></label>
	                                <div class="col-sm-4"><input type="text" class="form-control" name="refAmt" id="refAmt"
	                                                             th:value="${rfdActivity}? ${rfdActivity.refAmt}"
	                                                             required="required" th:disabled = "${rfdActivity}? true">
	                                </div>
	                            </div>
                                <div class="hr-line-dashed"></div>
                                <div class="form-group" >
                                    <label class="col-sm-2 control-label" data-i18n="onr.couponType"></label>
                                    <div class="col-sm-4"><input type="text" class="form-control" name="couponType" id="couponType"
                                                                 th:value="${rfdActivity}? ${rfdActivity.couponType}"
                                                                 required="required" th:disabled = "${rfdActivity}? true">
                                    </div>
                                    <label class="col-sm-2 control-label" data-i18n="onr.couponAmt"></label>
                                    <div class="col-sm-4"><input type="text" class="form-control" name="couponAmt" id="couponAmt"
                                                                 th:value="${rfdActivity}? ${rfdActivity.couponAmt}"
                                                                 required="required" th:disabled = "${rfdActivity}? true">
                                    </div>
                                </div>
                                <div class="hr-line-dashed"></div>
                                <div class="form-group" >
                                    <label class="col-sm-2 control-label" data-i18n="onr.orderStat"></label>
                                    <div class="col-sm-4"><input type="text" class="form-control" name="orderStat" id="orderStat"
                                                                 th:value="${rfdActivity}? ${rfdActivity.orderStat}"
                                                                 required="required" th:disabled = "${rfdActivity}? true">
                                    </div>
                                    <label class="col-sm-2 control-label" data-i18n="onr.orderDesc"></label>
                                    <div class="col-sm-4"><input type="text" class="form-control" name="orderDesc" id="orderDesc"
                                                                 th:value="${rfdActivity}? ${rfdActivity.orderDesc}"
                                                                 required="required" th:disabled = "${rfdActivity}? true">
                                    </div>
                                </div>
                                <div class="hr-line-dashed"></div>
                                <div class="form-group" >
                                    <label class="col-sm-2 control-label" data-i18n="onr.needRfdAmt"></label>
                                    <div class="col-sm-4"><input type="text" class="form-control" name="needRfdAmt" id="needRfdAmt"
                                                                 required="required" >
                                    </div>
                                    <label class="col-sm-2 control-label" data-i18n="onr.refRmk"></label>
                                    <div class="col-sm-4"><textarea class="form-control" name="refRmk" id="refRmk"></textarea>
                                    </div>
                                </div>
                                <div class="hr-line-dashed"></div>
                                <div class="form-group">
                                    <div class="col-sm-8 col-sm-offset-2">
                                        <button class="btn btn-primary" type="button" data-i18n="onr.save" id = 'save'></button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>

    </div>
</div>


<div th:replace="script"></div>
</body>
<script type="text/javascript" th:inline="javascript">
    $(function () {
        var uri = [[${api}]];
        $("#save").click(function () {
            var mercId = $("#mercId").val() ;
            var cshOrderNo = $("#cshOrderNo").val();
            var CCY = $("#ccyEn").val();
            var needRfdAmt = $("#needRfdAmt").val();
            var refRmk = $("#refRmk").val();
            var param = {
                 "mercId" : mercId,
                 "cshOrderNo" : cshOrderNo,
                 "CCY" : CCY,
                 "refAmt" : needRfdAmt,
                 "refRmk" : refRmk,
             };
            $.ajax({
                type: "POST",
                url: uri,
                dataType: "json",
                data: param,
                success: function (data) {
                    var msgCd = data.msgCd;
                    if (msgCd !='ONR00000') {
                    	swal(data.msgInfo);
                    	document.getElementById("save").disabled = false;
                    } else {
                        swal({
                            title:$.i18n.t("onr.refundSuc"),
                            animation:"slide-from-top",
                            	timer:3000},
                            function(){ 
                            		history.go(-1); 
                            		location.reload(); 
}
                            );
                        
                    }
                },
                error : function (data) {
                	document.getElementById("save").disabled = false;
                    swal({text:data.msgInfo});
                },
            });
            this.disabled = true;
        });
    });
    $(document).ready(function(){ 
    i18nLoad.then( function(){
    	var ccy = $("#ccy").val();
    	switch (ccy) {
	        case "CNY":
	        	ccy = $.i18n.t("CCY.CNY");
	        	break;
	        case "USD":
	        	ccy = $.i18n.t("CCY.USD");
	        	break;
	        case "KHR":
	        	ccy = $.i18n.t("CCY.KHR");
	        	break;
	    };
	    $("#ccy").val(ccy);
    	var couponType = $("#couponType").val();
    	switch (couponType) {
	        case "00":
	        	couponType = $.i18n.t("onr.coupon.00");
	        	break;
	        case "01":
	        	couponType = $.i18n.t("onr.coupon.01");
	        	break;
	        case "02":
	        	couponType = $.i18n.t("onr.coupon.02");
	        	break;
	        case "03":
	        	couponType = $.i18n.t("onr.coupon.03");
	        	break;
	        case "04":
	        	couponType = $.i18n.t("onr.coupon.04");
	        	break;
	    };
	    $("#couponType").val(couponType);
    	var orderStat = $("#orderStat").val();
    	switch (orderStat) {
	        case "U":
	        	orderStat = $.i18n.t("onr.statsub.U");
	        	break;
	    	case "W":
	    		orderStat = $.i18n.t("onr.statsub.W");
	    		break;
	    	case "P":
	    		orderStat = $.i18n.t("onr.statsub.P");
	    		break;
	    	case "S":
	    		orderStat = $.i18n.t("onr.statsub.S");
	    		break;
	    	case "F":
	    		orderStat = $.i18n.t("onr.statsub.F");
	    		break;
	    	case "PR":
	    		orderStat = $.i18n.t("onr.statsub.PR");
	    		break;
	    	case "RB":
	    		orderStat = $.i18n.t("onr.statsub.RB");
	    		break;
	    	case "R":
	    		orderStat = $.i18n.t("onr.statsub.R");
	    		break;
	    	case "PZ":
	    		orderStat = $.i18n.t("onr.statsub.PZ");
	    		break;
	    	case "Z":
	    		orderStat = $.i18n.t("onr.statsub.Z");
	    		break;
	    	case "D":
	    		orderStat = $.i18n.t("onr.statsub.D");
	    		break;
	    };
    	$("#orderStat").val(orderStat);
    });
   　});
</script>
</html>

