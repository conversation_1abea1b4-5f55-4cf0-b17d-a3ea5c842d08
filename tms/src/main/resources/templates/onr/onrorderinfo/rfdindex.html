<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>

    <title data-i18n="nav.bussub.trdsub.onrrfdordctrl.title"></title>

    <div th:replace="head"></div>
</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="nav.bussub.trdsub.onrrfdordctrl.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.busmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.bussub.trdmgr"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="nav.bussub.trdsub.onrrfdordctrl.content"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form id="queryForm" class="form-horizontal">
                                <div class="form-group col-sm-12">
                                    <!--手机号/商户号-->
                                    <label class="col-sm-2 control-label" for="mercId" data-i18n="onr.mercId"></label>
                                    <div class="col-sm-4">
                                        <input name="mercId" id="mercId" class="form-control" value=""/>
                                    </div>
                                    <label class="col-sm-2 control-label" for="cshOrderNo" data-i18n="onr.cshOrderNo"></label>
                                    <div class="col-sm-4">
                                        <input name="cshOrderNo" id="cshOrderNo" class="form-control" value=""/>
                                    </div>
                                </div>
                                <div class="form-group col-sm-8">
                                    <label class="col-sm-3 control-label" for="rfdStat" data-i18n="onr.rfdStat"></label>
                                    <div class="col-sm-5">
                                        <select name="rfdStat" id="rfdStat" class="form-control">
                                            <option value="" data-i18n="onr.select"></option>
                                            <option value="U" data-i18n="onr.rfdStatSub.U"></option>
                                            <option value="P" data-i18n="onr.rfdStatSub.P"></option>
                                            <option value="F" data-i18n="onr.rfdStatSub.F"></option>
                                            <option value="S" data-i18n="onr.rfdStatSub.S"></option>
                                            <option value="T" data-i18n="onr.rfdStatSub.T"></option>
                                            <option value="D" data-i18n="onr.rfdStatSub.D"></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group col-sm-12">
                                    <!--提交日期-->
                                    <label class="col-sm-2 control-label" for="beginDate" data-i18n="onr.cshOrderDt"></label>
                                    <div class="col-sm-4">
                                        <input name="beginDate" id="beginDate" class="form-control" value=""/>
                                    </div>
                                    <div class="col-sm-4">
                                        <input name="endDate" id="endDate" class="form-control" value=""/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-4 col-sm-offset-1">
                                        <button type="button" id="searchBtn" class="btn btn-default" onclick="search()">查询</button>
                                    </div>
                                </div>
                            </form>
                            <div class="table-responsive">
                                <table id="orderInf" class="table table-striped table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th data-i18n="onr.cshOrderNo"></th>
                                        <th data-i18n="onr.mercId"></th>
                                        <th data-i18n="onr.userId"></th>
                                        <th data-i18n="onr.onrRfdNo"></th>
                                        <th data-i18n="onr.rfdAmt"></th>
                                        <th data-i18n="onr.ccy"></th>
                                        <th data-i18n="onr.rfdStat"></th>
                                        <th data-i18n="onr.cshRfdDt"></th>
                                        <th data-i18n="onr.rfdRmk"></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>

    </div>
</div>
<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script>
    var editor;
    // A 2D array in which the first array is used to define the value options and the second array the displayed options (useful for language strings such as 'All').
    //    var array1 = [10, 25, 50, -1];
    //    var array2 = [10, 25, 50, "All"];
    var table;
    
    <!--查询按钮-->
    function search() {
        table.ajax.reload();
    }

    $(document).ready(function () {

        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        i18nLoad.then(function () {
            var d_url=document.location.href;
            var d_url1=d_url.split("id=")[1];
            if (d_url1 != null) {
                $("#queryForm").hide();
            }else {
                $("#queryForm").show();
            }
            table = $('#orderInf').DataTable({
                dom: 'Blfrtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/onr/onrrfdordctrl/findAll',
                    type: 'POST',
                    data: function (d) {
                    	d.extra_search = {
                    		"onrOrderNo" : d_url1,
                            "cshOrderNo" : $("#cshOrderNo").val(),
                            "mercId" : $("#mercId").val(),
                            "rfdStat" : $("#rfdStat").val(),
                            "beginDate" : $("#beginDate").val(),
                            "endDate" : $("#endDate").val()
                            };
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
                lengthMenu: [10, 50, 100, 500, 1000],
                language: {
//                "decimal": ",",
//                "thousands": ".",
//                    url: '/datatables/plugins/i18n/Chinese.lang'
                    url: languageUrl
                    //url: '//cdn.datatables.net/plug-ins/1.10.15/i18n/Chinese.json'
                },
                select: true,
                columnDefs: [{
                    targets:[0,1,2,3],//指定哪几列
                    render: function(data){
                        return "\u200C" + data;
                    }
                }],
                buttons: [
                    {
                        extend: 'excelHtml5',
                        filename: "Export",
                        exportOptions: {
                            columns: [ 0,1,2,3,4,5,6,7,8 ]
                        }
                    }
                ],
                columns: [{
                    data: 'cshOrderNo'
                },{
                    data: 'mercId'
                },{
                    data: 'userId'
                },{
                    data: 'onrRfdNo'
                },{
                    data: 'rfdAmt',
                    render: $.fn.dataTable.render.number(',', '.', 2, '$')
                },{
                    data: 'ccy',
                    render: function (data, type, row) {
                        switch (data) {
                            case "CNY":
                                return $.i18n.t("CCY.CNY");
                            case "USD":
                                return $.i18n.t("CCY.USD");
                            case "KHR":
                                return $.i18n.t("CCY.KHR");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'rfdStat',
                    render: function (data, type, row) {
                        switch (data) {
                            case "U":
                                return $.i18n.t("onr.rfdStatSub.U");
                            case "P":
                                return $.i18n.t("onr.rfdStatSub.P");
                            case "F":
                                return $.i18n.t("onr.rfdStatSub.F");
                            case "S":
                                return $.i18n.t("onr.rfdStatSub.S");
                            case "T":
                                return $.i18n.t("onr.rfdStatSub.T");
                            case "D":
                                return $.i18n.t("onr.rfdStatSub.D");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'cshRfdDt'
                },{
                    data: 'rfdRmk'
                }
                ]
            });
        });
    });
    /**初始化日期控件**/
    var beginTimePick = $('#beginDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        onChangeDate: function(dateText, inst) {
            endTimePick.datetimepicker('minDate',new Date(dateText.replace("-", "-")));
        }
    });

    var endTimePick = $('#endDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d'
        // maxDate:'+1970/01/01',
    });
</script>
</body>

</html>