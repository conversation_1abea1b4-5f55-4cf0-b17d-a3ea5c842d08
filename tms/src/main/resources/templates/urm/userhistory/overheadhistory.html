<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>

    <title data-i18n="nav.bussub.usrsub.userhistoryctrl.title"></title>

    <div th:replace="head"></div>
</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="nav.bussub.usrsub.userhistoryctrl.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.busmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.bussub.usrmgr"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="nav.bussub.usrsub.userhistoryctrl.content"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form id="queryForm" class="form-horizontal">
                                <div class="box-header">
                                    <!-- 表头搜索栏 -->
                                    <table style="width: 80%">
                                        <tr>
                                            <!--用户号-->
                                            <td align="center">
                                                <label class="control-label" for="idNo" data-i18n="urm.info.userId"></label>
                                            </td>
                                            <td>&nbsp; </td>
                                            <td>
                                                <input name="userId" id="userId" class="form-control" value=""/>
                                            </td>
                                            <td>&nbsp;</td>

                                            <!--手机号-->
                                            <td align="center">
                                                <label class="control-label" for="mblNo" data-i18n="urm.info.mblNo"></label>
                                            </td>
                                            <td>&nbsp;</td>
                                            <td>
                                                <input name="mblNo" id="mblNo" class="form-control" value=""/>
                                            </td>
                                            <td>&nbsp; </td>

                                            <!--提交按钮-->
                                            <td>
                                                <button type="button" id="searchBtn" class="btn btn-primary" onclick="search()" data-i18n="urm.search"></button>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <hr/>
                            </form>
                            <div class="table-responsive">
                                <table id="userInf" class="table table-striped table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th data-i18n="urm.info.userId"></th>
                                        <th data-i18n="urm.info.mblNo"></th>
                                        <th data-i18n="urm.info.usrLvl"></th>
                                        <th data-i18n="urm.info.idChkFlg"></th>
                                        <th data-i18n="urm.info.usrRegCnl"></th>
                                        <th data-i18n="urm.info.usrRegIp"></th>
                                        <th data-i18n="urm.info.usrRegDt"></th>
                                        <th data-i18n="urm.info.usrRegTm"></th>
                                        <th data-i18n="urm.info.usrClsCnl"></th>
                                        <th data-i18n="urm.info.usrClsIp"></th>
                                        <th data-i18n="urm.info.usrClsDt"></th>
                                        <th data-i18n="urm.info.usrClsTm"></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>

    </div>
</div>
<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script>
    var editor;
    //A 2D array in which the first array is used to define the value options and the second array the displayed options (useful for language strings such as 'All').
    //    var array1 = [10, 25, 50, -1];
    //    var array2 = [10, 25, 50, "All"];
    var table;
    
    <!--查询按钮-->
    function search() {
        table.ajax.reload();
    }

    $(document).ready(function () {

        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        i18nLoad.then(function () {
            editor = new $.fn.dataTable.Editor({
                ajax: {
                 //  create: {
                 //      type: 'POST',
                 //      url: '/urm/transferctrl/add',
                 //      contentType: 'application/json',
                 //      data: function (d) {
                 //          return JSON.stringify(d);
                 //      }
                 //  },
                 //  edit: {
                 //      type: 'POST',
                 //      url: '/urm/mercinfctrl/modify',
                 //      contentType: 'application/json',
                 //      data: function (d) {
                 //          return JSON.stringify(d);
                 //      }
                 //  },
                 //  remove: {
                 //      type: 'POST',
                 //      url: '/urm/mercinfctrl/delete',
                 //      contentType: 'application/json',
                 //      data: function (d) {
                 //          return JSON.stringify(d);
                 //      }
                 //  }
                },
                table: "#userInf",
                idSrc: 'userId',
             //   fields: [
                  //  {name: "id", type: "hidden"},
                  //  {label: $.i18n.t("urm.urmOrderDt"),name: "urmOrderDt"},
                  //  {label: $.i18n.t("urm.urmOrderNo"), name: "urmOrderNo"},
                  //  {label: $.i18n.t("urm.userNo"), name: "userNo"},
                  //  {label: $.i18n.t("urm.mblNo"), name: "mblNo"},
                  //  {label: $.i18n.t("urm.bossCopType"), name: "bossCopType"},
                  //  {label: $.i18n.t("urm.orderType"), name: "orderType"},
                  //  {label: $.i18n.t("urm.orderAmt"), name: "orderAmt"},
                  //  {label: $.i18n.t("urm.orderSts"), name: "orderSts"},
                  //  {label: $.i18n.t("urm.payType"), name: "payType"},
                  //  {label: $.i18n.t("urm.payAmt"), name: "payAmt"}
                    //{
                        //label: $.i18n.t("demo.startDate"),
                        //name: "startDate",
                        //type: "datetime",
                        //def: function () {
                            //return new Date();
                       // },
                       // format: "YYYY-MM-DD HH:mm:ss"
                   // },
                    //{label: $.i18n.t("demo.salary"), name: "salary"}
              //  ],
                //i18n: {
                  // create: {button: $.i18n.t("urm.add"), title: $.i18n.t("urm.add"), submit: $.i18n.t("urm.create")},
                  // edit: {button: $.i18n.t("urm.modify"), title: $.i18n.t("urm.modify"), submit: $.i18n.t("urm.update")},
                  // remove: {
                  //     button: $.i18n.t("urm.delete"), title: $.i18n.t("urm.delete"), submit: $.i18n.t("urm.delete"),
                  //     confirm: {
                  //         _: $.i18n.t("urm.multi-delete"),
                  //         1: $.i18n.t("urm.single-delete")
                  //     }
                  // }
                //}
            });

            //editor.on('preSubmit', function (e, o, action) {
            //    var id = editor.field('urmOrderNo');
            //    o.id = id.val();  // create a new parameter to pass over to the server called entityId
            //});
            var d_url=document.location.href;
            var d_url1=d_url.split("id=")[1];
            if (d_url1 != null) {
                $("#queryForm").hide();
                // 修复URL参数处理，进行URL解码
                var userId = decodeURIComponent(d_url1);
                // 如果参数中包含其他参数，只取第一个
                if (userId.indexOf('&') > -1) {
                    userId = userId.split('&')[0];
                }
                document.getElementById("userId").value=userId;
            }else {
                $("#queryForm").show();
            }
            
            // 延迟初始化DataTable，确保参数设置完成
            setTimeout(function() {
                table = $('#userInf').DataTable({
                    dom: 'Blfrtip',
                    ajax: {
                        contentType: 'application/json',
                        url: '/busmgr/userhistoryctrl/findAll',
                        type: 'POST',
                        data: function (d) {
                            d.extra_search = {
                                "userId" : $("#userId").val(),
                                "mblNo" : $("#mblNo").val()
                                };
                            return JSON.stringify(d);
                        }
                    },
                    serverSide: true,
                    searchDelay: 1000,
                    responsive: true,
                    processing: true,
    //            "lengthMenu": [array1, array2],
                    language: {
    //                "decimal": ",",
    //                "thousands": ".",
    //                    url: '/datatables/plugins/i18n/Chinese.lang'
                        url: languageUrl
                        //url: '//cdn.datatables.net/plug-ins/1.10.15/i18n/Chinese.json'
                    },
                    select: true,
                    buttons: [
                        //{extend: "create", editor: editor},
                        //{extend: "edit", editor: editor},
                        //{extend: "remove", editor: editor},
    //                {extend: 'copyHtml5'},
    //                {extend: 'csvHtml5'},
    //                {extend: 'excelHtml5', title: '文件名'},
                        'copy',
                        'csv',
                        'excel'
    //                'pdf',
    //                'print'
                    ],
                    columns: [{
                        data: 'userId'
                    },{
                        data: 'mblNo'
                    },{
                        data: 'usrLvl',
                        render: function (data, type, row) {
                            switch (data) {
                                case "0":
                                    return $.i18n.t("urm.info.lvlsub.0");
                                case "1":
                                    return $.i18n.t("urm.info.lvlsub.1");
                                case "2":
                                    return $.i18n.t("urm.info.lvlsub.2");
                                case "3":
                                    return $.i18n.t("urm.info.lvlsub.3");
                                default:
                                    return data;
                            }
                        }
                    },{
                        data: 'idChkFlg',
                        render: function (data, type, row) {
                            switch (data) {
                                case "0":
                                    return $.i18n.t("urm.info.idChksub.0");
                                case "1":
                                    return $.i18n.t("urm.info.idChksub.1");
                                default:
                                    return data;
                            }
                        }
                    },{
                        data: 'usrRegCnl'
                    },{
                        data: 'usrRegIp'
                    },{
                        data: 'usrRegDt'
                    },{
                        data: 'usrRegTm'
                    },{
                        data: 'usrClsCnl'
                    },{
                        data: 'usrClsIp'
                    },{
                        data: 'usrClsDt'
                    },{
                        data: 'usrClsTm'
                    }
                    ]
                });
            }, 100); // 延迟100ms确保DOM操作完成
        });
    });
</script>
</body>

</html>