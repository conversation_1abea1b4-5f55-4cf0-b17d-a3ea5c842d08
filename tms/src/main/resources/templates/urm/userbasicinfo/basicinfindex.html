<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>

    <title data-i18n="nav.bussub.usrsub.userbasicinfoctrl.title"></title>

    <div th:replace="head"></div>
</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="nav.bussub.usrsub.userbasicinfoctrl.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.busmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.bussub.usrmgr"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="nav.bussub.usrsub.userbasicinfoctrl.content"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form id="queryForm" class="form-horizontal">
                                <div class="box-header">
                                    <!-- 表头搜索栏 -->
                                    <table style="width: 80%">
                                        <tr>
                                            <!--用户号-->
                                            <td align="center">
                                                <label class="control-label" for="userId" data-i18n="urm.info.userId"></label>
                                            </td>
                                            <td>&nbsp; </td>
                                            <td>
                                                <input name="userId" id="userId" class="form-control" value=""/>
                                            </td>
                                            <td>&nbsp;</td>

                                            <!--手机号-->
                                            <td align="center">
                                                <label class="control-label" for="mblNo" data-i18n="urm.info.mblNo"></label>
                                            </td>
                                            <td>&nbsp;</td>
                                            <td>
                                                <input name="mblNo" id="mblNo" class="form-control" value=""/>
                                            </td>
                                            <td>&nbsp; </td>

                                            <!--证件号-->
                                            <td align="center">
                                                <label class="control-label" for="idNo" data-i18n="urm.info.idNo"></label>
                                            </td>
                                            <td>&nbsp;</td>
                                            <td>
                                                <input name="idNo" id="idNo" class="form-control" value=""/>
                                            </td>
                                            <td>&nbsp;&nbsp;</td>

                                            <!--提交按钮-->
                                            <td>
                                                <button type="button" id="searchBtn"  class="btn btn-primary" onclick="search()" data-i18n="urm.search"></button>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </form>
                            <!--华丽的分割线-->
                            <hr/>
                            <div class="table-responsive">
                                <table id="userInf" class="table table-striped table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th data-i18n="urm.info.userId"></th>
                                        <th data-i18n="urm.info.mblNo"></th>
                                        <th data-i18n="urm.info.usrNm"></th>
                                        <th data-i18n="urm.info.idType"></th>
                                        <th data-i18n="urm.info.idNoHid"></th>
                                        <th data-i18n="urm.info.usrRegDt"></th>
                                        <th data-i18n="urm.info.idChkFlg"></th>
                                        <th data-i18n="urm.info.usrRegCnl"></th>
                                        <th data-i18n="urm.cancelUser"></th>
                                        <th data-i18n="urm.operate"></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>

    </div>
</div>
<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script>
    var editor;
    //A 2D array in which the first array is used to define the value options and the second array the displayed options (useful for language strings such as 'All').
    //    var array1 = [10, 25, 50, -1];
    //    var array2 = [10, 25, 50, "All"];
    var table;
    
    <!--查询按钮-->
    function search() {
        table.ajax.reload();
    }

    $(document).ready(function () {

        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        i18nLoad.then(function () {
            editor = new $.fn.dataTable.Editor({
                ajax: {
                 //  create: {
                 //      type: 'POST',
                 //      url: '/urm/transferctrl/add',
                 //      contentType: 'application/json',
                 //      data: function (d) {
                 //          return JSON.stringify(d);
                 //      }
                 //  },
                 //  edit: {
                 //      type: 'POST',
                 //      url: '/urm/mercinfctrl/modify',
                 //      contentType: 'application/json',
                 //      data: function (d) {
                 //          return JSON.stringify(d);
                 //      }
                 //  },
                 //  remove: {
                 //      type: 'POST',
                 //      url: '/urm/mercinfctrl/delete',
                 //      contentType: 'application/json',
                 //      data: function (d) {
                 //          return JSON.stringify(d);
                 //      }
                 //  }
                },
                table: "#userInf",
                idSrc: 'userId',
             //   fields: [
                  //  {name: "id", type: "hidden"},
                  //  {label: $.i18n.t("urm.urmOrderDt"),name: "urmOrderDt"},
                  //  {label: $.i18n.t("urm.urmOrderNo"), name: "urmOrderNo"},
                  //  {label: $.i18n.t("urm.userNo"), name: "userNo"},
                  //  {label: $.i18n.t("urm.mblNo"), name: "mblNo"},
                  //  {label: $.i18n.t("urm.bossCopType"), name: "bossCopType"},
                  //  {label: $.i18n.t("urm.orderType"), name: "orderType"},
                  //  {label: $.i18n.t("urm.orderAmt"), name: "orderAmt"},
                  //  {label: $.i18n.t("urm.orderSts"), name: "orderSts"},
                  //  {label: $.i18n.t("urm.payType"), name: "payType"},
                  //  {label: $.i18n.t("urm.payAmt"), name: "payAmt"}
                    //{
                        //label: $.i18n.t("demo.startDate"),
                        //name: "startDate",
                        //type: "datetime",
                        //def: function () {
                            //return new Date();
                       // },
                       // format: "YYYY-MM-DD HH:mm:ss"
                   // },
                    //{label: $.i18n.t("demo.salary"), name: "salary"}
              //  ],
                //i18n: {
                  // create: {button: $.i18n.t("urm.add"), title: $.i18n.t("urm.add"), submit: $.i18n.t("urm.create")},
                  // edit: {button: $.i18n.t("urm.modify"), title: $.i18n.t("urm.modify"), submit: $.i18n.t("urm.update")},
                  // remove: {
                  //     button: $.i18n.t("urm.delete"), title: $.i18n.t("urm.delete"), submit: $.i18n.t("urm.delete"),
                  //     confirm: {
                  //         _: $.i18n.t("urm.multi-delete"),
                  //         1: $.i18n.t("urm.single-delete")
                  //     }
                  // }
                //}
            });

            //editor.on('preSubmit', function (e, o, action) {
            //    var id = editor.field('urmOrderNo');
            //    o.id = id.val();  // create a new parameter to pass over to the server called entityId
            //});
            table = $('#userInf').DataTable({
                dom: 'Blfrtip',
                columnDefs: [{
                    targets:[0,1,2,3,4],//指定哪几列
                    render: function(data){
                        return "\u200C" + data;
                    }
                }],
                ajax: {
                    contentType: 'application/json',
                    url: '/busmgr/userbasicinfoctrl/findAll',
                    type: 'POST',
                    data: function (d) {
                    	d.extra_search = {
                            "userId" : $("#userId").val(),
                            "mblNo" : $("#mblNo").val(),
                            "idNo" : $("#idNo").val()
                            };
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
//            "lengthMenu": [array1, array2],
                language: {
//                "decimal": ",",
//                "thousands": ".",
//                    url: '/datatables/plugins/i18n/Chinese.lang'
                    url: languageUrl
                    //url: '//cdn.datatables.net/plug-ins/1.10.15/i18n/Chinese.json'
                },
                select: true,
                buttons: [
                    //{extend: "create", editor: editor},
                    //{extend: "edit", editor: editor},
                    //{extend: "remove", editor: editor},
                {extend: 'copyHtml5'},
                {extend: 'csvHtml5'},
                {extend: 'excelHtml5', title: '用户资料列表'},
//                    'copy',
//                    'csv',
//                    'excel'
//                'pdf',
//                'print'
                ],
                columns: [{
                    data: 'userId'
                },{
                    data: 'mblNo'
                },{
                    data: 'usrNm'
                },{
                    data: 'idType',
                    render: function (data, type, row) {
                        switch (data) {
                            case "0":
                                return $.i18n.t("urm.info.idTypesub.0");
                            case "1":
                                return $.i18n.t("urm.info.idTypesub.1");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'idNo'
                },{
                    data: 'usrRegDt'
                },{
                    data: 'idChkFlg',
                    render: function (data, type, row) {
                        switch (data) {
                            case "0":
                                return $.i18n.t("urm.info.idChksub.0");
                            case "1":
                                return $.i18n.t("urm.info.idChksub.1");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'usrRegCnl'
                },{
                    data: 'usrRegCnl',
                    render: function (data, type, row) {
                        console.log(row.usrSts)
                        if (row.usrSts != '1') {
                            return '<td><span  id="' + row.userId +
                                '" onclick="cancelUser( \'' + row.userId + '\')"  style="cursor: pointer;color: #00b7ee" class="fa fa-clipboard ">' + $.i18n.t("urm.cancelUser") + '</span></td>';
                        } else {
                            return $.i18n.t("urm.cancelUsered");
                        }
                    }


                },{
                    data: 'usrRegCnl',
                    render: function (data, type, row) {
                            return '<td><a href="/busmgr/userhistoryctrl?id=' + row.userId +
                            '"><span class="fa fa-clipboard">'+ $.i18n.t("urm.operatetype.overhistory") +'</span></a></td>'
                            +'  '+'<td><a href="/urm/queryBalanceView?id=' + row.userId +
                            '"><span class="fa fa-money">' + $.i18n.t("urm.operatetype.acmbal") + '</span></a></td>';
                    }
                    
                }
                ]
            });
        });
    });
    
    function cancelUser(userId) {
        //确认提交弹出框
        swal({
            title: $.i18n.t("urm.cancelUserConfirmTitle"),
            text: $.i18n.t("urm.cancelUserConfirmText"),
            type: "warning",
            showCancelButton: true,
            cancelButtonText: $.i18n.t("urm.cancel"),
            confirmButtonColor: "#DD6B55",
            confirmButtonText: $.i18n.t("urm.confirm"),
            closeOnConfirm: false
        }, function () {
            $.ajax({
                type: "POST",
                url: "/busmgr/userbasicinfoctrl/cancelUser",
                dataType: "json",
                data: {"userId": userId},
                success: function (data) {
                    var result = data.msgCd;
                    if (result != 'URM00000') {
                        swal(data.msgCd, data.msgInfo, "error")
                    } else {
                        swal($.i18n.t("urm.cancelUserConfirmTitle"), $.i18n.t("urm.cancelUserSuc"), "success")
                    }
                    table.ajax.reload();
                },
                error: function (data) {
                    swal($.i18n.t("urm.error"), "error");
                    table.ajax.reload();
                },
            });
        });
    }
</script>
</body>

</html>