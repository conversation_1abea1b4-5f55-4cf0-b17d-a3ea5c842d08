<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
    <head>
        <title data-i18n="urm.mermgr.statusMng.title"></title>
        <div th:replace="head"></div>
    </head>
    <body>
        <div id="wrapper">
            <div th:replace="nav"></div>
            <div id="page-wrapper" class="gray-bg">
                <div th:replace="top"></div>
                <div class="row wrapper border-bottom white-bg page-heading">
                    <div class="col-lg-10">
                        <h2 data-i18n="urm.mermgr.statusMng.content"></h2>
                        <ol class="breadcrumb">
                            <li>
                                <a data-i18n="nav.busmgr"></a>
                            </li>
                            <li>
                                <a data-i18n="nav.bussub.mermgr"></a>
                            </li>
                            <li class="active">
                                <strong data-i18n="urm.mermgr.statusMng.content"></strong>
                            </li>
                        </ol>
                    </div>
                </div>
                <div class="wrapper wrapper-content  animated fadeInRight">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="ibox float-e-margins">
                                <div class="ibox-content">

                                    <!-- 表头导航栏 -->
                                    <div class="box-header">
                                        <table style="width: 80%">
                                            <tr>
                                                <td align="center">
                                                    <label class="control-label" data-i18n="urm.mermgr.mercId"></label>
                                                </td>
                                                <td align="center">
                                                    <input class="form-control" name="searchMercId" />
                                                </td>
                                                <td align="center">
                                                    <button type="button" class="btn btn-w-m btn-primary _right"
                                                            data-i18n="urm.search" onclick="searchButton()">
                                                    </button>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <hr/>

                                    <!-- TODO -->
                                    <table width="90%" style="border-collapse:separate; border-spacing:0px 10px;">
                                        <tr>
                                            <td align="center">
                                                <label class="control-label" data-i18n="urm.mermgr.mercId"></label>
                                            </td>
                                            <td>
                                                <input class="form-control" name="mercId" readonly="readonly" />
                                            </td>

                                            <td align="center">
                                                <label class="control-label" data-i18n="urm.mermgr.mercNm"></label>
                                            </td>
                                            <td>
                                                <input class="form-control" name="mercNm" readonly="readonly" />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="center">
                                                <label class="control-label" data-i18n="urm.mermgr.mercShortNm"></label>
                                            </td>
                                            <td>
                                                <input class="form-control" name="mercShortNm" readonly="readonly" />
                                            </td>

                                            <td align="center">
                                                <label class="control-label" data-i18n="urm.mermgr.openAccTime"></label>
                                            </td>
                                            <td>
                                                <input class="form-control" name="useRegDt" readonly="readonly" />
                                            </td>

                                        </tr>
                                        <tr>
                                            <td align="center">
                                                <label class="control-label" data-i18n="urm.mermgr.mercSts"></label>
                                            </td>
                                            <td>
                                                <select class="form-control" name="mercSts">
                                                    <option value="0" data-i18n="urm.mermgr.normalAccount"></option>
                                                    <option value="1" data-i18n="urm.mermgr.freeze"></option>
                                                    <option value="2" data-i18n="urm.mermgr.pause"></option>
                                                </select>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="center" colspan="4">
                                                <button type="button" class="btn btn-w-m btn-primary _right"
                                                        data-i18n="urm.submit" onclick="submit()">
                                                </button>
                                            </td>
                                        </tr>
                                    </table>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div th:replace="footer"></div>
            </div>
        </div>
        <div th:replace="script"></div>

        <script>
            function submit() {
                var mercId = $("input[name='mercId']").val();
                var mercSts = $("select[name='mercSts']").val();
                $.ajax({
                    type: 'POST',
                    url: "/urm/mermgr/status/change",
                    data: {
                      "mercId": mercId,
                      "mercSts": mercSts
                    },
                    success: function (d) {
                        swal($.i18n.t("urm.success"), "", "success");
                        searchButton();
                    }
                })
            }

            function searchButton() {
                clean();
                var t = $("input[name='searchMercId']");
                if (t.val() == "") {
                    t.focus();
                    return;
                }
                var id = t.val();
                $.ajax({
                    type: 'GET',
                    url: "/urm/mermgr/status/" + id ,
                    success: function (d) {
                        //销户时直接弹窗提示
                        if (d.mercSts == '-1') {
                            //TODO  补充销户弹窗
                            return;
                        }
                        $("input[name='mercId']").val(d.mercId);
                        $("input[name='mercNm']").val(d.mercNm);
                        $("input[name='mercShortNm']").val(d.mercShortNm);
                        $("select[name='mercSts']").val(d.mercSts);
                        $("input[name='useRegDt']").val(d.useRegDt);
                    }
                })
            }

            function clean() {
                $("input[name='mercId']").val("");
                $("input[name='mercNm']").val("");
                $("input[name='mercShortNm']").val("");
                $("select[name='mercSts']").val("");
                $("input[name='useRegDt']").val("");
            }
        </script>
    </body>
</html>