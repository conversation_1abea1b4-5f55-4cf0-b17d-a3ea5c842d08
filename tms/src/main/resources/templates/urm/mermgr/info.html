<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
    <head>
        <title data-i18n="urm.mermgr.infoMng.title"></title>
        <div th:replace="head"></div>
        <style>
            div.floatright{
                display:inline;
                float:right;
                padding-left:20px;
                padding-right:20px;
            }

            ._right{
                display:inline;
                float:right;
                padding-right:15px;
            }
        </style>
    </head>
    <body>
        <div id="wrapper">
            <div th:replace="nav"></div>
            <div id="page-wrapper" class="gray-bg">
                <div th:replace="top"></div>
                <div class="row wrapper border-bottom white-bg page-heading">
                    <div class="col-lg-10">
                        <h2 data-i18n="urm.mermgr.infoMng.content"></h2>
                        <ol class="breadcrumb">
                            <li>
                                <a data-i18n="nav.busmgr"></a>
                            </li>
                            <li>
                                <a data-i18n="nav.bussub.mermgr"></a>
                            </li>
                            <li class="active">
                                <strong data-i18n="urm.mermgr.infoMng.content"></strong>
                            </li>
                        </ol>
                    </div>
                </div>

                <!--商户录入modal-->
                <div th:replace="urm/mermgr/merModal"></div>

                <div class="wrapper wrapper-content  animated fadeInRight">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="ibox float-e-margins">
                                <div class="ibox-content">

                                    <!-- 表头导航栏 -->
                                    <div class="box-header">
                                        <table style="width: 80%">
                                            <tr>
                                                <td align="center">
                                                    <label class="control-label" data-i18n="urm.mermgr.mercId"></label>
                                                </td>
                                                <td>
                                                    <input class="form-control" name="searchMercId" />
                                                </td>
                                                <td align="center">
                                                    <label class="control-label" data-i18n="urm.mermgr.mercNm"></label>
                                                </td>
                                                <td>
                                                    <input class="form-control" name="searchMercNm" />
                                                </td>
                                                <td align="center">
                                                    <label class="control-label" data-i18n="urm.mermgr.mercSts"></label>
                                                </td>
                                                <td>
                                                    <select class="form-control" name="searchMercSts" id = 'searchMercSts'>
                                                        <option value="" data-i18n="urm.all"></option>
                                                        <option value="0" data-i18n="urm.mermgr.normalAccount"></option>
                                                        <option value="1" data-i18n="urm.mermgr.cancelAccount"></option>
                                                    </select>
                                                </td>
                                            <!--</tr>-->
                                            <!--<tr>-->
                                                <!--<td align="center">-->
                                                    <!--<label class="control-label" data-i18n="urm.beginDt"></label>-->
                                                <!--</td>-->
                                                <!--<td>-->
                                                    <!--<input class="form-control" name="searchBeginDt" id="beginTime"/>-->
                                                <!--</td>-->
                                                <!--<td align="center">-->
                                                    <!--<label class="control-label" data-i18n="urm.endDt"></label>-->
                                                <!--</td>-->
                                                <!--<td>-->
                                                    <!--<input class="form-control" name="searchEndDt" id="endTime"/>-->
                                                <!--</td>-->
                                                <td></td>
                                                <td>
                                                    <button type="button" class="btn btn-w-m btn-primary _right" data-i18n="urm.search" onclick="searchButton()">
                                                    </button>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <hr/>

                                    <!-- 表格主体 -->
                                    <div class="table-responsive">
                                        <table id="example" class="table table-striped table-bordered table-hover">
                                            <thead>
                                                <tr>
                                                    <th data-i18n="urm.mermgr.mercId"></th>
                                                    <th data-i18n="urm.mermgr.mercNm"></th>
                                                    <th data-i18n="urm.mermgr.mercSts"></th>
                                                    <th data-i18n="urm.mermgr.createTime"></th>
                                                </tr>
                                            </thead>
                                        </table>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div th:replace="footer"></div>
            </div>
        </div>
        <div th:replace="script"></div>
        <script>
            var editor;
            var table;

            $(document).ready(function () {

                var languageUrl;
                switch ($.cookie('lang')) {
                    case 'zh':
                        languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                        break;
                    case 'en':
                        languageUrl = '/datatables/plugins/i18n/English.lang';
                        break;
                    case 'km':
                        languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                        break;
                }

                i18nLoad.then(function () {
                    editor = new $.fn.dataTable.Editor({
                        ajax: {
                            edit: {
                                type: 'POST',
                                url: '/urm/mermgr/info/status',
                                contentType: 'application/json',
                                data: function (d) {
                                    return JSON.stringify(d);
                                }
                            }
                        },
                        table: "#example",
                        idSrc: 'mercId',
                        fields: [
                            {name: "mercId", type: "hidden"},
                            {
                                label: $.i18n.t("urm.mermgr.mercSts"), name: "mercSts",
                                type:  "select",
                                options: [
                                    { label: $.i18n.t("urm.mermgr.cancelAccount"), value: "1" }
                                ]
                            }
                        ],
                        i18n: {
                            edit: {
                                button: $.i18n.t("urm.mermgr.status"),
                                title: $.i18n.t("demo.modify"),
                                submit: $.i18n.t("demo.update")}
                        }
                    });

                    table = $('#example').DataTable({
                        dom: 'B<"floatright"l>rtip',
                        ajax: {
                            contentType: 'application/json',
                            url: '/urm/mermgr/info/findAll',
                            type: 'POST',
                            data: function (d) {
                                return JSON.stringify(d);
                            }
                        },
                        drawCallback: function(settings) {
                            table.button(1).disable();
                            table.button(2).disable();
                            table.button(0).disable();
                        },
                        serverSide: true,
                        searchDelay: 1000,
                        //responsive: true,
                        processing: true,
                        select: {
                            style:    'single',
                            selector: 'td:first-child'
                        },
                        language: {
                            url: languageUrl
                        },
                        buttons: [
                            {
                                text: $.i18n.t("urm.detail"),
                                action: function ( e, dt, node, config ) {
                                    modifyMerChant();
                                }
                            }, {
                                extend: "edit", editor: editor
                            }, {
                                text: $.i18n.t("urm.mermgr.authority"),
                                action: function ( e, dt, node, config ) {
                                    var rowData = table.row('.selected').data();
                                    window.location.href="/busmgr/mercitfInfoctrl?id=" + rowData["mercId"];
                                }
                            }
                        ],
                        columns: [
                            {
                                data: 'mercId'
                            }, {
                                data: 'mercNm'
                            }, {
                                data: 'mercSts',
                                render: function (data, type, row) {
                                    switch (data) {
                                        case '0':
                                            return $.i18n.t("urm.mermgr.normalAccount");
                                        case '1':
                                            return $.i18n.t("urm.mermgr.cancelAccount");
                                    }
                                }
                            }, {
                                data: 'createTime',
                                type: "datetime",
                                def: function () {
                                    return new Date();
                                },
                                format: "YYYY-MM-DD HH:mm:ss"
                            }
                        ],
                        initComplete: function () {
                            table.button(1).disable();
                            table.button(2).disable();
                            table.button(0).disable();
                        }
                    });

                    $('#example').on('select.dt deselect.dt', function() {
                        table.buttons(1).enable(
                            table.rows({selected: true}).indexes().length === 0 ? false : true
                        );
                        table.buttons(2).enable(
                            table.rows({selected: true}).indexes().length === 0 ? false : true
                        );
                        table.buttons(0).enable(
                            table.rows({selected: true}).indexes().length === 0 ? false : true
                        );
                    })
                });
                // 初始化渠道费率页面
                initMerRatePage() ;
                //初始化商户级别下拉选择框
                innitMerLevel();

                //初始化商户级别下拉选择框
                innitBankSelect() ;
                //查看汇款凭证图片
                $('#imercLogo').viewer({zIndex:9999});
                $('#ibusLicImg').viewer({zIndex:9999});
                $('#icertImg').viewer({zIndex:9999});
                $('#icertImgB').viewer({zIndex:9999});
                $('#icardImg').viewer({zIndex:9999});
                $('#icardImgB').viewer({zIndex:9999});
                $('#imerPotocolImg').viewer({zIndex:9999});
                $('#qrcode').viewer({zIndex:9999});
                //设置模态框backdrop:static时,空白处不关闭；keyboard:false时,esc键盘不关闭
                $('#handleModal').modal({backdrop: "static", keyboard: false, show: false});
            });

            //初始化商户级别下拉选择框
            function innitMerLevel() {
                var html="";
                //html = "  <option value='7' >护手霜</option>";
                $.ajax({
                    url:"/urm/mermgr/info/getMerLevel",
                    dataType: "json",
                    type:"post",
                    data :{
                    },
                    success:function(data) {
                        if (data.list != null) {
                            list = data.list;
                            for (var i=0 ; i< list.length ; i++ ) {
                                html += "<option value='" +list[i].parmVal + "'>" + list[i].parmDispNm + "</option>";
                                $("#merLvl").html(html);
                            }
                        }
                    },
                    error: function() {
                        swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.innitMerLevelFailed"), "error");
                    }
                });

            }
            //初始化商户级别下拉选择框
            function innitBankSelect() {
                var html="";
                //查询所有的合作机构信息
                $.ajax({
                    url:"/cpt/cpoorg/info/findAllOrgnInfo",
                    dataType: "json",
                    type:"post",
                    success:function(data){
                        $("#capCorgNm").append("<option value=''>" + $.i18n.t("cpo.selectByNull") + "</option>");
                        for (var one in data)
                        {
                            var orgId = data[one].corpOrgId;
                            var orgNm = data[one].corpOrgNm;
                            $("#capCorgNm").append("<option value='" + orgId + "'>" +orgId+" -- "+orgNm+ "</option>");
                        }
                    }
                });

            }

            //弹出修改窗口
            function modifyMerChant() {
                //重设置输入
                $("#mermgrAdd").hide();
                $("#mercModify").hide();
                clear();
                //获取修改数据 根据如果商户状态为未审核则数据从审批流水表中获取，如果商户状态为审核通过，则从商户信息表中获取
                var row = table.row('.selected');
                if(row.length == 0) {
                    $("#handleModal").modal("hide");
                    swal($.i18n.t("activity.swal-fail"), $.i18n.t("activity.shouldSelectOne"), "error");
                    return;
                }

                $("#qrcodeDiv").show();

                var rowData =row.data();
                var mercId = rowData["mercId"];
                var mercSts = rowData["mercSts"];
                $("#mercId").val(mercId);
                $("#mercSts").val(mercSts);
                var putdata = {
                    "mercId" : mercId,
                    "mercSts" : "1"
                };
                <!--ajax异步调起后台服务，用户号查询商户信息-->
                $.ajax({
                    url:"/urm/mermgr/info/modifyView",
                    data: putdata,
                    dataType: "json",
                    type:"post",
                    success:function(data) {
                        if (data != null) {
                            putData(data);
                            $("#handleModal").modal("show");
                            var qrdata = {
                                "userId" : mercId,
                                "loginId" :  $("#mngAcc").val()
                            };
                            $.ajax({
                                url:"/urm/mermgr/info/merQrCode",
                                data: qrdata,
                                type:"post",
                                success:function(data) {
                                    if (data != "" && data.indexOf("html" ) <0) {
                                        console.log(data);
                                        $("#qrcode").attr("src","/urm/mermgr/info/getQRCode?filename=" +data+"&r="+new Date().getTime());
                                        console.log($("#qrcode").attr("src"));
                                        $("#downloadQrcode").attr("href","/urm/mermgr/info/getQRCode?filename=" +data);
                                    }
                                }
                            });
                        } else {
                            <!--隐藏模态框-->
                            $("#handleModal").modal("hide");
                            swal($.i18n.t("urm.mermgr.failed"), data.msgInfo, "error");
                        }
                    },
                    error: function() {
                        $("#handleModal").modal("hide");
                        swal($.i18n.t("urm.mermgr.failed"),  "error");
                    }
                });


            }

            //无效输入
            function disableData() {
                $("#mercForm input[type='text']").each(function(){
                    $(this).attr("disabled","disabled");
                });
                $("#mercForm select").each(function(){
                    $(this).attr("disabled","disabled");
                });
                $("#mercForm input[type='file']").each(function(){
                    $(this).attr("disabled","disabled");
                });
            }
            //展示数据
            function putData(mercobj) {
                var merRegAddr = mercobj.merRegAddr;
                if (merRegAddr!= "" && merRegAddr != null) {
                    $("#merRsgaddrS").val(merRegAddr.split("_")[0]);
                    $("#detailAddr").val(merRegAddr.split("_")[1]);
                }
                $("#imercLogo").attr("src",mercobj.mercLogoPath);
                $("#ibusLicImg").attr("src",mercobj.busLicImgPath);
                $("#icertImg").attr("src",mercobj.cardImgPath);
                $("#icertImgB").attr("src",mercobj.cardImgPathB);
                $("#icardImg").attr("src",mercobj.certImgPath);
                $("#icardImgB").attr("src",mercobj.certImgPathB);
                $("#imerPotocolImg").attr("src",mercobj.merPotocolImgPath);
                $("#imercLogo").css("cursor","pointer");
                $("#ibusLicImg").css("cursor","pointer");
                $("#icertImg").css("cursor","pointer");
                $("#icertImgB").css("cursor","pointer");
                $("#icardImg").css("cursor","pointer");
                $("#icardImgB").css("cursor","pointer");
                $("#imerPotocolImg").css("cursor","pointer");
                $("#qrcode").css("cursor","pointer");
                $("#mercNm").val(mercobj.mercName);
                $("#mercShortName").val(mercobj.mercShortName);
                $("#comercReg").val(mercobj.comercReg);
                $("#mercTrdCls").val(mercobj.mercTrdCls);
                $("#cprTyp").val(mercobj.cprTyp);
                $("#mgtScp").val(mercobj.mgtScp);
                //$("#merRsgaddr").val(mercobj.merRegAddr);
                //$("#merRsgaddrS").val(mercobj.merRegAddr);
                $("#merRsgaddrI").val("");
                $("#fixedaddrBut").val("");
                $("#prinNm").val(mercobj.crpNm);
                $("#term").val(mercobj.opnBusDtSr);
                $("#certTyp").val(mercobj.crpIdTyp);
                $("#certNo").val(mercobj.crpIdNo);
                $("#mangerNm").val(mercobj.diplayNm);
                $("#mngMoblie").val(mercobj.mblNo);
                $("#mngAcc").val(mercobj.loginId);
                $("#mngEmail").val(mercobj.email);
                $("#recMobile").val(mercobj.refereeMblNo);
                $("#capCardNo").val(mercobj.capCardNo);
                $("#capCardName").val(mercobj.capCardName);
                $("#capCorgNm").val(mercobj.capCorgNo);
                $("#subbranch").val(mercobj.subbranch);
                $("#belongMerc").val(mercobj.belongMerc);
                $("#settleType").val(mercobj.settleType);
                $("#settleCycleType").val(mercobj.settleCycleType);
                $("#settleEffDate").val(mercobj.settleEffDate);
                $("#settleExpDate").val(mercobj.settleExpDate);
                $("#drawDays").val(mercobj.drawDays);
                $("#settleSite").val(mercobj.settleSite);
                $("#merLvl").val(mercobj.merLvl);
                $("#hallSites").val(mercobj.hallSites);
                if  (mercobj.rateInfolist != null ) {
                    for(var i =0 ; i < mercobj.rateInfolist.length ; i++) {
                        var index = "";
                        if ( mercobj.rateInfolist[i].busType == "1010") {
                            index = ""
                        }
                        if (mercobj.rateInfolist[i].busType == "0403") {
                            index = 1;
                        }
                        if (mercobj.rateInfolist[i].busType == "0201" && mercobj.rateInfolist[i].channel == "WeChat" ) {
                            index = 2;
                        }
                        if (mercobj.rateInfolist[i].busType == "0202" && mercobj.rateInfolist[i].channel == "WeChat" ) {
                            index = 3;
                        }
                        if (mercobj.rateInfolist[i].busType == "0201" && mercobj.rateInfolist[i].channel == "Alipay" ) {
                            index = 4;
                        }
                        if (mercobj.rateInfolist[i].busType == "0202" && mercobj.rateInfolist[i].channel == "Alipay" ) {
                            index = 5;
                        }
                        if (mercobj.rateInfolist[i].busType == "0201" && mercobj.rateInfolist[i].channel == "BESTPAY" ) {
                            index = 6;
                        }
                        if (mercobj.rateInfolist[i].busType == "0202" && mercobj.rateInfolist[i].channel == "BESTPAY" ) {
                            index = 7;
                        }
                        $("#busType" + index).val(mercobj.rateInfolist[i].busType);
                        $("#chargeType" + index).val(mercobj.rateInfolist[i].chargeType);
                        $("#calculateType" + index).val(mercobj.rateInfolist[i].calculateType);
                        $("#beginCalAmt" + index).val(mercobj.rateInfolist[i].beginCalAmt);
                        $("#rate" + index).val(mercobj.rateInfolist[i].rate);
                        $("#minFee" + index).val(mercobj.rateInfolist[i].minFee);
                        $("#maxFee" + index ).val(mercobj.rateInfolist[i].maxFee);
                        $("#expDate" + index ).val(mercobj.rateInfolist[i].expDate);
                        $("#effDate" + index ).val(mercobj.rateInfolist[i].effDate);
                        $("#beginCalFee" + index ).val(mercobj.rateInfolist[i].beginCalFee);
                        $("#calculateMod" + index ).val(mercobj.rateInfolist[i].calculateMod);

                    }
                }

            }

            function clear() {
                $("#imercLogo").attr("src","/img/uploadBut.png");
                $("#ibusLicImg").attr("src","/img/uploadBut.png");
                $("#icertImg").attr("src","/img/uploadBut.png");
                $("#icardImg").attr("src","/img/uploadBut.png");
                $("#icertImgB").attr("src","/img/uploadBut.png");
                $("#icardImgB").attr("src","/img/uploadBut.png");
                $("#imerPotocolImg").attr("src","/img/uploadBut.png");
                $("#mercNm").val("");
                $("#mercShortName").val("");
                $("#comercReg").val("");
                $("#mercTrdCls").val("7395");
                $("#cprTyp").val("01");
                $("#mgtScp").val("");
                $("#merRsgaddr").val("");
                $("#merRsgaddrS").val("baiLin");
                $("#merRsgaddrI").val("");
                $("#fixedaddrBut").val("");
                $("#term").val("");
                $("#prinNm").val("");
                $("#certTyp").val("0");
                $("#certNo").val("");
                $("#mangerNm").val("");
                $("#mngMoblie").val("");
                $("#mngAcc").val("");
                $("#mngEmail").val("");
                $("#recMobile").val("");
                $("#mercLogo").val("");
                $("#busLicImg").val("");
                $("#certImg").val("");
                $("#cardImg").val("");
                $("#merPotocolImg").val("");
                $("#capCardNo").val("");
                $("#capCardName").val("");
                $("#capCorgNm").val("ICBC");
                $("#subbranch").val("");
                $("#belongMerc").val("");
                $("#settleType").val("auto");
                $("#settleCycleType").val("daily");
                $("#settleEffDate").val();
                $("#settleExpDate").val();
                $("#drawDays").val("");
                $("#settleSite").val("001");
                $("#hallSites").val("no");
                $("#busType").val("1010");
                $("#chargeType").val("single");
                $("#calculateType").val("percent");
                $("#beginCalAmt").val("");
                $("#calculateMod").val("internal");
                $("#calculateMod").attr("disabled",true);
                $("#rate").val("");
                $("#minFee").val("");
                $("#maxFee").val("");
                $("#expDate").val("");
                $("#effDate").val("");
                $("#busType1").val("0403");
                $("#chargeType1").val("single");
                $("#calculateType1").val("percent");
                $("#calculateMod1").val("internal");
                $("#calculateMod1").attr("disabled",true);
                $("#beginCalAmt1").val("");
                $("#rate1").val("");
                $("#minFee1").val("");
                $("#maxFee1").val("");
                $("#expDate1").val("");
                $("#effDate1").val("");
                $("#mercId").val("");
                $("#merLvl").val("");
                for (var i = 2 ; i < 8 ; i ++ ) {
                    var busType = "0201"
                    if ( i % 2 != 0 ) {
                        busType = "0202"
                    }
                    $("#busType" + i).val(busType);
                    $("#busType" + i).attr("disabled",true);
                    $("#chargeType"+ i).val("single");
                    $("#calculateType"+ i).val("percent");
                    $("#beginCalAmt"+ i).val("");
                    $("#calculateMod"+ i).val("internal");
                    $("#calculateMod"+ i).attr("disabled",true);
                    $("#rate"+ i).val("");
                    $("#minFee"+ i).val("");
                    $("#minFee"+ i).attr("disabled",false);
                    $("#maxFee"+ i).val("");
                    $("#maxFee"+ i).attr("disabled",false);
                    $("#expDate"+ i).val("");
                    $("#effDate"+ i).val("");
                }
                mercLogoPath = '' ,busLicImgPath = '' ,certImgPath = '' , cardImgPath = '' ,merPotocolImgPath = '';
            }

            var expDate = $('#expDate').datetimepicker({
                lang:'en',
                timepicker:false,
                validateOnBlur: false,
                format:'Y-m-d ',
                formatDate:'Y-m-d',
                // maxDate:'+1970/01/01',
            });
            var effDate = $('#effDate').datetimepicker({
                lang:'en',
                timepicker:false,
                validateOnBlur: false,
                format:'Y-m-d ',
                formatDate:'Y-m-d',
                // maxDate:'+1970/01/01',
            });

            var expDate1 = $('#expDate1').datetimepicker({
                lang:'en',
                timepicker:false,
                validateOnBlur: false,
                format:'Y-m-d ',
                formatDate:'Y-m-d',
                // maxDate:'+1970/01/01',
            });
            var effDate1 = $('#effDate1').datetimepicker({
                lang:'en',
                timepicker:false,
                validateOnBlur: false,
                format:'Y-m-d ',
                formatDate:'Y-m-d',
                // maxDate:'+1970/01/01',
            });

            function searchButton() {
                var id = $("input[name='searchMercId']").val();
                var nm = $("input[name='searchMercNm']").val();
                var sts = $("#searchMercSts").val();
                table.column(0).search(id)
                    .column(1).search(nm)
                    .column(2).search(sts);
                table.draw();
            }

            function initMerRatePage() {
                var html = $("#rateInfo").html();
                var busType = "" ;
                var busTypeDes = "" ;
                var channel = "Mpay";
                for (var i=0 ; i < 6 ; i++) {
                    var index = i + 2 ;
                    var rateIndex = index + 1;
                    if (index % 2 == 0 ) {
                        busType = "0201" ;
                        busTypeDes = "data-i18n='urm.mermgr.busTypeEnum.barCode'";
                    } else {
                        busType = "0202" ;
                        busTypeDes = "data-i18n='urm.mermgr.busTypeEnum.scan'";
                    }
                    if(index == 2 || index == 3 ) {
                        channel = "WeChat" ;
                    }
                    if(index == 4 || index == 5 ) {
                        channel = "Alipay" ;
                    }
                    if(index == 6 || index == 7 ) {
                        channel = "BESTPAY" ;
                    }
                    html += "<div class='hr-line-dashed ' style='border-top:1px dashed #121415;' ></div>";
                    html += "<div style='width: 1000px;high:20px ;margin-bottom : 10px'  data-i18n='urm.mermgr.rate" + rateIndex + "'> </div>";
                    html += "<div class='form-group'>";
                    html += "    <label class='col-sm-3 control-label' for='" + "busType" + index +"' data-i18n='urm.mermgr.busType' ></label>" ;
                    html += "    <div class='col-sm-3'>" ;
                    html += "        <select type='' class='form-control' name='busType" +index + "' id = 'busType" + index + "' >"
                    html+=  "            <option value='" +busType + "'" + busTypeDes +"  ></option>";
                    html+=  "        </select>";
                    html+=  "    </div>";
                    html += "    <label class='col-sm-3 control-label' for='" + "chargeType" + index +"' data-i18n='urm.mermgr.chargeType' ></label>" ;
                    html += "    <div class='col-sm-3'>" ;
                    html += "        <select type='' class='form-control' name='chargeType" +index + "' id = 'chargeType" + index + "' >"
                    html+=  "             <option value='single' data-i18n='urm.mermgr.chargeTypeEnum.single' ></option>";
                    html+=  "        </select>";
                    html+=  "    </div>";
                    html+=  "</div>";

                    html += "<div class='form-group'>";
                    html += "    <label class='col-sm-3 control-label' for='" + "calculateType" + index +"' data-i18n='urm.mermgr.calculateType' ></label>" ;
                    html += "    <div class='col-sm-3'>" ;
                    html += "        <select type='' class='form-control' name='calculateType" +index + "' id = 'calculateType" + index + "' >"
                    html+=  "            <option value='percent' data-i18n='urm.mermgr.calculateTypeEnum.percent' ></option>";
                    html+=  "            <option value='fixed' data-i18n='urm.mermgr.calculateTypeEnum.fixed' ></option>";
                    html+=  "        </select>";
                    html+=  "    </div>";
                    html += "    <label class='col-sm-3 control-label' for='" + "rate" + index +"' ><span style='color: red'>*</span><span data-i18n='urm.mermgr.rate'></span></label>" ;
                    html += "    <div class='col-sm-3'>" ;
                    html += "        <input type='text' class='form-control' name='rate"+ index + "' required='required' id='rate" + index + "' >"
                    html+=  "    </div>";
                    html+=  "</div>";

                    html += "<div class='form-group'>";
                    html += "    <label class='col-sm-3 control-label' for='" + "minFee" + index +"' data-i18n='urm.mermgr.minFee' ></label>" ;
                    html += "    <div class='col-sm-3'>" ;
                    html += "        <input type='text' class='form-control' name='minFee"+ index + " 'required='required' id='minFee" + index + "' >"
                    html+=  "    </div>";
                    html += "    <label class='col-sm-3 control-label' for='" + "maxFee" + index +"' data-i18n='urm.mermgr.maxFee' ></label>" ;
                    html += "    <div class='col-sm-3'>" ;
                    html += "        <input type='text' class='form-control' name='maxFee"+ index + " 'required='required' id='maxFee" + index + "' >"
                    html+=  "    </div>";
                    html+=  "</div>";

                    html += "<div class='form-group'>";
                    html += "    <label class='col-sm-3 control-label' for='" + "effDate" + index +"' ><span style='color: red'>*</span><span data-i18n='urm.mermgr.effDate'></span></label>" ;
                    html += "    <div class='col-sm-3'>" ;
                    html += "        <input type='text' class='form-control' name='effDate"+ index + " 'required='required' id='effDate" + index + "' >"
                    html+=  "    </div>";
                    html += "    <label class='col-sm-3 control-label' for='" + "expDate" + index +"' ><span style='color: red'>*</span><span data-i18n='urm.mermgr.expDate'></span></label>" ;
                    html += "    <div class='col-sm-3'>" ;
                    html += "        <input type='text' class='form-control' name='expDate"+ index + "' required='required' id='expDate" + index + "' >"
                    html+=  "    </div>";
                    html+=  "</div>";

                    html += "<div class='form-group'>";
                    html += "    <label class='col-sm-3 control-label' for='beginCalFee"+ index + "' data-i18n='urm.mermgr.beginCalFee'></label>" ;
                    html += "    <div class='col-sm-3'>" ;
                    html += "        <input type='text' class='form-control' name='beginCalFee"+ index + "' required='required' id='beginCalFee" + index + "' >"
                    html+=  "    </div>";
                    html += "    <label class='col-sm-3 control-label' for='calculateMod" + index + "' data-i18n='urm.mermgr.calculateMod'></label>" ;
                    html += "    <div class='col-sm-3'>" ;
                    html += "        <select type='' class='form-control' name='calculateMod" +index + "' id = 'calculateMod" + index + "' >"
                    html+=  "            <option value='internal' data-i18n='urm.mermgr.calculateModEnum.internal' ></option>";
                    html+=  "            <option value='external' data-i18n='urm.mermgr.calculateModEnum.external' ></option>";
                    html+=  "        </select>";
                    html+=  "    </div>";
                    html+=  "</div>";
                    html += " <input id='channel" +index+  "' name='channel" +index+ "' value='" + channel+ "'  type='hidden'/>"
                }
                html += " <input id='channel' name='channel' value='Seatelpay'  type='hidden'/>"
                html += " <input id='channel1' name='channel1' value='Seatelpay'  type='hidden'/>"
                $("#rateInfo").html(html);
                for (var j=2 ; j < 8 ; j++ ) {
                    $('#expDate' + j ).datetimepicker({
                        lang:'en',
                        timepicker:false,
                        validateOnBlur: false,
                        format:'Y-m-d ',
                        formatDate:'Y-m-d',
                        // maxDate:'+1970/01/01',
                    });
                    var effDate1 = $('#effDate' + j ).datetimepicker({
                        lang:'en',
                        timepicker:false,
                        validateOnBlur: false,
                        format:'Y-m-d ',
                        formatDate:'Y-m-d',
                        // maxDate:'+1970/01/01',
                    });
                }
                disableData();
            }

        </script>
    </body>
</html>