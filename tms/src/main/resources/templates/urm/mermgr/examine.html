<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title data-i18n="urm.mermgr.examine.title"></title>
    <div th:replace="head"></div>
    <style>
        div.floatright{
            display:inline;
            float:right;
            padding-left:20px;
            padding-right:20px;
        }

        ._right{
            display:inline;
            float:right;
            padding-right:15px;
        }
    </style>
</head>
<body>
<div id="wrapper">
    <div th:replace="nav"></div>
    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>
        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="urm.mermgr.examine.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.busmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.bussub.mermgr"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="urm.mermgr.examine.content"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <!--商户录入modal-->
        <div th:replace="urm/mermgr/examineModal"></div>
        <div th:replace="urm/mermgr/refuseModal"></div>
        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">

                            <!-- 表头导航栏 -->
                            <div class="box-header">
                                <table style="width: 80%">
                                    <tr>
                                        <td align="center">
                                            <label class="control-label" data-i18n="urm.mermgr.mercNm"></label>
                                        </td>
                                        <td>
                                            <input class="form-control" name="searchMercNm" id="searchMercNm"/>
                                        </td>
                                        <td align="center">
                                            <label class="control-label" data-i18n="urm.mermgr.examinStatus"></label>
                                        </td>
                                        <td>
                                            <select class="form-control" name="searchExaminStatus" id="searchExaminStatus">
                                                <option value="" data-i18n="urm.all"></option>
                                                <option value="00" data-i18n="urm.mermgr.examineWait"></option>
                                                <!--<option value="02" data-i18n="urm.mermgr.examinePass"></option>-->
                                                <option value="03" data-i18n="urm.mermgr.examineModify"></option>
                                                <option value="01" data-i18n="urm.mermgr.examineNoPass"></option>
                                            </select>
                                        </td>
                                        <td></td>
                                        <td>
                                            <button type="button" class="btn btn-w-m btn-primary _right" data-i18n="urm.search" onclick="searchButton()">
                                            </button>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <hr/>

                            <!-- 表格主体 -->
                            <div class="table-responsive">
                                <table id="example" class="table table-striped table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th data-i18n="urm.mermgr.examineId"></th>
                                        <th data-i18n="urm.mermgr.mercNm"></th>
                                        <th data-i18n="urm.mermgr.mercSts"></th>
                                        <th data-i18n="urm.mermgr.createTime"></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>
    </div>
</div>
<div th:replace="script"></div>
<script>
    var table;

    $(document).ready(function () {

        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        i18nLoad.then(function () {
            table = $('#example').DataTable({
                dom: 'B<"floatright"l>rtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/urm/mermgr/examine/list',
                    type: 'POST',
                    data: function (d) {
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                searchDelay: 1000,
                //responsive: true,
                processing: true,
                select: {
                    style:    'single',
                    selector: 'td:first-child'
                },
                language: {
                    url: languageUrl
                },
                buttons: [
                    {
                        text: $.i18n.t("urm.mermgr.examine.content"),
                        action: function ( e, dt, node, config ) {
                            //按钮js
                            examineMerChant() ;
                        }
                    }
                ],
                columns: [
                    {
                        data: 'id'
                    }, {
                        data: 'examinNm'
                    }, {
                        data: 'examinStatus',
                        render: function (data, type, row) {
                            switch (data) {
                                case '00':
                                    return $.i18n.t("urm.mermgr.examineWait");
                                case '02':
                                    return $.i18n.t("urm.mermgr.examinePass");
                                case '01':
                                    return $.i18n.t("urm.mermgr.examineNoPass");
                                case '03':
                                    return $.i18n.t("urm.mermgr.examineModify");
                                default : return '';
                            }
                        }
                    }, {
                        data: 'createTime',
                        type: "datetime",
                        def: function () {
                            return new Date();
                        },
                        format: "YYYY-MM-DD HH:mm:ss"
                    }
                ],
                initComplete: function () {
                }
            });

        });

        // 初始化渠道费率页面
        initMerRatePage() ;

        //审核通过
        $("#examinePass").click(function () {
            var status = table.rows('.selected').data()[0].examinStatus;
            if ("03" == status) {
                examine("03");
            } else {
                examine("02");
            }
        });

        //审核不通过
        $("#examineNoPass").click(function () {
            examine("01");
        });
        $("#showRefuseModel").click(function () {
           //
            showRefuseModal();
        });

        //初始化商户下拉框
        innitMerLevel();
        //初始化商户级别下拉选择框
        innitBankSelect() ;


        //查看汇款凭证图片
        $('#imercLogo').viewer({zIndex:9999});
        $('#ibusLicImg').viewer({zIndex:9999});
        $('#icertImg').viewer({zIndex:9999});
        $('#icertImgB').viewer({zIndex:9999});
        $('#icardImg').viewer({zIndex:9999});
        $('#icardImgB').viewer({zIndex:9999});
        $('#imerPotocolImg').viewer({zIndex:9999});
        //设置模态框backdrop:static时,空白处不关闭；keyboard:false时,esc键盘不关闭
        $('#handleModal').modal({backdrop: "static", keyboard: false, show: false});
    });

    function showRefuseModal() {
        $("#refuseModal").modal("show");
    }
    //初始化商户级别下拉选择框
    function innitMerLevel() {
        var html="";
        //html = "  <option value='7' >护手霜</option>";
        $.ajax({
            url:"/urm/mermgr/info/getMerLevel",
            dataType: "json",
            type:"post",
            data :{
            },
            success:function(data) {
                if (data.list != null) {
                    list = data.list;
                    for (var i=0 ; i< list.length ; i++ ) {
                        html += "<option value='" +list[i].parmVal + "'>" + list[i].parmDispNm + "</option>";
                        $("#merLvl").html(html);
                    }
                }
            },
            error: function() {
                swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.innitMerLevelFailed"), "error");
            }
        });

    }
    //初始化商户级别下拉选择框
    function innitBankSelect() {
        var html = "";
        //查询所有的合作机构信息
        $.ajax({
            url: "/cpt/cpoorg/info/findAllOrgnInfo",
            dataType: "json",
            type: "post",
            success: function (data) {
                $("#capCorgNm").append("<option value=''>" + $.i18n.t("cpo.selectByNull") + "</option>");
                for (var one in data) {
                    var orgId = data[one].corpOrgId;
                    var orgNm = data[one].corpOrgNm;
                    $("#capCorgNm").append("<option value='" + orgId + "'>" + orgId + " -- " + orgNm + "</option>");
                }
            }
        });
    }
    function examine(examinStatus) {
        var refuseReson = $("#refuseReson").val();
        if(refuseReson.length>100) {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkrefuseReson"), "error");
            return
        }
        var putdata = {
            "id" : $("#seqid").val(),
            "examinStatus": examinStatus,
            "remark" : refuseReson
        }
        $.ajax({
            contentType: 'application/json',
            url:"/urm/mermgr/examine/examine",
            data: JSON.stringify(putdata),
            dataType: "json",
            type:"post",
            success:function(data) {
                if (data.msgCd == "1") {
                    $("#handleModal").modal("hide");
                    $("#refuseModal").modal("hide");
                    if(data.msgInfo!=null && data.msgInfo!="") {
                        swal($.i18n.t("urm.mermgr.success"), data.msgInfo, "success");
                    } else {
                        swal($.i18n.t("urm.success"), $.i18n.t("urm.success"), "success");
                    }

                    table.ajax.reload();

                } else {
                    <!--隐藏模态框-->
                    //$("#handleModal").modal("hide");

                    swal($.i18n.t("urm.mermgr.failed"), data.msgInfo, "error");
                    table.ajax.reload();
                }
            },
            error: function() {
                //$("#handleModal").modal("hide");
                swal($.i18n.t("urm.mermgr.failed"),  "error");
            }
        });
    }
    function searchButton() {
        var nm = $("#searchMercNm").val();
        var sts = $("#searchExaminStatus").val();
        table.column(1).search(nm)
            .column(2).search(sts);
        table.draw();
    }
    //弹出审核窗口
    function examineMerChant() {

        var row = table.row('.selected');
        if(row.length == 0) {
            $("#handleModal").modal("hide");
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.shouldSelectOne"), "error");
            return;
        }
        var rowData =row.data();
        //重设置输入
        clear();
        <!--ajax异步调起后台服务，根据内部订单号查询明细-->
        var id = rowData["id"];
        var examinStatus = rowData["examinStatus"];
        if (examinStatus == '02') {
            $("#handleModal").modal("hide");
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.examinePassAdmit"), "error");
            return ;
        } else if(examinStatus == '01'){
            $("#handleModal").modal("hide");
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.examinePassAdmit"), "error");
            return ;
//            $("#resufeDiv").show();
//            $("#refuseResonShow").val( rowData["remark"]);
        } else {
            $("#resufeDiv").hide();
        }
        $("#seqid").val(id);
        var mercInfo = rowData["mercInfo"];
        var mercobj = JSON.parse(mercInfo);
        //展示数据
        putData(mercobj);
        disableData(mercInfo);

        $("#handleModal").modal("show");


    }

    //无效输入
    function disableData() {
        $("#mercForm input[type='text']").each(function(){
            $(this).attr("disabled","disabled");
        });
        $("#mercForm select").each(function(){
            $(this).attr("disabled","disabled");
        });
        $("#mercForm input[type='file']").each(function(){
            $(this).attr("disabled","disabled");
        });
    }
    //展示数据
    function putData(mercobj) {
        var merRegAddr = mercobj.merRegAddr;
        if (merRegAddr!= "" && merRegAddr != null) {
            $("#merRsgaddrS").val(merRegAddr.split("_")[0]);
            $("#detailAddr").val(merRegAddr.split("_")[1]);
        }
        $("#imercLogo").attr("src",mercobj.mercLogoPath);
        $("#ibusLicImg").attr("src",mercobj.busLicImgPath);
        $("#icertImg").attr("src",mercobj.cardImgPath);
        $("#icardImg").attr("src",mercobj.certImgPath);
        $("#icertImgB").attr("src",mercobj.cardImgPathB);
        $("#icardImgB").attr("src",mercobj.certImgPathB);
        $("#imerPotocolImg").attr("src",mercobj.merPotocolImgPath);
        $("#imercLogo").css("cursor","pointer");
        $("#ibusLicImg").css("cursor","pointer");
        $("#icertImg").css("cursor","pointer");
        $("#icertImgB").css("cursor","pointer");
        $("#icardImg").css("cursor","pointer");
        $("#icardImgB").css("cursor","pointer");
        $("#imerPotocolImg").css("cursor","pointer");
        $("#mercNm").val(mercobj.mercName);
        $("#mercShortName").val(mercobj.mercShortName);
        $("#comercReg").val(mercobj.comercReg);
        $("#mercTrdCls").val(mercobj.mercTrdCls);
        $("#cprTyp").val(mercobj.cprTyp);
        $("#mgtScp").val(mercobj.mgtScp);
      //  $("#merRsgaddr").val(mercobj.merRegAddr);
        //$("#merRsgaddrS").val(mercobj.merRegAddr);
        $("#merRsgaddrI").val("");
        $("#fixedaddrBut").val("");
        $("#term").val(mercobj.opnBusDtSr);
        $("#prinNm").val(mercobj.crpNm);
        $("#certTyp").val(mercobj.crpIdTyp);
        $("#certNo").val(mercobj.crpIdNo);
        $("#mangerNm").val(mercobj.diplayNm);
        $("#mngMoblie").val(mercobj.mblNo);
        $("#mngAcc").val(mercobj.loginId);
        $("#mngEmail").val(mercobj.email);
        $("#recMobile").val(mercobj.refereeMblNo);
        $("#merLvl").val(mercobj.merLvl);
        $("#openWechatMerflag").val(mercobj.openWechatMerflag);
        $("#capCardNo").val(mercobj.capCardNo);
        $("#capCardName").val(mercobj.capCardName);
        $("#capCorgNm").val(mercobj.capCorgNo);
        $("#subbranch").val(mercobj.subbranch);
        $("#belongMerc").val(mercobj.belongMerc);
        $("#settleType").val(mercobj.settleType);
        $("#settleCycleType").val(mercobj.settleCycleType);
        $("#drawDays").val(mercobj.drawDays);
        $("#settleEffDate").val(mercobj.settleEffDate);
        $("#settleExpDate").val(mercobj.settleExpDate);
        $("#settleSite").val(mercobj.settleSite);
        if  (mercobj.rateInfolist != null ) {
            for(var i =0 ; i < mercobj.rateInfolist.length ; i++) {
                var index = "";
                if ( mercobj.rateInfolist[i].busType == "1010") {
                    index = ""
                }
                if (mercobj.rateInfolist[i].busType == "0403") {
                    index = 1;
                }
                if (mercobj.rateInfolist[i].busType == "0201" && mercobj.rateInfolist[i].channel == "WeChat" ) {
                    index = 2;
                }
                if (mercobj.rateInfolist[i].busType == "0202" && mercobj.rateInfolist[i].channel == "WeChat" ) {
                    index = 3;
                }
                if (mercobj.rateInfolist[i].busType == "0201" && mercobj.rateInfolist[i].channel == "Alipay" ) {
                    index = 4;
                }
                if (mercobj.rateInfolist[i].busType == "0202" && mercobj.rateInfolist[i].channel == "Alipay" ) {
                    index = 5;
                }
                if (mercobj.rateInfolist[i].busType == "0201" && mercobj.rateInfolist[i].channel == "BESTPAY" ) {
                    index = 6;
                }
                if (mercobj.rateInfolist[i].busType == "0202" && mercobj.rateInfolist[i].channel == "BESTPAY" ) {
                    index = 7;
                }
                $("#busType" + index).val(mercobj.rateInfolist[i].busType);
                $("#chargeType" + index).val(mercobj.rateInfolist[i].chargeType);
                $("#calculateType" + index).val(mercobj.rateInfolist[i].calculateType);
                $("#beginCalAmt" + index).val(mercobj.rateInfolist[i].beginCalAmt);
                $("#rate" + index).val(mercobj.rateInfolist[i].rate);
                $("#minFee" + index).val(mercobj.rateInfolist[i].minFee);
                $("#maxFee" + index ).val(mercobj.rateInfolist[i].maxFee);
                $("#expDate" + index ).val(mercobj.rateInfolist[i].expDate);
                $("#effDate" + index ).val(mercobj.rateInfolist[i].effDate);
                $("#beginCalFee" + index ).val(mercobj.rateInfolist[i].beginCalFee);
                $("#calculateMod" + index ).val(mercobj.rateInfolist[i].calculateMod);

            }
        }
//        if(mercobj.rateInfolist[0] != null) {
//            $("#busType").val(mercobj.rateInfolist[0].busType);
//            $("#chargeType").val(mercobj.rateInfolist[0].chargeType);
//            $("#calculateType").val(mercobj.rateInfolist[0].calculateType);
//            $("#beginCalAmt").val(mercobj.rateInfolist[0].beginCalAmt);
//            $("#rate").val(mercobj.rateInfolist[0].rate);
//            $("#minFee").val(mercobj.rateInfolist[0].minFee);
//            $("#maxFee").val(mercobj.rateInfolist[0].maxFee);
//            $("#expDate").val(mercobj.rateInfolist[0].expDate);
//            $("#effDate").val(mercobj.rateInfolist[0].effDate);
//            $("#beginCalFee").val(mercobj.rateInfolist[0].beginCalFee);
//            $("#calculateMod").val(mercobj.rateInfolist[0].calculateMod);
//        }
//        if (mercobj.rateInfolist[1] != null) {
//            $("#busType1").val(mercobj.rateInfolist[1].busType);
//            $("#chargeType1").val(mercobj.rateInfolist[1].chargeType);
//            $("#calculateType1").val(mercobj.rateInfolist[1].calculateType);
//            $("#beginCalAmt1").val(mercobj.rateInfolist[1].beginCalAmt);
//            $("#rate1").val(mercobj.rateInfolist[1].rate);
//            $("#minFee1").val(mercobj.rateInfolist[1].minFee);
//            $("#maxFee1").val(mercobj.rateInfolist[1].maxFee);
//            $("#expDate1").val(mercobj.rateInfolist[1].expDate);
//            $("#effDate1").val(mercobj.rateInfolist[1].effDate);
//            $("#beginCalFee1").val(mercobj.rateInfolist[1].beginCalFee);
//            $("#calculateMod1").val(mercobj.rateInfolist[1].calculateMod);
//        }
    }
    function clear() {

        $("#imercLogo").attr("src","/img/uploadBut.png");
        $("#ibusLicImg").attr("src","/img/uploadBut.png");
        $("#icertImg").attr("src","/img/uploadBut.png");
        $("#icardImg").attr("src","/img/uploadBut.png");
        $("#icertImgB").attr("src","/img/uploadBut.png");
        $("#icardImgB").attr("src","/img/uploadBut.png");
        $("#imerPotocolImg").attr("src","/img/uploadBut.png");
        $("#seqid").val("");
        $("#mercNm").val("");
        $("#mercShortName").val("");
        $("#comercReg").val("");
        $("#mercTrdCls").val("7395");
        $("#cprTyp").val("01");
        $("#mgtScp").val("");
        $("#merRsgaddr").val("");
        $("#merRsgaddrS").val("");
        $("#merRsgaddrI").val("");
        $("#fixedaddrBut").val("");
        $("#term").val("");
        $("#prinNm").val("");
        $("#certTyp").val("0");
        $("#certNo").val("");
        $("#mangerNm").val("");
        $("#mngMoblie").val("");
        $("#mngAcc").val("");
        $("#mngEmail").val("");
        $("#recMobile").val("");
        $("#mercLogo").val("");
        $("#busLicImg").val("");
        $("#certImg").val("");
        $("#cardImg").val("");
        $("#merPotocolImg").val("");
        $("#capCardNo").val("");
        $("#capCardName").val("");
        $("#capCorgNm").val("ICBC");
        $("#subbranch").val("");
        $("#belongMerc").val("");
        $("#settleType").val("auto");
        $("#settleCycleType").val("daily");
        $("#settleEffDate").val();
        $("#settleExpDate").val();
        $("#drawDays").val("");
        $("#busType").val("1010");
        $("#chargeType").val("single");
        $("#calculateType").val("percent");
        $("#calculateMod").val("internal");
        $("#beginCalAmt").val("");
        $("#rate").val("");
        $("#minFee").val("");
        $("#maxFee").val("");
        $("#expDate").val("");
        $("#effDate").val("");
        $("#busType1").val("0403");
        $("#chargeType1").val("single");
        $("#calculateType1").val("percent");
        $("#calculateMod1").val("internal");
        $("#beginCalAmt1").val("");
        $("#rate1").val("");
        $("#minFee1").val("");
        $("#maxFee1").val("");
        $("#expDate1").val("");
        $("#effDate1").val("");
        $("#merLvl").val('1');
        $("#openWechatMerflag").val('open');
        $("#detailAddr").val("");
        $("#refuseReson").val("");

        for (var i = 2 ; i < 8 ; i ++ ) {
            var busType = "0201"
            if ( i % 2 != 0 ) {
                busType = "0202"
            }
            $("#busType" + i).val(busType);
            $("#busType" + i).attr("disabled",true);
            $("#chargeType"+ i).val("single");
            $("#calculateType"+ i).val("percent");
            $("#beginCalAmt"+ i).val("");
            $("#calculateMod"+ i).val("internal");
            $("#calculateMod"+ i).attr("disabled",true);
            $("#rate"+ i).val("");
            $("#minFee"+ i).val("");
            $("#minFee"+ i).attr("disabled",false);
            $("#maxFee"+ i).val("");
            $("#maxFee"+ i).attr("disabled",false);
            $("#expDate"+ i).val("");
            $("#effDate"+ i).val("");
        }
        mercLogoPath = '' ,busLicImgPath = '' ,certImgPath = '' , cardImgPath = '' ,merPotocolImgPath = '';
    }

    var expDate = $('#effDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });

    var expDate1 = $('#expDate1').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });
    var effDate1 = $('#effDate1').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });

    var settleEffDate = $('#settleEffDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });
    var settleExpDate = $('#settleExpDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });
    var term = $('#term').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });

    function initMerRatePage() {
        var html = $("#rateInfo").html();
        var busType = "" ;
        var busTypeDes = "" ;
        var channel = "Mpay";
        for (var i=0 ; i < 6 ; i++) {
            var index = i + 2 ;
            var rateIndex = index + 1;
            if (index % 2 == 0 ) {
                busType = "0201" ;
                busTypeDes = "data-i18n='urm.mermgr.busTypeEnum.barCode'";
            } else {
                busType = "0202" ;
                busTypeDes = "data-i18n='urm.mermgr.busTypeEnum.scan'";
            }
            if(index == 2 || index == 3 ) {
                channel = "WeChat" ;
            }
            if(index == 4 || index == 5 ) {
                channel = "Alipay" ;
            }
            if(index == 6 || index == 7 ) {
                channel = "BESTPAY" ;
            }
            html += "<div class='hr-line-dashed ' style='border-top:1px dashed #121415;' ></div>";
            html += "<div style='width: 1000px;high:20px ;margin-bottom : 10px'  data-i18n='urm.mermgr.rate" + rateIndex + "'> </div>";
            html += "<div class='form-group'>";
            html += "    <label class='col-sm-3 control-label' for='" + "busType" + index +"' data-i18n='urm.mermgr.busType' ></label>" ;
            html += "    <div class='col-sm-3'>" ;
            html += "        <select type='' class='form-control' name='busType" +index + "' id = 'busType" + index + "' >"
            html+=  "            <option value='" +busType + "'" + busTypeDes +"  ></option>";
            html+=  "        </select>";
            html+=  "    </div>";
            html += "    <label class='col-sm-3 control-label' for='" + "chargeType" + index +"' data-i18n='urm.mermgr.chargeType' ></label>" ;
            html += "    <div class='col-sm-3'>" ;
            html += "        <select type='' class='form-control' name='chargeType" +index + "' id = 'chargeType" + index + "' >"
            html+=  "             <option value='single' data-i18n='urm.mermgr.chargeTypeEnum.single' ></option>";
            html+=  "        </select>";
            html+=  "    </div>";
            html+=  "</div>";

            html += "<div class='form-group'>";
            html += "    <label class='col-sm-3 control-label' for='" + "calculateType" + index +"' data-i18n='urm.mermgr.calculateType' ></label>" ;
            html += "    <div class='col-sm-3'>" ;
            html += "        <select type='' class='form-control' name='calculateType" +index + "' id = 'calculateType" + index + "' >"
            html+=  "            <option value='percent' data-i18n='urm.mermgr.calculateTypeEnum.percent' ></option>";
            html+=  "            <option value='fixed' data-i18n='urm.mermgr.calculateTypeEnum.fixed' ></option>";
            html+=  "        </select>";
            html+=  "    </div>";
            html += "    <label class='col-sm-3 control-label' for='" + "rate" + index +"' ><span style='color: red'>*</span><span data-i18n='urm.mermgr.rate'></span></label>" ;
            html += "    <div class='col-sm-3'>" ;
            html += "        <input type='text' class='form-control' name='rate"+ index + "' required='required' id='rate" + index + "' >"
            html+=  "    </div>";
            html+=  "</div>";

            html += "<div class='form-group'>";
            html += "    <label class='col-sm-3 control-label' for='" + "minFee" + index +"' data-i18n='urm.mermgr.minFee' ></label>" ;
            html += "    <div class='col-sm-3'>" ;
            html += "        <input type='text' class='form-control' name='minFee"+ index + " 'required='required' id='minFee" + index + "' >"
            html+=  "    </div>";
            html += "    <label class='col-sm-3 control-label' for='" + "maxFee" + index +"' data-i18n='urm.mermgr.maxFee' ></label>" ;
            html += "    <div class='col-sm-3'>" ;
            html += "        <input type='text' class='form-control' name='maxFee"+ index + " 'required='required' id='maxFee" + index + "' >"
            html+=  "    </div>";
            html+=  "</div>";

            html += "<div class='form-group'>";
            html += "    <label class='col-sm-3 control-label' for='" + "effDate" + index +"' ><span style='color: red'>*</span><span data-i18n='urm.mermgr.effDate'></span></label>" ;
            html += "    <div class='col-sm-3'>" ;
            html += "        <input type='text' class='form-control' name='effDate"+ index + " 'required='required' id='effDate" + index + "' >"
            html+=  "    </div>";
            html += "    <label class='col-sm-3 control-label' for='" + "expDate" + index +"' ><span style='color: red'>*</span><span data-i18n='urm.mermgr.expDate'></span></label>" ;
            html += "    <div class='col-sm-3'>" ;
            html += "        <input type='text' class='form-control' name='expDate"+ index + "' required='required' id='expDate" + index + "' >"
            html+=  "    </div>";
            html+=  "</div>";

            html += "<div class='form-group'>";
            html += "    <label class='col-sm-3 control-label' for='beginCalFee"+ index + "' data-i18n='urm.mermgr.beginCalFee'></label>" ;
            html += "    <div class='col-sm-3'>" ;
            html += "        <input type='text' class='form-control' name='beginCalFee"+ index + "' required='required' id='beginCalFee" + index + "' >"
            html+=  "    </div>";
            html += "    <label class='col-sm-3 control-label' for='calculateMod" + index + "' data-i18n='urm.mermgr.calculateMod'></label>" ;
            html += "    <div class='col-sm-3'>" ;
            html += "        <select type='' class='form-control' name='calculateMod" +index + "' id = 'calculateMod" + index + "' >"
            html+=  "            <option value='internal' data-i18n='urm.mermgr.calculateModEnum.internal' ></option>";
            html+=  "            <option value='external' data-i18n='urm.mermgr.calculateModEnum.external' ></option>";
            html+=  "        </select>";
            html+=  "    </div>";
            html+=  "</div>";
            html += " <input id='channel" +index+  "' name='channel" +index+ "' value='" + channel+ "'  type='hidden'/>"
        }
        html += " <input id='channel' name='channel' value='Seatelpay'  type='hidden'/>"
        html += " <input id='channel1' name='channel1' value='Seatelpay'  type='hidden'/>"
        $("#rateInfo").html(html);
        for (var j=2 ; j < 8 ; j++ ) {
            $('#expDate' + j ).datetimepicker({
                lang:'en',
                timepicker:false,
                validateOnBlur: false,
                format:'Y-m-d ',
                formatDate:'Y-m-d',
                // maxDate:'+1970/01/01',
            });
            var effDate1 = $('#effDate' + j ).datetimepicker({
                lang:'en',
                timepicker:false,
                validateOnBlur: false,
                format:'Y-m-d ',
                formatDate:'Y-m-d',
                // maxDate:'+1970/01/01',
            });
        }
    }

</script>
</body>
</html>