<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title data-i18n="urm.mermgr.manage.title"></title>
    <div th:replace="head"></div>
    <style>
        div.floatright{
            display:inline;
            float:right;
            padding-left:20px;
            padding-right:20px;
        }

        ._right{
            display:inline;
            float:right;
            padding-right:15px;
        }
    </style>
</head>
<body>
<div id="wrapper">
    <div th:replace="nav"></div>
    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>
        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="urm.mermgr.manage.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.busmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.bussub.mermgr"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="urm.mermgr.manage.content"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <!--商户录入modal-->
        <div th:replace="urm/mermgr/merModal"></div>
        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">

                            <!-- 表头导航栏 -->
                            <div class="box-header">
                                <table style="width: 80%">
                                    <tr>
                                        <td align="center">
                                            <label class="control-label" data-i18n="urm.mermgr.mercId"></label>
                                        </td>
                                        <td>
                                            <input class="form-control" name="searchMercId" id = "searchMercId"/>
                                        </td>
                                        <td align="center">
                                            <label class="control-label" data-i18n="urm.mermgr.mercNm"></label>
                                        </td>
                                        <td>
                                            <input class="form-control" name="searchMercNm" id = "searchMercNm" />
                                        </td>
                                        <td align="center">
                                            <label class="control-label" data-i18n="urm.mermgr.mercSts"></label>
                                        </td>
                                        <td>
                                            <select class="form-control" name="searchMercSts" id = "searchMercSts">
                                                <option value="" data-i18n="urm.mermgr.passed"></option>
                                                <option value="0" data-i18n="urm.mermgr.normalAccount"></option>
                                                <option value="1" data-i18n="urm.mermgr.cancelAccount"></option>
                                                <option value="2" data-i18n="urm.mermgr.underReview"></option>
                                                <option value="3" data-i18n="urm.mermgr.reviewRejected"></option>
                                            </select>
                                        </td>
                                        <!--</tr>-->
                                        <!--<tr>-->
                                        <!--<td align="center">-->
                                        <!--<label class="control-label" data-i18n="urm.beginDt"></label>-->
                                        <!--</td>-->
                                        <!--<td>-->
                                        <!--<input class="form-control" name="searchBeginDt" id="beginTime"/>-->
                                        <!--</td>-->
                                        <!--<td align="center">-->
                                        <!--<label class="control-label" data-i18n="urm.endDt"></label>-->
                                        <!--</td>-->
                                        <!--<td>-->
                                        <!--<input class="form-control" name="searchEndDt" id="endTime"/>-->
                                        <!--</td>-->
                                        <td></td>
                                        <td>
                                            <button type="button" class="btn btn-w-m btn-primary _right" data-i18n="urm.search" onclick="searchButton()">
                                            </button>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <hr/>

                            <!-- 表格主体 -->
                            <div class="table-responsive">
                                <table id="example" class="table table-striped table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th data-i18n="urm.mermgr.mercId"></th>
                                        <th data-i18n="urm.mermgr.mercNm"></th>
                                        <th data-i18n="urm.mermgr.mercSts"></th>
                                        <th data-i18n="urm.mermgr.createTime"></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>
    </div>
</div>
<div th:replace="script"></div>
<script>
    var editor;
    var table;

    $(document).ready(function () {

        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        i18nLoad.then(function () {
            editor = new $.fn.dataTable.Editor({
                ajax: {
                    remove: {
                        type: 'POST',
                        url: '#',
                        contentType: 'application/json',
                        data: function (d) {
                            return JSON.stringify(d);
                        }
                    }
                },
                table: "#example",
                idSrc: 'mercId',
                fields: [
                    {name: "mercId", type: "hidden"},
                    {
                        label: $.i18n.t("urm.mermgr.mercSts"), name: "mercSts",
                        type:  "select",
                        options: [
                            { label: $.i18n.t("urm.mermgr.normalAccount"), value: "0" },
                            { label: $.i18n.t("urm.mermgr.cancelAccount"), value: "1" }
                        ]
                    }
                ],
                i18n: {
                    remove: {
                        button: $.i18n.t("demo.delete"), title: $.i18n.t("demo.delete"), submit: $.i18n.t("demo.delete"),
                        confirm: {
                            _: $.i18n.t("demo.multi-delete"),
                            1: $.i18n.t("demo.single-delete")
                        }
                    }
                }
            });

            table = $('#example').DataTable({
                dom: 'B<"floatright"l>rtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/urm/mermgr/info/findAll',
                    type: 'POST',
                    data: function (d) {
                        return JSON.stringify(d);
                    }
                },
                drawCallback: function(settings) {
                    table.button(1).disable();
//                    table.button(2).disable();
//                    table.button(3).disable();
                },
                serverSide: true,
                searchDelay: 1000,
                //responsive: true,
                processing: true,
                select: {
                    style:    'single',
                    selector: 'td:first-child'
                },
                language: {
                    url: languageUrl
                },
                buttons: [
                    {
                        text: $.i18n.t("urm.mermgr.add"),
                        action: function ( e, dt, node, config ) {
                            //按钮js
                           addMerChant() ;
                        }
                    }, {
                        text: $.i18n.t("urm.mermgr.edit"),
                        action: function ( e, dt, node, config ) {
                            //按钮js
                            modifyMerChant();
                        }
                    }
//                    , {
//                        extend: "remove", editor: editor
//                    }, {
//                        text: $.i18n.t("urm.mermgr.review"),
//                        action: function ( e, dt, node, config ) {
//                            //按钮js
//                        }
//                    }
                ],
                columns: [
                    {
                        data: 'mercId'
                    }, {
                        data: 'mercNm'
                    }, {
                        data: 'mercSts',
                        render: function (data, type, row) {
                            switch (data) {
                                case '0':
                                    return $.i18n.t("urm.mermgr.normalAccount");
                                case '1':
                                    return $.i18n.t("urm.mermgr.cancelAccount");
                                case '2':
                                    return $.i18n.t("urm.mermgr.underReview");
                                case '3':
                                    return $.i18n.t("urm.mermgr.reviewRejected");
                            }
                        }
                    }, {
                        data: 'createTime',
                        type: "datetime",
                        def: function () {
                            return new Date();
                        },
                        format: "YYYY-MM-DD HH:mm:ss"
                    }
                ],
                initComplete: function () {
                    table.button(1).disable();
//                    table.button(2).disable();
//                    table.button(3).disable();
                }
            });

            $('#example').on('select.dt deselect.dt', function() {
                table.buttons(1).enable(
                    table.rows({selected: true}).indexes().length === 0 ? false : true
                );
//                table.buttons(2).enable(
//                    table.rows({selected: true}).indexes().length === 0 ? false : true
//                );
//                table.buttons(3).enable(
//                    table.rows({selected: true}).indexes().length === 0 ? false : true
//                );
            })
        });

//        $.getJSON("/locales/zh.json",function(data){
//            var mercTrdCls = $("#mercTrdCls");
//            mercTrdCls.empty();//清空内容
//            var cls =data["cls"];
//            for(var key in cls){
//                strHtml +="<option value='"+cls[key]+"''></option>";
//            }
//
//            mercTrdCls.html(strHtml);
//        });


        
     // 初始化渠道费率页面
     initMerRatePage() ;


    $("imercLogo").click(function () {
            $("#mercLogo").click();
        });
        $("ibusLicImg").click(function () {
            $("#busLicImg").click();
        });
        $("icertImg").click(function () {
            $("#certImg").click();
        });
        $("icertImgB").click(function () {
            $("#certImgB").click();
        });
        $("icardImg").click(function () {
            $("#cardImg").click();
        });
        $("icardImgB").click(function () {
            $("#cardImgB").click();
        });
        $("imerPotocolImg").click(function () {
            $("#merPotocolImg").click();
        });
        if (window.FileReader) {
            $("#mercLogo").change(function() {
                var fileObj = document.getElementById("mercLogo").files[0];
                mercLogoPath = '' ;
                uploadImg("mercLogo" ,fileObj) ;
                //changeFile(this,"mercLogo","imercLogo");
            });
            $("#busLicImg").change(function() {
                busLicImgPath = '' ;
                //changeFile(this,"busLicImg","ibusLicImg");
                var fileObj = document.getElementById("busLicImg").files[0];
                uploadImg("busLicImg" ,fileObj) ;
            });
            $("#certImg").change(function() {
                certImgPath = '' ;
                //changeFile(this,"certImg","icertImg");
                var fileObj = document.getElementById("certImg").files[0];
                uploadImg("certImg" ,fileObj) ;
            });
            $("#certImgB").change(function() {
                certImgPath = '' ;
                //changeFile(this,"certImgB","icertImgB");
                var fileObj = document.getElementById("certImgB").files[0];
                uploadImg("certImgB" ,fileObj) ;
            });
            $("#cardImg").change(function() {
                cardImgPath = '' ;
                //changeFile(this,"cardImg","icardImg");
                var fileObj = document.getElementById("cardImg").files[0];
                uploadImg("cardImg" ,fileObj) ;
            });
            $("#cardImgB").change(function() {
                cardImgPath = '' ;
                //changeFile(this,"cardImgB","icardImgB");
                var fileObj = document.getElementById("cardImgB").files[0];
                uploadImg("cardImgB" ,fileObj) ;
            });
            $("#merPotocolImg").change(function() {
                merPotocolImgPath = '';
                //changeFile(this,"merPotocolImg","imerPotocolImg");
                var fileObj = document.getElementById("merPotocolImg").files[0];
                uploadImg("merPotocolImg" ,fileObj) ;
            });
        } else {
            //$inputImage.addClass("hide");
        }

        //添加
        $("#mermgrAdd").click(function () {
            mermgrAdd();
        });
        //修改
        $("#mercModify").click(function () {
            mermgrModify();
        });

        //结算地点选择点击事件
        $("#settleSite").change(function () {

            var settleSite = $("#settleSite").val();
            if (settleSite == '001') {
                $("#capCardNo").attr("disabled",false);
                $("#capCardName").attr("disabled",false);
                $("#capCorgNm").attr("disabled",false);
                $("#subbranch").attr("disabled",false);
                $("#settleCycleType").attr("disabled",false);
                $("#settleEffDate").attr("disabled",false);
                $("#settleExpDate").attr("disabled",false);
                $("#hallSites").attr("disabled",true);
                $("#settleType").attr("disabled",false);
                $("#settleType").val("auto");
            }else {
                $("#hallSites").attr("disabled",false);
                $("#capCardNo").attr("disabled",true);
                $("#capCardName").attr("disabled",true);
                $("#capCorgNm").attr("disabled",true);
                $("#subbranch").attr("disabled",true);
                $("#settleType").attr("disabled",true);
                $("#settleCycleType").attr("disabled",true);
                $("#settleType").val("hall");

            }
        });

        //计费方式选择事件
        $("#calculateType").change(function () {
             calculateTypeChange("calculateType","");
        });
        $("#calculateType1").change(function () {
             calculateTypeChange("calculateType","1");
        });
        $("#calculateType2").change(function () {
             calculateTypeChange("calculateType","2");
        });
        $("#calculateType3").change(function () {
             calculateTypeChange("calculateType","3");
        });
        $("#calculateType4").change(function () {
             calculateTypeChange("calculateType","4");
        });
        $("#calculateType5").change(function () {
             calculateTypeChange("calculateType","5");
        });
        $("#calculateType6").change(function () {
             calculateTypeChange("calculateType","6");
        });
        $("#calculateType7").change(function () {
             calculateTypeChange("calculateType","7");
        });

        //检查唯一性
        $("#mngAcc").blur(function () {
            if(!checkMerAcc()){
                return ;
            }
            $.ajax({
                url:"/urm/mermgr/info/checkMngAcc",
                dataType: "json",
                type:"post",
                data :{
                    "mngAcc": $("#mngAcc").val(),
                    "merId":   $("#mercId").val()
                },
                success:function(data) {
                    if (data.result != '1') {
                        swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.mngAccExist"), "error");
                        $("#mngAcc").val("");
                }
                },
                error: function() {
                    $("#mngAcc").val("");
                    swal($.i18n.t("urm.mermgr.failed"),  "error");
                }
            });
        });
        //检查手机号的合法性
        $("#mngMoblie").blur(function () {
            checkMobile($("#mngMoblie").val() ,"mngMoblie");
        });
        $("#recMobile").blur(function () {
            checkMobile($("#recMobile").val() , "recMobile");
        });

        //检查数据类型
        $("#comercReg").blur(function () {
            var comercReg = $("#comercReg").val() ;
            if (comercReg == "") {
                return ;
            }
            $.ajax({
                url:"/urm/mermgr/info/checkComercReg",
                dataType: "json",
                type:"post",
                data :{
                    "comercReg": $("#comercReg").val(),
                    "merId":   $("#mercId").val()
                },
                success:function(data) {
                    if (data.result != '1') {
                        swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.comercRegExist"), "error");
                        $("#comercReg").val("");
                    }
                },
                error: function() {
                    swal($.i18n.t("urm.mermgr.failed"),  "error");
                }
            });
        });
        //检查商户简称不能重复
        $("#mercShortName").blur(function () {
            var comercReg = $("#mercShortName").val() ;
            if (comercReg == "") {
                return ;
            }
            $.ajax({
                url:"/urm/mermgr/info/checkMercShortName",
                dataType: "json",
                type:"post",
                data :{
                    "mercShortName": $("#mercShortName").val(),
                    "merId":   $("#mercId").val()
                },
                success:function(data) {
                    if (data.result != '1') {
                        swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.mercShortNameExist"), "error");
                        $("#mercShortName").val("");
                    }
                },
                error: function() {
                    $("#mercShortName").val("");
                    swal($.i18n.t("urm.mermgr.failed"),  "error");
                }
            });
        });

        //初始化商户级别下拉选择框
        innitMerLevel();
        //初始化商银行列表下拉框
        innitBankSelect();

        //添加结算模式changeshijian
        $("#settleType").change(function () {
            changeSettleType();
        });

    })


    function changeSettleType() {
        var settleType =  $("#settleType").val();
        if (settleType == 'self') {
            $("#settleCycleType").attr("disabled",true);
        } else {
            $("#settleCycleType").attr("disabled",false);
        }
    }
    //初始化商户级别下拉选择框
    function innitMerLevel() {
        var html="";
        //html = "  <option value='7' >护手霜</option>";
        $.ajax({
            url:"/urm/mermgr/info/getMerLevel",
            dataType: "json",
            type:"post",
            data :{
            },
            success:function(data) {
                if (data.list != null) {
                    list = data.list;
                    for (var i=0 ; i< list.length ; i++ ) {
                        html += "<option value='" +list[i].parmVal + "'>" + list[i].parmDispNm + "</option>";
                        $("#merLvl").html(html);
                    }
                }
            },
            error: function() {
                swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.innitMerLevelFailed"), "error");
            }
        });

    }
    //初始化商户级别下拉选择框
    function innitBankSelect() {
        var html="";
        //查询所有的合作机构信息
        $.ajax({
            url:"/cpt/cpoorg/info/findAllOrgnInfo",
            dataType: "json",
            type:"post",
            success:function(data){
                $("#capCorgNm").append("<option value=''>" + $.i18n.t("cpo.selectByNull") + "</option>");
                for (var one in data)
                {
                    var orgId = data[one].corpOrgId;
                    var orgNm = data[one].corpOrgNm;
                    $("#capCorgNm").append("<option value='" + orgId + "'>" +orgId+" -- "+orgNm+ "</option>");
                }
            }
        });

    }
    //点击事件
    function calculateTypeChange(id , index) {
        var calculateType = $("#calculateType"+index ).val();
        if (calculateType == 'fixed') {
            $("#minFee"+index).attr("disabled",true);
            $("#maxFee"+index).attr("disabled",true);
            $("#minFee"+index).val("");
            $("#maxFee"+index).val("");

        } else {
            $("#minFee"+index).attr("disabled",false);
            $("#maxFee"+index).attr("disabled",false);
        }
    }
    //检查手机号的合法性
    function  checkMobile(mobile , id) {
        $.ajax({
            url:"/urm/mermgr/info/checkMobile",
            dataType: "json",
            type:"post",
            data :{
                "mobile": mobile,
            },
            success:function(data) {
                if (data.result != '1') {
                    swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkMoblieFormat"), "error");
                    $("#" + id).val("");
                }
            },
            error: function() {
                swal($.i18n.t("urm.mermgr.failed"),  "error");
                $("#" + id).val("");
            }
        });
    }
    //图片文件路径全局变量,做修改等操作前需要重制先
    var mercLogoPath = '' ,busLicImgPath = '' ,certImgPath = '' , cardImgPath = '' ,merPotocolImgPath = '';
    //上传文件
    function uploadImg(type , object ) {
        var formData = new FormData();
        if (/^image\/\w+$/.test(object.type) || /^image\/\w+$/.test(object.type)) {


            formData.append("file", object);
            formData.append("uploadFlag", "Y");
            $.ajax({
                url: "/file/upload",
                data: formData,
                type: 'POST',
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    if (data != '' && data.indexOf("html" ) <0) {
                        console.log(type + data)
                        if(type == 'mercLogo') {
                            $("#imercLogo").attr("src",data);

                        }else if ( type == "busLicImg") {
                            $("#ibusLicImg").attr("src",data);

                        } else if (type == "certImg") {
                            $("#icertImg").attr("src",data);

                        } else if (type == "certImgB") {
                            $("#icertImgB").attr("src",data);

                        } else if(type == "cardImg") {
                            $("#icardImg").attr("src",data);

                        } else if(type == "cardImgB") {
                            $("#icardImgB").attr("src",data);

                        }else if (type == "merPotocolImg"){
                            $("#imerPotocolImg").attr("src",data);
                        } else {
                             alert("not exist this type");
                        }
                    } else {
                        <!--隐藏模态框-->
                        swal($.i18n.t("activity.swal-fail"), $.i18n.t("activity.fileUploadfail"), "error");
                    }
                },
                error: function () {

                    swal($.i18n.t("activity.swal-fail"), "error");
                }
            });
        } else {
            alert("please choose the xls or xlsx file");
        }
        //    };
//        } else {
//            $("#userAppoint").val("");
//            alert("Please choose an image file.");
//        }
    }
    //file改变出发时间
//    function changeFile(object, buId ,imgId){
//        var fileReader = new FileReader(),
//            files = object.files,
//            file;
//        if (!files.length) {
//            return;
//        }
//        file = files[0];
//        if (/^image\/\w+$/.test(file.type)) {
//            fileReader.readAsDataURL(file);
//            //fileReader.readAsText(file, "UTF-8");
//            fileReader.onload = function () {
//                $("#"+buId).val("");
//                var base64String = this.result.split(",");
//                //上传图片
//             //   $("#"+imgId).attr("src",this.result);
//                uploadImg(file.name,file.type,base64String[1],buId);
//            };
//        } else {
//            alert("Please choose an image file.");
//        }
//    }
//    //上传图片
//    function uploadImg(name,imgType,imgStr ,type) {
//        $.ajax({
//            url:"/file/upload/base64Img",
//            data:{
//                "name": name,
//                "fileType":imgType,
//                "base64String":imgStr,
//                "uploadToFileServer" :"Y",
//                "type":type
//            },
//            dataType: "json",
//            type:"post",
//            success:function(data) {
//                if (data != null) {
//                   // mercLogoPath = '' ,busLicImgPath = '' ,certImgPath = '' , cardImgPath = '' ,merPotocolImgPath = '';
//                    if(type == 'mercLogo') {
//                        $("#imercLogo").attr("src",data.path);
//
//                    }else if ( type == "busLicImg") {
//                        $("#ibusLicImg").attr("src",data.path);
//
//                    } else if (type == "certImg") {
//                        $("#icertImg").attr("src",data.path);
//
//                    } else if (type == "certImgB") {
//                        $("#icertImgB").attr("src",data.path);
//
//                    } else if(type == "cardImg") {
//                        $("#icardImg").attr("src",data.path);
//
//                    } else if(type == "cardImgB") {
//                        $("#icardImgB").attr("src",data.path);
//
//                    }else if (type == "merPotocolImg"){
//                        $("#imerPotocolImg").attr("src",data.path);
//                    } else {
//                        alert("not exist this type");
//                    }
//
//                } else {
//                    <!--隐藏模态框-->
//                    swal($.i18n.t("activity.swal-fail"), $.i18n.t("activity.detailNull"), "error");
//                }
//            },
//            error: function() {
//                swal($.i18n.t("activity.swal-fail"),  "error");
//            }
//        });
//    }
    function searchButton() {
        var id = $("#searchMercId").val();
        var nm = $("#searchMercNm").val();
        var sts = $("#searchMercSts").val();
        table.column(0).search(id)
            .column(1).search(nm)
            .column(2).search(sts);
            table.draw();
    }
    //弹出录入窗口
    function addMerChant() {
        //重设置输入
        clear();
        $("#mermgrAdd").show();
        $("#mercModify").hide();
        $("#handleModal").modal("show");
        $("#resufeDiv").hide();
        $("#mngAcc").attr("disabled", false);
    }


    //弹出修改窗口
    function modifyMerChant() {
        //重设置输入
        $("#mermgrAdd").hide();
        $("#mercModify").show();
        clear();

        $("#mngAcc").attr("disabled", true);

        //获取修改数据 根据如果商户状态为未审核则数据从审批流水表中获取，如果商户状态为审核通过，则从商户信息表中获取
        var row = table.row('.selected');
        if(row.length == 0) {
            $("#handleModal").modal("hide");
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.shouldSelectOne"), "error");
            return;
        }
        var rowData =row.data();
        var mercId = rowData["mercId"];
        var mercSts = rowData["mercSts"];
        $("#mercId").val(mercId);
        $("#mercSts").val(mercSts);
        var putdata = {
            "mercId" : mercId,
            "mercSts" : mercSts
        }
        if(mercSts == '3') {
            $("#resufeDiv").show();
           // $("#resufeDiv").removeAttr("hidden");

        } else {
            $("#resufeDiv").hide();
        }
        <!--ajax异步调起后台服务，用户号查询商户信息-->
        $.ajax({
            url:"/urm/mermgr/info/modifyView",
            data: putdata,
            dataType: "json",
            type:"post",
            success:function(data) {
                if (data != null) {
                    putData(data);
                    var settleSite = $("#settleSite").val();
                    if (settleSite != '001') {
                        $("#hallSites").attr("disabled", false);
                        $("#capCardNo").attr("disabled", true);
                        $("#capCardName").attr("disabled", true);
                        $("#capCorgNm").attr("disabled", true);
                        $("#subbranch").attr("disabled", true);
                        $("#settleType").attr("disabled", true);
                        $("#settleCycleType").attr("disabled", true);
                        $("#settleType").val("hall");
                    }

                    for (var i = 0 ; i < 8 ; i++ ) {
                        var index = i ;
                        if ( i == 0 ) {
                            index ="";
                        }
                        calculateTypeChange("calculateType" ,index);
                    }
                    $("#handleModal").modal("show");
                } else {
                    <!--隐藏模态框-->
                    $("#handleModal").modal("hide");
                    swal($.i18n.t("urm.mermgr.failed"), data.msgInfo, "error");
                }
            },
            error: function() {
                $("#handleModal").modal("hide");
                swal($.i18n.t("urm.mermgr.failed"),  "error");
            }
        });
    }

    //控制重复提交参数
    var repeat = false ;
    //修改商户
    function mermgrModify() {
        if(!check()) {
            return ;
        }

        //控制重复提交
        if (repeat) {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.repeat"), "error");
        } else {
            repeat = true
        }
        var putdata = getData();
        $.ajax({
            contentType: 'application/json',
            url:"/urm/mermgr/info/modify",
            data: JSON.stringify(putdata),
            dataType: "json",
            type:"post",
            success:function(data) {
                if (data.msgCd == "1") {
                    repeat = false
                    $("#handleModal").modal("hide");
                    if(data.msgInfo!=null && data.msgInfo!="") {
                        swal($.i18n.t("urm.mermgr.success"), data.msgInfo, "success");
                    } else {
                        swal($.i18n.t("urm.mermgr.success"), $.i18n.t("urm.mermgr.success"), "success");
                    }

                } else {
                    repeat = false
                    <!--隐藏模态框-->
                    //$("#handleModal").modal("hide");
                    swal($.i18n.t("urm.mermgr.failed"), data.msgInfo, "error");
                }
            },
            error: function() {
                repeat = false
                //$("#handleModal").modal("hide");
                swal($.i18n.t("urm.mermgr.failed"),  "error");
            }
        });
    }
    //添加商户
    function mermgrAdd() {
        if(!check()) {
            return ;
        }
        var putdata = getData();
        $.ajax({
            contentType: 'application/json',
            url:"/urm/mermgr/info/merRegister",
            data: JSON.stringify(putdata),
            dataType: "json",
            type:"post",
            success:function(data) {
                if (data.msgCd == "1") {
                    $("#handleModal").modal("hide");
                    swal($.i18n.t("urm.mermgr.success"), $.i18n.t("urm.mermgr.success"), "success");

                } else {
                    <!--隐藏模态框-->
                    //$("#handleModal").modal("hide");
                    swal($.i18n.t("urm.mermgr.failed"), data.msgInfo, "error");
                }
            },
            error: function() {
                //$("#handleModal").modal("hide");
                swal($.i18n.t("urm.mermgr.failed"),  "error");
            }
        });
    }

    $("#qrcodeDiv").hide();
    //展示数据
    function putData(mercobj) {

        var merRegAddr = mercobj.merRegAddr;
        if (merRegAddr!= "" && merRegAddr != null) {
            $("#merRsgaddrS").val(merRegAddr.split("_")[0]);
            $("#detailAddr").val(merRegAddr.split("_")[1]);
        }
        $("#imercLogo").attr("src",mercobj.mercLogoPath);
        $("#ibusLicImg").attr("src",mercobj.busLicImgPath);
        $("#icertImg").attr("src",mercobj.cardImgPath);
        $("#icertImgB").attr("src",mercobj.cardImgPathB);
        $("#icardImg").attr("src",mercobj.certImgPath);
        $("#icardImgB").attr("src",mercobj.certImgPathB);
        $("#imerPotocolImg").attr("src",mercobj.merPotocolImgPath);
        $("#mercNm").val(mercobj.mercName);
        $("#mercShortName").val(mercobj.mercShortName);
        $("#refuseResonShow").val(mercobj.refuseReson);
        $("#comercReg").val(mercobj.comercReg);
        $("#openWechatMerflag").val(mercobj.openWechatMerflag);
        $("#mercTrdCls").val(mercobj.mercTrdCls);
        $("#cprTyp").val(mercobj.cprTyp);
        $("#mgtScp").val(mercobj.mgtScp);
        $("#merRsgaddr").val(mercobj.merRegAddr);
        $("#merRsgaddrI").val("");
        $("#fixedaddrBut").val("");
        $("#prinNm").val(mercobj.crpNm);
        $("#term").val(mercobj.opnBusDtSr);
        $("#certTyp").val(mercobj.crpIdTyp);
        $("#certNo").val(mercobj.crpIdNo);
        $("#mangerNm").val(mercobj.diplayNm);
        $("#mngMoblie").val(mercobj.mblNo);
        $("#mngAcc").val(mercobj.loginId);
        $("#mngEmail").val(mercobj.email);
        $("#recMobile").val(mercobj.refereeMblNo);
        $("#merLvl").val(mercobj.merLvl);
        $("#capCardNo").val(mercobj.capCardNo);
        $("#capCardName").val(mercobj.capCardName);
        $("#capCorgNm").val(mercobj.capCorgNo);
        $("#subbranch").val(mercobj.subbranch);
        $("#belongMerc").val(mercobj.belongMerc);
        $("#settleType").val(mercobj.settleType);
        $("#settleCycleType").val(mercobj.settleCycleType);
        $("#settleEffDate").val(mercobj.settleEffDate);
        $("#settleExpDate").val(mercobj.settleExpDate);
        $("#drawDays").val(mercobj.drawDays);
        $("#settleSite").val(mercobj.settleSite);
        $("#hallSites").val(mercobj.hallSites);
        if  (mercobj.rateInfolist != null ) {
            for(var i =0 ; i < mercobj.rateInfolist.length ; i++) {
                var index = "";
                if ( mercobj.rateInfolist[i].busType == "1010") {
                    console.log("1010")
                    index = ""
                }
                if (mercobj.rateInfolist[i].busType == "0403") {
                    index = 1;
                }
                if (mercobj.rateInfolist[i].busType == "0201" && mercobj.rateInfolist[i].channel == "WeChat" ) {
                    index = 2;
                }
                if (mercobj.rateInfolist[i].busType == "0202" && mercobj.rateInfolist[i].channel == "WeChat" ) {
                    index = 3;
                }
                if (mercobj.rateInfolist[i].busType == "0201" && mercobj.rateInfolist[i].channel == "Alipay" ) {
                    index = 4;
                }
                if (mercobj.rateInfolist[i].busType == "0202" && mercobj.rateInfolist[i].channel == "Alipay" ) {
                    index = 5;
                }
                if (mercobj.rateInfolist[i].busType == "0201" && mercobj.rateInfolist[i].channel == "BESTPAY" ) {
                    index = 6;
                }
                if (mercobj.rateInfolist[i].busType == "0202" && mercobj.rateInfolist[i].channel == "BESTPAY" ) {
                    index = 7;
                }

                $("#busType" + index).val(mercobj.rateInfolist[i].busType);
                console.info("sss"+index+ $("#busType" + index).val());
                $("#chargeType" + index).val(mercobj.rateInfolist[i].chargeType);
                $("#calculateType" + index).val(mercobj.rateInfolist[i].calculateType);
                $("#beginCalAmt" + index).val(mercobj.rateInfolist[i].beginCalAmt);
                $("#rate" + index).val(mercobj.rateInfolist[i].rate);
                $("#minFee" + index).val(mercobj.rateInfolist[i].minFee);
                $("#maxFee" + index ).val(mercobj.rateInfolist[i].maxFee);
                $("#expDate" + index ).val(mercobj.rateInfolist[i].expDate);
                $("#effDate" + index ).val(mercobj.rateInfolist[i].effDate);
                $("#beginCalFee" + index ).val(mercobj.rateInfolist[i].beginCalFee);
                $("#calculateMod" + index ).val(mercobj.rateInfolist[i].calculateMod);

            }
        }
//        if(mercobj.rateInfolist[0] != null) {
//            $("#busType").val(mercobj.rateInfolist[0].busType);
//            $("#chargeType").val(mercobj.rateInfolist[0].chargeType);
//            $("#calculateType").val(mercobj.rateInfolist[0].calculateType);
//            $("#beginCalAmt").val(mercobj.rateInfolist[0].beginCalAmt);
//            $("#rate").val(mercobj.rateInfolist[0].rate);
//            $("#minFee").val(mercobj.rateInfolist[0].minFee);
//            $("#maxFee").val(mercobj.rateInfolist[0].maxFee);
//            $("#expDate").val(mercobj.rateInfolist[0].expDate);
//            $("#effDate").val(mercobj.rateInfolist[0].effDate);
//            $("#beginCalFee").val(mercobj.rateInfolist[0].beginCalFee);
//            $("#calculateMod").val(mercobj.rateInfolist[0].calculateMod);
//        }
//        if (mercobj.rateInfolist[1] != null) {
//            $("#busType1").val(mercobj.rateInfolist[1].busType);
//            $("#chargeType1").val(mercobj.rateInfolist[1].chargeType);
//            $("#calculateType1").val(mercobj.rateInfolist[1].calculateType);
//            $("#beginCalAmt1").val(mercobj.rateInfolist[1].beginCalAmt);
//            $("#rate1").val(mercobj.rateInfolist[1].rate);
//            $("#minFee1").val(mercobj.rateInfolist[1].minFee);
//            $("#maxFee1").val(mercobj.rateInfolist[1].maxFee);
//            $("#expDate1").val(mercobj.rateInfolist[1].expDate);
//            $("#effDate1").val(mercobj.rateInfolist[1].effDate);
//            $("#beginCalFee1").val(mercobj.rateInfolist[1].beginCalFee);
//            $("#calculateMod1").val(mercobj.rateInfolist[1].calculateMod);
//
//        }
        //判断下拉框的选项
        changeSettleType();
    }
    function getData() {
        var capCorgNm = "corg." +  $("#capCorgNm").val()
        capCorgNm = $.i18n.t(capCorgNm);
        var merRegAddr =  $("#merRsgaddrS").val()+"_"+ $("#detailAddr").val();
        var rateInfolist=[];
        for (var i = 0 ; i<8 ; i++ ) {
            if (i == 0) {
                rateInfolist = [ {
                    "busType":$("#busType").val(),
                    "chargeType":$("#chargeType").val(),
                    "calculateType":$("#calculateType").val(),
                    "beginCalAmt":$("#beginCalAmt").val(),
                    "rate":$("#rate").val(),
                    "minFee":$("#minFee").val(),
                    "maxFee":$("#maxFee").val(),
                    "expDate":$("#expDate").val(),
                    "effDate":$("#effDate").val(),
                    "beginCalFee":$("#beginCalFee").val(),
                    "calculateMod":$("#calculateMod").val(),
                    "channel":$("#channel").val(),
                }];
            } else {
                rateInfolist.push(
                 {
                    "busType":$("#busType" +i).val(),
                    "chargeType":$("#chargeType" +i).val(),
                    "calculateType":$("#calculateType" + i).val(),
                    "beginCalAmt":$("#beginCalAmt" + i).val(),
                    "rate":$("#rate"+i).val(),
                    "minFee":$("#minFee" + i).val(),
                    "maxFee":$("#maxFee" + i).val(),
                    "expDate":$("#expDate" + i).val(),
                    "effDate":$("#effDate" + i).val(),
                    "beginCalFee":$("#beginCalFee" + i).val(),
                    "calculateMod":$("#calculateMod" + i).val(),
                    "channel":$("#channel" + i).val(),
                 }
                );
            }
        }
        var data = {
            "userId": $("#mercId").val(),
            "mercSts": $("#mercSts").val(),
            "mercName": $("#mercNm").val(),
             "mercShortName": $("#mercShortName").val(),
            "comercReg": $("#comercReg").val(),
            "mercTrdCls": $("#mercTrdCls").val(),
            "cprTyp": $("#cprTyp").val(),
            "opnBusDtSr": $("#term").val(),
            "mgtScp": $("#mgtScp").val(),
            "merRegAddr": merRegAddr,
            "crpNm": $("#prinNm").val(),
            "crpIdTyp": $("#certTyp").val(),
            "crpIdNo": $("#certNo").val(),
            "diplayNm": $("#mangerNm").val(),
            "openWechatMerflag": $("#openWechatMerflag").val(),
            "mblNo": $("#mngMoblie").val(),
            "loginId": $("#mngAcc").val(),
            "email": $("#mngEmail").val(),
            "refereeMblNo": $("#recMobile").val(),
            "merLvl": $("#merLvl").val(),
            "capCardNo": $("#capCardNo").val(),
            "capCardName": $("#capCardName").val(),
            "capCorgNo": $("#capCorgNm").val(),
            "capCorgNm": capCorgNm,
            "subbranch": $("#subbranch").val(),
            "belongMerc": $("#belongMerc").val(),
            "settleType": $("#settleType").val(),
            "settleCycleType": $("#settleCycleType").val(),
            "drawDays": $("#drawDays").val(),
            "hallSites": $("#hallSites").val(),
            "settleSite": $("#settleSite").val(),
            "settleEffDate": $("#settleEffDate").val(),
            "settleExpDate": $("#settleExpDate").val(),
            "mercLogoPath":  $("#imercLogo").attr("src"),
            "busLicImgPath":  $("#ibusLicImg").attr("src"),
            "certImgPath": $("#icertImg").attr("src"),
            "certImgPathB": $("#icertImgB").attr("src"),
            "cardImgPath": $("#icardImg").attr("src"),
            "cardImgPathB": $("#icardImgB").attr("src"),
            "merPotocolImgPath":  $("#imerPotocolImg").attr("src"),
            "rateInfolist" : rateInfolist
        };
        return data ;
    }
    function clear() {
        $("#imercLogo").attr("src","/img/uploadBut.png");
        $("#ibusLicImg").attr("src","/img/uploadBut.png");
        $("#icertImg").attr("src","/img/uploadBut.png");
        $("#icardImg").attr("src","/img/uploadBut.png");
        $("#icertImgB").attr("src","/img/uploadBut.png");
        $("#icardImgB").attr("src","/img/uploadBut.png");
        $("#imerPotocolImg").attr("src","/img/uploadBut.png");
        $("#refuseResonShow").val("");
        $("#mercNm").val("");
        $("#mercShortName").val("");
        $("#comercReg").val("");
        $("#mercTrdCls").val("7395");
        $("#cprTyp").val("01");
        $("#mgtScp").val("");
        $("#merRsgaddr").val("");
        $("#merRsgaddrS").val("baiLin");
        $("#merRsgaddrI").val("");
        $("#fixedaddrBut").val("");
        $("#term").val("");
        $("#prinNm").val("");
        $("#certTyp").val("0");
        $("#certNo").val("");
        $("#mangerNm").val("");
        $("#mngMoblie").val("");
        $("#mngAcc").val("");
        $("#mngEmail").val("");
        $("#recMobile").val("");
        $("#mercLogo").val("");
        $("#busLicImg").val("");
        $("#certImg").val("");
        $("#cardImg").val("");
        $("#merPotocolImg").val("");
        $("#capCardNo").val("");
        $("#capCardName").val("");
        $("#capCorgNm").val("ICBC");
        $("#subbranch").val("");
        $("#belongMerc").val("");
        $("#settleType").val("auto");
        $("#settleCycleType").val("daily");
        $("#settleEffDate").val();
        $("#settleExpDate").val();
        $("#drawDays").val("");
        $("#settleSite").val("001");
        $("#hallSites").val("no");

        $("#busType").val("1010");
        $("#chargeType").val("single");
        $("#calculateType").val("percent");
        $("#beginCalAmt").val("");
        $("#calculateMod").val("internal");
        $("#calculateMod").attr("disabled",true);
        $("#rate").val("");
        $("#minFee").val("");
        $("#minFee").attr("disabled",false);
        $("#maxFee").val("");
        $("#maxFee").attr("disabled",false);
        $("#expDate").val("");
        $("#effDate").val("");

        $("#busType1").val("0403");
        $("#chargeType1").val("single");
        $("#calculateType1").val("percent");
        $("#calculateMod1").val("internal");
        $("#calculateMod1").attr("disabled",true);
        $("#beginCalAmt1").val("");
        $("#rate1").val("");
        $("#minFee1").val("");
        $("#minFee1").attr("disabled",false);
        $("#maxFee1").val("");
        $("#maxFee1").attr("disabled",false);
        $("#expDate1").val("");
        $("#effDate1").val("");

        for (var i = 2 ; i < 8 ; i ++ ) {
            var busType = "0201"
            if ( i % 2 != 0 ) {
                busType = "0202"
            }
            $("#busType" + i).val(busType);
            $("#busType" + i).attr("disabled",true);
            $("#chargeType"+ i).val("single");
            $("#calculateType"+ i).val("percent");
            $("#beginCalAmt"+ i).val("");
            $("#calculateMod"+ i).val("internal");
            $("#calculateMod"+ i).attr("disabled",true);
            $("#rate"+ i).val("");
            $("#minFee"+ i).val("");
            $("#minFee"+ i).attr("disabled",false);
            $("#maxFee"+ i).val("");
            $("#maxFee"+ i).attr("disabled",false);
            $("#expDate"+ i).val("");
            $("#effDate"+ i).val("");
        }
        $("#mercId").val("");
        $("#merLvl").val("1");
        $("#openWechatMerflag").val("open");
        $("#detailAddr").val("");

        $("#refuseResonShow").attr("disabled",true);
        $("#capCardNo").attr("disabled",false);
        $("#capCardName").attr("disabled",false);
        $("#capCorgNm").attr("disabled",false);
        $("#subbranch").attr("disabled",false);
        $("#settleType").attr("disabled",false);
        $("#settleCycleType").attr("disabled",false);

        mercLogoPath = '' ,busLicImgPath = '' ,certImgPath = '' , cardImgPath = '' ,merPotocolImgPath = '';
    }

    //校验商户输入必须为字母和数字
    function checkMerAcc() {
        var mngAcc = $("#mngAcc").val();
        if(mngAcc.replace(/[a-zA-Z]/,"").length==mngAcc.length) {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkMngAcc"), "error");
            $("#mngAcc").val("");
            return false;
        }
        if(mngAcc.replace(/[0-9]/,"").length==mngAcc.length) {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkMngAcc"), "error");
            $("#mngAcc").val("");
            return false;
        }
        return true;
    }

    //检查数据
    function check() {
        //商户信息校验
        var mercNm = $("#mercNm").val();
        if (mercNm == '') {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkMercNm"), "error");
            return false;
        }
        var mercShortName = $("#mercShortName").val();
        if (mercShortName == '') {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkMercShortName"), "error");
            return false;
        } else {
//            //商户简称必须为英文
//            var str = /^[A-Za-z]*$/;
//            if (!str.test(mercShortName)) {
//                swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.testMercShortName"), "error");
//                return false;
//            }

        }
        var comercReg = $("#comercReg").val();
        if (comercReg == '') {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkComercReg"), "error");
            return false;
        }
        var prinNm = $("#prinNm").val();
        if (prinNm == '') {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkPrinNm"), "error");
            return false;
        }
        var certNo = $("#certNo").val();
        if (certNo == '') {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkCertNo"), "error");
            return false;
        }
        var mangerNm = $("#mangerNm").val();
        if (mangerNm == '') {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkMangerNm"), "error");
            return false;
        }
        var mngMoblie = $("#mngMoblie").val();
        if (mngMoblie == '') {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkMngMoblie"), "error");
            return false;
        }
        var mngEmail = $("#mngEmail").val();
        if (mngEmail == '') {
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkMngEmail"), "error");
            return false;
        } else {
            var patrn2=/[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/;
            if (!patrn2.exec(mngEmail) ) {
                swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkMngEmailFormat"), "error");
                return false
            }

        }

        //校验商户账号的格式化
        if(!checkMerAcc()) {
            return false;
        }

        //商户结算信息校验
        var settleSite = $("#settleSite").val();
        if (settleSite == '001') {
            var settleType = $("#settleType").val();
            if (settleType == 'hall') {
                swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkSettleType"), "error");
                return false;
            }
            var capCardNo = $("#capCardNo").val();
            if (capCardNo == ''){
                swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkCapCardNo"), "error");
                return false;
            }
            var capCardName = $("#capCardName").val();
            if (capCardName == ''){
                swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkCapCardName"), "error");
                return false;
            }
        }
        var settleEffDate = $("#settleEffDate").val();
        if (settleEffDate == ''){
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkSettleEffDate"), "error");
            return false;
        }
        var settleExpDate = $("#settleExpDate").val();
        if (settleExpDate == ''){
            swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkSettleExpDate"), "error");
            return false;
        }

        var drawDays = $("#drawDays").val();
        if (drawDays != ''){
            if (drawDays.replace(/[0-9]/g,"").length !=0 ) {
                swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkDrawDays"), "error");
                return false;
            }
        } else {
            //初始化
            $("#drawDays").val("0");
        }
        //费率信息校验
        for (var i = 0 ; i< 8 ; i++) {
            var index = i ;
            if ( index == 0 ) {
                index = "";
            }
            var rate = $("#rate" +index).val();
            if (rate == '' ) {
                swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkRate"), "error");
                return false;
            }

            var expDate = $("#expDate" +index).val();
            if (expDate == '' ) {
                swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkSettleExpDate"), "error");
                return false;
            }
            var effDate = $("#effDate" +index).val();
            if (effDate == '' || effDate1 == '') {
                swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkSettleEffDate"), "error");
                return false;
            }
            var calculateType = $("#calculateType"  +index ).val();
            var rate = $("#rate"  +index).val();
            if (calculateType == 'percent') {
                if (rate >= 100) {
                    swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.rateCheck"), "error");
                    $("#rate").val("");
                    return false;
                }
            }
        }

        /* 对字段长度进行控制 */
        // 商户名称
      	var mercNm = $("#mercNm").val();
        if (mercNm.length > 100) {
             swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkMercNmLength"), "error");
            return false;
        }
        // 商户简称
        var mercNm = $("#mercShortName").val();
        if (mercNm.length > 100) {
             swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.mercShortNameLength"), "error");
            return false;
        }
        // 工商注册号
        var mercNm = $("#comercReg").val();
        if (mercNm.length > 32) {
             swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.comercRegLength"), "error");
            return false;
        }
        // 经营范围
        var mercNm = $("#mgtScp").val();
        if (mercNm.length > 200) {
             swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.mgtScpLength"), "error");
            return false;
        }
    	// 法人姓名
        var mercNm = $("#prinNm").val();
        if (mercNm.length > 64) {
             swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.prinNmLength"), "error");
            return false;
        }
   	 	// 证件号
        var mercNm = $("#certNo").val();
        if (mercNm.length > 25) {
             swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.certNoLength"), "error");
            return false;
        }
    	// 管理员姓名
        var mercNm = $("#mangerNm").val();
        if (mercNm.length > 64) {
             swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.mangerNmLength"), "error");
            return false;
        }
    	// 管理员手机号
        var mercNm = $("#mngMoblie").val();
        if (mercNm.length > 20) {
             swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.mngMoblieLength"), "error");
            return false;
        }
    	// 管理员账号
        var mercNm = $("#mngAcc").val();
        if (mercNm.length > 20) {
             swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.mngAccLength"), "error");
            return false;
        }
   		// 管理员邮箱
        var mercNm = $("#mngEmail").val();
        if (mercNm.length > 128) {
             swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.mngEmailLength"), "error");
            return false;
        }
    	// 推荐人手机号
        var mercNm = $("#recMobile").val();
        if (mercNm.length > 20) {
             swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.recMobileLength"), "error");
            return false;
        }

    	// 备注
/*         var mercNm = $("#prinNm").val();
        if (mercNm.length > 20) {
             swal($.i18n.t("urm.mermgr.failed"), $.i18n.t("urm.mermgr.checkMercNmLength"), "error");
            return false;
        } */

        return true;
    }



    var settleEffDate = $('#settleEffDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });
    var settleExpDate = $('#settleExpDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });

    var term = $('#term').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });

    function initMerRatePage() {
        var html = $("#rateInfo").html();
        var busType = "" ;
        var busTypeDes = "" ;
        var channel = "Mpay";
            for (var i=0 ; i < 6 ; i++) {
            var index = i + 2 ;
            var rateIndex = index + 1;
            if (index % 2 == 0 ) {
                busType = "0201" ;
                busTypeDes = "data-i18n='urm.mermgr.busTypeEnum.barCode'";
            } else {
                busType = "0202" ;
                busTypeDes = "data-i18n='urm.mermgr.busTypeEnum.scan'";
            }
            if(index == 2 || index == 3 ) {
                channel = "WeChat" ;
            }
            if(index == 4 || index == 5 ) {
                channel = "Alipay" ;
            }
            if(index == 6 || index == 7 ) {
                channel = "BESTPAY" ;
            }
            html += "<div class='hr-line-dashed ' style='border-top:1px dashed #121415;' ></div>";
            html += "<div style='width: 1000px;high:20px ;margin-bottom : 10px'  data-i18n='urm.mermgr.rate" + rateIndex + "'> </div>";
            html += "<div class='form-group'>";
            html += "    <label class='col-sm-3 control-label' for='" + "busType" + index +"' data-i18n='urm.mermgr.busType' ></label>" ;
            html += "    <div class='col-sm-3'>" ;
            html += "        <select type='' class='form-control' name='busType" +index + "' id = 'busType" + index + "' >"
            html+=  "            <option value='" +busType + "'" + busTypeDes +"  ></option>";
            html+=  "        </select>";
            html+=  "    </div>";
            html += "    <label class='col-sm-3 control-label' for='" + "chargeType" + index +"' data-i18n='urm.mermgr.chargeType' ></label>" ;
            html += "    <div class='col-sm-3'>" ;
            html += "        <select type='' class='form-control' name='chargeType" +index + "' id = 'chargeType" + index + "' >"
            html+=  "             <option value='single' data-i18n='urm.mermgr.chargeTypeEnum.single' ></option>";
            html+=  "        </select>";
            html+=  "    </div>";
            html+=  "</div>";

            html += "<div class='form-group'>";
            html += "    <label class='col-sm-3 control-label' for='" + "calculateType" + index +"' data-i18n='urm.mermgr.calculateType' ></label>" ;
            html += "    <div class='col-sm-3'>" ;
            html += "        <select type='' class='form-control' name='calculateType" +index + "' id = 'calculateType" + index + "' >"
            html+=  "            <option value='percent' data-i18n='urm.mermgr.calculateTypeEnum.percent' ></option>";
            html+=  "            <option value='fixed' data-i18n='urm.mermgr.calculateTypeEnum.fixed' ></option>";
            html+=  "        </select>";
            html+=  "    </div>";
            html += "    <label class='col-sm-3 control-label' for='" + "rate" + index +"' ><span style='color: red'>*</span><span data-i18n='urm.mermgr.rate'></span></label>" ;
            html += "    <div class='col-sm-3'>" ;
            html += "        <input type='text' class='form-control' name='rate"+ index + "' required='required' id='rate" + index + "' >"
            html+=  "    </div>";
            html+=  "</div>";

            html += "<div class='form-group'>";
            html += "    <label class='col-sm-3 control-label' for='" + "minFee" + index +"' data-i18n='urm.mermgr.minFee' ></label>" ;
            html += "    <div class='col-sm-3'>" ;
            html += "        <input type='text' class='form-control' name='minFee"+ index + " 'required='required' id='minFee" + index + "' >"
            html+=  "    </div>";
            html += "    <label class='col-sm-3 control-label' for='" + "maxFee" + index +"' data-i18n='urm.mermgr.maxFee' ></label>" ;
            html += "    <div class='col-sm-3'>" ;
            html += "        <input type='text' class='form-control' name='maxFee"+ index + " 'required='required' id='maxFee" + index + "' >"
            html+=  "    </div>";
            html+=  "</div>";

            html += "<div class='form-group'>";
            html += "    <label class='col-sm-3 control-label' for='" + "effDate" + index +"' ><span style='color: red'>*</span><span data-i18n='urm.mermgr.effDate'></span></label>" ;
            html += "    <div class='col-sm-3'>" ;
            html += "        <input type='text' class='form-control' name='effDate"+ index + " 'required='required' id='effDate" + index + "' >"
            html+=  "    </div>";
            html += "    <label class='col-sm-3 control-label' for='" + "expDate" + index +"' ><span style='color: red'>*</span><span data-i18n='urm.mermgr.expDate'></span></label>" ;
            html += "    <div class='col-sm-3'>" ;
            html += "        <input type='text' class='form-control' name='expDate"+ index + "' required='required' id='expDate" + index + "' >"
            html+=  "    </div>";
            html+=  "</div>";

            html += "<div class='form-group'>";
            html += "    <label class='col-sm-3 control-label' for='beginCalFee"+ index + "' data-i18n='urm.mermgr.beginCalFee'></label>" ;
            html += "    <div class='col-sm-3'>" ;
            html += "        <input type='text' class='form-control' name='beginCalFee"+ index + "' required='required' id='beginCalFee" + index + "' >"
            html+=  "    </div>";
            html += "    <label class='col-sm-3 control-label' for='calculateMod" + index + "' data-i18n='urm.mermgr.calculateMod'></label>" ;
            html += "    <div class='col-sm-3'>" ;
            html += "        <select type='' class='form-control' name='calculateMod" +index + "' id = 'calculateMod" + index + "' >"
            html+=  "            <option value='internal' data-i18n='urm.mermgr.calculateModEnum.internal' ></option>";
            html+=  "            <option value='external' data-i18n='urm.mermgr.calculateModEnum.external' ></option>";
            html+=  "        </select>";
            html+=  "    </div>";
            html+=  "</div>";
            html += " <input id='channel" +index+  "' name='channel" +index+ "' value='" + channel+ "'  type='hidden'/>"
        }
        html += " <input id='channel' name='channel' value='Seatelpay'  type='hidden'/>"
        html += " <input id='channel1' name='channel1' value='Seatelpay'  type='hidden'/>"
        $("#rateInfo").html(html);
        for (var j=0 ; j < 8 ; j++ ) {
            var index = "";
            if (j!=0) {
                index= j;
            }
            $('#expDate' + index ).datetimepicker({
                lang:'en',
                timepicker:false,
                validateOnBlur: false,
                format:'Y-m-d ',
                formatDate:'Y-m-d',
                // maxDate:'+1970/01/01',
            });
             $('#effDate' + index ).datetimepicker({
                lang:'en',
                timepicker:false,
                validateOnBlur: false,
                format:'Y-m-d ',
                formatDate:'Y-m-d',
                // maxDate:'+1970/01/01',
            });
        }
    }
</script>
</body>
</html>