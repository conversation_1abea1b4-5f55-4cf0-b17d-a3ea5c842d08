<div class="modal inmodal" id="handleModal" tabindex="-1" role="dialog"  aria-hidden="true"  data-backdrop="static">
    <div class="modal-dialog" style="width: 1000px;">
        <div class="modal-content animated">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title" data-i18n="urm.mermgr.manage.content"></h4>
            </div>
            <div class="modal-body" style="height:500px;overflow:auto; ">

                <div class="form-horizontal" id="mercForm">
                    <div>
                        <div style="width: 1000px;high:40px ;background: #8B91A0 ;margin-bottom : 10px" data-i18n="urm.mermgr.baseInfo"> 基本信息</div>
                        <div class="form-group">
                            <div class="form-group">
                                <label class="col-sm-3 control-label" for="mercNm"  aria-flowto="right"><span style="color: red">*</span><span data-i18n="urm.mermgr.mercNm"></span></label><!--内部用户号-->
                                <div class="col-sm-3">
                                    <input type="text" class="form-control required" name="mercNm" id="mercNm" >
                                </div>
                                <label class="col-sm-3 control-label" for="mercShortName" ><span style="color: red">*</span><span data-i18n="urm.mermgr.mercShortNm"></span></label>
                                <div class="col-sm-3">
                                    <input type="text" class="form-control" name="mercShortName" id="mercShortName"  >
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="form-group">
                                <label class="col-sm-3 control-label" for="comercReg"  aria-flowto="right"><span style="color: red">*</span><span data-i18n="urm.mermgr.comercReg"></span></label><!--内部用户号-->
                                <div class="col-sm-3">
                                    <input type="text" class="form-control" name="comercReg" id="comercReg"  >
                                </div>
                                <label class="col-sm-3 control-label" for="mercTrdCls" data-i18n="urm.mermgr.mercTrdCls">所属行业</label><!--付款类型-->
                                <div class="col-sm-3">
                                    <select type="" class="form-control" name="mercTrdCls" required="required" maxlength="4" id = 'mercTrdCls' >
                                        <option value='7395' data-i18n="cls.7395"></option>
                                        <option value='7407' data-i18n="cls.7407"></option>
                                        <option value='7408' data-i18n="cls.7408"></option>
                                        <option value='7409' data-i18n="cls.7409"></option>
                                        <option value='7420' data-i18n="cls.7420"></option>
                                        <option value='7421' data-i18n="cls.7421"></option>
                                        <option value='7422' data-i18n="cls.7422"></option>
                                        <option value='7423' data-i18n="cls.7423"></option>
                                        <option value='7424' data-i18n="cls.7424"></option>
                                        <option value='7425' data-i18n="cls.7425"></option>
                                        <option value='7426' data-i18n="cls.7426"></option>
                                        <option value='7427' data-i18n="cls.7427"></option>
                                        <option value='7428' data-i18n="cls.7428"></option>
                                        <option value='7429' data-i18n="cls.7429"></option>
                                        <option value='7430' data-i18n="cls.7430"></option>
                                        <option value='7431' data-i18n="cls.7431"></option>
                                        <option value='7519' data-i18n="cls.7519"></option>
                                        <option value='7523' data-i18n="cls.7523"></option>
                                        <option value='7538' data-i18n="cls.7538"></option>
                                        <option value='7832' data-i18n="cls.7832"></option>
                                        <option value='7991' data-i18n="cls.7991"></option>
                                        <option value='7993' data-i18n="cls.7993"></option>
                                        <option value='7994' data-i18n="cls.7994"></option>
                                        <option value='7995' data-i18n="cls.7995"></option>
                                        <option value='8050' data-i18n="cls.8050"></option>
                                        <option value='8062' data-i18n="cls.8062"></option>
                                        <option value='8211' data-i18n="cls.8211"></option>
                                        <option value='8398' data-i18n="cls.8398"></option>
                                        <option value='8931' data-i18n="cls.8931"></option>
                                        <option value='9399' data-i18n="cls.9399"></option>
                                        <option value='4111' data-i18n="cls.4111"></option>
                                        <option value='4112' data-i18n="cls.4112"></option>
                                        <option value='4121' data-i18n="cls.4121"></option>
                                        <option value='4131' data-i18n="cls.4131"></option>
                                        <option value='4214' data-i18n="cls.4214"></option>
                                        <option value='4215' data-i18n="cls.4215"></option>
                                        <option value='4411' data-i18n="cls.4411"></option>
                                        <option value='4511' data-i18n="cls.4511"></option>
                                        <option value='4582' data-i18n="cls.4582"></option>
                                        <option value='4722' data-i18n="cls.4722"></option>
                                        <option value='4814' data-i18n="cls.4814"></option>
                                        <option value='4816' data-i18n="cls.4816"></option>
                                        <option value='4899' data-i18n="cls.4899"></option>
                                        <option value='4900' data-i18n="cls.4900"></option>
                                        <option value='5172' data-i18n="cls.5172"></option>
                                        <option value='5192' data-i18n="cls.5192"></option>
                                        <option value='5199' data-i18n="cls.5199"></option>
                                        <option value='5200' data-i18n="cls.5200"></option>
                                        <option value='5311' data-i18n="cls.5311"></option>
                                        <option value='5399' data-i18n="cls.5399"></option>
                                        <option value='5442' data-i18n="cls.5442"></option>
                                        <option value='5451' data-i18n="cls.5451"></option>
                                        <option value='5511' data-i18n="cls.5511"></option>
                                        <option value='5531' data-i18n="cls.5531"></option>
                                        <option value='5621' data-i18n="cls.5621"></option>
                                        <option value='5655' data-i18n="cls.5655"></option>
                                        <option value='5714' data-i18n="cls.5714"></option>
                                        <option value='5722' data-i18n="cls.5722"></option>
                                        <option value='5732' data-i18n="cls.5732"></option>
                                        <option value='5734' data-i18n="cls.5734"></option>
                                        <option value='5812' data-i18n="cls.5812"></option>
                                        <option value='5912' data-i18n="cls.5912"></option>
                                        <option value='5931' data-i18n="cls.5931"></option>
                                        <option value='5942' data-i18n="cls.5942"></option>
                                        <option value='5943' data-i18n="cls.5943"></option>
                                        <option value='5944' data-i18n="cls.5944"></option>
                                        <option value='5964' data-i18n="cls.5964"></option>
                                        <option value='5992' data-i18n="cls.5992"></option>
                                        <option value='5994' data-i18n="cls.5994"></option>
                                        <option value='5995' data-i18n="cls.5995"></option>
                                        <option value='5999' data-i18n="cls.5999"></option>
                                        <option value='6012' data-i18n="cls.6012"></option>
                                        <option value='6211' data-i18n="cls.6211"></option>
                                        <option value='6212' data-i18n="cls.6212"></option>
                                        <option value='6300' data-i18n="cls.6300"></option>
                                        <option value='7011' data-i18n="cls.7011"></option>
                                        <option value='7210' data-i18n="cls.7210"></option>
                                        <option value='7295' data-i18n="cls.7295"></option>
                                        <option value='7298' data-i18n="cls.7298"></option>
                                        <option value='7299' data-i18n="cls.7299"></option>
                                        <option value='7372' data-i18n="cls.7372"></option>
                                        <option value='7375' data-i18n="cls.7375"></option>

                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="form-group">
                                <label class="col-sm-3 control-label" for="cprTyp" data-i18n="urm.mermgr.cprTyp" aria-flowto="right">商户类型</label><!--内部用户号-->
                                <div class="col-sm-3">
                                    <select type="" class="form-control" name="cprTyp" required="required" maxlength="02" id = "cprTyp" >
                                        <option value='01' data-i18n="urm.mermgr.cprTypEnum.nationalized" ></option>
                                        <option value='02' data-i18n="urm.mermgr.cprTypEnum.private" ></option>
                                        <option value='03' data-i18n="urm.mermgr.cprTypEnum.foreign" ></option>
                                        <option value='04' data-i18n="urm.mermgr.cprTypEnum.joinVentrue" ></option>
                                        <option value='08' data-i18n="urm.mermgr.cprTypEnum.person" ></option>
                                        <option value='10' data-i18n="urm.mermgr.cprTypEnum.company" ></option>
                                        <option value='11' data-i18n="urm.mermgr.cprTypEnum.pVentrue" ></option>
                                    </select>

                                </div>
                                <label class="col-sm-3 control-label" for="mgtScp" data-i18n="urm.mermgr.mgtScp">经营范围</label><!--付款类型-->
                                <div class="col-sm-3">
                                    <input type="text" class="form-control" name="mgtScp" id="mgtScp"  >
                                </div>
                            </div>
                        </div>

                        <div class="form-group">

                            <label class="col-sm-3 control-label"  data-i18n="urm.mermgr.merRsgaddr">经营场所地址</label><!--付款类型-->
                            <div class="col-sm-2" >
                                <select type="" class="form-control" name="merRsgaddrS" required="required" maxlength="02" id = 'merRsgaddrS' >
                                    <option value= "baiLin" data-i18n="kampucheaCity.baiLin"></option>
                                    <option value= "jinBian" data-i18n="kampucheaCity.jinBian"></option>
                                    <option value= "xiHaNuKe" data-i18n="kampucheaCity.xiHaNuKe"></option>
                                    <option value= "baiMa" data-i18n="kampucheaCity.baiMa"></option>
                                    <option value= "banDieMianJi"  data-i18n="kampucheaCity.banDieMianJi"></option>
                                    <option value= "maDeWang" data-i18n="kampucheaCity.maDeWang"></option>
                                    <option value= "bangZhan" data-i18n="kampucheaCity.bangZhan"></option>
                                    <option value= "bangQingYang" data-i18n="kampucheaCity.bangQingYang"></option>
                                    <option value= "bangSshiBei" data-i18n="kampucheaCity.bangSshiBei"></option>
                                    <option value= "bangTong" data-i18n="kampucheaCity.bangTong"></option>
                                    <option value= "gongBu" data-i18n="kampucheaCity.gongBu"></option>
                                    <option value= "ganDan" data-i18n="kampucheaCity.ganDan"></option>
                                    <option value= "geGong" data-i18n="kampucheaCity.geGong"></option>
                                    <option value= "jiJin" data-i18n="kampucheaCity.jiJin"></option>
                                    <option value= "mengDuoJiLi" data-i18n="kampucheaCity.mengDuoJiLi"></option>
                                    <option value= "aoDuoMianJi" data-i18n="kampucheaCity.aoDuoMianJi"></option>
                                    <option value= "baiWeiXia" data-i18n="kampucheaCity.baiWeiXia"></option>
                                    <option value= "puSa" data-i18n="kampucheaCity.puSa"></option>
                                    <option value= "boLuoMian" data-i18n="kampucheaCity.boLuoMian"></option>
                                    <option value= "laTaNaJiLi" data-i18n="kampucheaCity.laTaNaJiLi"></option>
                                    <option value= "xianLi" data-i18n="kampucheaCity.xianLi"></option>
                                    <option value= "shangDing" data-i18n="kampucheaCity.shangDing"></option>
                                    <option value= "caiZhen" data-i18n="kampucheaCity.caiZhen"></option>
                                    <option value= "chaJiao" data-i18n="kampucheaCity.chaJiao"></option>
                                </select>
                            </div>
                            <div  style="width: 4px;float: left">
                                <span style="width: 4px;margin-top : 6px;" data-i18n="urm.mermgr.merRsgaddrS">省</span>
                            </div>
                            <label class="col-sm-3 control-label"  data-i18n="urm.mermgr.detailAddr">详细地址</label><!--付款类型-->
                            <div class="col-sm-3">
                                <!--<input type="" class="form-control" name="detailAddr" id="detailAddr"  >-->
                                <textarea cols="60" rows="3" class="form-control" ame="detailAddr" id="detailAddr" ></textarea>
                            </div>
                            <!--<div class="col-sm-2">-->
                            <!--<input type="text" class="form-control" name="merRsgaddrI" id="merRsgaddrI"  >-->
                            <!--</div>-->
                            <!--<div  style="width: 4px;float: left">-->
                            <!--<span style="width: 4px;margin-top : 6px;" data-i18n="urm.mermgr.merRsgaddrI">市</span>-->
                            <!--</div>-->
                            <!--<div class="col-sm-2">-->
                            <!--<input type="text" class="form-control" name="fixedaddr" id="fixedaddr"  >-->
                            <!--</div>-->
                            <!--<div class="col-sm-2">-->
                            <!--<button type="button" id="fixedaddrBut" class="btn btn-white"  data-i18n="urm.mermgr.fixedaddr">地图定位</button>-->
                            <!--</div>-->

                        </div>
                        <div class="form-group">
                            <div class="form-group">
                                <label class="col-sm-3 control-label" for="term" data-i18n="urm.mermgr.term" aria-flowto="right">经营期限</label><!--内部用户号-->
                                <div class="col-sm-3">
                                    <input type="text" class="form-control" name="term" id="term"  >
                                </div>
                                <label class="col-sm-3 control-label" for="prinNm" ><span style="color: red">*</span><span data-i18n="urm.mermgr.prinNm"></span></label><!--付款类型-->
                                <div class="col-sm-3">
                                    <input type="text" class="form-control" name="prinNm" id="prinNm"  >
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="form-group">
                                <label class="col-sm-3 control-label" for="certTyp"  aria-flowto="right"><span style="color: red">*</span><span data-i18n="urm.mermgr.certTyp"></span></label><!--内部用户号-->
                                <div class="col-sm-3">
                                    <select type="" class="form-control" name="certTyp" required="required" maxlength="02" id = "certTyp" >
                                        <option value='0' data-i18n="certType.0"></option>
                                        <option value='1' data-i18n="certType.1"></option>
                                        <option value='2' data-i18n="certType.2"></option>
                                        <option value='3' data-i18n="certType.3"></option>
                                        <option value='4' data-i18n="certType.4"></option>
                                        <option value='5' data-i18n="certType.5"></option>
                                        <option value='6' data-i18n="certType.6"></option>
                                        <option value='7' data-i18n="certType.7"></option>
                                        <option value='8' data-i18n="certType.8"></option>
                                        <option value='9' data-i18n="certType.9"></option>
                                        <option value='10' data-i18n="certType.10"></option>
                                        <option value='11' data-i18n="certType.11"></option>
                                        <option value='12' data-i18n="certType.12"></option>
                                        <option value='13' data-i18n="certType.13"></option>
                                        <option value='14' data-i18n="certType.14"></option>
                                        <option value='15' data-i18n="certType.15"></option>
                                    </select>
                                </div>
                                <label class="col-sm-3 control-label" for="certNo" ><span style="color: red">*</span><span data-i18n="urm.mermgr.certNo"></span></label><!--付款类型-->
                                <div class="col-sm-3">
                                    <input type="text" class="form-control" name="certNo" id="certNo"  >
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="form-group">
                                <label class="col-sm-3 control-label" for="mangerNm"  aria-flowto="right"><span style="color: red">*</span><span data-i18n="urm.mermgr.mangerNm"></span></label><!--内部用户号-->
                                <div class="col-sm-3">
                                    <input type="text" class="form-control" name="mangerNm" id="mangerNm"  >
                                </div>
                                <label class="col-sm-3 control-label" for="mngMoblie" ><span style="color: red">*</span><span data-i18n="urm.mermgr.mngMoblie"></span></label><!--付款类型-->
                                <div class="col-sm-3">
                                    <input type="text" class="form-control" name="mngMoblie" id="mngMoblie"  >
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="form-group">
                                <label class="col-sm-3 control-label" for="mngAcc"  aria-flowto="right"><span style="color: red">*</span><span data-i18n="urm.mermgr.mngAcc"></span></label><!--内部用户号-->
                                <div class="col-sm-3">
                                    <input type="text" class="form-control" name="mngAcc" id="mngAcc"  >
                                </div>
                                <label class="col-sm-3 control-label" for="mngEmail" ><span style="color: red">*</span><span data-i18n="urm.mermgr.mngEmail"></span></label><!--付款类型-->
                                <div class="col-sm-3">
                                    <input type="text" class="form-control" name="mngEmail" id="mngEmail"  >
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="form-group">
                                <label class="col-sm-3 control-label" for="recMobile" data-i18n="urm.mermgr.atvNm" aria-flowto="right">推荐人手机号</label><!--内部用户号-->
                                <div class="col-sm-3">
                                    <input type="text" class="form-control" name="recMobile" id="recMobile"  >
                                </div>
                                <label class="col-sm-3 control-label" for="merLvl" data-i18n="urm.mermgr.merevel" aria-flowto="right">商户级别</label><!--内部用户号-->
                                <div class="col-sm-3">
                                    <select type="" class="form-control" name="merLvl"  id = "merLvl" >
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="form-group">
                                <label class="col-sm-3 control-label" for="openWechatMerflag" data-i18n="urm.mermgr.openWechatMerflag" aria-flowto="right">是否开通微信子商户</label><!--内部用户号-->
                                <div class="col-sm-3">
                                    <select type="" class="form-control" name="openWechatMerflag"  id = "openWechatMerflag" >
                                        <option value="open" data-i18n="urm.mermgr.openWechatMerflagEnum.open"></option>
                                        <option value="no" data-i18n="urm.mermgr.openWechatMerflagEnum.no"></option>
                                    </select>
                                </div>
                                <label class="col-sm-3 control-label" for="recMobile" data-i18n="urm.mermgr.belongMerc" aria-flowto="right">上级商户</label>
                                <div class="col-sm-3">
                                    <input type="text" class="form-control" name="belongMerc" id="belongMerc" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="form-group">
                                <label class="col-sm-3 control-label" for="mercLogo" data-i18n="urm.mermgr.mercLogo" aria-flowto="right"></label>
                                <div class="col-sm-3">
                                    <label for="mercLogo">　　
                                        <img src="/img/uploadBut.png" id ='imercLogo' height="84px" width="118px">
                                        <input type="file"  name="mercLogo" id="mercLogo" class="hide" >
                                    </label> 　

                                </div>
                                <label class="col-sm-3 control-label" for="busLicImg" data-i18n="urm.mermgr.busLicImg"></label>
                                <div class="col-sm-3">
                                    <label for="busLicImg">　　
                                        <img src="/img/uploadBut.png" id ='ibusLicImg'  height="84px" width="118px">
                                    </label> 　
                                    <input type="file"  name="busLicImg" id="busLicImg" class="hide" >
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="certImg" data-i18n="urm.mermgr.certImg" aria-flowto="right"></label>
                            <div class="col-sm-3">
                                <label for="certImg">　　
                                    <img src="/img/uploadBut.png" id = 'icertImg'  height="84px" width="118px">
                                </label> 　
                                <input type="file"  name="certImg" id="certImg" class="hide" >

                            </div>
                            <label class="col-sm-3 control-label" for="certImgB" data-i18n="urm.mermgr.certImgB" aria-flowto="right"></label>
                            <div class="col-sm-3">
                                <label for="certImgB">　　
                                    <img src="/img/uploadBut.png" id = 'icertImgB'  height="84px" width="118px">
                                </label> 　
                                <input type="file"  name="certImgB" id="certImgB" class="hide" >

                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="cardImg" data-i18n="urm.mermgr.cardImg"></label>
                            <div class="col-sm-3">

                                <label for="cardImg">　　
                                    <img src="/img/uploadBut.png" id = 'icardImg'  height="84px" width="118px">
                                </label> 　
                                <input type="file"  name="cardImg" id="cardImg" class="hide" >
                            </div>
                            <label class="col-sm-3 control-label" for="cardImgB" data-i18n="urm.mermgr.cardImgB"></label>
                            <div class="col-sm-3">

                                <label for="cardImgB">　　
                                    <img src="/img/uploadBut.png" id = 'icardImgB'  height="84px" width="118px">
                                </label> 　
                                <input type="file"  name="cardImgB" id="cardImgB" class="hide" >
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="form-group">
                                <label class="col-sm-3 control-label" for="merPotocolImg" data-i18n="urm.mermgr.merPotocolImg" aria-flowto="right">上传商户协议扫描件</label>
                                <!--<div class="col-sm-3">
                                    <label for="merPotocolImg">
                                        <img src="/img/uploadBut.png" id = 'imerPotocolImg'  height="84px" width="118px">　　
                                    </label> 　
                                    <input type="file"  name="merPotocolImg" id="merPotocolImg" class="hide" >

                                </div>-->
                                <div class="col-sm-3">

                                    <label for="merPotocolImg">　　
                                        <img src="/img/uploadBut.png" id = 'imerPotocolImg'  height="84px" width="118px">
                                    </label> 　
                                    <input type="file"  name="merPotocolImg" id="merPotocolImg" class="hide" >
                                </div>
                                <div id = 'qrcodeDiv'>
                                    <label class="col-sm-3 control-label" for="qrcode" data-i18n="urm.mermgr.qrcode" aria-flowto="right"></label>
                                    <div class="col-sm-3">
                                        <label for="qrcode">　　
                                            <img src="" id = 'qrcode'  height="84px" width="118px">
                                        </label> 　
                                        <a href=""  data-i18n="urm.mermgr.downqrcode"  id = "downloadQrcode"></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!--.................................结算信息-->
                    <div>
                        <div style="width: 1000px;high:40px ;background: #8B91A0 ;margin-bottom : 10px" data-i18n="urm.mermgr.settleInfo"> 结算信息</div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="settleSite" ><span style="color: red">*</span><span data-i18n="urm.mermgr.settleSite"></span></label>
                            <div class="col-sm-3">
                                <select type="" class="form-control" name="settleSite" id = 'settleSite' >
                                    <option value='001' data-i18n="urm.mermgr.settleSiteEnum.bank" ></option>
                                    <option value='002' data-i18n="urm.mermgr.settleSiteEnum.hallSite" ></option>
                                </select>
                            </div>
                            <label class="col-sm-3 control-label" for="hallSites" data-i18n="urm.mermgr.hallSites">取现营业网点</label>
                            <div class="col-sm-3">
                                <select type="" class="form-control" name="hallSites"  id = 'hallSites' >
                                    <option value='no' data-i18n="urm.mermgr.hallSitesEnum.no" ></option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="capCardNo" data-i18n="urm.mermgr.startTm">结算银行卡</label><!--付款银行-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="capCardNo" required="required" id="capCardNo" >
                            </div>
                            <label class="col-sm-3 control-label" for="capCardName" data-i18n="urm.mermgr.endTm">开户名</label><!--付款金额-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="capCardName" required="required" id="capCardName" >
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="capCorgNm" data-i18n="urm.mermgr.capCorgNm">开户银行</label><!--付款银行-->
                            <div class="col-sm-3">
                                <select type="" class="form-control" name="capCorgNm" required="required" maxlength="02" id = 'capCorgNm' >
                                    <!--<option value='ICBC' data-i18n="corg.ICBC" ></option>-->
                                    <!--<option value='ABC' data-i18n="corg.ABC" ></option>-->
                                    <!--<option value='ICBC' data-i18n="corg.ICBC" ></option>-->
                                    <!--<option value='CCB' data-i18n="corg.CCB" ></option>-->
                                    <!--<option value='BOC' data-i18n="corg.BOC" ></option>-->
                                    <!--<option value='CMB' data-i18n="corg.CMB" ></option>-->
                                    <!--<option value='CMBC' data-i18n="corg.CMBC" ></option>-->
                                    <!--<option value='CMB' data-i18n="corg.CMB" ></option>-->
                                    <!--<option value='CIB' data-i18n="corg.CIB" ></option>-->
                                    <!--<option value='BCM' data-i18n="corg.BCM" ></option>-->
                                    <!--<option value='CEB' data-i18n="corg.CEB" ></option>-->
                                    <!--<option value='GDB' data-i18n="corg.GDB" ></option>-->
                                    <!--<option value='BEA' data-i18n="corg.BEA" ></option>-->

                                </select>
                            </div>
                            <label class="col-sm-3 control-label" for="subbranch" data-i18n="urm.mermgr.subbranch">开户支行</label><!--付款金额-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="subbranch" required="required" id="subbranch" >
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="settleType" ><span style="color: red">*</span><span data-i18n="urm.mermgr.settleType"></span></label><!--付款银行-->
                            <div class="col-sm-3">
                                <select type="" class="form-control" name="settleType" required="required" maxlength="02" id = 'settleType' >
                                    <option value='auto' data-i18n="urm.mermgr.settleTypeEnum.auto" ></option>
                                    <option value='self' data-i18n="urm.mermgr.settleTypeEnum.self" ></option>
                                    <option value='hall' data-i18n="urm.mermgr.settleTypeEnum.hall" ></option>
                                </select>
                            </div>
                            <label class="col-sm-3 control-label" for="settleCycleType" data-i18n="urm.mermgr.settleCycleType"> 结算周期</label><!--付款金额-->
                            <div class="col-sm-3">
                                <select type="" class="form-control" name="settleCycleType" required="required" maxlength="02" id = 'settleCycleType' >
                                    <option value='daily' data-i18n="urm.mermgr.settleCycleTypeEnum.daily" ></option>
                                    <option value='weekly' data-i18n="urm.mermgr.settleCycleTypeEnum.weekly" ></option>
                                    <option value='monthly' data-i18n="urm.mermgr.settleCycleTypeEnum.monthly" ></option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="settleEffDate" ><span style="color: red">*</span><span data-i18n="urm.mermgr.effDate"></span></label><!--付款银行-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="settleEffDate" required="required" id="settleEffDate" >
                            </div>
                            <label class="col-sm-3 control-label" for="settleExpDate" ><span style="color: red">*</span><span data-i18n="urm.mermgr.expDate"></span></label><!--付款金额-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="settleExpDate" required="required" id="settleExpDate" >
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="drawDays" data-i18n="urm.mermgr.drawDays"> </label><!--付款银行-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="drawDays" required="required" id="drawDays" >
                            </div>
                        </div>
                    </div>


                    <!--.................................费率信息-->
                    <div id="rateInfo">
                        <div style="width: 1000px;high:40px ;background: #8B91A0 ;margin-bottom : 10px" data-i18n="urm.mermgr.rateInfo"> 费率信息</div>
                        <div style="width: 1000px;high:20px ;margin-bottom : 10px" data-i18n="urm.mermgr.rate1"> 费率1</div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="busType" data-i18n="urm.mermgr.busType">产品类型</label><!--付款银行-->
                            <div class="col-sm-3">
                                <select type="" class="form-control" name="busType" id = 'busType' disabled = 'disabled'>
                                    <option value='1010' data-i18n="urm.mermgr.busTypeEnum.consume" selected = "selected" ></option>
                                </select>
                            </div>
                            <label class="col-sm-3 control-label" for="chargeType" data-i18n="urm.mermgr.chargeType">收费方式</label><!--付款金额-->
                            <div class="col-sm-3">
                                <select type="" class="form-control" name="chargeType" required="required" maxlength="02" id = 'chargeType' >
                                    <option value='single' data-i18n="urm.mermgr.chargeTypeEnum.single" ></option>
                                    <!--   <option value='cycle' data-i18n="urm.mermgr.chargeTypeEnum.cycle" ></option>-->
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="calculateType" data-i18n="urm.mermgr.calculateType">计费方式</label><!--付款银行-->
                            <div class="col-sm-3">
                                <select type="" class="form-control" name="calculateType" required="required" maxlength="02" id = 'calculateType' >
                                    <option value='percent' data-i18n="urm.mermgr.calculateTypeEnum.percent" ></option>
                                    <option value='fixed' data-i18n="urm.mermgr.calculateTypeEnum.fixed" ></option>
                                </select>

                            </div>
                            <label class="col-sm-3 control-label" for="rate" ><span style="color: red">*</span><span data-i18n="urm.mermgr.rate"></span></label><!--付款银行-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="rate" required="required" id="rate" >
                            </div>
                            <!--             <label class="col-sm-3 control-label" for="endTime" data-i18n="urm.mermgr.endTm">计费起始金额</label>&lt;!&ndash;付款金额&ndash;&gt;
                                         <div class="col-sm-3">
                                             <input type="text" class="form-control" name="beginTime" required="required" id="beginTime" >
                                         </div>-->
                        </div>
                        <div class="form-group">

                            <label class="col-sm-3 control-label" for="minFee" data-i18n="urm.mermgr.minFee">最低收取金额</label><!--付款金额-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="minFee" required="required" id="minFee" >
                            </div>

                            <label class="col-sm-3 control-label" for="maxFee" data-i18n="urm.mermgr.maxFee">最高收取金额</label><!--付款银行-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="maxFee" required="required" id="maxFee" >
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="effDate" ><span style="color: red">*</span><span data-i18n="urm.mermgr.effDate"></span></label><!--付款金额-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="effDate" required="required" id="effDate" >
                            </div>
                            <label class="col-sm-3 control-label" for="expDate" ><span style="color: red">*</span><span data-i18n="urm.mermgr.expDate"></span></label><!--付款金额-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="expDate" required="required" id="expDate" >
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="beginCalFee" data-i18n="urm.mermgr.beginCalFee"></label>
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="beginCalFee" required="required1" id="beginCalFee" >
                            </div>

                            <label class="col-sm-3 control-label" for="calculateMod" data-i18n="urm.mermgr.calculateMod"></label>
                            <div class="col-sm-3">
                                <select type="" class="form-control" name="calculateMod"  id = 'calculateMod' >
                                    <option value='internal' data-i18n="urm.mermgr.calculateModEnum.internal" ></option>
                                    <option value='external' data-i18n="urm.mermgr.calculateModEnum.external" ></option>
                                </select>
                            </div>
                        </div>

                        <!--  ..................................费率2-->
                        <div class="hr-line-dashed " style="border-top:1px dashed #121415;" ></div>
                        <div style="width: 1000px;high:20px ;margin-bottom : 10px"  data-i18n="urm.mermgr.rate2"> 费率2</div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="busType1" data-i18n="urm.mermgr.busType" >产品类型</label><!--付款银行-->
                            <div class="col-sm-3">
                                <select type="" class="form-control" name="busType1" id = 'busType1' disabled = 'disabled'>
                                    <option value='0403' data-i18n="urm.mermgr.busTypeEnum.draw"  ></option>
                                </select>
                            </div>
                            <label class="col-sm-3 control-label" for="chargeType1" data-i18n="urm.mermgr.chargeType">收费方式</label><!--付款金额-->
                            <div class="col-sm-3">
                                <select type="" class="form-control" name="chargeType1" required="required" maxlength="02" id = 'chargeType1' >
                                    <option value='single' data-i18n="urm.mermgr.chargeTypeEnum.single" ></option>
                                    <!--    <option value='cycle' data-i18n="urm.mermgr.chargeTypeEnum.cycle" ></option>-->
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="calculateType1" data-i18n="urm.mermgr.calculateType">计费方式</label><!--付款银行-->
                            <div class="col-sm-3">
                                <select type="" class="form-control" name="calculateType1" required="required" maxlength="02" id = 'calculateType1' >
                                    <option value='percent' data-i18n="urm.mermgr.calculateTypeEnum.percent" ></option>
                                    <option value='fixed' data-i18n="urm.mermgr.calculateTypeEnum.fixed" ></option>
                                </select>
                            </div>
                            <label class="col-sm-3 control-label" for="rate1" ><span style="color: red">*</span><span data-i18n="urm.mermgr.rate"></span></label><!--付款银行-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="rate1" required="required" id="rate1" >
                            </div>
                        </div>
                        <div class="form-group">

                            <label class="col-sm-3 control-label" for="minFee1" data-i18n="urm.mermgr.minFee">最低收取金额</label><!--付款金额-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="minFee1" required="required" id="minFee1" >
                            </div>
                            <label class="col-sm-3 control-label" for="maxFee1" data-i18n="urm.mermgr.maxFee">最高收取金额</label><!--付款银行-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="maxFee1" required="required" id="maxFee1" >
                            </div>
                        </div>
                        <!--    <label class="col-sm-3 control-label" for="endTime" data-i18n="urm.mermgr.endTm">计费起始金额</label>&lt;!&ndash;付款金额&ndash;&gt;
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="beginTime" required="required" id="beginTime" >
                            </div>-->
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="effDate1"><span style="color: red">*</span><span data-i18n="urm.mermgr.effDate"></span></label><!--付款银行-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="effDate1" required="required" id="effDate1" >
                            </div>
                            <label class="col-sm-3 control-label" for="expDate1" ><span style="color: red">*</span><span data-i18n="urm.mermgr.expDate"></span></label><!--付款金额-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="expDate1" required="required" id="expDate1" >
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="beginCalFee1" data-i18n="urm.mermgr.beginCalFee"></label>
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="beginCalFee1" required="required1" id="beginCalFee1" >
                            </div>

                            <label class="col-sm-3 control-label" for="calculateMod1" data-i18n="urm.mermgr.calculateMod"></label>
                            <div class="col-sm-3">
                                <select type="" class="form-control" name="calculateMod1"  id = 'calculateMod1' >
                                    <option value='internal' data-i18n="urm.mermgr.calculateModEnum.internal" ></option>
                                    <option value='external' data-i18n="urm.mermgr.calculateModEnum.external" ></option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div id = 'resufeDiv' hidden="hidden">
                        <div style="width: 1000px;high:40px ;background: #8B91A0 ;margin-bottom : 10px" data-i18n="urm.mermgr.refuseReson"> 审核理由</div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="refuseResonShow" data-i18n="urm.mermgr.refuseReson" aria-flowto="right">商户名称</label><!--内部用户号-->
                            <div class="col-sm-8">
                                <textarea  class="form-control" rows="5" name="refuseResonShow" id="refuseResonShow"  > </textarea>
                            </div>
                        </div>
                    </div>
                    <input id="mercId" name="mercId" value="" type="hidden"/><!--商户号-->
                    <input id="mercSts" name="mercSts" value="" type="hidden"/><!--商户状态-->
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" id="mermgrAdd" class="btn btn-white" data-i18n="urm.mermgr.sumbit"></button>
                <button type="button" id="mercModify" class="btn btn-white" data-i18n="urm.mermgr.edit"></button>
                <button type="button" class="btn btn-default" data-dismiss="modal" data-i18n="urm.mermgr.close">关闭</button>
            </div>
        </div>
    </div>
</div>