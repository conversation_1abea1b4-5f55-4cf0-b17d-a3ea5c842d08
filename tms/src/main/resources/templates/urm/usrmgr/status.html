<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
    <head>
        <title data-i18n="urm.usrmgr.status.title"></title>
        <div th:replace="head"></div>
    </head>
    <body>
        <div id="wrapper">
            <div th:replace="nav"></div>
            <div id="page-wrapper" class="gray-bg">
                <div th:replace="top"></div>
                <div class="row wrapper border-bottom white-bg page-heading">
                    <div class="col-lg-10">
                        <h2 data-i18n="urm.usrmgr.status.content"></h2>
                        <ol class="breadcrumb">
                            <li>
                                <a data-i18n="nav.busmgr"></a>
                            </li>
                            <li>
                                <a data-i18n="nav.bussub.usrmgr"></a>
                            </li>
                            <li class="active">
                                <strong data-i18n="urm.usrmgr.status.content"></strong>
                            </li>
                        </ol>
                    </div>
                </div>
                <div class="wrapper wrapper-content  animated fadeInRight">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="ibox float-e-margins">
                                <div class="ibox-content">

                                    <!-- 表头导航栏 -->
                                    <div class="box-header">
                                        <table style="width: 80%">
                                            <tr>
                                                <td align="center">
                                                        <label class="control-label" data-i18n="urm.usrmgr.mblno"></label>
                                                </td>
                                                <td align="center">
                                                    <input class="form-control" name="searchMblNo" />
                                                </td>
                                                <td align="center">
                                                        <label class="control-label" data-i18n="urm.usrmgr.userId"></label>
                                                </td>
                                                <td align="center">
                                                    <input class="form-control" name="searchUserId" />
                                                </td>
                                                <td align="center">
                                                    <button type="button" class="btn btn-w-m btn-primary _right"
                                                            data-i18n="urm.search" onclick="searchButton()">
                                                    </button>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <hr/>

                                    <table width="90%" style="border-collapse:separate; border-spacing:0px 10px;">
                                        <tr>
                                            <td align="center">
                                                <label class="control-label" data-i18n="urm.usrmgr.userId"></label>
                                            </td>
                                            <td>
                                                <input class="form-control" name="userId" readonly="readonly" />
                                            </td>

                                            <td align="center">
                                                <label class="control-label" data-i18n="urm.usrmgr.mblno"></label>
                                            </td>
                                            <td>
                                                <input class="form-control" name="mblno" readonly="readonly" />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="center">
                                                <label class="control-label" data-i18n="urm.usrmgr.userSts"></label>
                                            </td>
                                            <td>
                                                <select class="form-control" name="userSts">
                                                    <option value="0" data-i18n="urm.usrmgr.normal"></option>
                                                    <option value="1" data-i18n="urm.usrmgr.freeze"></option>
                                                </select>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="center" colspan="4">
                                                <button type="button" class="btn btn-w-m btn-primary _right"
                                                        data-i18n="urm.submit" onclick="submit()">
                                                </button>
                                            </td>
                                        </tr>
                                    </table>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div th:replace="footer"></div>
            </div>
        </div>
        <div th:replace="script"></div>

        <script>
            function submit() {
                var mercId = $("input[name='userId']").val();
                var mercSts = $("select[name='userSts']").val();
                $.ajax({
                    type: 'POST',
                    url: "/urm/usrmgr/status/change",
                    data: {
                      "userId": mercId,
                      "userSts": mercSts
                    },
                    success: function (d) {
                        swal($.i18n.t("urm.success"), "", "success");
                        searchButton();
                    }
                })
            }

            function searchButton() {
                clean();
                var t = $("input[name='searchMblNo']");
                var userId = $("input[name='searchUserId']");
                if (t.val() == "" && userId.val() == '') {
                    t.focus();
                    return;
                }
                var id = t.val();
                if (id=="" || id == null){
                    id="blank";
                }
                var userId = userId.val();
                if (userId=="" || userId == null){
                    userId="blank";
                }
                $.ajax({
                    type: 'GET',
                    url: "/urm/usrmgr/status/" + id+"/"+userId ,
                    success: function (d) {
                        $("input[name='userId']").val(d.userId);
                        $("input[name='mblno']").val(d.mblNo);
                        $("select[name='userSts']").val(d.userSts);
                    }
                })
            }

            function clean() {
                $("input[name='userId']").val("");
                $("input[name='mblno']").val("");
                $("select[name='userSts']").val("");
            }
        </script>
    </body>
</html>