<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
    <head>
        <title data-i18n="urm.usrmgr.title"></title>
        <div th:replace="head"></div>
        <style>
            div.floatright{
                display:inline;
                float:right;
                padding-left:20px;
                padding-right:20px;
            }

            ._right{
                display:inline;
                float:right;
                padding-right:15px;
            }
        </style>
        </head>
    <body>
        <div id="wrapper">
            <div th:replace="nav"></div>
            <div id="page-wrapper" class="gray-bg">
                <div th:replace="top"></div>
                <div class="row wrapper border-bottom white-bg page-heading">
                    <div class="col-lg-10">
                        <h2 data-i18n="urm.usrmgr.amtInfo"></h2>
                        <ol class="breadcrumb">
                            <li>
                                <a data-i18n="nav.busmgr"></a>
                            </li>
                            <li>
                                <a data-i18n="nav.bussub.usrmgr"></a>
                            </li>
                            <li class="active">
                                <strong data-i18n="urm.usrmgr.amtInfo"></strong>
                            </li>
                        </ol>
                    </div>
                </div>
                <div class="wrapper wrapper-content  animated fadeInRight">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="ibox float-e-margins">
                                <div class="ibox-content">

                                    <div class="box-header">
                                        <table style="width: 90%">
                                            <tr>
                                                <td align="center">
                                                    <label class="control-label" data-i18n="urm.info.mblNo"></label>
                                                </td>
                                                <td>
                                                    <select class="form-control" name="areaCore" id="areaCore">
                                                        <option>+855-</option>
                                                        <option>+86-</option>
                                                    </select>
                                                </td>
                                                <td>
                                                    <input class="form-control" name="mblNo" id="mblNo"/>
                                                </td>
                                                <td align="center" style="padding-left: 5px">
                                                    <label class="control-label" data-i18n="rsm.riskList.txTyp"></label>
                                                </td>
                                                <td>
                                                    <select class="form-control" id="searchTxTyp" name="searchTxTyp">
                                                        <option value="" data-i18n="rsm.constant.all"></option>
                                                        <option value="01" data-i18n="rsm.constant.txTyp.recharge"></option>
                                                        <option value="02" data-i18n="rsm.constant.txTyp.consume"></option>
                                                        <option value="03" data-i18n="rsm.constant.txTyp.transfer"></option>
                                                        <option value="04" data-i18n="rsm.constant.txTyp.withdraw"></option>
                                                        <option value="05" data-i18n="rsm.constant.txTyp.seatel"></option>
                                                        <option value="06" data-i18n="rsm.constant.txTyp.refunds"></option>
                                                        <option value="07" data-i18n="rsm.constant.txTyp.interest"></option>
                                                        <option value="08" data-i18n="rsm.constant.txTyp.payment"></option>
                                                    </select>
                                                </td>
                                                <td align="center" style="padding-left: 10px">
                                                    <label class="control-label" data-i18n="csm.regDt"></label>
                                                </td>
                                                <td align="right" style="padding-right: 5px">
                                                    <input class="form-control" style="width: 90%" name="beginDate" id="beginDate" value=""/>
                                                </td>
                                                <td style="width: 10px">
                                                    <label>-</label>
                                                </td>
                                                <td align="left" style="padding-left: 5px">
                                                    <input class="form-control" style="width: 90%" name="endDate" id="endDate" value=""/>
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-w-m btn-primary _right" data-i18n="urm.search" onclick="search()">
                                                    </button>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <hr/>

                                    <div class="table-responsive">
                                        <table id="example" class="table table-striped table-bordered table-hover">
                                            <thead>
                                                <tr>
                                                    <th data-i18n="urm.usrmgr.acNo"></th>
                                                    <th data-i18n="urm.usrmgr.txTyp"></th>
                                                    <th data-i18n="urm.usrmgr.txSts"></th>
                                                    <th data-i18n="urm.usrmgr.dcFlg"></th>
                                                    <th data-i18n="urm.usrmgr.orderAmt"></th>
                                                    <th data-i18n="inv.ccy"></th>
                                                    <th data-i18n="urm.usrmgr.drAmt"></th>
                                                    <th data-i18n="urm.usrmgr.crAmt"></th>
                                                    <th data-i18n="manrecinput.tableTh.realAmt"></th>
                                                    <th data-i18n="urm.usrmgr.txDt"></th>
                                                    <th data-i18n="urm.usrmgr.txTm"></th>
                                                    <th data-i18n="tfm.busOrderNo"></th>
                                                </tr>
                                            </thead>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div th:replace="footer"></div>
            </div>
        </div>
        <div th:replace="script"></div>
        <script>
            var table;
            var order = [1, 'asc'];
            <!--查询按钮-->
            function search() {
                table.ajax.reload();
            }

            $(document).ready(function () {

                var languageUrl;
                switch ($.cookie('lang')) {
                    case 'zh':
                        languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                        break;
                    case 'en':
                        languageUrl = '/datatables/plugins/i18n/English.lang';
                        break;
                    case 'km':
                        languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                        break;
                }

                i18nLoad.then(function () {

                    table = $('#example').DataTable({
                        dom: 'B<"floatright"l>rtip',
                        buttons: [
                            {extend: 'csvHtml5'},
                            {extend: 'excelHtml5'}
                        ],
                        ajax: {
                            contentType: 'application/json',
                            url: '/urm/usrmgr/amtinfo/findAll',
                            type: 'POST',
                            data: function (d) {
                            	d.extra_search = {
                            	    "areaCore" : $("#areaCore").val(),
                                    "mblNo" : $("#mblNo").val(),
                                    "txTyp" : $("#searchTxTyp").val(),
                                    "begDt" : $("#beginDate").val(),
                                    "endDt" : $("#endDate").val()
                                };
                                return JSON.stringify(d);
                            }
                        },
                        searching: true,
                        serverSide: true,
                        searchDelay: 1000,
                        responsive: true,
                        processing: true,
                        language: {
                            url: languageUrl
                        },
                        select: true,
                        columns: [
                            {
                                data: 'acNo'
                            },{
                                data: 'txTyp',
                                render: function (data, type, row) {
                                    switch (data) {
                                        case '00':
                                            return $.i18n.t("rsm.constant.txTyp.all");
                                        case '01':
                                            return $.i18n.t("rsm.constant.txTyp.recharge");
                                        case '02':
                                            return $.i18n.t("rsm.constant.txTyp.consume");
                                        case '03':
                                            return $.i18n.t("rsm.constant.txTyp.transfer");
                                        case '04':
                                            return $.i18n.t("rsm.constant.txTyp.withdraw");
                                        case '05':
                                            return $.i18n.t("rsm.constant.txTyp.seatel");
                                        case '06':
                                            return $.i18n.t("rsm.constant.txTyp.refunds");
                                        case '07':
                                            return $.i18n.t("rsm.constant.txTyp.interest");
                                        case '08':
                                            return $.i18n.t("rsm.constant.txTyp.payment");
                                        case '09':
                                            return $.i18n.t("rsm.constant.txTyp.revoke");
                                        default:return null;
                                    }
                                }
                            }, {
                                data: 'txSts',
                                render: function (data, type, row) {
                                    switch (data) {
                                        case 'N':
                                            return $.i18n.t("urm.usrmgr.txStsN");
                                        case 'C':
                                            return $.i18n.t("urm.usrmgr.txStsC");
                                    }
                                }
                            }, {
                                data: "dcFlg",
                                render: function (data, type, row) {
                                    switch (data) {
                                        case 'D':
                                            return $.i18n.t("urm.usrmgr.dcFlgD");
                                        case 'C':
                                            return $.i18n.t("urm.usrmgr.dcFlgC");
                                    }
                                }
                            }, {
                                data: "txAmt"
                            }, {
                                data: "ccy"
                            }, {
                                data: "drAmt"
                            }, {
                                data: "crAmt"
                            }, {
                                data: "curBal"
                            }, {
                                data: "txOrdDt"
                            }, {
                                data: "txOrdTm"
                            }, {
                                data: "txOrdNo"
                            }
                        ]
                    });
                });

            });

            <!--初始化日期控件-->
            $('#beginDate').datetimepicker({
                lang:'en',
                timepicker:false,
                validateOnBlur: false,
                format:'Y-m-d',
                formatDate:'Y-m-d'
            });

            $('#endDate').datetimepicker({
                lang:'en',
                timepicker:false,
                validateOnBlur: false,
                format:'Y-m-d ',
                formatDate:'Y-m-d',
            });

//     function searchButton() {
//         var id = $("input[name='searchId']").val();
//         var txTyp = $("select[name='searchTxTyp']").val();
//         console.log(id);
//         console.log(txTyp);
//         table.column(1).search(id)
//             .column(3).search(txTyp)
//             .draw();
//     }
        </script>
    </body>
</html>