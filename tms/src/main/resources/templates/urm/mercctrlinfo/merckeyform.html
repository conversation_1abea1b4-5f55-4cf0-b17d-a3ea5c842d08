<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>

    <title data-i18n="nav.bussub.mersub.merckeyresetctrl.title"></title>

    <div th:replace="head"></div>

</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="nav.bussub.mersub.merckeyresetctrl.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.busmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.bussub.mermgr"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="nav.bussub.mersub.merckeyresetctrl.content"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form method="post" id = "form" class="form-horizontal">
                                <div class="form-group col-sm-12" >
                                    <label class="col-sm-2 control-label" data-i18n="urm.info.mercId"></label>
                                    <div class="col-sm-4"><input type="text" class="form-control" name="mercId" id="mercId"
                                                                  required="required" ></div>
                                    <label class="col-sm-2 control-label" data-i18n="urm.info.proNm"></label>
                                    <div class="col-sm-4"><input type="text" class="form-control" name="usrNm" id="usrNm"
                                                                  th:disabled = "true" ></div>
                                </div>
                                <div class="hr-line-dashed" ></div>
                                <div class="form-group" >
                                    <label class="col-sm-2 control-label" data-i18n="urm.info.email"></label>
                                    <div class="col-sm-4"><input type="text" class="form-control" name="email"
                                                                 id="email" th:disabled = "true">
                                    </div>
                                    <div class="col-sm-8 col-sm-offset-2">
                                        <button class="btn btn-primary" type="button" data-i18n="urm.reset" id = 'reset'></button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>

    </div>
</div>


<div th:replace="script"></div>
</body>
<script type="text/javascript" th:inline="javascript">
    $(function () {
        $("#reset").click(function () {
            var mercId = $("#mercId").val().trim();
            var email = $("#email").val().trim();
            var usrNm = $("#usrNm").val().trim();
            var flg=check(mercId);
            if (flg){
                return;
            }
            var param = {
                 "userId" : mercId,
                 "usrNm" : usrNm,
                 "email" : email
             };
            $.ajax({
                type: "POST",
                url: "/busmgr/keyresetctrl/keyreset",
                dataType:"json",
                data:param,
                success: function (data) {
                    var result = data.msgCd;
                    if (result !='URM00000') {
                        swal(data.msgInfo);
                    } else {
                    	swal($.i18n.t("urm.keyreset.success"), $.i18n.t("urm.keyreset.emailtips")+email, "success")
                    }
                },
                error : function (data) {
                    alert(data.msgCd);
                    return;
                },
            });
        });
        $("#mercId").focusout(function () {
            var mercId = $("#mercId").val().trim();
            var flg=check(mercId);
            if (flg){
            	return;
            }
            var param = {
                 "userId" : mercId
             };
            $.ajax({
                type: "POST",
                url: "/busmgr/keyresetctrl/query",
                dataType:"json",
                data:param,
                success: function (data) {
                	document.getElementById("email").value=data.email;
                	document.getElementById("usrNm").value=data.usrNm;
                	return;
                },
                error : function (data) {
                	swal($.i18n.t("urm.info.notexit"))
                	document.getElementById("email").value="";
                    document.getElementById("usrNm").value="";
                    return;
                },
            });
        });
        function check(mercId){
        	if(mercId==""){
        		return true;
        	}else{
        		return false;
        	}
        }
    });
</script>
</html>

