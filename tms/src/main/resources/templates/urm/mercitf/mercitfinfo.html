<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>

    <title data-i18n="nav.bussub.mersub.mercitfInfoctrl.title"></title>

    <div th:replace="head"></div>

</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="nav.bussub.mersub.mercitfInfoctrl.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.busmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.bussub.mermgr"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="nav.bussub.mersub.mercitfInfoctrl.content"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form method="post" id = "form" class="form-horizontal">
                                <div class="form-group" >

                                    <table>
                                        <tr>
                                            <!-- 商户编号 -->
                                            <td align="right">
                                                <label class="control-label" data-i18n="urm.info.mercId"></label>
                                            </td>
                                            <td>
                                                <input type="text" class="form-control" name="mercId" id="mercId">
                                            </td>
                                            <td>&nbsp; </td>
                                            <!-- 商户管理员姓名 -->
                                            <td align="right">
                                                <label class="control-label" data-i18n="urm.mermgr.mercNm"></label>
                                            </td>
                                            <td>
                                                <input type="text" class="form-control" name="usrNm" id="usrNm">
                                            </td>
                                            <td>&nbsp; </td>
                                            <td>
                                                <button class="btn btn-primary" type="button" data-i18n="urm.search" id = 'search'></button>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                               <!-- <div class="form-group" >
                                    <div class="col-sm-8 col-sm-offset-2">
                                        <button class="btn btn-primary" type="button" data-i18n="urm.search" id = 'search'></button>
                                    </div>
                                </div>-->
                                <hr/>
                                <div><label data-i18n="urm.mercinfo.basicAuthority"></label></div>
                                <div class="form-group">&nbsp;&nbsp;&nbsp;&nbsp;<input type="radio" id="recharge" name="recharge" value="recharge" tag=1></input><label for="recharge" data-i18n="urm.mercITF.recharge"></label>&nbsp;&nbsp;&nbsp;&nbsp;<input type="radio" id="settle" name="settle" value="settle" tag=1></input><label for="settle" data-i18n="urm.mercITF.settle"></label></div>
                                <div><label data-i18n="urm.mercinfo.collectionAuthority"></label></div>
                                <div class="form-group">&nbsp;&nbsp;&nbsp;&nbsp;<input type="radio" id="APPpay" name="APPpay" value="APPpay" tag=1></input><label for="APPpay" data-i18n="urm.mercITF.APPpay"></label>&nbsp;&nbsp;&nbsp;&nbsp;<input type="radio" id="barcodepay" name="barcodepay" value="barcodepay" tag=1></input><label for="barcodepay" data-i18n="urm.mercITF.barcodepay"></label>&nbsp;&nbsp;&nbsp;&nbsp;<input type="radio" id="scancodepay" name="scancodepay" value="scancodepay" tag=1></input><label for="scancodepay" data-i18n="urm.mercITF.scancodepay"></label>&nbsp;&nbsp;&nbsp;&nbsp;<input type="radio" id="IPOSpay" name="IPOSpay" value="IPOSpay" tag=1></input><label for="IPOSpay" data-i18n="urm.mercITF.IPOSpay"></label>&nbsp;&nbsp;&nbsp;&nbsp;<input type="radio" id="bankcard" name="bankcard" value="bankcard" tag=1></input><label for="bankcard" data-i18n="urm.mercITF.bankcard"></label></div>
                                <div><label data-i18n="urm.mercinfo.payAuthority"></label></div>
                                <div class="form-group">&nbsp;&nbsp;&nbsp;&nbsp;<input type="radio" id="transMpay" name="transMpay" value="transMpay" tag=1></input><label for="transMpay" data-i18n="urm.mercITF.transMpay"></label>&nbsp;&nbsp;&nbsp;&nbsp;<input type="radio" id="transbankcard" name="transbankcard" value="transbankcard" tag=1></input><label for="transbankcard" data-i18n="urm.mercITF.transbankcard"></label>&nbsp;&nbsp;&nbsp;&nbsp;<input type="radio" id="payMpay" name="payMpay"  value="payMpay" tag=1></input><label for="payMpay" data-i18n="urm.mercITF.payMpay"></label>&nbsp;&nbsp;&nbsp;&nbsp;<input type="radio" id="paybankcard" name="paybankcard" value="paybankcard" tag=1></input><label for="paybankcard" data-i18n="urm.mercITF.paybankcard"></label></div>
                                <div class="form-group" >
                                    <div class="col-sm-8 col-sm-offset-2">
                                        <button class="btn btn-primary" type="button" data-i18n="urm.setpaytype" id = 'setpaytype'></button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>

    </div>
</div>


<div th:replace="script"></div>
</body>
<script type="text/javascript" th:inline="javascript">
    $(function () {

        $("#search").click(function () {
            var mercId = $("#mercId").val() ;
            var usrNm = $("#usrNm").val() ;
            var param = {
                 "mercId" : mercId,
                 "usrNm" : usrNm
             };
            $.ajax({
                type: "POST",
                url: "/busmgr/mercitfInfoctrl/query",
                dataType:"json",
                data:param,
                success: function (data) {
                    var result = data.result;
                    if (result !='URM00000') {
                    	if(result=='mercIsNull'){
                    		swal($.i18n.t("urm.info.notexit"))
                    	}else if(result=="mercMoreThanOne"){
                    		swal($.i18n.t("urm.info.moreThanOne"))
                    	}else {
                            swal(result)
                        }
                    } else {
                       swal($.i18n.t("urm.info.searchsuc"))
                       document.getElementById("mercId").value=data.mercId;
                       document.getElementById("usrNm").value=data.usrNm;
                       if(data.settle=='settle'){
                           document.getElementById("settle").checked=true;
                           document.getElementById("settle").setAttribute('tag',1);
                       }else{
                    	   document.getElementById("settle").checked=false;
                    	   document.getElementById("settle").setAttribute('tag',0);
                       }
                       if(data.APPpay=='APPpay'){
                           document.getElementById("APPpay").checked=true;
                           document.getElementById("APPpay").setAttribute('tag',1);
                       }else{
                    	   document.getElementById("APPpay").checked=false;
                    	   document.getElementById("APPpay").setAttribute('tag',0);
                       }
                       if(data.recharge=='recharge'){
                           document.getElementById("recharge").checked=true;
                           document.getElementById("recharge").setAttribute('tag',1);
                       }else{
                           document.getElementById("recharge").checked=false;
                           document.getElementById("recharge").setAttribute('tag',0);
                       }
                       if(data.barcodepay=='barcodepay'){
                           document.getElementById("barcodepay").checked=true;
                           document.getElementById("barcodepay").setAttribute('tag',1);
                       }else{
                           document.getElementById("barcodepay").checked=false;
                           document.getElementById("barcodepay").setAttribute('tag',0);
                       }
                       if(data.scancodepay=='scancodepay'){
                           document.getElementById("scancodepay").checked=true;
                           document.getElementById("scancodepay").setAttribute('tag',1);
                       }else{
                           document.getElementById("scancodepay").checked=false;
                           document.getElementById("scancodepay").setAttribute('tag',0);
                       }
                       if(data.IPOSpay=='IPOSpay'){
                           document.getElementById("IPOSpay").checked=true;
                           document.getElementById("IPOSpay").setAttribute('tag',1);
                       }else{
                           document.getElementById("IPOSpay").checked=false;
                           document.getElementById("IPOSpay").setAttribute('tag',0);
                       }
                       if(data.bankcard=='bankcard'){
                           document.getElementById("bankcard").checked=true;
                           document.getElementById("bankcard").setAttribute('tag',1);
                       }else{
                           document.getElementById("bankcard").checked=false;
                           document.getElementById("bankcard").setAttribute('tag',0);
                       }
                       if(data.transMpay=='transMpay'){
                           document.getElementById("transMpay").checked=true;
                           document.getElementById("transMpay").setAttribute('tag',1);
                       }else{
                           document.getElementById("transMpay").checked=false;
                           document.getElementById("transMpay").setAttribute('tag',0);
                       }
                       if(data.transbankcard=='transbankcard'){
                           document.getElementById("transbankcard").checked=true;
                           document.getElementById("transbankcard").setAttribute('tag',1);
                       }else{
                           document.getElementById("transbankcard").checked=false;
                           document.getElementById("transbankcard").setAttribute('tag',0);
                       }
                       if(data.payMpay=='payMpay'){
                           document.getElementById("payMpay").checked=true;
                           document.getElementById("payMpay").setAttribute('tag',1);
                       }else{
                           document.getElementById("payMpay").checked=false;
                           document.getElementById("payMpay").setAttribute('tag',0);
                       }
                       if(data.paybankcard=='paybankcard'){
                           document.getElementById("paybankcard").checked=true;
                           document.getElementById("paybankcard").setAttribute('tag',1);
                       }else{
                           document.getElementById("paybankcard").checked=false;
                           document.getElementById("paybankcard").setAttribute('tag',0);
                       }
                    }
                },
                error : function (data) {
                    alert(data.result);
                    return;
                },
            });
        });
        $(":radio").click(function () {
        	var r = $(this).attr("name");
        	var itfSts=false;
            $(":radio[name=" + r + "]:not(:checked)").attr("tag",0);
            if ($(this).attr("tag") == 1) {
                $(this).attr("checked", false);
                $(this).attr("tag",0);
                itfSts=false;
            }else {
                $(this).attr("tag",1);
                $(this).attr("checked", true);
                itfSts=true;
            }
            var mercId = $("#mercId").val().trim();
            if(mercId==""){
                return;
            }
            var param = {
                 "mercId" : mercId,
                 "itfNm" : r,
                 "itfSts" : itfSts
            };
            $.ajax({
                type: "POST",
                url: "/busmgr/mercitfInfoctrl/itfset",
                dataType:"json",
                data:param,
                success: function (data) {
                    var result = data.result;
                    if (result !='URM00000') {
                        alert(data.result);
                    }
                },
                error : function (data) {
                    alert(data.result);
                    return;
                },
            });
        });
        $("#setpaytype").click(function () {
        	var mercId = $("#mercId").val();
        	if(mercId==''){
        		return;
        	}
	        swal({
	            title: $.i18n.t("urm.uimessage.success"),
	            text: $.i18n.t("urm.uimessage.itfsetsuc"),
	            type: "warning",
	            showCancelButton: true,
	            confirmButtonColor: "#DD6B55",
	            confirmButtonText: $.i18n.t("urm.uimessage.yes"),
	            cancelButtonText: $.i18n.t("urm.uimessage.no"),
	            closeOnConfirm: false,
	        }, function() {
	        	   window.location.href = "/csh/paytype?id=" + mercId ;
	        });
        });

        var mId = getUrlParam("id");
        if(mId != null) {
            $("#mercId").val(mId);
            $("#search").click();
        }

        //获取url参数方法
        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
            var r = window.location.search.substr(1).match(reg);  //匹配目标参数
            if (r != null) return unescape(r[2]); return null; //返回参数值
        }

    });
</script>
</html>

