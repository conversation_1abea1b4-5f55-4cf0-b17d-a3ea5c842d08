<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>

    <title data-i18n="chk.controllerLevel.title"></title>

    <div th:replace="head"></div>
</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="chk.controllerLevel.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="cpi.firstLevel"></a>
                    </li>
                    <li>
                        <a data-i18n="chk.head"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="chk.controllerLevel.content"></strong>
                    </li>
                </ol>
            </div>
        </div>

        <!--修改对账主控状态-->
        <div class="modal inmodal" id="handleModal" tabindex="-1" role="dialog"  aria-hidden="true" data-backdrop="static">
            <div class="modal-dialog">
                <div class="modal-content animated">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                        <h4 class="modal-title" data-i18n="chk.controller.updateSts" ></h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal">
                            <div class="form-group">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label" for="main_no" data-i18n="chk.mainNo"></label>
                                    <div class="col-sm-6">
                                        <input name="main_no" id="main_no" class="form-control" disabled/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label" for="oppo_no" data-i18n="chk.oppoNo"></label>
                                    <div class="col-sm-6">
                                        <input name="oppo_no" id="oppo_no" class="form-control" disabled/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label" for="chk_bus_typ" data-i18n="chk.chkBusTyp"></label>
                                    <div class="col-sm-6">
                                        <input name="chk_bus_typ" id="chk_bus_typ" class="form-control" disabled/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label" for="chk_fil_dt" data-i18n="chk.controller.chkFilDt"></label>
                                    <div class="col-sm-6">
                                        <input name="chk_fil_dt" id="chk_fil_dt" class="form-control" disabled/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label" for="chk_fil_sts" data-i18n="chk.controller.chkFilSts"></label>
                                    <div class="col-sm-6">
                                        <select name="chk_fil_sts" id="chk_fil_sts" class="form-control" >
                                            <option value="0" data-i18n="chk.chkFilSts0"></option>
                                            <option value="1" data-i18n="chk.chkFilSts1"></option>
                                            <option value="2" data-i18n="chk.chkFilSts2"></option>
                                            <option value="3" data-i18n="chk.chkFilSts3"></option>
                                            <option value="4" data-i18n="chk.chkFilSts4"></option>
                                            <option value="5" data-i18n="chk.chkFilSts5"></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label" for="rmk" data-i18n="chk.rmk"></label>
                                    <div class="col-sm-6">
                                        <input name="rmk" id="rmk" class="form-control"/>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" id="updConfirm" class="btn btn-primary" onclick="updConfirm()" data-i18n="chk.controller.confirmUpdate"></button>
                        <button type="button" class="btn btn-primary" data-dismiss="modal" data-i18n="chk.close"></button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="wrapper wrapper-content  animated fadeInRight">
            <!--查询条件-->
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form id="queryForm" class="form-horizontal">
                                <div class="form-group col-sm-4">
                                    <!--合作机构-->
                                    <label class="col-sm-4 control-label" for="mainNo" data-i18n="chk.mainNo"></label>
                                    <div class="col-sm-8">
                                        <select name="mainNo" id="mainNo" class="form-control">
                                            <option value="" data-i18n="chk.selectByNull" selected></option>
                                            <option value="ICBC" data-i18n="chk.capICBC"></option>
                                            <option value="BESTPAY" data-i18n="chk.capBESTPAY"></option>
                                            <option value="CPI" data-i18n="chk.capCPI"></option>
                                            <option value="CSH" data-i18n="chk.capCSH"></option>
                                            <option value="PWM" data-i18n="chk.capPWM"></option>
                                            <option value="TAM" data-i18n="chk.capTAM"></option>
                                            <option value="CPM" data-i18n="chk.capCPM"></option>
                                            <option value="ONR" data-i18n="chk.capONR"></option>
                                            <option value="HALL" data-i18n="chk.capHALL"></option>
                                            <option value="CPO" data-i18n="chk.capCPO"></option>
                                            <option value="CSM" data-i18n="chk.capCSM"></option>
                                            <option value="INV" data-i18n="chk.capINV"></option>
                                        </select>
                                    </div>
                                    <!--合作机构-->
                                    <label class="col-sm-4 control-label" for="oppoNo" data-i18n="chk.oppoNo"></label>
                                    <div class="col-sm-8">
                                        <select name="oppoNo" id="oppoNo" class="form-control">
                                            <option value="" data-i18n="chk.selectByNull" selected></option>
                                            <option value="ICBC" data-i18n="chk.capICBC"></option>
                                            <option value="BESTPAY" data-i18n="chk.capBESTPAY"></option>
                                            <option value="CPI" data-i18n="chk.capCPI"></option>
                                            <option value="CSH" data-i18n="chk.capCSH"></option>
                                            <option value="PWM" data-i18n="chk.capPWM"></option>
                                            <option value="TAM" data-i18n="chk.capTAM"></option>
                                            <option value="CPM" data-i18n="chk.capCPM"></option>
                                            <option value="ONR" data-i18n="chk.capONR"></option>
                                            <option value="HALL" data-i18n="chk.capHALL"></option>
                                            <option value="CPO" data-i18n="chk.capCPO"></option>
                                            <option value="CSM" data-i18n="chk.capCSM"></option>
                                            <option value="INV" data-i18n="chk.capINV"></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group col-sm-4">
                                    <!--业务类型-->
                                    <label class="col-sm-4 control-label" for="chkBusTyp" data-i18n="chk.chkBusTyp"></label>
                                    <div class="col-sm-8">
                                        <select name="chkBusTyp" id="chkBusTyp" class="form-control" >
                                            <option value="" data-i18n="chk.selectByNull"></option>
                                            <option value="01" data-i18n="chk.fastpayChk"></option>
                                            <option value="02" data-i18n="chk.ebankChk"></option>
                                            <option value="03" data-i18n="chk.refundChk"></option>
                                            <option value="04" data-i18n="chk.fundChk"></option>
                                            <option value="05" data-i18n="chk.invChk"></option>
                                            <option value="06" data-i18n="chk.tamChk"></option>
                                            <option value="07" data-i18n="chk.withdrawChk"></option>
                                        </select>
                                    </div>
                                    <!--对账状态-->
                                    <label class="col-sm-4 control-label" for="chkFilSts" data-i18n="chk.controller.chkFilSts"></label>
                                    <div class="col-sm-8">
                                        <select name="chkFilSts" id="chkFilSts" class="form-control" >
                                            <option value="" data-i18n="chk.selectByNull"></option>
                                            <option value="0" data-i18n="chk.chkFilSts0"></option>
                                            <option value="1" data-i18n="chk.chkFilSts1"></option>
                                            <option value="2" data-i18n="chk.chkFilSts2"></option>
                                            <option value="3" data-i18n="chk.chkFilSts3"></option>
                                            <option value="4" data-i18n="chk.chkFilSts4"></option>
                                            <option value="5" data-i18n="chk.chkFilSts5"></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group col-sm-4">
                                    <!--对账日期-->
                                    <label class="col-sm-4 control-label" for="beginDate" data-i18n="chk.controller.chkFilDt"></label>
                                    <div class="col-sm-4">
                                        <input name="beginDate" id="beginDate" class="form-control" value=""/>
                                    </div>
                                    <div class="col-sm-4">
                                        <input name="endDate" id="endDate" class="form-control" value=""/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-4 col-sm-offset-1">
                                        <button type="button" id="searchBtn" class="btn btn-primary" onclick="search()" data-i18n="chk.search"></button>
                                        <button type="button" id="updateBtn" class="btn btn-primary" data-toggle="modal" onclick="updateClick()" data-i18n="chk.modify"></button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">

                            <div class="table-responsive">
                                <table id="ctlTable" class="table table-striped table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th data-i18n="chk.mainNo"></th>
                                        <th data-i18n="chk.oppoNo"></th>
                                        <th data-i18n="chk.chkBusTyp"></th>
                                        <th data-i18n="chk.controller.chkFilDt"></th>
                                        <th data-i18n="chk.controller.mainTotCnt"></th>
                                        <th data-i18n="chk.controller.mainTotAmt"></th>
                                        <th data-i18n="chk.controller.chkFilSts"></th>
                                        <th data-i18n="chk.controller.totMchCnt"></th>
                                        <th data-i18n="chk.controller.totMchAmt"></th>
                                        <th data-i18n="chk.controller.longAmt"></th>
                                        <th data-i18n="chk.controller.longCnt"></th>
                                        <th data-i18n="chk.controller.shortAmt"></th>
                                        <th data-i18n="chk.controller.shortCnt"></th>
                                        <th data-i18n="chk.controller.doubtAmt"></th>
                                        <th data-i18n="chk.controller.doubtCnt"></th>
                                        <th data-i18n="chk.oprId"></th>
                                        <th data-i18n="chk.rmk"></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>

    </div>
</div>
<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script>
    var editor;
    var table;
    //A 2D array in which the first array is used to define the value options and the second array the displayed options (useful for language strings such as 'All').
    //    var array1 = [10, 25, 50, -1];
    //    var array2 = [10, 25, 50, "All"];
    var order = [1, 'asc'];

    $(document).ready(function () {

        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        i18nLoad.then(function () {
            editor = new $.fn.dataTable.Editor({
                table: "#ctlTable",
                idSrc: 'chkBatNo',
                fields: [
                    {name: "chkBatNo"},
                    {label: $.i18n.t("chk.controller.mainNo"), name: "mainNo"},
                    {label: $.i18n.t("chk.controller.oppoNo"), name: "oppoNo"},
                    {label: $.i18n.t("chk.controller.chkBusTyp"), name: "chkBusTyp"},
                    {label: $.i18n.t("chk.controller.chkFilDt"), name: "chkFilDt"},
                    {label: $.i18n.t("chk.controller.mainTotCnt"), name: "mainTotCnt"},
                    {label: $.i18n.t("chk.controller.mainTotAmt"), name: "mainTotAmt"},
                    {label: $.i18n.t("chk.controller.chkFilSts"), name: "chkFilSts"},
                    {label: $.i18n.t("chk.controller.totMchCnt"), name: "totMchCnt"},
                    {label: $.i18n.t("chk.controller.totMchAmt"), name: "totMchAmt"},
                    {label: $.i18n.t("chk.controller.longAmt"), name: "longAmt"},
                    {label: $.i18n.t("chk.controller.longCnt"), name: "longCnt"},
                    {label: $.i18n.t("chk.controller.shortAmt"), name: "shortAmt"},
                    {label: $.i18n.t("chk.controller.shortCnt"), name: "shortCnt"},
                    {label: $.i18n.t("chk.controller.doubtAmt"), name: "doubtAmt"},
                    {label: $.i18n.t("chk.controller.doubtCnt"), name: "doubtCnt"},
                    {label: $.i18n.t("chk.oprId"), name: "oprId"},
                    {label: $.i18n.t("chk.rmk"), name: "rmk"}
                ],
                i18n: {
                    edit: {button: $.i18n.t("chk.modify"), title: $.i18n.t("chk.modify"), submit: $.i18n.t("chk.modify")},
                }
            });

            editor.on('preSubmit', function (e, o, action) {
                var id = editor.field('chkBatNo');
                o.id = id.val();  // create a new parameter to pass over to the server called entityId
            });

            /**初始化日期控件**/
            var beginDatePick = $('#beginDate').datetimepicker({
                lang:'en',
                timepicker:false,
                validateOnBlur: false,
                format:'Y-m-d ',
                formatDate:'Y-m-d',
                onChangeDate: function(dateText, inst) {
                    endDatePick.datetimepicker('minDate',new Date(dateText.replace("-", "-")));
                }
            });

            var endDatePick = $('#endDate').datetimepicker({
                lang:'en',
                timepicker:false,
                validateOnBlur: false,
                format:'Y-m-d ',
                formatDate:'Y-m-d',
                // maxDate:'+1970/01/01',
            });

            table = $('#ctlTable').DataTable({
                dom: 'Blfrtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/chk/controller/findAll',
                    type: 'POST',
                    data: function (d) {
                        d.extra_search = {
                            "mainNo" : $("#mainNo").val(),
                            "oppoNo" : $("#oppoNo").val(),
                            "chkBusTyp" : $("#chkBusTyp").val(),
                            "chkFilSts" : $("#chkFilSts").val(),
                            "beginDate" : $("#beginDate").val(),
                            "endDate" : $("#endDate").val()
                        };
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
                language: {
                    url: languageUrl
                },
                select: true,
                buttons: [
//                    {extend: "edit", editor: editor},
                    'copy',
                    'csv',
                    'excel'
                ],
                columns: [{
                    data: 'mainNo',
                    render: function (data, type, row) {
                        switch (data) {
                            case "ICBC":
                                return $.i18n.t("chk.capICBC");
                            case "BESTPAY":
                                return $.i18n.t("chk.capBESTPAY");
                            case "CPI":
                                return $.i18n.t("chk.capCPI");
                            case "CSH":
                                return $.i18n.t("chk.capCSH");
                            case "PWM":
                                return $.i18n.t("chk.capPWM");
                            case "HALL":
                                return $.i18n.t("chk.capHALL");
                            case "TAM":
                                return $.i18n.t("chk.capTAM");
                            case "CPM":
                                return $.i18n.t("chk.capCPM");
                            case "ONR":
                                return $.i18n.t("chk.capONR");
                            case "CPO":
                                return $.i18n.t("chk.capCPO");
                            case "CSM":
                                return $.i18n.t("chk.capCSM");
                            case "INV":
                                return $.i18n.t("chk.capINV");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'oppoNo',
                    render: function (data, type, row) {
                        switch (data) {
                            case "ICBC":
                                return $.i18n.t("chk.capICBC");
                            case "BESTPAY":
                                return $.i18n.t("chk.capBESTPAY");
                            case "CPI":
                                return $.i18n.t("chk.capCPI");
                            case "CSH":
                                return $.i18n.t("chk.capCSH");
                            case "PWM":
                                return $.i18n.t("chk.capPWM");
                            case "HALL":
                                return $.i18n.t("chk.capHALL");
                            case "TAM":
                                return $.i18n.t("chk.capTAM");
                            case "CPM":
                                return $.i18n.t("chk.capCPM");
                            case "ONR":
                                return $.i18n.t("chk.capONR");
                            case "CPO":
                                return $.i18n.t("chk.capCPO");
                            case "CSM":
                                return $.i18n.t("chk.capCSM");
                            case "INV":
                                return $.i18n.t("chk.capINV");
                            default:
                                return data;
                        }
                    }
                }, {
                    data: 'chkBusTyp',
                    render: function (data, type, row) {
                        switch (data) {
                            case "01":
                                return $.i18n.t("chk.fastpayChk");
                            case "02":
                                return $.i18n.t("chk.ebankChk");
                            case "03":
                                return $.i18n.t("chk.refundChk");
                            case "04":
                                return $.i18n.t("chk.fundChk");
                            case "05":
                                return $.i18n.t("chk.invChk");
                            case "06":
                                return $.i18n.t("chk.tamChk");
                            case "07":
                                return $.i18n.t("chk.withdrawChk");
                            default:
                                return data;
                        }
                    }
                }, {
                    data: 'chkFilDt'
                }, {
                    data: 'mainTotCnt'
                }, {
                    data: 'mainTotAmt'
                }, {
                    data: 'chkFilSts',
                    render: function (data, type, row) {
                        switch (data) {
                            case "0":
                                return $.i18n.t("chk.chkFilSts0");
                            case "1":
                                return $.i18n.t("chk.chkFilSts1");
                            case "2":
                                return $.i18n.t("chk.chkFilSts2");
                            case "3":
                                return $.i18n.t("chk.chkFilSts3");
                            case "4":
                                return $.i18n.t("chk.chkFilSts4");
                            case "5":
                                return $.i18n.t("chk.chkFilSts5");
                            default:
                                return data;
                        }
                    }
                }, {
                    data: 'totMchCnt'
                }, {
                    data: 'totMchAmt'
                },{
                    data: 'longAmt'
                }, {
                    data: 'longCnt'
                }, {
                    data: 'shortAmt'
                }, {
                    data: 'shortCnt'
                }, {
                    data: 'doubtAmt'
                }, {
                    data: 'doubtCnt'
                }, {
                    data: 'oprId'
                }, {
                    data: 'rmk'
                }
                ]
            });
        });

        $('#handleModal').on('shown.bs.modal',
            function () {
                var row = table.row('.selected');
                if(row.length == 0) {
                    $("#handleModal").modal("hide");
                    return;
                }
            }
        );

    });

    <!--查询按钮-->
    function search() {
        table.ajax.reload();
    }

    <!--点击修改按钮-->
    function updateClick() {
        <!--获取表格中选中的单行-->
        var row = table.row('.selected');

        if(row.length == 0) {
            swal($.i18n.t("chk.swal-fail"), $.i18n.t("chk.shouldSelectOne"), "error");
            return;
        }
        <!--获取选中行的内容-->
        var rowData = table.row(row).data();
        $('#handleModal').on('show.bs.modal',
            function () {
                $("#main_no").val(convertOrng(rowData["mainNo"]));
                $("#oppo_no").val(convertOrng(rowData["oppoNo"]));
                $("#chk_bus_typ").val(convertChkBusTyp(rowData["chkBusTyp"]));
                $("#chk_fil_dt").val(rowData["chkFilDt"]);
                $("#chk_fil_sts").val(rowData["chkFilSts"]);
            }
        );
        $("#handleModal").modal("show");
    }

    <!--银行名称转换-->
    function convertOrng(corgId) {
        switch (corgId) {
            case "ICBC":
                return $.i18n.t("chk.capICBC");
            case "BESTPAY":
                return $.i18n.t("chk.capBESTPAY");
            case "CPI":
                return $.i18n.t("chk.capCPI");
            case "CSH":
                return $.i18n.t("chk.capCSH");
            default:
                return data;
        }
    }

    <!--类型转换-->
    function convertChkBusTyp(chkBusTyp) {
        switch (chkBusTyp) {
            case "01":
                return $.i18n.t("chk.fastpayChk");
            case "02":
                return $.i18n.t("chk.ebankChk");
            case "03":
                return $.i18n.t("chk.refundChk");
            case "04":
                return $.i18n.t("chk.fundChk");
            case "05":
                return $.i18n.t("chk.invChk");
            case "06":
                return $.i18n.t("chk.tamChk");
            case "07":
                return $.i18n.t("chk.withdrawChk");
            default:
                return data;
        }
    }

    <!--确认修改-->
    function updConfirm() {
        var chkFilSts = $("#chk_fil_sts").val();
        var rmk = $("#rmk").val();
        var row = table.row('.selected');
        if(row.length == 0) {
            swal($.i18n.t("chk.swal-fail"), $.i18n.t("chk.shouldSelectOne"), "error");
            return;
        }

        <!--获取选中行的内容-->
        var rowData = table.row(row).data();
        var chkBatNo = rowData["chkBatNo"];
        if(chkFilSts == null || chkFilSts == "") {
            swal($.i18n.t("chk.swal-fail"), $.i18n.t("chk.chkFilStsNull"), "error");
            return;
        }
        //确认提交弹出框
        swal({
            title: $.i18n.t("chk.updConfirmTitle"),
            text: $.i18n.t("chk.updConfirmText"),
            type: "warning",
            showCancelButton: true,
            cancelButtonText: $.i18n.t("chk.swal-cancel"),
            confirmButtonColor: "#DD6B55",
            confirmButtonText: $.i18n.t("chk.swal-confirm"),
            closeOnConfirm: true
        }, function () {
            <!--隐藏模态框-->
            $("#handleModal").modal("hide");
            <!--ajax异步调起后台服务-->
            $.ajax({
                url:"/chk/controller/modify",
                data: {
                    "chkBatNo":chkBatNo,
                    "chkFilSts":chkFilSts,
                    "rmk":rmk
                },
                dataType: "json",
                type:"post",
                success:function(data){
                    var msgCd = data.msgCd;
                    $("#handleModal").modal("hide");
                    if (msgCd == "TMS00000") {
                        swal($.i18n.t("chk.swal-sucess"), $.i18n.t("chk.updateSuss"), "success");
                        table.ajax.reload();
                    } else {
                        swal($.i18n.t("chk.swal-fail"), msgCd + $.i18n.t("chk.updateFail"), "error");
                    }
                },
                error: function(){
                    swal($.i18n.t("chk.swal-fail"), $.i18n.t("chk.systemException"), "error");
                    $("#handleModal").modal("hide");
                }
            });
        });
    }
</script>
</body>

</html>