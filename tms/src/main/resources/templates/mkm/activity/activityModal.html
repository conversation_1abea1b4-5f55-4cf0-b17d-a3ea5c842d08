<div class="modal inmodal" id="handleModal" tabindex="-1" role="dialog"  aria-hidden="true" data-backdrop="static">
    <div class="modal-dialog" style="width: 1000px;">
        <div class="modal-content animated">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title" data-i18n="activity.sysmanage"></h4>
            </div>
            <div class="modal-body" style="height:500px;overflow:auto; ">
                <form class="form-horizontal" id="activityForm">
                    <div class="form-group">
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="atvNm" data-i18n="activity.atvNm" aria-flowto="right"></label><!--内部用户号-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="atvNm" id="atvNm"  >
                            </div>
                            <label class="col-sm-3 control-label" for="mkTool" data-i18n="activity.mkTool"></label><!--付款类型-->
                            <div class="col-sm-3">
                                <select type="" class="form-control" name="mkTool"
                                        required="required" maxlength="02" id = 'mkTool' >
                                    <option value='01' data-i18n="activity.eleCoupon" ></option>
                                    <option value='02' data-i18n="activity.seaCyy"></option>
                                    <!--<option value='03' data-i18n="activity.coupon"></option>-->
                                    <option value='04' data-i18n="activity.discount"></option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="beginTime" data-i18n="activity.startTm"></label><!--付款银行-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="beginTime"
                                       required="required" id="beginTime" >
                            </div>
                            <label class="col-sm-3 control-label" for="endTime" data-i18n="activity.endTm"></label><!--付款金额-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="endTime"
                                       required="required" id="endTime" >
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="total" data-i18n="activity.tatol"></label><!--收款银行-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="total" id="total" required="required" >
                            </div>

                            <label class="col-sm-3 control-label" for="totalAmt" data-i18n="activity.tatolAmt"></label><!--收款户名-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="totalAmt" id="totalAmt"  required="required" >
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="receiveTimes" data-i18n="activity.receiveTimes"></label><!--收款卡号 **** **** **** 1234 -->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="receiveTimes" id="receiveTimes" required="required" >
                            </div>
                            <label class="col-sm-3 control-label" for="receiveCycle" data-i18n="activity.receiveCycle"></label><!--订单状态-->
                            <div class="col-sm-3">
                            <select type="" class="form-control" name="receiveCycle" required="required" maxlength="02" id = 'receiveCycle' >
                                <option value='N' data-i18n="activity.receiveCycleOption.N" ></option>
                                <option value='day' data-i18n="activity.receiveCycleOption.day" ></option>
                                <option value='month' data-i18n="activity.receiveCycleOption.month"></option>
                                <option value='year' data-i18n="activity.receiveCycleOption.year"></option>
                            </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="item" data-i18n="activity.item"></label><!--订单状态-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="item" id="item" >
                            </div>
                            <label class="col-sm-3 control-label" for="costSide" data-i18n="activity.costSide"></label><!--订单状态-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="costSide" id="costSide" >
                            </div>
                        </div>

                        <!-- 电子券-->
                        <div id ='coupon'>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="amt" data-i18n="activity.couponName"></label><!--处理理由-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="couponName" id="couponName" required="required" >
                            </div>
                            <label class="col-sm-3 control-label" for="userAppoint" data-i18n="activity.userAppoint"></label>
                            <div class="col-sm-3">
                                <!--<button data-i18n="activity.userAppoint" class="btn btn-primary" id = 'userAppoint'></button>-->
                               <!-- <input type="in" name="userAppoint" id="userAppoint"  >-->
                                <div  name="userAppoint" id="userAppoint"  ></div>

                            </div>
                            <!--<label class="col-sm-3 control-label" for="userAppoint" data-i18n="activity.userAppoint"></label>&lt;!&ndash;处理理由&ndash;&gt;-->
                            <!--<div class="col-sm-3">-->
                                <!--<input type="button" class="form-control" name="userAppoint" id="userAppoint"  >-->
                            <!--</div>-->


                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="amt" data-i18n="activity.amt"></label><!--处理理由-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="amt" id="amt" required="required" >
                            </div>
                            <label class="col-sm-3 control-label" for="discount" data-i18n="activity.disc"></label><!--处理理由-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="discount" id="discount"  >
                            </div>


                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="startDays" ><span data-i18n="activity.couponValTm"></span>:
                                <span data-i18n="activity.couponValTmRemark.head"></span>(<span data-i18n="activity.couponValTmRemark.unit"> </span>)</label><!--处理理由-->
                            <div class="col-sm-3">
                                 <input type="text" class="form-control" name="startDays" id="startDays" >
                            </div>

                            <label class="col-sm-3 control-label" for="couponValDays" data-i18n="activity.couponValDays"></label><!--处理理由-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="couponValDays" id = "couponValDays"  required="required" >
                            </div>
                        </div>
                        <div class="form-group">

                            <label class="col-sm-3 control-label" for="couponInvalTm" data-i18n="activity.couponInvalTm"></label><!--处理理由-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="couponInvalTm" id = "couponInvalTm"  required="required" >
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="minAmt" data-i18n="activity.minAmt"></label><!--处理理由-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="minAmt" id="minAmt"  required="required" >
                            </div>
                            <label class="col-sm-3 control-label" for="maxAmt" data-i18n="activity.maxAmt"></label><!--处理理由-->
                            <div class="col-sm-3">
                                <input type="text" class="form-control" name="maxAmt" id="maxAmt"  required="required" >
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="instId" data-i18n="activity.instId"></label><!--处理理由-->
                            <div class="col-sm-3">
                                <textarea class="form-control" name="instId" id="instId" > </textarea>
                            </div>
                        </div>
                        </div>

                        <div class="form-group" id = 'examineDiv'>
                            <label class="col-sm-4 control-label" for="examineStatus" data-i18n="activity.examine"></label><!--付款类型-->
                            <div class="col-sm-6">
                                <select type="" class="form-control" name="examineStatus"
                                        required="required" maxlength="02" id = 'examineStatus' >
                                    <option value='00' data-i18n="activity.examimeNoPass" ></option>
                                    <option value='02' data-i18n="activity.examimePass"></option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group" id = 'examineResonDiv'>
                            <label class="col-sm-4 control-label" for="examineReson" data-i18n="activity.examineReson"></label><!--付款类型-->
                            <div class="col-sm-6">
                                <textarea class="form-control" name="examineReson" id="examineReson" maxlength="100"> </textarea>
                            </div>
                        </div>
                        <input id="id" name="id" value="" type="hidden"/><!--提现内部订单号-->
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="activityAdd" class="btn btn-white" data-i18n="activity.add"></button>
                <button type="button" id="activityModify" class="btn btn-white" data-i18n="activity.modify"></button>
                <button type="button" id="activityExamine" class="btn btn-white" data-i18n="activity.examine"></button>
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>