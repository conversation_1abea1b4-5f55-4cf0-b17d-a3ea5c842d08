<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>

    <title data-i18n="activity.title"></title>

    <div th:replace="head"></div>

</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="activity.head"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="activity.sysmanage"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="activity.activitymanage"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form method="post" id = "form" class="form-horizontal" th:action="@{${api}}">
                                <div class="form-group" ><label class="col-sm-2 control-label" data-i18n="activity.atvNm"></label>
                                    <div class="col-sm-4"><input type="text" class="form-control" name="atvNm" id="atvNm"
                                                                  th:value="${mkActivity}? ${mkActivity.atvNm}"
                                                                  required="required" maxlength="100" ></div>
                                    <label class="col-sm-2 control-label" data-i18n="activity.mkTool"></label>
                                    <div class="col-sm-4"><select type="" class="form-control" name="mkTool"
                                                                  th:value="${mkActivity}? ${mkActivity.mkTool}"
                                                                  required="required" maxlength="02" id = 'mkTool' th:disabled = "${mkActivity}? true">
                                        <option value='01' data-i18n="activity.eleCoupon" ></option>
                                        <option value='02' data-i18n="activity.seaCyy"></option>
                                        <option value='03' data-i18n="activity.coupon"></option>
                                        <option value='04' data-i18n="activity.discount"></option>
                                    </select>
                                    </div>
                                </div>
                                <div class="hr-line-dashed" ></div>
                                <div class="form-group"><label class="col-sm-2 control-label" data-i18n="activity.startTm"></label>
                                        <div class="col-sm-4"><input type="text" class="form-control" name="beginTime"
                                                                     required="required" id="beginTime" th:disabled = "${mkActivity}? true"
                                                                     th:value="${mkActivity}? ${mkActivity.beginTime}">
                                        </div>
                                    <label
                                            class="col-sm-2 control-label" data-i18n="activity.endTm"></label>
                                    <div class="col-sm-4"><input type="text" class="form-control" name="endTime"
                                                                 required="required" id="endTime" th:value="${mkActivity}? ${mkActivity.endTime}">
                                    </div>
                                </div>

                                <div class="hr-line-dashed"></div>
                                <div class="form-group" ><label
                                            class="col-sm-2 control-label" data-i18n="activity.tatol"></label>
                                        <div class="col-sm-4"><input type="text" class="form-control" name="total" id="total"
                                                                     th:value="${mkActivity}? ${mkActivity.total}"
                                                                     required="required" >
                                        </div>
                                    <label
                                            class="col-sm-2 control-label" data-i18n="activity.tatolAmt"></label>
                                    <div class="col-sm-4"><input type="text" class="form-control" name="totalAmt" id="totalAmt"
                                                                 th:value="${mkActivity}? ${mkActivity.totalAmt}"
                                                                 required="required" >
                                    </div>
                                </div>

                                <div class="hr-line-dashed"></div>
                                <div class="form-group" ><label
                                            class="col-sm-2 control-label" data-i18n="activity.receiveTimes"></label>
                                        <div class="col-sm-4"><input type="text" class="form-control" name="receiveTimes" id="receiveTimes"
                                                                     th:value="${mkActivity}? ${mkActivity.receiveTimes}"
                                                                     required="required" >
                                        </div>
                                    <label
                                            class="col-sm-2 control-label" data-i18n="activity.item"></label>
                                    <div class="col-sm-4"><input type="text" class="form-control" name="item" id="item"
                                                                 th:value="${mkActivity}? ${mkActivity.item}"
                                                                 required="required" th:disabled = "${mkActivity}? true">
                                    </div>
                                </div>
                                <!-- 电子券-->
                                <div id="coupon">
                                <div class="hr-line-dashed"></div>
                                <div class="form-group" ><label
                                        class="col-sm-2 control-label" data-i18n="activity.amt"></label>
                                    <div class="col-sm-4"><input type="text" class="form-control" name="amt" id="amt"
                                                                 th:value="${mkActivity}? ${mkActivity.amt}"
                                                                 required="required" >
                                    </div>
                                    <label class="col-sm-2 control-label" data-i18n="activity.disc"></label>
                                    <div class="col-sm-3"><input type="text" class="form-control" name="discount" id="discount"
                                                                 th:value="${mkActivity}? ${mkActivity.discount}"
                                                                 required="required" >
                                    </div>

                                </div>
                                <div class="hr-line-dashed"></div>
                                <div class="form-group" ><label
                                            class="col-sm-2 control-label" data-i18n="activity.couponValTm"></label>
                                        <div class="col-sm-4"><input type="text" class="form-control" name="couponValTm" id="couponValTm"
                                                                     th:value="${mkActivity}? ${mkActivity.couponValTm}"
                                                                     required="required" th:disabled = "${mkActivity}? true">
                                        </div>
                                    <label
                                            class="col-sm-2 control-label" data-i18n="activity.couponInvalTm"></label>
                                    <div class="col-sm-4"><input type="text" class="form-control" name="couponInvalTm" id = "couponInvalTm"
                                                                 th:value="${mkActivity}? ${mkActivity.couponInvalTm}"
                                                                 required="required" th:disabled = "${mkActivity}? true">
                                    </div>
                                </div>
                                <div class="hr-line-dashed"></div>
                                <div class="form-group" ><label
                                            class="col-sm-2 control-label" data-i18n="activity.minAmt"></label>
                                        <div class="col-sm-4"><input type="text" class="form-control" name="minAmt" id="minAmt"
                                                                           th:value="${mkActivity}? ${mkActivity.minAmt}"
                                                                           required="required" >
                                    </div>
                                    <label class="col-sm-2 control-label" data-i18n="activity.maxAmt"></label>
                                    <div class="col-sm-4"><input type="text" class="form-control" name="maxAmt" id="maxAmt"
                                                                 th:value="${mkActivity}? ${mkActivity.maxAmt}"
                                                                 required="required" >
                                    </div>
                                </div>
                                <div class="hr-line-dashed"></div>
                                <div class="form-group" ><label
                                            class="col-sm-2 control-label" data-i18n="activity.instId"></label>
                                        <div class="col-sm-4"><textarea class="form-control" name="instId" id="instId"
                                                                     th:value="${mkActivity}? ${mkActivity.instId}" th:text="${mkActivity}? ${mkActivity.instId}"
                                                                       > </textarea>
                                        </div>
                                </div>
                                </div>
                                <div class="hr-line-dashed"></div>
                                <div class="form-group">
                                    <div class="col-sm-8 col-sm-offset-2">
                                        <button class="btn btn-primary" type="button" data-i18n="activity.save" id = 'save'></button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>

    </div>
</div>


<div th:replace="script"></div>
</body>
<script type="text/javascript" th:inline="javascript">
    $(function () {
        /**初始化日期控件**/
       var beginTimePick = $('#beginTime').datetimepicker({
            lang:'en',
            timepicker:true,
            validateOnBlur: false,
            format:'Y-m-d H:i:00',
            formatDate:'Y-m-d',
            closeOnInputClick:'',
            onChangeDate: function(dateText, inst) {
                endTimePick.datetimepicker('minDate',new Date(dateText.replace("-", "-")));
            }
        });

        var endTimePick = $('#endTime').datetimepicker({
            lang:'en',
            timepicker:true,
            validateOnBlur: false,
            format:'Y-m-d H:i:00',
            formatDate:'Y-m-d',
           // maxDate:'+1970/01/01',
        });
        var couponValTm = $('#couponValTm').datetimepicker({
            lang:'en',
            timepicker:true,
            validateOnBlur: false,
            format:'Y-m-d H:i:00',
            formatDate:'Y-m-d',
           // maxDate:'+1970/01/01',
        });
        var couponInvalTm = $('#couponInvalTm').datetimepicker({
            lang:'en',
            timepicker:true,
            validateOnBlur: false,
            format:'Y-m-d H:i:00',
            formatDate:'Y-m-d',
           // maxDate:'+1970/01/01',
        });
        /*<![CDATA[*/
        var uri = [[${api}]];
        if (uri.indexOf("modify")>0){
            var mkTool = [[${mkTool}]];
            $("#mkTool").val(mkTool);
            if (mkTool == '02') {
                $("#coupon").hide();
            }else {
                $("#coupon").show();
            }
        }

        /*]]>*/
        $("#mkTool").change(function () {

            var mkTool = $("#mkTool").val();
            if (mkTool == '02') {
                $("#coupon").hide();
            }else {
                $("#coupon").show();
            }
        });
        //动态设置最小值
        $("#save").click(function () {
            var atvNm = $("#atvNm").val() ;
            var mkTool = $("#mkTool").val();
            var endTime = $("#endTime").val();
            var beginTime = $("#beginTime").val();
            var total = $("#total").val();
            var totalAmt = $("#totalAmt").val();
            var receiveTimes = $("#receiveTimes").val();
            var item = $("#item").val();
            var amt = $("#amt").val();
            var couponValTm = $("#couponValTm").val();
            var couponInvalTm = $("#couponInvalTm").val();
            var minAmt = $("#minAmt").val();
            var maxAmt = $("#maxAmt").val();
            var instId = $("#instId").val();
            var discount = $("#discount").val();
            var param;
            if(uri.indexOf("add") >0){
                param = {
                    "atvNm" : atvNm,
                    "mkTool" : mkTool,
                    "beginTime" : beginTime,
                    "endTime" : endTime,
                    "total" : total,
                    "totalAmt" : totalAmt,
                    "receiveTimes" : receiveTimes,
                    "item" : item,
                    "amt" : amt,
                    "couponValTm" : couponValTm,
                    "couponInvalTm" : couponInvalTm,
                    "minAmt" : minAmt,
                    "maxAmt" : maxAmt,
                    "instId" : instId,
                    "discount" : discount,

                };
            }else{
                param = {
                    "atvNm" : atvNm,
                    "endTime" : endTime,
                    "total" : total,
                    "totalAmt" : totalAmt,
                    "receiveTimes" : receiveTimes,
                    "item" : item,
                    "minAmt" : minAmt,
                    "maxAmt" : maxAmt,
                    "amt" : amt,
                    "instId" : instId,
                    "discount" : discount,

                };
            }
            if(check()){

            }
            $.ajax({
                type: "POST",
                url: uri,
                dataType:"json",
                data:param,
                success: function (data) {
                    var result = data.result;
                    if (result !='MKM00000') {
                        alert(data.result);
                    } else {
                       alert( $.i18n.t("activity.success"));
                        window.location.href='/mkm/activity';
                    }
                },
                error : function (data) {
                    alert(data.result);
                },
            });
        });
    });
    function check() {

    }
</script>
</html>

