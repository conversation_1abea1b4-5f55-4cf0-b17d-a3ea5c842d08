<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">

<head>

    <title data-i18n="activity.title"></title>

    <div th:replace="head"></div>
</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="activity.head"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.busmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="activity.sysmanage"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="activity.activitymanage"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form id="queryForm" class="form-horizontal">
                                <div class="form-group col-sm-4">
                                    <!--请求订单号-->
                                    <label class="col-sm-4 control-label" for="sId" data-i18n="activity.id"></label>
                                    <div class="col-sm-8">
                                        <input type="text" class="form-control" name="sId" id="sId">
                                    </div>

                                    <label class="col-sm-4 control-label" for="sBeginTime" data-i18n="activity.startTm" ></label>
                                    <div class="col-sm-8">
                                        <input type="text" class="form-control" name="sBeginTime" id="sBeginTime">
                                    </div>
                                </div>
                                <div class="form-group col-sm-4">
                                    <label class="col-sm-4 control-label" for="sId" data-i18n="activity.atvNm"></label>
                                    <div class="col-sm-8">
                                        <input type="text" class="form-control" name="sAtvNm" id="sAtvNm">
                                    </div>
                                    <label class="col-sm-4 control-label" for="sEndTime" data-i18n="activity.endTm" ></label>
                                    <div class="col-sm-8">
                                        <input type="text" class="form-control" name="sEndTime" id="sEndTime">
                                    </div>
                                </div>
                                <div class="form-group col-sm-4">
                                    <!--付款银行-->
                                    <label class="col-sm-4 control-label" for="sStatus" data-i18n="activity.statusN"></label>
                                    <div class="col-sm-8">
                                        <select class="form-control"  name="sStatus" id="sStatus" >
                                            <option value='' data-i18n="activity.all"></option>
                                            <option value='00' data-i18n="activity.N"></option>
                                            <option value='02' data-i18n="activity.F"></option>
                                            <option value='04' data-i18n="activity.TF"></option>
                                            <option value='05' data-i18n="activity.S"></option>
                                            <option value='03' data-i18n="activity.E"></option>
                                        </select>

                                    </div>
                                </div>
                                <div class="form-group">
                                        <div class="col-sm-4 col-sm-offset-1">
                                            <button type="button" id="query" class="btn btn-primary" data-i18n="activity.search"></button>
                                            <button type="button" id="addBtn" class="btn btn-primary" data-toggle="modal"  data-i18n="activity.add"></button>
                                            <button type="button" id="modifyBtn" class="btn btn-primary" data-toggle="modal"  data-i18n="activity.modify"></button>
                                            <button type="button" id="activityStop" class="btn btn-primary" data-i18n="activity.stop"></button>
                                            <button type="button" id="activityRecover" class="btn btn-primary" data-i18n="activity.recover"></button>
                                        </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>



            <!--活动增加和修改详情-->
            <div th:replace="mkm/activity/activityModal"></div>



            <!--数据表格-->
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <div class="table-responsive">
                                <div class="table-responsive">
                                    <table id="dataTables" class="table table-striped table-bordered table-hover ">
                                        <thead>
                                        <tr>
                                            <th data-i18n="activity.id"></th>
                                            <th data-i18n="activity.atvNm"></th>
                                            <th data-i18n="activity.statusN"></th>
                                            <th data-i18n="activity.mkTool"></th>
                                            <th data-i18n="activity.tatol"></th>
                                            <th data-i18n="activity.tatolAmt"></th>
                                            <th data-i18n="activity.releaseNum"></th>
                                            <th data-i18n="activity.releaseAmt"></th>
                                            <th data-i18n="activity.aclt"></th>
                                            <th data-i18n="activity.acltAmt"></th>
                                            <th data-i18n="activity.amt"></th>
                                            <th data-i18n="activity.receiveTimes"></th>
                                            <th data-i18n="activity.startTm"></th>
                                            <th data-i18n="activity.endTm"></th>
                                            <th sec:authorize="hasPermission('','/mkm/activity:delete')"
                                                data-i18n="activity.delete"></th>
                                        </tr>
                                        </thead>
                                    </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
        <div th:replace="footer"></div>
    </div>
</div>

<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script type="text/javascript" th:inline="javascript">
    var table;
    $(document).ready(function () {
        var languageUrl;

        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }
        i18nLoad.then(function () {
            table = $('#dataTables').DataTable({
                ajax: {
                    contentType: 'application/json',
                    url: '/mkm/activity/findAll',
                    type: 'POST',
                    data: function (d) {
                        var id = $("#sId").val();
                        var atvNm = $("#sAtvNm").val();
                        var status = $("#sStatus").val();
                        var endTime = $("#sEndTime").val();
                        var beginTime = $("#sBeginTime").val();

                        d.extra_search = {
                            "id":id,
                            "atvNm":atvNm,
                            "endTime":endTime,
                            "beginTime":beginTime,
                            "status":status
                        };
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                responsive: true,
                processing: true,
                searching: false,
                select: true,
                language: {
                    url: languageUrl
                    //url: '//cdn.datatables.net/plug-ins/1.10.15/i18n/Chinese.json'
                },
                columns: [{
                    data: 'id'
                }, {
                    data: 'atvNm'
                }, {
                    data: 'status',
                    render: function (data, type, row) {
                        switch (data) {
                            case '00':
                                return $.i18n.t("activity.N");
                            case '02':
                                return $.i18n.t("activity.F");
                            case '03':
                                return $.i18n.t("activity.E");
                            case '04':
                                return $.i18n.t("activity.TF");
                            case '05':
                                return $.i18n.t("activity.S");
                            default:
                                return "";
                        }
                    }
                }, {
                    data: 'mkTool',
                    render: function (data, type, row) {
                        switch (data) {
                            case '01':
                                return $.i18n.t("activity.eleCoupon");
                            case '02':
                                return $.i18n.t("activity.seaCyy");
                            case '03':
                                return $.i18n.t("activity.coupon");
                            case '04':
                                return $.i18n.t("activity.discount");
                            default:
                                return "";

                        }
                    }
                },{
                    data: 'total'
                },{
                    data: 'totalAmt'
                },{
                    data: 'remainNum',
                },{
                    data: 'remainAmt',
                },{
                    data: 'aclt'
                },{
                    data: 'acltAmt'
                },{
                    data: 'amt'
                },{
                    data: 'receiveTimes'
                },{
                    data: 'beginTime'
                },{
                    data: 'endTime'
                },

                    /*[# sec:authorize="hasPermission('','/mkm/activity:delete')"]*/
                    {
                        data: 'id',
                        orderable: false,
                        searchable: false,
                        render: function (data, type, row) {
                            if(row.status != '00'){
                                return '<span name="trash" title="' + $.i18n.t("activity.delete") + '" id="' + row.id + '" onclick="ajaxClick(\'trash\',\'DELETE\',\'' + row.id + '\')" ' +
                                    'data="/mkm/activity/delete/' + data + '" ' + 'style="cursor: pointer" class="fa fa-trash"></span></td>';
                            } else {
                                return "";
                            }
                        }
                    }
                    /*[/]*/
                ]
            });
        });

        $("#query").click(function () {
            table.ajax.reload();
        })
        $("#mkTool").change(function () {

            var mkTool = $("#mkTool").val();
            if (mkTool == '02') {
                $("#coupon").hide();
            }else {
                $("#coupon").show();
            }
        });
        //检查数据
        function check() {

        }
        //添加
        $("#activityAdd").click(function () {

            var handleReason = check();
            if(handleReason != null && handleReason != "") {
                swal($.i18n.t("activity.swal-fail"), handleReason, "error");
                return;
            }
            //确认提交弹出框
            swal({
                title: $.i18n.t("activity.addConfirmTitle"),
                text: $.i18n.t("activity.addConfirmText"),
                type: "warning",
                showCancelButton: true,
                cancelButtonText: $.i18n.t("activity.cancel"),
                confirmButtonColor: "#DD6B55",
                confirmButtonText: $.i18n.t("activity.confirm"),
                closeOnConfirm: false
            }, function () {
                if (!submitFlg) {
                    swal($.i18n.t("activity.swal-fail"),$.i18n.t("activity.check.repeat") , "error");
                    return ;
                }
                submitFlg = false ;
                operate("/mkm/activity/add","add");
            });
        });


        //修改
        $("#activityModify").click(function () {

            var id = $("#id").val();
            var handleReason = check();
            if(handleReason != null && handleReason != "") {
                swal($.i18n.t("activity.swal-fail"), handleReason, "error");
                return;
            }
            //确认提交弹出框
            swal({
                title: $.i18n.t("activity.addConfirmTitle"),
                text: $.i18n.t("activity.addConfirmText"),
                type: "warning",
                showCancelButton: true,
                cancelButtonText: $.i18n.t("activity.cancel"),
                confirmButtonColor: "#DD6B55",
                confirmButtonText: $.i18n.t("activity.confirm"),
                closeOnConfirm: false
            }, function () {
                if (!submitFlg) {
                    swal($.i18n.t("activity.swal-fail"),$.i18n.t("activity.check.repeat") , "error");
                    return ;
                }
                submitFlg = false ;
                operate("/mkm/activity/modify/"+id,"modify");
            });
        });
        var submitFlg  = true ;
        function operate(url,type) {

            <!--隐藏模态框-->
            var atvNm = $("#atvNm").val() ;
            var mkTool = $("#mkTool").val();
            var endTime = $("#endTime").val();
            endTime = endTime.replace(/:/g,"").replace(/-/g,"").replace(/ /g,"");
            var beginTime = $("#beginTime").val();
            beginTime = beginTime.replace(/:/g,"").replace(/-/g,"").replace(/ /g,"");
            var total = $("#total").val();
            var totalAmt = $("#totalAmt").val();
            var receiveTimes = $("#receiveTimes").val();
            var receiveCycle = $("#receiveCycle").val();
            var item = $("#item").val();
            var amt = $("#amt").val();
            var couponName = $("#couponName").val();
//            var couponValTm = $("#couponValTm").val();
//            couponValTm = couponValTm.replace(/:/g,"").replace(/-/g,"").replace(/ /g,"");
            var couponInvalTm = $("#couponInvalTm").val();
            couponInvalTm = couponInvalTm.replace(/:/g,"").replace(/-/g,"").replace(/ /g,"");
            var minAmt = $("#minAmt").val();
            var startDays = $("#startDays").val();
            var couponValDays = $("#couponValDays").val();
            var maxAmt = $("#maxAmt").val();
            var instId = $("#instId").val();
            var discount = $("#discount").val();
            var costSide = $("#costSide").val();
            var param;
            if(type == 'add') {
                param = {
                    "atvNm" : atvNm,
                    "mkTool" : mkTool,
                    "beginTime" : beginTime,
                    "endTime" : endTime,
                    "total" : total,
                    "totalAmt" : totalAmt,
                    "receiveTimes" : receiveTimes,
                    "receiveCycle" : receiveCycle,
                    "filePath" : filePath,
                    "costSide" : costSide,
                    "item" : item,
                    "amt" : amt,
//                    "couponValTm" : couponValTm,
                    "startDays" : startDays,
                    "couponValDays" : couponValDays,
                    "couponName" : couponName,
                    "couponInvalTm" : couponInvalTm,
                    "minAmt" : minAmt,
                    "maxAmt" : maxAmt,
                    "instId" : instId,
                    "discount" : discount,

                };
            } else {
                param = {
                    "atvNm" : atvNm,
                    "endTime" : endTime,
                    "total" : total,
                    "totalAmt" : totalAmt,
                    "receiveTimes" : receiveTimes,
                    "receiveCycle" : receiveCycle,
                    "item" : item,
                    "minAmt" : minAmt,
                    "maxAmt" : maxAmt,
                    "amt" : amt,
                    "instId" : instId,
                    "discount" : discount,

                };
            }

            if (checkParam(type ,param ) ) {
                submitFlg  = true ;
                return false;
            }

            <!--ajax异步调起后台服务-->
            $.ajax({
                url:url,
                data:param,
                dataType: "json",
                type:"post",
                success:function(data){
                    submitFlg = true ;
                    var result = data.result;
                    if (result !='MKM00000') {
                        swal($.i18n.t("activity.swal-fail"), result , "error");
                    } else {
                        swal($.i18n.t("activity.success"));
                        $("#handleModal").modal("hide");
                        table.ajax.reload();
                    }

                },
                error: function(){
                    submitFlg = true ;
                    swal($.i18n.t("activity.swal-fail") , "error");
                }
            });
        }
        $.ajaxSetup({headers: {'X-CSRF-TOKEN': $("#csrf_token").attr("content")}});

        //检查提交的参数
        function checkParam(type , param) {
            //alert("ni hao ");
            if (type == 'add' ) {
                if (isBlank(param.atvNm)) {
                    //活动名称不能为空
                    swal($.i18n.t("activity.swal-fail"),$.i18n.t("activity.check.atvNmIsNull") , "error");
                    return true ;
                }
                if (isBlank(param.mkTool)) {
                    //活动名称不能为空
                    swal($.i18n.t("activity.swal-fail"),$.i18n.t("activity.check.mkToolIsNull") , "error");
                    return true ;
                }
                if (isBlank(param.beginTime)) {
                    //活动名称不能为空
                    swal($.i18n.t("activity.swal-fail"),$.i18n.t("activity.check.beginTimeIsNull") , "error");
                    return true ;
                }
                if (isBlank(param.endTime)) {
                    //活动名称不能为空
                    swal($.i18n.t("activity.swal-fail"),$.i18n.t("activity.check.endTimeIsNull") , "error");
                    return true ;
                }
                if (isBlank(param.total)) {
                    //活动名称不能为空
                    swal($.i18n.t("activity.swal-fail"),$.i18n.t("activity.check.totalIsNull") , "error");
                    return true ;
                }
                if (isBlank(param.totalAmt)) {
                    //活动名称不能为空
                    swal($.i18n.t("activity.swal-fail"),$.i18n.t("activity.check.totalAmtIsNull") , "error");
                    return true ;
                }
                if (isBlank(param.receiveTimes)) {
                    //活动名称不能为空
                    swal($.i18n.t("activity.swal-fail"),$.i18n.t("activity.check.receiveTimesIsNull") , "error");
                    return true ;
                }
                if (isBlank(param.costSide)) {
                    //活动名称不能为空
                    swal($.i18n.t("activity.swal-fail"),$.i18n.t("activity.check.costSideIsNull") , "error");
                    return true ;
                }
                //非海币校验
                if (param.mkTool != '02') {
                    if (isBlank(param.couponInvalTm)) {
                        //活动的整体失效不能为空
                        swal($.i18n.t("activity.swal-fail"),$.i18n.t("activity.check.couponInvalTmIsNull") , "error");
                        return true ;
                    }
                    if (isBlank(param.startDays)) {
                        //活动的整体失效不能为空
                        swal($.i18n.t("activity.swal-fail"),$.i18n.t("activity.check.startDaysIsNull") , "error");
                        return true ;
                    }
                    if (isBlank(param.couponValDays)) {
                        //活动的整体失效不能为空
                        swal($.i18n.t("activity.swal-fail"),$.i18n.t("activity.check.couponValDaysIsNull") , "error");
                        return true ;
                    }
                    if (isBlank(param.couponName)) {
                        //活动的整体失效不能为空
                        swal($.i18n.t("activity.swal-fail"),$.i18n.t("activity.check.couponNameIsNull") , "error");
                        return true ;
                    }
                    if (isBlank(param.minAmt)) {
                        //活动的整体失效不能为空
                        swal($.i18n.t("activity.swal-fail"),$.i18n.t("activity.check.minAmtIsNull") , "error");
                        return true ;
                    }
                    if (isBlank(param.maxAmt)) {
                        //活动的整体失效不能为空
                        swal($.i18n.t("activity.swal-fail"),$.i18n.t("activity.check.maxAmtIsNull") , "error");
                        return true ;
                    }

                    if ( param.mkTool == '04') {
                        if (isBlank(param.discount)) {
                            //活动的折扣不能为空
                            swal($.i18n.t("activity.swal-fail"),$.i18n.t("activity.check.discountTmIsNull") , "error");
                            return true ;
                        } else {
                            if (!isDigit(param.discount) || param.discount >= 10) {
                                swal($.i18n.t("activity.swal-fail"),$.i18n.t("activity.check.discountTmFormat") , "error");
                                return true ;
                            }
                        }
                    } else {
                        //活动的单券金额不能为空
                        if (isBlank(param.amt)) {
                            //活动名称不能为空
                            swal($.i18n.t("activity.swal-fail"),$.i18n.t("activity.check.amtIsNull") , "error");
                            return true ;
                        } else {
                            var amt = param.amt;
                            var minAmt = param.minAmt ;

                            if (amt >= minAmt) {
                                swal($.i18n.t("activity.swal-fail"),$.i18n.t("activity.check.amtMoreThanMinAmt") , "error");
                                return true ;
                            }
                            if (amt*param.total != param.totalAmt) {
                                swal($.i18n.t("activity.swal-fail"),$.i18n.t("activity.check.amtNoEquelsTolAmt") , "error");
                                return true ;
                            }
                        }
                    }
                }
            }
         }

        //校验是否为数字
        function isDigit(s)
        {
            var patrn=/^[0-9]{1,20}$/;
            var patrn2=/^[0-9]\.[0-9]$/;
            if (!patrn.exec(s) && !patrn2.exec(s)) return false
            return true
        }

        function isBlank(val) {
             if (val == "" || val == null ) {
                 return true
             } else {
                 return false ;
             }
         }
        function  clearModalValues() {
            //$("#activityForm").empty();
            $("#atvNm").val('') ;
            $("#mkTool").val('01');
            $("#endTime").val('');
            $("#beginTime").val('');
            $("#total").val('');
            $("#totalAmt").val('');
            $("#receiveTimes").val('');
            $("#item").val('');
            $("#amt").val('');
            $("#couponInvalTm").val('');
            $("#minAmt").val('');
            $("#maxAmt").val('');
            $("#instId").val('');
            $("#discount").val('');
            $("#startDays").val('');
            $("#couponValDays").val('');
            $("#userAppoint").val('');
            $("#couponName").val('');
            $("#costSide").val('');
            $("#examineReson").val('');
        }
        var uploader = "";
        //添加
        $("#addBtn").click(function () {
            $("#examineResonDiv").hide();
            $("#mkTool").attr("disabled", false);
            $("#beginTime").attr("disabled", false);
            $("#couponValTm").attr("disabled", false);
            $("#couponInvalTm").attr("disabled", false);
            $("#startDays").attr("disabled", false);
            $("#couponValDays").attr("disabled", false);
            $("#costSide").attr("disabled", false);
            $("#activityModify").hide();
            $("#userAppoint").show();
            $("#activityAdd").show();
            $("#examineDiv").hide();
            $("#activityExamine").hide()
            clearModalValues();
            var mkTool = $("#mkTool").val();
            if (mkTool == '02') {
                $("#coupon").hide();
            }else {
                $("#coupon").show();
            }
            $("#handleModal").modal("show");
            //初始化上传组件
            if (uploader == "") {
                uploader = WebUploader.create({
                    // 选完文件后，是否自动上传。
                    auto: true,
                    // swf文件路径
                    swf: '/js/webuploader/Uploader.swf',

                    // 文件接收服务端。
                    server: '/file/uploadNuploadFlag',

                    // 选择文件的按钮。可选。
                    // 内部根据当前运行是创建，可能是input元素，也可能是flash.
                    pick: {
                        id: "#userAppoint",
                        label: $.i18n.t("activity.userAppoint"),
                        multiple: false            //默认为true，就是可以多选
                    },
                    accept: {
                        title: 'excel',
                        extensions: 'xls,xlsx',
                        mimeTypes: 'application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    },

                    // 不压缩image, 默认如果是jpeg，文件上传前会压缩一把再上传！
                    method: 'POST',
                    //resize: false

                });
//                // 当有文件添加进来的时候
//                uploader.on( 'fileQueued', function( file ) {  // webuploader事件.当选择文件后，文件被加载到文件队列中，触发该事件。等效于 uploader.onFileueued = function(file){...} ，类似js的事件定义。
//                    alert("jsjsjs"+file.name);
//                });
                uploader.on('uploadSuccess', function (file, data) {
                    if (data != null && data !="" && data.indexOf("<")<0) {
                        filePath = strToHexCharCode(data);
                        swal($.i18n.t("activity.swal-sucess"), $.i18n.t("activity.fileUploadSuccess"),"success" );
                        uploader.reset();
                    } else {
                        filePath = "";
                        swal($.i18n.t("activity.swal-fail"), $.i18n.t("activity.fileUploadfail"), "error");
                        uploader.reset();
                    }
                });

                uploader.on('uploadError', function (file) {
                    filePath = ""
                    swal($.i18n.t("activity.swal-fail"), $.i18n.t("activity.fileUploadfail"), "error");
                    uploader.reset();
                });
            }

            //$("#activityForm").empty();

        });


        //暂停
        $("#activityStop").click(function () {
            var row = table.row('.selected');
            if(row.length == 0) {
                $("#handleModal").modal("hide");
                swal($.i18n.t("activity.swal-fail"), $.i18n.t("activity.shouldSelectOne"), "error");
                return;
            }
            var rowData = table.row(row).data();

            <!--ajax异步调起后台服务，根据内部订单号查询明细-->
            var id = rowData["id"];
            $.ajax({
                url:"/mkm/activity/manager",
                data:{
                    "id": id,
                    "status" : "05"
                },
                dataType: "json",
                type:"post",
                success:function(data) {
                    var result = data.result;
                    if (result !='MKM00000') {
                        swal($.i18n.t("activity.swal-fail"), result , "error");
                    } else {
                        swal($.i18n.t("activity.success"));
                        table.ajax.reload();
                    }
                },
                error: function() {
                    swal($.i18n.t("activity.swal-fail"),  "error");
                }
            });

        });

        //恢复
        $("#activityRecover").click(function () {
            var row = table.row('.selected');
            if(row.length == 0) {
                $("#handleModal").modal("hide");
                swal($.i18n.t("activity.swal-fail"), $.i18n.t("activity.shouldSelectOne"), "error");
                return;
            }
            var rowData = table.row(row).data();

            <!--ajax异步调起后台服务，根据内部订单号查询明细-->
            var id = rowData["id"];
            $.ajax({
                url:"/mkm/activity/manager",
                data:{
                    "id": id,
                    "status" : "00"
                },
                dataType: "json",
                type:"post",
                success:function(data) {
                    var result = data.result;
                    if (result !='MKM00000') {
                        swal($.i18n.t("activity.swal-fail"), result , "error");
                    } else {
                        swal($.i18n.t("activity.success"));
                        table.ajax.reload();
                    }
                },
                error: function() {
                    swal($.i18n.t("activity.swal-fail"),  "error");
                }
            });

        });

        $("#modifyBtn").click(function () {
            clearModalValues();
            $("#examineResonDiv").show();
            $("#mkTool").attr("disabled", "disabled");
            $("#beginTime").attr("disabled", "disabled");
            $("#couponValTm").attr("disabled", "disabled");
            $("#couponInvalTm").attr("disabled", "disabled");
            $("#startDays").attr("disabled", "disabled");
            $("#couponValDays").attr("disabled", "disabled");
            $("#costSide").attr("costSide", "disabled");
            $("#activityModify").show();
            $("#activityAdd").hide();
            $("#examineDiv").hide();
            $("#userAppoint").hide();
            $("#activityExamine").hide();
            var row = table.row('.selected');
            if(row.length == 0) {
                $("#handleModal").modal("hide");
                swal($.i18n.t("activity.swal-fail"), $.i18n.t("activity.shouldSelectOne"), "error");
                return;
            }
            $("#handleModal").modal("show");
            <!--获取选中行的内容-->
            var rowData = table.row(row).data();

            <!--ajax异步调起后台服务，根据内部订单号查询明细-->
            var id = rowData["id"];
            $.ajax({
                url:"/mkm/activity/getActivity",
                data:{
                    "id": id
                },
                dataType: "json",
                type:"post",
                success:function(data) {
                    if (data != null) {
                        <!--获取选中行的内容-->
                        $("#atvNm").val(data.atvNm) ;
                        $("#mkTool").val(data.mkTool);
                        if ("02" == data.mkTool){
                            $("#coupon").hide();
                        }else {
                            $("#coupon").show();
                        }
                        $("#endTime").val(data.endTime);
                        $("#beginTime").val(data.beginTime);
                        $("#total").val(data.total);
                        $("#totalAmt").val(data.totalAmt);
                        $("#receiveTimes").val(data.receiveTimes);
                        $("#item").val(data.item);
                        $("#amt").val(data.amt);
                        $("#couponValTm").val(data.couponValTm);
                        $("#couponInvalTm").val(data.couponInvalTm);
                        $("#maxAmt").val(data.maxAmt);
                        $("#minAmt").val(data.minAmt);
                        $("#instId").val(data.instId);
                        $("#discount").val(data.discount);
                        $("#id").val(id);
                        $("#receiveCycle").val(data.receiveCycle);
                        $("#userScope").val(data.userScope);
                        $("#startDays").val(data.startDays);
                        $("#couponValDays").val(data.couponValDays);
                        $("#instId").val(data.instId);
                        $("#costSide").val(data.costSide);
                        $("#couponName").val(data.couponName);
                        $("#examineReson").val(data.examineReson);
                        $("#examineReson").attr("disabled",true) ;
                    } else {
                        <!--隐藏模态框-->
                        $("#handleModal").modal("hide");
                        swal($.i18n.t("activity.swal-fail"), $.i18n.t("activity.detailNull"), "error");
                    }
                },
                error: function() {
                    $("#handleModal").modal("hide");
                    swal($.i18n.t("activity.swal-fail"),  "error");
                }
            });

        });



        //上传图片
//        if (window.FileReader) {
//            //上传指定用户
//            $("#userAppoint").change(function () {
//                uploadFile(this);
//            });
//        }
//        //modal模态框隐藏后处理
//        $('#handleModal').on('shown.bs.modal',
//            function () {
//                var row = table.row('.selected');
//                if(row.length == 0) {
//                    $("#handleModal").modal("hide");
//                }
//            }
//        );
    });

    function strToHexCharCode(str) {
        if(str === "")
            return "";
        var hexCharCode = [];
        hexCharCode.push("");
        for(var i = 0; i < str.length; i++) {
            hexCharCode.push((str.charCodeAt(i)).toString(16).toUpperCase());
        }
        return hexCharCode.join("");
    }
    var filePath ="";
    //上传文件
    function uploadFile(object) {
        filePath = '';
        var fileReader = new FileReader(),
            files = object.files,
            file;
        if (!files.length) {
            return;
        }
        file = files[0];
        if (/sheet$/.test(file.type) || /excel$/.test(file.type)) {
            fileReader.readAsDataURL(file);
            //fileReader.readAsText(file, "UTF-8");
            fileReader.onload = function () {
                var base64String = this.result.split(",");
                $.ajax({
                    url:"/file/upload/base64Img",
                    data:{
                        "name": file.name,
                        "fileType":file.type,
                        "base64String":base64String[1],
                        "uploadToFileServer" :"N",
                        "type":file.type
                    },
                    dataType: "json",
                    type:"post",
                    success:function(data) {
                        if (data != null) {
                            filePath  = data.localPath;
                        } else {
                            <!--隐藏模态框-->
                            swal($.i18n.t("activity.swal-fail"), $.i18n.t("activity.detailNull"), "error");
                        }
                    },
                    error: function() {

                        swal($.i18n.t("activity.swal-fail"),  "error");
                    }
                });
            };
        } else {
            $("#userAppoint").val("");
            alert("Please choose an excel file.");
        }
    }
    /**初始化日期控件**/

    var sbeginTimePick = $('#sBeginTime').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        onChangeDate: function(dateText, inst) {
            endTimePick.datetimepicker('minDate',new Date(dateText.replace("-", "-")));
        }
    });

    var sendTimePick = $('#sEndTime').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });
    /**初始化日期控件**/
    var beginTimePick = $('#beginTime').datetimepicker({
        lang:'en',
        timepicker:true,
        validateOnBlur: false,
        format:'Y-m-d H:i:00',
        formatDate:'Y-m-d',
        closeOnInputClick:'',
        onChangeDate: function(dateText, inst) {
            endTimePick.datetimepicker('minDate',new Date(dateText.replace("-", "-")));
        }
    });

    var endTimePick = $('#endTime').datetimepicker({
        lang:'en',
        timepicker:true,
        validateOnBlur: false,
        format:'Y-m-d H:i:00',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });
    var couponValTm = $('#couponValTm').datetimepicker({
        lang:'en',
        timepicker:true,
        validateOnBlur: false,
        format:'Y-m-d H:i:00',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });
    var couponInvalTm = $('#couponInvalTm').datetimepicker({
        lang:'en',
        timepicker:true,
        validateOnBlur: false,
        format:'Y-m-d H:i:00',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });
    function ajaxClick(name, type, id) {
        var url = $("span[name='" + name + "'][id='" + id + "']").attr("data");
        if (name == "lock") {
            $.ajax({
                type: type,
                url: url,
                success: function (data) {
                    if (data) {
                        alert(data);
                    } else {
                        window.location.reload();
                    }
                }
            })
        } else if (name == "trash") {
            swal({
                title: $.i18n.t("user.swal-title"),
                text: "",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: $.i18n.t("user.swal-confirm"),
                cancelButtonText: $.i18n.t("user.swal-cancel"),
                closeOnConfirm: false
            }, function (isConfirm) {
                if (!isConfirm) return;
                $.ajax({
                    url: url,
                    type: type,
                    success: function () {
                        swal($.i18n.t("user.swal-sucess"), "", "success");
                        $('.confirm').click(function () {   //额外绑定一个事件，当确定执行之后返回成功的页面的确定按钮，点击之后刷新当前页面或者跳转其他页面
                            location.reload();
                        });
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        swal($.i18n.t("user.swal-error"), $.i18n.t("user.swal-error-tips"), "error");
                    }
                });
            });
        }
    }


</script>
</body>

</html>
