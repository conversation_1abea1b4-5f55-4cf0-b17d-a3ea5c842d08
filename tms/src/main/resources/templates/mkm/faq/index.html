<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title data-i18n="nav.busmgr.mkmmgr.faq.title">常见问题管理</title>
    <div th:replace="head"></div>
</head>

<body>
    <div id="wrapper">
        <div th:replace="nav"></div>

        <div id="page-wrapper" class="gray-bg">
            <div th:replace="top"></div>

            <div class="row wrapper border-bottom white-bg page-heading">
                <div class="col-lg-10">
                    <h2 data-i18n="nav.busmgr.mkmmgr.faq.content">常见问题管理</h2>
                    <ol class="breadcrumb">
                        <li>
                            <a data-i18n="nav.busmgr">业务管理</a>
                        </li>
                        <li>
                            <a data-i18n="nav.busmgr.mkmmgr">营销管理</a>
                        </li>
                        <li class="active">
                            <strong data-i18n="nav.busmgr.mkmmgr.faq.content">常见问题管理</strong>
                        </li>
                    </ol>
                </div>
                <div class="col-lg-2">
                    <div class="btn-group pull-right" style="margin-top: 30px;">
                        <button type="button" class="btn btn-primary btn-sm" id="btnAdd">
                            <i class="fa fa-plus"></i> <span data-i18n="mkm.faq.add">新增</span>
                        </button>
                    </div>
                </div>
            </div>

            <div class="wrapper wrapper-content animated fadeInRight">
                <!-- 查询表单 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <form id="queryForm" class="form-horizontal">
                                    <div class="form-group col-sm-12">
                                        <!-- 问题ID -->
                                        <label class="col-sm-2 control-label" for="searchId"
                                            data-i18n="mkm.faq.id">问题ID</label>
                                        <div class="col-sm-4">
                                            <input name="id" id="searchId" class="form-control" value="" />
                                        </div>
                                        <!-- 问题内容 -->
                                        <label class="col-sm-2 control-label" for="searchQuestionContent"
                                            data-i18n="mkm.faq.questionContent">问题内容</label>
                                        <div class="col-sm-4">
                                            <input name="questionContent" id="searchQuestionContent"
                                                class="form-control" value="" />
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-sm-4 col-sm-offset-1">
                                            <button type="button" id="searchBtn" class="btn btn-primary"
                                                onclick="search()" data-i18n="mkm.faq.search">查询</button>
                                            <button type="button" id="resetBtn" class="btn btn-default"
                                                onclick="resetForm()" data-i18n="mkm.faq.reset">重置</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="faqTable" class="table table-striped table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th data-i18n="mkm.faq.id">问题ID</th>
                                                <th data-i18n="mkm.faq.questionContent">问题内容</th>
                                                <th data-i18n="mkm.faq.createdBy">创建人</th>
                                                <th data-i18n="mkm.faq.createdDate">创建时间</th>
                                                <th data-i18n="mkm.faq.operations">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:replace="footer"></div>
        </div>
    </div>

    <!-- 新增/编辑模态框 -->
    <div class="modal fade" id="faqModal" tabindex="-1" role="dialog" aria-labelledby="faqModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="faqModalLabel" data-i18n="mkm.faq.addTitle">新增常见问题</h4>
                </div>
                <div class="modal-body">
                    <form id="faqForm" class="form-horizontal">
                        <input type="hidden" id="id" name="id" />
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="questionContent"
                                data-i18n="mkm.faq.questionContent">问题内容</label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="questionContent" name="questionContent" rows="3"
                                    required></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" for="answerContent"
                                data-i18n="mkm.faq.answerContent">回答内容</label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="answerContent" name="answerContent" rows="5"
                                    required></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="mkm.faq.cancel">取消</button>
                    <button type="button" class="btn btn-primary" id="btnSave" data-i18n="mkm.faq.save">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 查看详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="detailModalLabel" data-i18n="mkm.faq.detailTitle">常见问题详情</h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="mkm.faq.questionContent">问题内容</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailQuestionContent"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="mkm.faq.answerContent">回答内容</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailAnswerContent"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="mkm.faq.createdBy">创建人</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailCreatedBy"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="mkm.faq.createdDate">创建时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailCreatedDate"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="mkm.faq.updatedBy">更新人</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUpdatedBy"></p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label" data-i18n="mkm.faq.updatedDate">更新时间</label>
                            <div class="col-sm-9">
                                <p class="form-control-static" id="detailUpdatedDate"></p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"
                        data-i18n="mkm.faq.close">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <div th:replace="script"></div>

    <!-- Page-Level Scripts -->
    <script>
        var table;
        $(document).ready(function () {
            var languageUrl;
            switch ($.cookie('lang')) {
                case 'zh':
                    languageUrl = '/js/Chinese.json';
                    break;
                case 'en':
                    languageUrl = '/js/English.json';
                    break;
                default:
                    languageUrl = '/js/Chinese.json';
            }

            // 初始化DataTables
            table = $('#faqTable').DataTable({
                dom: 'Blfrtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/mkm/faq/findAll',
                    type: 'post',
                    data: function (d) {
                        // 添加额外的查询参数
                        d.extra_search = {
                            "id": $("#searchId").val() || "",
                            "questionContent": $("#searchQuestionContent").val() || ""
                        };
                        return JSON.stringify(d);
                    },
                    dataSrc: function (json) {
                        // 确保返回的是数组
                        return json.data || [];
                    },
                    error: function (xhr, error, thrown) {
                        console.error('DataTables AJAX error:', error, thrown);
                        toastr.error('加载数据失败，请刷新页面重试');
                    }
                },
                serverSide: true,
                searching: false, // 禁用内置搜索
                searchDelay: 1000,
                responsive: true,
                processing: true,
                language: {
                    url: languageUrl
                },
                columns: [
                    { data: 'id' },
                    { data: 'questionContent' },
                    { data: 'createdBy' },
                    {
                        data: 'createdDate',
                        render: function (data) {
                            if (!data) return '';
                            return new Date(data).toLocaleString();
                        }
                    },
                    {
                        data: null,
                        orderable: false,
                        render: function (data, type, row) {
                            var buttons = '<button type="button" class="btn btn-xs btn-info view-detail" data-id="' + row.id + '">详情</button> ';
                            buttons += '<button type="button" class="btn btn-xs btn-primary btn-edit" data-id="' + row.id + '">编辑</button> ';
                            buttons += '<button type="button" class="btn btn-xs btn-danger btn-delete" data-id="' + row.id + '">删除</button>';
                            return buttons;
                        }
                    }
                ],
                buttons: [
                    { extend: 'copyHtml5' },
                    { extend: 'csvHtml5' },
                    { extend: 'excelHtml5', title: '常见问题列表' }
                ]
            });

            // 新增按钮
            $("#btnAdd").click(function () {
                $("#faqModalLabel").text("新增常见问题");
                $("#faqForm")[0].reset();
                $("#id").val("");
                $("#faqModal").modal("show");
            });

            // 保存按钮
            $("#btnSave").click(function () {
                if (!$("#faqForm").valid()) {
                    toastr.error('请填写必填项');
                    return;
                }

                var id = $("#id").val();
                var formData = $("#faqForm").serialize();
                var url = id ? "/mkm/faq/modify/" + id : "/mkm/faq/add";

                $.ajax({
                    url: url,
                    type: "POST",
                    data: formData,
                    success: function (res) {
                        if (res.result === "MKM00000") {
                            toastr.success('操作成功');
                            $("#faqModal").modal("hide");
                            table.ajax.reload();
                        } else {
                            toastr.error('操作失败：' + res.result);
                        }
                    },
                    error: function (xhr, status, error) {
                        toastr.error('操作失败：' + error);
                    }
                });
            });

            // 查看详情
            $(document).on("click", ".view-detail", function () {
                var id = $(this).data("id");
                $.ajax({
                    url: "/mkm/faq/getFAQ",
                    type: "POST",
                    data: { id: id },
                    success: function (data) {
                        $("#detailQuestionContent").text(data.questionContent || '');
                        $("#detailAnswerContent").text(data.answerContent || '');
                        $("#detailCreatedBy").text(data.createdBy || '');
                        $("#detailCreatedDate").text(data.createdDate ? new Date(data.createdDate).toLocaleString() : '');
                        $("#detailUpdatedBy").text(data.updatedBy || '');
                        $("#detailUpdatedDate").text(data.updatedDate ? new Date(data.updatedDate).toLocaleString() : '');
                        $("#detailModal").modal("show");
                    },
                    error: function (xhr, status, error) {
                        toastr.error('获取详情失败：' + error);
                    }
                });
            });

            // 编辑按钮
            $(document).on("click", ".btn-edit", function () {
                var id = $(this).data("id");
                $.ajax({
                    url: "/mkm/faq/getFAQ",
                    type: "POST",
                    data: { id: id },
                    success: function (data) {
                        $("#faqModalLabel").text("编辑常见问题");
                        $("#id").val(data.id);
                        $("#questionContent").val(data.questionContent);
                        $("#answerContent").val(data.answerContent);
                        $("#faqModal").modal("show");
                    },
                    error: function (xhr, status, error) {
                        toastr.error('获取详情失败：' + error);
                    }
                });
            });

            // 删除按钮
            $(document).on("click", ".btn-delete", function () {
                var id = $(this).data("id");
                swal({
                    title: "确定要删除该常见问题吗？",
                    text: "删除后将无法恢复！",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#DD6B55",
                    confirmButtonText: "确定删除",
                    cancelButtonText: "取消",
                    closeOnConfirm: false
                }, function () {
                    $.ajax({
                        url: "/mkm/faq/delete/" + id,
                        type: "DELETE",
                        success: function (res) {
                            if (res === "1") {
                                swal("删除成功！", "常见问题已被删除。", "success");
                                table.ajax.reload();
                            } else {
                                swal("删除失败", "请稍后重试", "error");
                            }
                        },
                        error: function (xhr, status, error) {
                            swal("删除失败", "错误: " + error, "error");
                        }
                    });
                });
            });

            // 表单验证
            $("#faqForm").validate({
                rules: {
                    questionContent: {
                        required: true,
                        maxlength: 300
                    },
                    answerContent: {
                        required: true,
                        maxlength: 300
                    }
                },
                messages: {
                    questionContent: {
                        required: "请输入问题内容",
                        maxlength: "问题内容不能超过300个字符"
                    },
                    answerContent: {
                        required: "请输入回答内容",
                        maxlength: "回答内容不能超过300个字符"
                    }
                }
            });
        });

        // 搜索方法
        function search() {
            table.ajax.reload();
        }

        // 重置表单
        function resetForm() {
            $("#queryForm")[0].reset();
            search();
        }
    </script>
</body>

</html>