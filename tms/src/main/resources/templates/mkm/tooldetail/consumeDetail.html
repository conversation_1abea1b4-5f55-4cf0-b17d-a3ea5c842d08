<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">

<head>

    <title data-i18n="nav.consumeDetail.title"></title>

    <div th:replace="head"></div>
</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="nav.consumeDetail.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.busmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="activity.sysmanage"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="nav.consumeDetail.content"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <div class="searchDiv">
                                <div>

                                    <span class=" col-search-1 control-label" data-i18n="activity.mobile"></span>
                                    <input type="text" class="searchInput" name="mobile" id="mobile">

                                    <span class=" col-search-1 control-label" data-i18n="activity.instId"></span>
                                    <input type="text" class="searchInput" name="instId" id="instId">

                                    <span class=" col-search-1 control-label" data-i18n="activity.userDt"></span>
                                    <input type="text" class="searchInput" name="userS" id="userS">-
                                    <input type="text" class="searchInput" name="userE" id="userE">
                                    &nbsp;
                                    <a class="btn btn-white btn-bitbucket btn-pading"
                                       id = 'query'>
                                        <i class="fa fa-search"></i> <span data-i18n="activity.search"></span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-content">
                            <div class="table-responsive">
                                <table id="dataTables" class="table table-striped table-bordered table-hover ">
                                    <thead>
                                    <tr>
                                        <th data-i18n="activity.mobile"></th>
                                        <th data-i18n="activity.instId"></th>
                                        <th data-i18n="activity.orderNo"></th>
                                        <th data-i18n="activity.orderAmt"></th>
                                        <th data-i18n="activity.id"></th>
                                        <th data-i18n="activity.atvNm"></th>
                                        <th data-i18n="activity.userNm"></th>
                                        <th data-i18n="activity.userDt"></th>
                                        <th data-i18n="activity.userTm"></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>
    </div>
</div>
<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script type="text/javascript" th:inline="javascript">
    var table;
    $(document).ready(function () {
        var languageUrl;

        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }
        i18nLoad.then(function () {
            table = $('#dataTables').DataTable({
                ajax: {
                    contentType: 'application/json',
                    url: '/mkm/consumeDetail/findAll',
                    type: 'POST',
                    data: function (d) {
                        var instId = $("#instId").val();
                        var mobile = $("#mobile").val();
                        var userS = $("#userS").val();
                        var userE = $("#userE").val();

                        d.extra_search = {
                            "instId":instId,
                            "mobile":mobile,
                            "releaseTmS":userS,
                            "releaseTmE":userE
                        };
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                responsive: true,
                processing: true,
                searching: false,
                language: {
                    url: languageUrl
                    //url: '//cdn.datatables.net/plug-ins/1.10.15/i18n/Chinese.json'
                },
                columns: [{
                    data: 'mobile'
                }, {
                    data: 'instId'
                }, {
                    data: 'orderNo'
                }, {
                    data: 'orderAmt'
                }, {
                    data: 'id'
                },{
                    data: 'atvNm'
                }, {
                    data: 'count'
                }, {
                    data: 'userDt'
                }, {
                    data: 'userTm'
                }
                ]
            });
        });
    });
    $("#query").click(function () {
        table.ajax.reload();
    })
    /**初始化日期控件**/
    var beginTimePick = $('#userS').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        onChangeDate: function(dateText, inst) {
            endTimePick.datetimepicker('minDate',new Date(dateText.replace("-", "-")));
        }
    });

    var endTimePick = $('#userE').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });
    function ajaxClick(name, type, id) {
        var url = $("span[name='" + name + "'][id='" + id + "']").attr("data");
        if (name == "lock") {
            $.ajax({
                type: type,
                url: url,
                success: function (data) {
                    if (data) {
                        alert(data);
                    } else {
                        window.location.reload();
                    }
                }
            })
        } else if (name == "trash") {
            swal({
                title: $.i18n.t("user.swal-title"),
                text: "",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: $.i18n.t("user.swal-confirm"),
                cancelButtonText: $.i18n.t("user.swal-cancel"),
                closeOnConfirm: false
            }, function (isConfirm) {
                if (!isConfirm) return;
                $.ajax({
                    url: url,
                    type: type,
                    success: function () {
                        swal($.i18n.t("user.swal-sucess"), "", "success");
                        $('.confirm').click(function () {   //额外绑定一个事件，当确定执行之后返回成功的页面的确定按钮，点击之后刷新当前页面或者跳转其他页面
                            location.reload();
                        });
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        swal($.i18n.t("user.swal-error"), $.i18n.t("user.swal-error-tips"), "error");
                    }
                });
            });
        }
    }
</script>
</body>

</html>
