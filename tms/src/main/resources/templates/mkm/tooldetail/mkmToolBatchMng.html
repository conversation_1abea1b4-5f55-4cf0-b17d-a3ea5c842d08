<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">

<head>

    <title data-i18n="activity.toolTitle"></title>

    <div th:replace="head"></div>
    <style>
        div.floatright{
            display:inline;
            float:right;
            padding-left:20px;
            padding-right:20px;
        }

        ._right{
            display:inline;
            float:right;
            padding-right:15px;
        }
    </style>
</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="activity.toolmanage"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.busmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.mkm"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="activity.toolmanage"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <div >
                                    <span class=" col-search-1 control-label" data-i18n="activity.batch.recNo"></span>
                                    <input type="text" class="searchInput" name="recNo" id="recNo">

                                    <span class=" col-search-1 control-label" data-i18n="activity.id"></span>
                                    <input type="text" class="searchInput" name="atvId" id="atvId">


                                    <!--  <span class=" col-search-1 control-label" data-i18n="activity.mkTool"></span>
                                      <select class="searchInput"  name="mkTool" id="mkTool">
                                      <option value='00' data-i18n="activity.orther"></option>
                                      <option value='02' data-i18n="activity.seaCyy"></option>
                                      </select>-->
                                    <a class="btn btn-white btn-bitbucket btn-pading"
                                       id = 'query' onclick="searchButton()">
                                        <i class="fa fa-search"></i> <span data-i18n="activity.search"></span>
                                    </a>
                                    <!--<input type="file" id  = "uploadDelayFile" class="hide" />-->
                                    <!--<input type="file" id  = "uploadOverdueFile" class="hide" />-->
                                    <!--<input type="file" id  = "uploadFreezeFile" class="hide" />-->
                                    <!--<input type="file" id  = "uploadUnfreezeFile" class="hide" />-->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <div class="form-group">
                                        <label class="col-sm-1 control-label" for="uploadBatchFile" data-i18n="activity.batch.uploadBatchFile" aria-flowto="right">商户名称</label><!--内部用户号-->
                                        <div class="col-sm-3">
                                           <div id = "uploadBatchFile" ></div>
                                        </div>
                                        <label class="col-sm-1 control-label" for="oprTyp" data-i18n="activity.batch.oprTyp"></label><!--付款类型-->
                                        <div class="col-sm-2">
                                            <select name="corpBusTyp" id="oprTyp" class="form-control" value="">
                                                <option value="" data-i18n="activity.batch.selected" selected></option>
                                                <option value="2" data-i18n="activity.batch.uploadDelayFile"></option>
                                                <option value="1" data-i18n="activity.batch.uploadOverdueFile"></option>
                                                <option value="2" data-i18n="activity.batch.uploadFreezeFile"></option>
                                                <option value="4" data-i18n="activity.batch.uploadUnfreezeFile"></option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <div class="ibox float-e-margins">
                            <div class="ibox-content">
                                <div class="table-responsive">
                                    <table id="dataTables" class="table table-striped table-bordered table-hover ">
                                        <thead>
                                        <tr>
                                            <th data-i18n="activity.batch.recNo"></th>
                                            <th data-i18n="activity.id"></th>
                                            <th data-i18n="activity.batch.batchFile"></th>
                                            <th data-i18n="activity.batch.processDt"></th>
                                            <th data-i18n="activity.batch.totNum"></th>
                                            <th data-i18n="activity.batch.totAmt"></th>
                                            <th data-i18n="activity.batch.successNum"></th>
                                            <th data-i18n="activity.batch.successAmt"></th>
                                            <th data-i18n="activity.batch.failureNum"></th>
                                            <th data-i18n="activity.batch.failureAmt"></th>
                                            <th data-i18n="activity.batch.updOprId"></th>
                                            <th data-i18n="activity.batch.processSts"></th>
                                            <th data-i18n="activity.batch.downloadBatchFile"></th>
                                            <th data-i18n="activity.batch.downloadResultFile"></th>
                                        </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>
    </div>
</div>
<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script type="text/javascript" th:inline="javascript">
    var datatable;
    $(document).ready(function () {
        var languageUrl;

        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }
        i18nLoad.then(function () {
            datatable = $('#dataTables').DataTable({
                ajax: {
                    contentType: 'application/json',
                    url: '/mkm/batchList',
                    type: 'POST',
                    data: function (d) {
                        return JSON.stringify(d);
                    }
                },
                dom: '<"floatright"l>rtip',
                serverSide: true,
                responsive: true,
                processing: true,
                language: {
                    url: languageUrl
                    //url: '//cdn.datatables.net/plug-ins/1.10.15/i18n/Chinese.json'
                },
//                buttons: [
//                    {
//                        text: $.i18n.t("activity.batch.uploadDelayFile"),
//                        action: function ( e, dt, node, config ) {
//                            //按钮js
//                            $("#uploadDelayFile").click();
//                        }
//                    }, {
//                        text: $.i18n.t("activity.batch.uploadOverdueFile"),
//                        action: function ( e, dt, node, config ) {
//                            //按钮js
//                            $("#uploadDelayFile").click();
//                            uploadExcel("1");
//                        }
//                    }, {
//                        text: $.i18n.t("activity.batch.uploadFreezeFile"),
//                        action: function ( e, dt, node, config ) {
//                            //按钮js
//                            $("#uploadFreezeFile").click();
//                        }
//                    },{
//                        text: $.i18n.t("activity.batch.uploadUnfreezeFile"),
//                        action: function ( e, dt, node, config ) {
//                            //按钮js
//                            $("#uploadFile").click();
//
//                            //上传图片
//                            if (window.FileReader) {
//                                //上传指定用户
//                                $("#uploadUnfreezeFile").change(function () {
//                                    uploadExcel("4");
//                                });
//                            } else {
//                                alert("浏览器不支持上传");
//                            };
//                        }
//                    }
////                    , {
////                        extend: "remove", editor: editor
////                    }, {
////                        text: $.i18n.t("urm.mermgr.review"),
////                        action: function ( e, dt, node, config ) {
////                            //按钮js
////                        }
////                    }
//                ],
                columns: [{
                    data: 'recNo'
                },{
                    data: 'acId'
                },  {
                    data: 'batchFile'
                }, {
                    data: 'processDt'
                }, {
                    data: 'totNum'
                }, {
                    data: 'totAmt'
                },{
                    data: 'successNum'
                }, {
                    data: 'successAmt'
                }, {
                    data: 'failureNum'
                }, {
                    data: 'failureAmt'
                },
               {
                    data: 'oprTyp'
                },
               {
                    data: 'processSts'
                },
               {
                    data: 'batchFile',
                    render: function (data, type, row) {
                        var download = $.i18n.t("activity.batch.downloadBatchFile");
                        if (data == null || data == "") {
                            return ""
                        } else {
                           // var a = "<a href='http://219.135.153.39:8888/group1/M00/00/0F/wKgDJ1noGO2ALfgPAAzodTsSnkc298.jpg'>下载</a>";
                            var name = data.substring(data.lastIndexOf("/"),data.length)
                            var a = "<a href='/file/downloadByFilePath?filePath="  + data+  "&downloadRemotFlg=Y" + "'>"+ download+"</a>";
                            return a;
                        }
                    }
                },
               {
                    data: 'resultFile',
                    render: function (data, type, row) {
                        var download = $.i18n.t("activity.batch.downloadResultFile");
                        if (data == null || data == "") {
                            return ""
                        } else {
                           // var a = "<a href='http://219.135.153.39:8888/group1/M00/00/0F/wKgDJ1noGO2ALfgPAAzodTsSnkc298.jpg'>下载</a>";
                            var a = "<a href='/file/downloadByFilePath?filePath="  + data+  "&downloadRemotFlg=Y" + "'>"+ download+"</a>";
                            return a;
                        }
                    }
                }
                ]
            });
            innitWebUpload();
        });

//        $("#uploadDelayFile").change(function (type) {
//            var fileObj = document.getElementById("uploadDelayFile").files[0];
//            uploadExcel("2" ,fileObj) ;
//        });
//        $("#uploadOverdueFile").change(function (type) {
//            var fileObj = document.getElementById("uploadOverdueFile").files[0];
//            uploadExcel("1" ,fileObj) ;
//        });
//        $("#uploadFreezeFile").change(function (type) {
//            var fileObj = document.getElementById("uploadFreezeFile").files[0];
//            uploadExcel("3" ,fileObj) ;
//        });
//        $("#uploadUnfreezeFile").change(function (type) {
//            var fileObj = document.getElementById("uploadUnfreezeFile").files[0];
//            uploadExcel("4" ,fileObj) ;
//        });

        function innitWebUpload() {
            var uploader = "";
            //初始化上传组件
            if (uploader == "") {
                uploader = WebUploader.create({
                    // 选完文件后，是否自动上传。
                    auto: true,
                    // swf文件路径
                    swf: '/js/webuploader/Uploader.swf',

                    // 文件接收服务端。
                    server: '/mkm/batch/upload',

                    // 选择文件的按钮。可选。
                    // 内部根据当前运行是创建，可能是input元素，也可能是flash.

                    pick: {
                        id: "#uploadBatchFile",
                        label: $.i18n.t("activity.batch.uploadBatchFile"),
                        multiple: false            //默认为true，就是可以多选
                    },
                    accept: {
                        title: 'excel',
                        extensions: 'xls,xlsx',
                        mimeTypes: 'application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    },
                    formData:{"oprTyp" :$("#oprTyp").val()},

                    // 不压缩image, 默认如果是jpeg，文件上传前会压缩一把再上传！
                    method: 'POST',
                    //resize: false

                });
                uploader.on('uploadSuccess', function (file, data) {
                    if (data == 'Success')  {
                        //filePath = strToHexCharCode(data);
                        swal($.i18n.t("activity.swal-sucess"), $.i18n.t("activity.fileUploadSuccess"), "success");
                        uploader.reset();
                    } else {
                        filePath = "";
                        swal($.i18n.t("activity.swal-fail"), $.i18n.t("activity.fileUploadfail"), "error");
                        uploader.reset();
                    }
                    datatable.ajax.reload();
                });
                uploader.on('beforeFileQueued', function (file,data) {
                    var oprType = $("#oprTyp").val();
                    if (oprType == "" || oprType == null) {
                        swal($.i18n.t("activity.swal-fail"), $.i18n.t("activity.batch.selectedOprType"), "error");
                        return false;
                    } else {
                        uploader.options.formData.oprTyp = oprType;
                    }
                    return true ;
                });

                uploader.on('uploadError', function (file) {
                    filePath = ""
                    swal($.i18n.t("activity.swal-fail"), $.i18n.t("activity.fileUploadfail"), "error");
                    uploader.reset();
                    datatable.ajax.reload();
                });
            }
        }
    });





//    //上传文件
//    function uploadExcel(type , object ) {
//                var formData = new FormData();
//                if (/sheet$/.test(object.type) || /excel$/.test(object.type)) {
//
//
//                    formData.append("file", object);
//                    formData.append("oprTyp", type);
//                    $.ajax({
//                        url: "/mkm/batch/upload",
//                        data: formData,
//                        type: 'POST',
//                        async: false,
//                        cache: false,
//                        contentType: false,
//                        processData: false,
//                        success: function (data) {
//                            if (data == 'Success') {
//                                swal($.i18n.t("activity.swal-sucess"), $.i18n.t("activity.fileUploadSuccess"), "success");
//                            } else {
//                                <!--隐藏模态框-->
//                                swal($.i18n.t("activity.swal-fail"), $.i18n.t("activity.fileUploadfail"), "error");
//                            }
//                            datatable.ajax.reload();
//                        },
//                        error: function () {
//
//                            swal($.i18n.t("activity.swal-fail"), "error");
//                            datatable.ajax.reload();
//                        }
//                    });
//                } else {
//                    alert("please choose the xls or xlsx file");
//                }
//
//    }
    /**初始化日期控件**/
    var beginTimePick = $('#userS').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        onChangeDate: function(dateText, inst) {
            endTimePick.datetimepicker('minDate',new Date(dateText.replace("-", "-")));
        }
    });

    var endTimePick = $('#userE').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });
    function ajaxClick(name, type, id) {
        var url = $("span[name='" + name + "'][id='" + id + "']").attr("data");
        if (name == "lock") {
            $.ajax({
                type: type,
                url: url,
                success: function (data) {
                    if (data) {
                        alert(data);
                    } else {
                        window.location.reload();
                    }
                }
            })
        } else if (name == "trash") {
            swal({
                title: $.i18n.t("user.swal-title"),
                text: "",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: $.i18n.t("user.swal-confirm"),
                cancelButtonText: $.i18n.t("user.swal-cancel"),
                closeOnConfirm: false
            }, function (isConfirm) {
                if (!isConfirm) return;
                $.ajax({
                    url: url,
                    type: type,
                    success: function () {
                        swal($.i18n.t("user.swal-sucess"), "", "success");
                        $('.confirm').click(function () {   //额外绑定一个事件，当确定执行之后返回成功的页面的确定按钮，点击之后刷新当前页面或者跳转其他页面
                            location.reload();
                        });
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        swal($.i18n.t("user.swal-error"), $.i18n.t("user.swal-error-tips"), "error");
                    }
                });
            });
        }
    }

    function searchButton() {
        var atvId = $("input[name='atvId']").val();
        var recNo = $("input[name='recNo']").val();
        console.log(recNo);
        datatable.column(0).search(recNo)
            .column(1).search(atvId)
            .draw();
    }
</script>
</body>

</html>
