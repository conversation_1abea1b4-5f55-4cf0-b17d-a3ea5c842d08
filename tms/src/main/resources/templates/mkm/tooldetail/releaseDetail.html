<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity4">

<head>

    <title data-i18n="nav.releaseDetail.title"></title>

    <div th:replace="head"></div>
</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="nav.releaseDetail.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.busmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="activity.sysmanage"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="nav.releaseDetail.content"></strong>
                    </li>
                </ol>
            </div>
        </div>

        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <div class="searchDiv">
                                <div>
                                    <span class=" col-search-1 control-label" data-i18n="activity.mobile"></span>
                                    <input type="text" class="searchInput" name="mobile" id="mobile">

                                    <span class=" col-search-1 control-label" data-i18n="activity.id"></span>
                                    <input type="text" class="searchInput" name="id" id="id">

                                    <span class=" col-search-1 control-label" data-i18n="activity.releaseTm"></span>
                                    <input type="text" class="searchInput" name="releaseTmS" id="releaseTmS">-
                                    <input type="text" class="searchInput" name="releaseTmE" id="releaseTmE">
                                    &nbsp;
                                    <a class="btn btn-white btn-bitbucket btn-pading"
                                       id = 'query'>
                                        <i class="fa fa-search"></i> <span data-i18n="activity.search"></span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <div class="table-responsive">
                                <table id="dataTables" class="table table-striped table-bordered table-hover ">
                                    <thead>
                                    <tr>
                                        <th data-i18n="activity.mobile"></th>
                                        <th data-i18n="activity.id"></th>
                                        <th data-i18n="activity.atvNm"></th>
                                        <th data-i18n="activity.mkTool"></th>
                                        <th data-i18n="activity.releaseDt"></th>
                                        <th data-i18n="activity.releaseTm"></th>
                                        <th data-i18n="activity.releaseNum"></th>
                                        <th data-i18n="activity.useredNum"></th>
                                        <th data-i18n="activity.overdueNum"></th>
                                        <th data-i18n="activity.statusN"></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>
    </div>
</div>
<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script type="text/javascript" th:inline="javascript">
    var table;
    $(document).ready(function () {
        var languageUrl;

        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }
        i18nLoad.then(function () {
             table = $('#dataTables').DataTable({
                ajax: {
                    contentType: 'application/json',
                    url: '/mkm/releaseDetail/findAll',
                    type: 'POST',
                    data: function (d) {
                        var id = $("#id").val();
                        var mobile = $("#mobile").val();
                        var releaseTmS = $("#releaseTmS").val();
                        var releaseTmE = $("#releaseTmE").val();

                        d.extra_search = {
                            "id":id,
                            "mobile":mobile,
                            "releaseTmS":releaseTmS,
                            "releaseTmE":releaseTmE
                        };
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                responsive: true,
                processing: true,
                 searching: false,
                language: {
                    url: languageUrl
                    //url: '//cdn.datatables.net/plug-ins/1.10.15/i18n/Chinese.json'
                },
                columns: [{
                    data: 'mobile'
                }, {
                    data: 'atvId'
                }, {
                    data: 'atvNm'
                },{
                    data: 'mkTool',
                    render: function (data, type, row) {
                        switch (data) {
                            case '01':
                                return $.i18n.t("activity.eleCoupon");
                            case '02':
                                return $.i18n.t("activity.seaCyy");
                            case '03':
                                return $.i18n.t("activity.coupon");
                            case '04':
                                return $.i18n.t("activity.discount");
                            default:
                                return "";

                        }
                    }
                }, {
                    data: 'releaseDt'
                }, {
                    data: 'releaseTm'
                }, {
                    data: 'count'
                }, {
                    data: 'useredNum',
                    render: function (data, type, row) {
                        if ((row.status == '02' || row.status == '07')  && row.mkTool != '02' ) {
                            return "1"
                        } else {
                            return  '0'
                        }
                    }
                }, {
                    data: 'overdueNum',
                    render: function (data, type, row) {
                        if (row.status == '03'&& row.mkTool != '02') {
                            return "1"
                        } else {
                            return  '0'
                        }
                    }
                },{
                    data: 'status',
                    render: function (data, type, row) {
                        if (row.mkTool == '02'){
                            return '';
                        }
                        switch (data) {
                            case '01':
                                return $.i18n.t("activity.sUser");
                            case '02':
                                return $.i18n.t("activity.sUnUser");
                            case '03':
                                return $.i18n.t("activity.sOverDue");
                            case '04':
                                return $.i18n.t("activity.sFreeze");
                            case '05':
                                return $.i18n.t("activity.sActivity");
                            case '06':
                                return $.i18n.t("activity.sBatchInVal");
                            case '07':
                                return $.i18n.t("activity.sRefund");
                            case '09':
                                return $.i18n.t("activity.sRevoked");
                            default:
                                return "";
                        }
                    }
                }
                ]
            });
        });
    });
     $("#query").click(function () {
         table.ajax.reload();
     })
    /**初始化日期控件**/
    var beginTimePick = $('#releaseTmS').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        onChangeDate: function(dateText, inst) {
            endTimePick.datetimepicker('minDate',new Date(dateText.replace("-", "-")));
        }
    });

    var endTimePick = $('#releaseTmE').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        // maxDate:'+1970/01/01',
    });
    function ajaxClick(name, type, id) {
        var url = $("span[name='" + name + "'][id='" + id + "']").attr("data");
        if (name == "lock") {
            $.ajax({
                type: type,
                url: url,
                success: function (data) {
                    if (data) {
                        alert(data);
                    } else {
                        window.location.reload();
                    }
                }
            })
        } else if (name == "trash") {
            swal({
                title: $.i18n.t("user.swal-title"),
                text: "",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: $.i18n.t("user.swal-confirm"),
                cancelButtonText: $.i18n.t("user.swal-cancel"),
                closeOnConfirm: false
            }, function (isConfirm) {
                if (!isConfirm) return;
                $.ajax({
                    url: url,
                    type: type,
                    success: function () {
                        swal($.i18n.t("user.swal-sucess"), "", "success");
                        $('.confirm').click(function () {   //额外绑定一个事件，当确定执行之后返回成功的页面的确定按钮，点击之后刷新当前页面或者跳转其他页面
                            location.reload();
                        });
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        swal($.i18n.t("user.swal-error"), $.i18n.t("user.swal-error-tips"), "error");
                    }
                });
            });
        }
    }
</script>
</body>

</html>
