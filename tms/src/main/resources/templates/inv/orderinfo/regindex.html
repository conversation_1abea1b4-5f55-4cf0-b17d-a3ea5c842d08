<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>

    <title data-i18n="nav.bussub.invsub.regorder.title"></title>

    <div th:replace="head"></div>
</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="nav.bussub.invsub.regorder.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.busmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.bussub.invmgr"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="nav.bussub.invsub.regorder.content"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <form id="queryForm" class="form-horizontal">
                                <div class="form-group col-sm-12">
	                                <label class="col-sm-2 control-label" for="userId" data-i18n="inv.userId"></label>
                                    <div class="col-sm-4">
                                        <input name="userId" id="userId" class="form-control" value=""/>
                                    </div>
                                    <!--手机号/商户号-->
                                    <label class="col-sm-2 control-label" for="mobileNo" data-i18n="inv.mobileNo"></label>
                                    <div class="col-sm-4">
                                        <input name="mobileNo" id="mobileNo" class="form-control" value=""/>
                                    </div>
                                </div>
                                <div class="form-group col-sm-12">
	                                <label class="col-sm-2 control-label" for="proId" data-i18n="inv.proId"></label>
                                    <div class="col-sm-4">
                                        <input name="proId" id="proId" class="form-control" value=""/>
                                    </div>
                                    <!--订单状态-->
                                    <label class="control-label col-sm-2" for="orderSts" data-i18n="inv.orderSts"></label>
                                    <div class="col-sm-4">
                                        <select name="orderSts" id="orderSts" class="form-control" value="">
                                            <option value="" data-i18n="inv.select"></option>
                                            <option value="S" data-i18n="inv.orderStsSub.S"></option>
                                            <option value="F" data-i18n="inv.orderStsSub.F"></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group col-sm-12">
                                	<label class="col-sm-2 control-label" for="proName" data-i18n="inv.proName"></label>
                                    <div class="col-sm-2">
                                        <input name="proName" id="proName" class="form-control" value=""/>
                                    </div>
                                    <label class="col-sm-2 control-label" for="invTerm" data-i18n="inv.invTerm"></label>
                                    <div class="col-sm-2">
                                        <input name="invTerm" id="invTerm" class="form-control" value=""/>
                                    </div>
	                                <label class="col-sm-2 control-label" for="proRate" data-i18n="inv.proRate"></label>
                                    <div class="col-sm-2">
                                        <input name="proRate" id="proRate" class="form-control" value=""/>
                                    </div>
                                </div>
                                <div class="form-group col-sm-10">
                                    <!--提交日期-->
                                    <label class="col-sm-2 control-label" for="cloPerBegin" data-i18n="inv.cloPerBegin"></label>
                                    <div class="col-sm-4">
                                        <input name="cloBeginb" id="cloBeginb" class="form-control" value=""/>
                                    </div>
                                    <div class="col-sm-4">
                                        <input name="cloBegine" id="cloBegine" class="form-control" value=""/>
                                    </div>
                                </div>
                                <div class="form-group col-sm-10">
                                    <!--提交日期-->
                                    <label class="col-sm-2 control-label" for="cloPerEnd" data-i18n="inv.cloPerEnd"></label>
                                    <div class="col-sm-4">
                                        <input name="cloEndb" id="cloEndb" class="form-control" value=""/>
                                    </div>
                                    <div class="col-sm-4">
                                        <input name="cloEnde" id="cloEnde" class="form-control" value=""/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-4 col-sm-offset-1">
                                        <button type="button" id="searchBtn" class="btn btn-default" onclick="search()" data-i18n="inv.search"></button>
                                    </div>
                                </div>
                            </form>
                            <div class="table-responsive">
                                <table id="orderInf" class="table table-striped table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th data-i18n="inv.mobileNo"></th>
                                        <th data-i18n="inv.userId"></th>
                                        <th data-i18n="inv.proId"></th>
                                        <th data-i18n="inv.proName"></th>
                                        <th data-i18n="inv.invTerm"></th>
                                        <th data-i18n="inv.proRate"></th>
                                        <th data-i18n="inv.orderType"></th>
                                        <th data-i18n="inv.orderAmt"></th>
                                        <th data-i18n="inv.ccy"></th>
                                        <th data-i18n="inv.invOrderDt"></th>
                                        <th data-i18n="inv.invOrderTm"></th>
                                        <th data-i18n="inv.cloPerBegin"></th>
                                        <th data-i18n="inv.cloPerEnd"></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>

    </div>
</div>
<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script>
    var editor;
    //A 2D array in which the first array is used to define the value options and the second array the displayed options (useful for language strings such as 'All').
    //    var array1 = [10, 25, 50, -1];
    //    var array2 = [10, 25, 50, "All"];
    var table;
    <!--查询按钮-->
    function search() {
        table.ajax.reload();
    }
    $(document).ready(function () {

        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
            	$('#beginDate').datetimepicker({lang:'ch'});
            	$('#endDate').datetimepicker({lang:'ch'});
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        i18nLoad.then(function () {
            editor = new $.fn.dataTable.Editor({
                ajax: {
                 //  create: {
                 //      type: 'POST',
                 //      url: '/inv/transferctrl/add',
                 //      contentType: 'application/json',
                 //      data: function (d) {
                 //          return JSON.stringify(d);
                 //      }
                 //  },
                 //  edit: {
                 //      type: 'POST',
                 //      url: '/inv/mercinfctrl/modify',
                 //      contentType: 'application/json',
                 //      data: function (d) {
                 //          return JSON.stringify(d);
                 //      }
                 //  },
                 //  remove: {
                 //      type: 'POST',
                 //      url: '/inv/mercinfctrl/delete',
                 //      contentType: 'application/json',
                 //      data: function (d) {
                 //          return JSON.stringify(d);
                 //      }
                 //  }
                },
                table: "#orderInf",
                idSrc: 'invOrderNo',
                fields: [
              //     {name: "id", type: "hidden"},
              //     {label: $.i18n.t("inv.invOrderNo"), name: "invOrderNo"},
              //     {label: $.i18n.t("inv.invJrnNo"), name: "invJrnNo"},
              //     {label: $.i18n.t("inv.invOrderDt"),name: "invOrderDt"},
              //     {label: $.i18n.t("inv.invOrderTm"), name: "invOrderTm"},
              //     {label: $.i18n.t("inv.userId"), name: "userId"},
              //     {label: $.i18n.t("inv.proId"), name: "proId"},
              //     {label: $.i18n.t("inv.proRate"), name: "proRate"},
              //     {label: $.i18n.t("inv.proType"), name: "proType"},
              //     {label: $.i18n.t("inv.ccy"), name: "ccy"},
              //     {label: $.i18n.t("inv.orderAmt"), name: "orderAmt"},
              //     {label: $.i18n.t("inv.orderType"), name: "orderType"},
              //     {label: $.i18n.t("inv.orderSts"), name: "orderSts"}
                    //{
                        //label: $.i18n.t("demo.startDate"),
                        //name: "startDate",
                        //type: "datetime",
                        //def: function () {
                            //return new Date();
                       // },
                       // format: "YYYY-MM-DD HH:mm:ss"
                   // },
                    //{label: $.i18n.t("demo.salary"), name: "salary"}
                ],
                i18n: {
                  // create: {button: $.i18n.t("inv.add"), title: $.i18n.t("inv.add"), submit: $.i18n.t("inv.create")},
                  // edit: {button: $.i18n.t("inv.modify"), title: $.i18n.t("inv.modify"), submit: $.i18n.t("inv.update")},
                  // remove: {
                  //     button: $.i18n.t("inv.delete"), title: $.i18n.t("inv.delete"), submit: $.i18n.t("inv.delete"),
                  //     confirm: {
                  //         _: $.i18n.t("inv.multi-delete"),
                  //         1: $.i18n.t("inv.single-delete")
                  //     }
                  // }
                }
            });

           // editor.on('preSubmit', function (e, o, action) {
           //     var id = editor.field('invOrderNo');
           //     o.id = id.val();  // create a new parameter to pass over to the server called entityId
           // });

             table = $('#orderInf').DataTable({
                dom: 'Blfrtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/inv/regorderctrl/findAll',
                    type: 'POST',
                    data: function (d) {
                    	d.extra_search = {
                    			"mobileNo" : $("#mobileNo").val().trim(),
                                "userId" : $("#userId").val().trim(),
                                "proId" : $("#proId").val().trim(),
                                "proName" : $("#proName").val().trim(),
                                "orderSts" : $("#orderSts").val().trim(),
                                "invTerm" : $("#invTerm").val().trim(),
                                "proRate" : $("#proRate").val().trim(),
                                "cloPerBeginb" : $("#cloBeginb").val().trim(),
                                "cloPerBegine" : $("#cloBegine").val().trim(),
                                "cloPerEndb" : $("#cloEndb").val().trim(),
                                "cloPerEnde" : $("#cloEnde").val().trim()
                                };
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
//            "lengthMenu": [array1, array2],
                language: {
//                "decimal": ",",
//                "thousands": ".",
//                    url: '/datatables/plugins/i18n/Chinese.lang'
                    url: languageUrl
                    //url: '//cdn.datatables.net/plug-ins/1.10.15/i18n/Chinese.json'
                },
                select: true,
                buttons: [
                    //{extend: "create", editor: editor},
                    //{extend: "edit", editor: editor},
                    //{extend: "remove", editor: editor},
//                {extend: 'copyHtml5'},
//                {extend: 'csvHtml5'},
//                {extend: 'excelHtml5', title: '文件名'},
                    'copy',
                    'csv',
                    'excel'
//                'pdf',
//                'print'
                ],
                columns: [{
                    data: 'mobileNo',
                   	render: function (data, type, row) {
                           if (data==null) {
                               return "";
                           }else{
                        	   return data;
                           }
                       }
                },{
                    data: 'userId'
                },{
                    data: 'proId'
                },{
                    data: 'proName'
                },{
                    data: 'invTerm'
                },{
                    data: 'proRate'
                },{
                	data: 'orderType',
                    render: function (data, type, row) {
                        switch (data) {
                            case "1":
                                return $.i18n.t("inv.buy");
                            case "2":
                                return $.i18n.t("inv.voluntaryRollOut");
                            case "3":
                                return $.i18n.t("inv.redemption");
                            default:
                                return data;
                        }
                    }
                },{
                	data: 'orderAmt',
                    render: $.fn.dataTable.render.number(',', '.', 2, '$')
                },{
                    data: 'ccy',
                   	render: function (data, type, row) {
                        switch (data) {
                            case "CNY":
                                return $.i18n.t("CCY.CNY");
                            case "USD":
                                return $.i18n.t("CCY.USD");
                            case "KHR":
                                return $.i18n.t("CCY.KHR");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'invOrderDt'
                },{
                    data: 'invOrderTm'
//                orderable: false
                },{
                    data: 'cloPerBegin'
                },{
                    data: 'cloPerEnd'
                }
                ]
            });
        });
    });
    /**初始化日期控件**/
    var cloPerBeginb = $('#cloBeginb').datetimepicker({
        timepicker: false,
        validateOnBlur: false,
        format: 'Y-m-d ',
        formatDate: 'Y-m-d'
    });

    var cloBegine = $('#cloBegine').datetimepicker({
        timepicker: false,
        validateOnBlur: false,
        format: 'Y-m-d ',
        formatDate: 'Y-m-d',
        //minDate:"1970/01/01"
        // maxDate:'+1970/01/01',
    });
    
    var cloEndb = $('#cloEndb').datetimepicker({
        timepicker: false,
        validateOnBlur: false,
        format: 'Y-m-d ',
        formatDate: 'Y-m-d'
    });

    var cloEnde = $('#cloEnde').datetimepicker({
        timepicker: false,
        validateOnBlur: false,
        format: 'Y-m-d ',
        formatDate: 'Y-m-d',
        //minDate:"1970/01/01"
        // maxDate:'+1970/01/01',
    });
</script>
</body>

</html>