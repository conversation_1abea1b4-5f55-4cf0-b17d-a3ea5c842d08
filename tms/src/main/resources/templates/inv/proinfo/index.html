<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>

    <title data-i18n="nav.bussub.invsub.proinf.title"></title>

    <div th:replace="head"></div>
    <style>
        div.floatright{
            display:inline;
            float:right;
            padding-left:20px;
            padding-right:20px;
        }

        ._right{
            display:inline;
            float:right;
            padding-right:15px;
        }
    </style>
</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="nav.bussub.invsub.proinf.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.busmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.bussub.invmgr"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="nav.bussub.invsub.proinf.content"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
							<form id="queryForm" class="form-horizontal">
                                <div class="form-group col-sm-12">
                                    <!--理财产品ID/名称-->
                                    <label class="col-sm-2 control-label" for="proId" data-i18n="inv.proId"></label>
                                    <div class="col-sm-4">
                                        <input name="proId" id="proId" class="form-control" value=""/>
                                    </div>
                                    <label class="col-sm-2 control-label" for="proName" data-i18n="inv.proName"></label>
                                    <div class="col-sm-2">
                                        <input name="proName" id="proName" class="form-control" value=""/>
                                    </div>
                                </div>
                                <div class="form-group col-sm-12">
                                    <!--提交日期-->
                                    <label class="col-sm-2 control-label" for="beginDate" data-i18n="inv.proEffTm"></label>
                                    <div class="col-sm-4">
                                        <input name="beginDate" id="beginDate" class="form-control" value=""/>
                                    </div>
                                    <div class="col-sm-4">
                                        <input name="endDate" id="endDate" class="form-control" value=""/>
                                    </div>
                                </div>
                                <div class="form-group">
                                	<label class="col-sm-1 control-label" for="rate" data-i18n="inv.rate"></label>
                                    <div class="col-sm-2">
                                        <input name="rate" id="rate" class="form-control" value=""/>
                                    </div>
                                    <div class="col-sm-4 col-sm-offset-1">
                                        <button type="button" id="searchBtn" class="btn btn-default" onclick="search()" data-i18n="inv.search"></button>
                                    </div>
                                </div>
                            </form>
                            <div class="table-responsive">
                                <table id="proInf" class="table table-striped table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th data-i18n="inv.proId"></th>
                                        <th data-i18n="inv.proName"></th>
                                        <th data-i18n="inv.proType"></th>
                                        <th data-i18n="inv.totalAmt"></th>
                                        <th data-i18n="inv.collectAmt"></th>
                                        <th data-i18n="inv.collectCnt"></th>
                                        <th data-i18n="inv.rate"></th>
                                        <th data-i18n="inv.invTerm"></th>
                                        <th data-i18n="inv.proEffTm"></th>
                                        <th data-i18n="inv.proExpTm"></th>
                                        <th data-i18n="inv.proDesc"></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>

    </div>
</div>
<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script>
    var editor;
    //A 2D array in which the first array is used to define the value options and the second array the displayed options (useful for language strings such as 'All').
    //    var array1 = [10, 25, 50, -1];
    //    var array2 = [10, 25, 50, "All"];
	var table;
    
    <!--查询按钮-->
    function search() {
        table.ajax.reload();
    }
    $(document).ready(function () {

        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        i18nLoad.then(function () {
            editor = new $.fn.dataTable.Editor({
                ajax: {
                    create: {
                        type: 'POST',
                        url: '/inv/proctrl/add',
                        contentType: 'application/json',
                        data: function (d) {
                            return JSON.stringify(d);
                        }
                    },
                    edit: {
                        type: 'POST',
                        url: '/inv/proctrl/modify',
                        contentType: 'application/json',
                        data: function (d) {
                            return JSON.stringify(d);
                        }
                    },
                    remove: {
                        type: 'POST',
                        url: '/inv/proctrl/delete',
                        contentType: 'application/json',
                        data: function (d) {
                            return JSON.stringify(d);
                        },
	                    success: function (data) {
	                        var result = data.result;
	                        if (result =='FAIL') {
	                        	swal($.i18n.t("inv.deleteFail"), $.i18n.t("inv.proEffTmErr"), "error")
	                        }
	                    },
	                    error : function (data) {
	                        swal({text:data});
	                    },
                    }
                },
                table: "#proInf",
                idSrc: 'proId',
                fields: [
                    {label: $.i18n.t("inv.proId"), name: "proId"},
                    {label: $.i18n.t("inv.proName"), name: "proName"},
                    {
                        label: $.i18n.t("inv.proType"),
                        name: "proType",
                        type:"select", 
                        options: [
                            { label: $.i18n.t("inv.accday"), value: "D" }
                        ]
                    },
                    {label: $.i18n.t("inv.totalAmt"), name: "totalAmt"},
                    {label: $.i18n.t("inv.collectAmt"), name: "collectAmt"},
                    {label: $.i18n.t("inv.collectCnt"), name: "collectCnt"},
                    {label: $.i18n.t("inv.rate"), name: "rate"},
                    {label: $.i18n.t("inv.invTerm"), name: "invTerm"},
                    {
                        label: $.i18n.t("inv.proEffTm"),
                        name: "proEffTm",
                        type: "datetime",
                        def: function () {
                            return new Date();
                        },
                        format: "YYYY-MM-DD HH:mm:ss"
                    },
                    {
                        label: $.i18n.t("inv.proExpTm"),
                        name: "proExpTm",
                        type: "datetime",
                        def: function () {
                            return new Date();
                        },
                        format: "YYYY-MM-DD HH:mm:ss"
                    },
                    {label: $.i18n.t("inv.proDesc"), name: "proDesc",type: "textarea"},
                    {label: $.i18n.t("inv.riskDesc"), name: "riskDesc",type: "textarea"},
                    {label: $.i18n.t("inv.rulesDesc"), name: "rulesDesc",type: "textarea"},
                    {
						label: $.i18n.t("inv.cloPerBegin"),
						name: "cloPerBegin",
						type: "datetime",
	                       def: function () {
	                           return new Date();
	                       },
	                       format: "YYYY-MM-DD HH:mm:ss"
					},
					{
							label: $.i18n.t("inv.cloPerEnd"),
							name: "cloPerEnd",
							type: "datetime",
		                       def: function () {
		                           return new Date();
		                       },
		                       format: "YYYY-MM-DD HH:mm:ss"
					},
					{label: $.i18n.t("inv.subscriptRate"), name: "subscriptRate"},
					{label: $.i18n.t("inv.cloPerRate"), name: "cloPerRate"},
					{label: $.i18n.t("inv.investAmt"), name: "investAmt"},
					{
						label: $.i18n.t("inv.earnTm"),
						name: "earnTm",
						type: "datetime",
	                       def: function () {
	                           return new Date();
	                       },
	                       format: "YYYY-MM-DD HH:mm:ss"
				}
                ],
                i18n: {
                    create: {button: $.i18n.t("inv.add"), title: $.i18n.t("inv.add"), submit: $.i18n.t("inv.create")},
                    edit: {button: $.i18n.t("inv.modify"), title: $.i18n.t("inv.modify"), submit: $.i18n.t("inv.update")},
                    remove: {
                       button: $.i18n.t("inv.delete"), title: $.i18n.t("inv.delete"), submit: $.i18n.t("inv.delete"),
                       confirm: {
                            _: $.i18n.t("inv.multi-delete"),
                            1: $.i18n.t("inv.single-delete")
                        }
                    }
                }
            });
            editor.field('proType').input().on('change', function (e, d) {
                if (editor.field('proType').val() === 'D') {
                	editor.field( 'riskDesc' ).hide(); 
                	editor.field( 'rulesDesc' ).hide(); 
                	editor.field( 'cloPerBegin' ).hide(); 
                	editor.field( 'cloPerEnd' ).hide(); 
                	editor.field( 'subscriptRate' ).hide(); 
                	editor.field( 'cloPerRate' ).hide(); 
                	editor.field( 'investAmt' ).hide(); 
                	editor.field( 'earnTm' ).hide();
                }else{
                	editor.field( 'riskDesc' ).show(); 
                	editor.field( 'rulesDesc' ).show(); 
                	editor.field( 'cloPerBegin' ).show(); 
                	editor.field( 'cloPerEnd' ).show(); 
                	editor.field( 'subscriptRate' ).show(); 
                	editor.field( 'cloPerRate' ).show(); 
                	editor.field( 'investAmt' ).show(); 
                	editor.field( 'earnTm' ).show();
                }
            });
            editor.on( 'preSubmit', function ( e, o, action ) {
                if ( action !== 'remove' ) {
                	var proType=this.field( 'proType' );
                    var proId=this.field( 'proId' );
                    var proEffTm=this.field( 'proEffTm' );
                    var proExpTm=this.field( 'proExpTm' );
                    var collectAmt=this.field( 'collectAmt' );
                    var collectCnt=this.field( 'collectCnt' );
                    var rate=this.field( 'rate' );
                    var invTerm=this.field( 'invTerm' );
                    var proName=this.field( 'proName' );
                    var totalAmt=this.field( 'totalAmt' );
                    if ( ! collectAmt.isMultiValue() ) {
                        if ( ! collectAmt.val() ) {
                        	collectAmt.error( $.i18n.t("inv.message.proinfo.null.collectAmt") );
                        }
                        if (  collectAmt.val()<0 ) {
                            collectAmt.error( $.i18n.t("inv.message.proinfo.err.collectAmt") );
                        }
                    }
                    if ( ! collectCnt.isMultiValue() ) {
                        if ( ! collectCnt.val() ) {
                        	collectCnt.error( $.i18n.t("inv.message.proinfo.null.collectCnt") );
                        }
                        if (  collectCnt.val()<0) {
                            collectCnt.error( $.i18n.t("inv.message.proinfo.err.collectCnt") );
                        }
                    }
                    if ( ! invTerm.isMultiValue() ) {
                        if ( ! invTerm.val() ) {
                        	invTerm.error( $.i18n.t("inv.message.proinfo.null.invTerm") );
                        }
                         
                        if ( invTerm.val()>999999||invTerm.val()<=0 ) {
                        	invTerm.error( $.i18n.t("inv.message.proinfo.err.invTerm") );
                        }
                    }
                    if ( ! proName.isMultiValue() ) {
                        if ( ! proName.val() ) {
                        	proName.error( $.i18n.t("inv.message.proinfo.null.proName") );
                        }
                         
                        if ( proName.val().length>256 ) {
                        	proName.error( $.i18n.t("inv.message.proinfo.length.proName") );
                        }
                    }
                    if ( ! totalAmt.isMultiValue() ) {
                        if ( ! totalAmt.val() ) {
                            totalAmt.error( $.i18n.t("inv.message.proinfo.null.totalAmt") );
                        }
                         
                        if ( totalAmt.val()<0 ) {
                            totalAmt.error( $.i18n.t("inv.message.proinfo.err.totalAmt") );
                        }
                    }
                    if ( ! proId.isMultiValue() ) {
                        if ( ! proId.val() ) {
                        	proId.error( $.i18n.t("inv.message.proinfo.null.proId") );
                        }
                         
                        if ( proId.val().length > 20 ) {
                        	proId.error( $.i18n.t("inv.message.proinfo.length.proId") );
                        }
                    }
                    var tm=Date.parse(proEffTm.val());
                    var now=Date.parse(proExpTm.val());
                    if(now<=tm){
                    	proExpTm.error( $.i18n.t("inv.message.proinfo.err.proExpTm") );
                    	proEffTm.error( $.i18n.t("inv.message.proinfo.err.proEffTm") );
                    }
                    
                    if ( ! rate.isMultiValue() ) {
                        if ( ! rate.val() ) {
                        	rate.error( $.i18n.t("inv.message.proinfo.null.rate") );
                        }
                         
                        if ( rate.val() <0 ) {
                        	rate.error( $.i18n.t("inv.message.proinfo.err.rate") );
                        }
                    }
                    if(proType.val()!=='D'){
                    	var cloPerBegin=this.field( 'cloPerBegin' );
                    	var cloPerEnd=this.field( 'cloPerEnd' );
                    	var subscriptRate=this.field( 'subscriptRate' );
                    	var cloPerRate=this.field( 'cloPerRate' );
                    	var investAmt=this.field( 'investAmt' );
                    	var earnTm=this.field( 'earnTm' );
                    	var clobeg=Date.parse(cloPerBegin.val());
                        var cloend=Date.parse(cloPerEnd.val());
                        var eTm=Date.parse(earnTm.val());
                        if(cloend<=clobeg){
                        	cloPerBegin.error( $.i18n.t("inv.message.proinfo.err.cloPerBegin") );
                        	cloPerEnd.error( $.i18n.t("inv.message.proinfo.err.cloPerEnd") );
                        }
                        if(eTm<cloend){
                        	investAmt.error( $.i18n.t("inv.message.proinfo.err.investAmt") );
                        }
                        if ( ! cloPerRate.isMultiValue() ) {
                            if ( ! cloPerRate.val() ) {
                            	cloPerRate.error( $.i18n.t("inv.message.proinfo.null.cloPerRate") );
                            }
                             
                            if ( cloPerRate.val() <0 ) {
                            	cloPerRate.error( $.i18n.t("inv.message.proinfo.err.cloPerRate") );
                            }
                        }
                        if ( ! subscriptRate.isMultiValue() ) {
                            if ( ! subscriptRate.val() ) {
                            	subscriptRate.error( $.i18n.t("inv.message.proinfo.null.subscriptRate") );
                            }
                             
                            if ( subscriptRate.val() <0 ) {
                            	subscriptRate.error( $.i18n.t("inv.message.proinfo.err.subscriptRate") );
                            }
                        }
                        if ( ! investAmt.isMultiValue() ) {
                            if ( ! investAmt.val() ) {
                            	investAmt.error( $.i18n.t("inv.message.proinfo.null.investAmt") );
                            }
                             
                            if ( investAmt.val()<0 ) {
                            	investAmt.error( $.i18n.t("inv.message.proinfo.err.investAmt") );
                            }
                        }
                    }
                    // If any error was reported, cancel the submission so it can be corrected
                    if ( this.inError() ) {
                        return false;
                    }
                }
            } );
            editor.on( 'open', function ( e, o, action ) {
                if ( action === 'edit' ) {
                    this.field( 'proType' ).disable();
                    this.field( 'proId' ).disable();
                    var proEffTm=this.field( 'proEffTm' );
                    var proExpTm=this.field( 'proExpTm' );
                    this.field( 'proEffTm' ).disable();
                    this.field( 'proExpTm' ).disable();
                    this.field( 'collectAmt' ).disable();
                    this.field( 'collectCnt' ).disable();
                    this.field( 'rate' ).disable();
                    this.field( 'invTerm' ).disable();
                    var exptm=Date.parse(proExpTm.val());
                    var efftm=Date.parse(proEffTm.val());
                    var now=Date.parse(new Date());
                    if(now>exptm){
                    	this.field( 'proName' ).disable();
                    	this.field( 'totalAmt' ).disable();
                    	this.field( 'proDesc' ).disable();
                    }
                    var proType=this.field( 'proType' );
                    if(proType.val()==="D"){
                    	this.field( 'riskDesc' ).hide(); 
                        this.field( 'rulesDesc' ).hide(); 
                        this.field( 'cloPerBegin' ).hide(); 
                        this.field( 'cloPerEnd' ).hide(); 
                        this.field( 'subscriptRate' ).hide(); 
                        this.field( 'cloPerRate' ).hide(); 
                        this.field( 'investAmt' ).hide(); 
                        this.field( 'earnTm' ).hide();
                    }else{
                    	this.field( 'riskDesc' ).show(); 
                        this.field( 'rulesDesc' ).show(); 
                        this.field( 'cloPerBegin' ).show(); 
                        this.field( 'cloPerEnd' ).show(); 
                        this.field( 'subscriptRate' ).show(); 
                        this.field( 'cloPerRate' ).show(); 
                        this.field( 'investAmt' ).show(); 
                        this.field( 'earnTm' ).show();
                        if(now>exptm){
                        	this.field( 'riskDesc' ).disable();
                        	this.field( 'rulesDesc' ).disable();
                        	this.field( 'cloPerBegin' ).disable();
                        	this.field( 'cloPerEnd' ).disable();
                        	this.field( 'subscriptRate' ).disable();
                        	this.field( 'cloPerRate' ).disable();
                        	this.field( 'investAmt' ).disable();
                        	this.field( 'earnTm' ).disable();
                        }else{
                        	if(now>efftm){
                        		this.field( 'cloPerBegin' ).disable();
                            	this.field( 'cloPerEnd' ).disable();
                            	this.field( 'earnTm' ).disable();
                        	}
                        }
                    }
                }else{
                	this.field( 'proType' ).enable();
                    this.field( 'proId' ).enable();
                    this.field( 'proName' ).enable();
                    this.field( 'totalAmt' ).enable();
                    this.field( 'collectAmt' ).enable();
                    this.field( 'collectCnt' ).enable();
                    this.field( 'rate' ).enable();
                    this.field( 'invTerm' ).enable();
                    this.field( 'proEffTm' ).enable();
                    this.field( 'proExpTm' ).enable();
                    this.field( 'proDesc' ).enable();
                    this.field( 'riskDesc' ).enable(); 
                    this.field( 'rulesDesc' ).enable(); 
                    this.field( 'cloPerBegin' ).enable(); 
                    this.field( 'cloPerEnd' ).enable(); 
                    this.field( 'subscriptRate' ).enable(); 
                    this.field( 'cloPerRate' ).enable(); 
                    this.field( 'investAmt' ).enable(); 
                    this.field( 'earnTm' ).enable();
                    var proType=this.field( 'proType' );
                    if(proType.val()==="D"){
                    	this.field( 'riskDesc' ).hide(); 
                        this.field( 'rulesDesc' ).hide(); 
                        this.field( 'cloPerBegin' ).hide(); 
                        this.field( 'cloPerEnd' ).hide(); 
                        this.field( 'subscriptRate' ).hide(); 
                        this.field( 'cloPerRate' ).hide(); 
                        this.field( 'investAmt' ).hide(); 
                        this.field( 'earnTm' ).hide();
                    }else{
                    	this.field( 'riskDesc' ).show(); 
                        this.field( 'rulesDesc' ).show(); 
                        this.field( 'cloPerBegin' ).show(); 
                        this.field( 'cloPerEnd' ).show(); 
                        this.field( 'subscriptRate' ).show(); 
                        this.field( 'cloPerRate' ).show(); 
                        this.field( 'investAmt' ).show(); 
                        this.field( 'earnTm' ).show();
                    }
                }
            } );
            table = $('#proInf').DataTable({
                dom: 'B<"floatright"l>rtp',
                ajax: {
                    contentType: 'application/json',
                    url: '/inv/proctrl/findAll',
                    type: 'POST',
                    data: function (d) {
                    	d.extra_search = {
                                "proId" : $("#proId").val(),
                                "proName" : $("#proName").val(),
                                "rate" : $("#rate").val(),
                                "beginDate" : $("#beginDate").val(),
                                "endDate" : $("#endDate").val()
                                };
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
//            "lengthMenu": [array1, array2],
                language: {
//                "decimal": ",",
//                "thousands": ".",
//                    url: '/datatables/plugins/i18n/Chinese.lang'
                    url: languageUrl
                    //url: '//cdn.datatables.net/plug-ins/1.10.15/i18n/Chinese.json'
                },
                select: true,
                buttons: [
                    {extend: "create", editor: editor},
                    {extend: "edit", editor: editor},
                    {extend: "remove", editor: editor},
//                {extend: 'copyHtml5'},
//                {extend: 'csvHtml5'},
//                {extend: 'excelHtml5', title: '文件名'},
                    'copy',
                    'csv',
                    'excel'
//                'pdf',
//                'print'
                ],
                columns: [{
                    data: 'proId'
                }, {
                    data: 'proName'
                }, {
                    data: 'proType',
                   	render: function (data, type, row) {
                        switch (data) {
                            case "D":
                                return $.i18n.t("inv.accday");
                            case "M":
                               return $.i18n.t("inv.accmonth");
                            case "S":
                                return $.i18n.t("inv.accseason");
                            case "Y":
                                return $.i18n.t("inv.accyear");
                            default:
                                return data;
                        }
                   	}
                },{
                    data: 'totalAmt',
                    render: $.fn.dataTable.render.number(',', '.', 2, '$')
//                orderable: false
                }, {
                    data: 'collectAmt',
                    render: $.fn.dataTable.render.number(',', '.', 2, '$')
                //    render: $.fn.dataTable.render.number(',', '.', 2, '$')
                },{
                	data: 'collectCnt'
                },{
                    data: 'rate',
                },{
                    data: 'invTerm',
                    render: function (data, type, row) {
                        switch (data) {
                            case 999999:
                                return 1;
                            case "M":
                               return $.i18n.t("inv.accmonth");
                            case "S":
                                return $.i18n.t("inv.accseason");
                            case "Y":
                                return $.i18n.t("inv.accyear");
                            default:
                                return data;
                        }
                    }
                },{
                    data: 'proEffTm',
                    render: function (data, type, row) {
                        if (data.length > 10) {
                            return data.substring(0, 10);
                        } else {
                            return data ;
                        }
                    }
                },{
                    data: 'proExpTm' ,
                    render: function (data, type, row) {
                        if (data.length > 10) {
                            return data.substring(0, 10);
                        } else {
                            return data ;
                        }
                    }
                },{
                    data: 'proDesc'
                }
                ]
            });
            $('#proInf tbody').on( 'click', 'tr', function () {
            	if ( $(this).hasClass('selected') ) {
                    $(this).removeClass('selected');
                }
                else {
                    table.$('tr.selected').removeClass('selected');
                    $(this).addClass('selected');
                }
            } );
        });

    });
    /**初始化日期控件**/
    var beginTimePick = $('#beginDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        onChangeDate: function(dateText, inst) {
            endTimePick.datetimepicker('minDate',new Date(dateText.replace("-", "-")));
        }
    });

    var endTimePick = $('#endDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d'
        // maxDate:'+1970/01/01',
    });
</script>
</body>

</html>