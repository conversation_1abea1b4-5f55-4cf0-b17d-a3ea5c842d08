<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>

    <title data-i18n="nav.bussub.invsub.fee.title"></title>

    <div th:replace="head"></div>
</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="nav.bussub.invsub.fee.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.busmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.bussub.invmgr"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="nav.bussub.invsub.fee.content"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
                            <div class="box-header">
                                <div class="form-horizontal">
                                    <table class="table table-striped table-bordered table-hover">
                                        <tr>
                                            <!--提交日期-->
                                            <td align="center"><label class="control-label"
                                                data-i18n="cmm.label.date"></label></td>
                                            <td><input class="form-control" id="beginDate"
                                                name="beginDate" /></td>
                                            <td><input class="form-control" id="endDate"
                                                name="endDate" /></td>
                                            <td align="center"><label class="control-label"
                                                data-i18n="inv.totalFeeAmt"></label></td>
                                            <td><input class="form-control" id="totalFeeAmt" name="totalFeeAmt" disabled = "true"/>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            <hr />
                            <div class="table-responsive">
                                <table id="feeInf" class="table table-striped table-bordered table-hover">
                                    <thead>
                                    <tr>
                                        <th data-i18n="inv.proId"></th>
                                        <th data-i18n="inv.invGrantDt"></th>
                                        <th data-i18n="inv.proRate"></th>
                                        <th data-i18n="inv.suminvAmt"></th>
                                        <th data-i18n="inv.sumfeeAmt"></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>

    </div>
</div>
<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script>
    var editor;
    //A 2D array in which the first array is used to define the value options and the second array the displayed options (useful for language strings such as 'All').
    //    var array1 = [10, 25, 50, -1];
    //    var array2 = [10, 25, 50, "All"];
    var table;
    $(document).ready(function () {
    	queryTotalFeeAmt();
        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        i18nLoad.then(function () {
            editor = new $.fn.dataTable.Editor({
                ajax: {
                 //  create: {
                 //      type: 'POST',
                 //      url: '/inv/transferctrl/add',
                 //      contentType: 'application/json',
                 //      data: function (d) {
                 //          return JSON.stringify(d);
                 //      }
                 //  },
                 //  edit: {
                 //      type: 'POST',
                 //      url: '/inv/mercinfctrl/modify',
                 //      contentType: 'application/json',
                 //      data: function (d) {
                 //          return JSON.stringify(d);
                 //      }
                 //  },
                 //  remove: {
                 //      type: 'POST',
                 //      url: '/inv/mercinfctrl/delete',
                 //      contentType: 'application/json',
                 //      data: function (d) {
                 //          return JSON.stringify(d);
                 //      }
                 //  }
                },
                table: "#feeInf",
                    //{
                        //label: $.i18n.t("demo.startDate"),
                        //name: "startDate",
                        //type: "datetime",
                        //def: function () {
                            //return new Date();
                       // },
                       // format: "YYYY-MM-DD HH:mm:ss"
                   // },
                    //{label: $.i18n.t("demo.salary"), name: "salary"}
                i18n: {
                  // create: {button: $.i18n.t("inv.add"), title: $.i18n.t("inv.add"), submit: $.i18n.t("inv.create")},
                  // edit: {button: $.i18n.t("inv.modify"), title: $.i18n.t("inv.modify"), submit: $.i18n.t("inv.update")},
                  // remove: {
                  //     button: $.i18n.t("inv.delete"), title: $.i18n.t("inv.delete"), submit: $.i18n.t("inv.delete"),
                  //     confirm: {
                  //         _: $.i18n.t("inv.multi-delete"),
                  //         1: $.i18n.t("inv.single-delete")
                  //     }
                  // }
                }
            });

         //   editor.on('preSubmit', function (e, o, action) {
         //       var id = editor.field('invId');
         //       o.id = id.val();  // create a new parameter to pass over to the server called entityId
         //   });

            table = $('#feeInf').DataTable({
                dom: 'Blfrtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/inv/feectrl/findAll',
                    type: 'POST',
                    data: function (d) {
                    	d.extra_search = {
                                "beginDate" : $("#beginDate").val(),
                                "endDate" : $("#endDate").val()
                                };
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
//            "lengthMenu": [array1, array2],
                language: {
//                "decimal": ",",
//                "thousands": ".",
//                    url: '/datatables/plugins/i18n/Chinese.lang'
                    url: languageUrl
                    //url: '//cdn.datatables.net/plug-ins/1.10.15/i18n/Chinese.json'
                },
                select: true,
                buttons: [
                    //{extend: "create", editor: editor},
                    //{extend: "edit", editor: editor},
                    //{extend: "remove", editor: editor},
//                {extend: 'copyHtml5'},
//                {extend: 'csvHtml5'},
//                {extend: 'excelHtml5', title: '文件名'},
                    'copy',
                    'csv',
                    'excel'
//                'pdf',
//                'print'
                ],
                columns: [{
                    data: 'proId'
                }, {
                    data: 'invGrantDt'
                }, {
                    data: 'proRate'
                },{
                    data: 'suminvAmt',
                    render: $.fn.dataTable.render.number(',', '.', 2, '$')
//                orderable: false
                }, {
                    data: 'sumfeeAmt',
                    render: $.fn.dataTable.render.number(',', '.', 2, '$')
                //    render: $.fn.dataTable.render.number(',', '.', 2, '$')
                }
                ]
            });
        });
    });
    /**初始化日期控件**/
    var beginTimePick = $('#beginDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        onChangeDate: function(dateText, inst) {
            endTimePick.datetimepicker('minDate',new Date(dateText.replace("-", "-")));
        }
    });

    var endTimePick = $('#endDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d'
        // maxDate:'+1970/01/01',
    });
    $("#beginDate").focusout(function () {
        table.ajax.reload();
        queryTotalFeeAmt();
    });
    $("#endDate").focusout(function () {
        table.ajax.reload();
        queryTotalFeeAmt();
    });
    function queryTotalFeeAmt(){
    	var beginDateStr = $("#beginDate").val() ;
    	var endDateStr = $("#endDate").val() ;
        var param = {
             "beginDateStr" : $.trim(beginDateStr),
             "endDateStr" : $.trim(endDateStr)
         };
    	$.ajax({
            type: "POST",
            url: "/inv/feectrl/query",
            dataType:"json",
            data:param,
            success: function (data) {
                document.getElementById("totalFeeAmt").value=data.totalFeeAmt;
                return;
            },
            error : function (data) {
                document.getElementById("totalFeeAmt").value="0";
                return;
            },
        });
    }
</script>
</body>

</html>