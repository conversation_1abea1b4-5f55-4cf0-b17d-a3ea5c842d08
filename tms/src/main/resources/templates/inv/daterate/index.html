<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>

    <title data-i18n="nav.bussub.invsub.datarate.title"></title>

    <div th:replace="head"></div>
</head>

<body>

<div id="wrapper">

    <div th:replace="nav"></div>

    <div id="page-wrapper" class="gray-bg">
        <div th:replace="top"></div>

        <div class="row wrapper border-bottom white-bg page-heading">
            <div class="col-lg-10">
                <h2 data-i18n="nav.bussub.invsub.datarate.content"></h2>
                <ol class="breadcrumb">
                    <li>
                        <a data-i18n="nav.busmgr"></a>
                    </li>
                    <li>
                        <a data-i18n="nav.bussub.invmgr"></a>
                    </li>
                    <li class="active">
                        <strong data-i18n="nav.bussub.invsub.datarate.content"></strong>
                    </li>
                </ol>
            </div>
        </div>
        <div class="wrapper wrapper-content  animated fadeInRight">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins">
                        <div class="ibox-content">
	                        <form id="queryForm" class="form-horizontal">
	                            <div class="form-group col-sm-6">
                                    <label class="col-sm-4" for="proId" data-i18n="inv.proId"></label>
                                    <div class="col-sm-5">
                                        <input name="proId" id="proId" class="form-control" value=""/>
                                    </div>
                                </div>
	                            <div class="form-group col-sm-6">
	                                <!--提交日期-->
	                                <label class="col-sm-4 control-label" for="beginDate" data-i18n="inv.tradeDate"></label>
	                                <div class="col-sm-4">
	                                    <input name="beginDate" id="beginDate" class="form-control" value=""/>
	                                </div>
	                                <div class="col-sm-4">
	                                    <input name="endDate" id="endDate" class="form-control" value=""/>
	                                </div>
	                            </div>
	                            <div class="form-group col-sm-12">
                                    <div class="col-sm-4 col-sm-offset-1">
                                        <button type="button" id="searchBtn" class="btn btn-default" onclick="search()" data-i18n="inv.search"></button>
                                    </div>
                                </div>
	                        </form>
	                        <div>  
	                            <label class="col-sm-2" for="proRate" data-i18n="inv.dayRate"></label>
	                            <div class="col-sm-2">
	                                <input name="proRate" id="proRate" class="form-control" th:disabled = "true"/>
	                            </div>
	                            <label class="col-sm-2" for="suminvAmt" data-i18n="inv.demBal"></label>
                                <div class="col-sm-2">
                                    <input name="suminvAmt" id="suminvAmt" class="form-control" th:disabled = "true"/>
                                </div>
                                <label class="col-sm-2" for="totalFeeAmt" data-i18n="inv.daySettle"></label>
                                <div class="col-sm-2">
                                    <input name="totalFeeAmt" id="totalFeeAmt" class="form-control" th:disabled = "true"/>
                                </div>
	                        </div>
	                        <div>  
                                <label class="col-sm-2" for="curDayRate" data-i18n="inv.curDayRate"></label>
                                <div class="col-sm-2">
                                    <input name="curDayRate" id="curDayRate" class="form-control"/>
                                </div>
                            </div>
                            <div>
                                <div class="form-group">
                                    <div class="col-sm-4 col-sm-offset-1">
                                        <button type="button" id="modifyBtn" class="btn btn-default" onclick="modify()" data-i18n="inv.modify"></button>
                                    </div>
                                </div>
                                <div class="form-group col-sm-6">
                                    <!--提交日期-->
                                    <label class="col-sm-2 control-label" for="ubeginDate" data-i18n="inv.filDate"></label>
                                    <div class="col-sm-4">
                                        <input name="ubeginDate" id="ubeginDate" class="form-control" value=""/>
                                    </div>
                                    <div class="col-sm-4">
                                        <input name="uendDate" id="uendDate" class="form-control" value=""/>
                                    </div>
                                </div>
                            </div>
                            <div>
	                            <div class="table-responsive col-sm-12">
	                                <table id="feeInf" class="table table-striped table-bordered table-hover">
	                                    <thead>
	                                    <tr>
	                                        <th data-i18n="inv.rateDate"></th>
	                                        <th data-i18n="inv.proId"></th>
	                                        <th data-i18n="inv.proName"></th>
	                                        <th data-i18n="inv.rate"></th>
	                                        <th data-i18n="inv.createUserId"></th>
	                                    </tr>
	                                    </thead>
	                                </table>
	                            </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:replace="footer"></div>

    </div>
</div>
<div th:replace="script"></div>
<!-- Page-Level Scripts -->
<script>
    var editor;
    var table;
    function search() {
    	table.ajax.reload();
        var proId = $("#proId").val();
        var param = {
             "proId" : proId
         };
        $.ajax({
            type: "POST",
            url: "/inv/dataratectrl/query",
            dataType:"json",
            data:param,
            success: function (data) {
                document.getElementById("proRate").value=data.proRate;
                document.getElementById("suminvAmt").value=data.suminvAmt;
                document.getElementById("totalFeeAmt").value=data.totalFeeAmt;
                return;
            },
            error : function (data) {
                document.getElementById("proRate").value="0";
                document.getElementById("suminvAmt").value="0";
                document.getElementById("totalFeeAmt").value="0";
                return;
            },
        });
    }
    function check(proId,ubeginDate,uendDate,rate){
    	if(proId==""){
    		swal($.i18n.t("inv.message.proIdNull"))
    		return true;
    	}
    	if(ubeginDate==""||uendDate==""){
    		swal($.i18n.t("inv.message.dtNull"))
    		return true;
    	}
    	if(rate==""){
    		swal($.i18n.t("inv.message.rateNull"))
            return true;
        }
    	return false;
    }
    function modify() {
        var proId = $("#proId").val();
        var ubeginDate = $("#ubeginDate").val();
        var uendDate = $("#uendDate").val();
        var rate = $("#curDayRate").val();
        var flg=check(proId,ubeginDate,uendDate,rate);
        if (flg){
        	return;
        };
        var param = {
             "proId" : proId,
             "ubeginDate" : $.trim(ubeginDate),
             "uendDate" : $.trim(uendDate),
             "rate" : rate
         };
        $.ajax({
            type: "POST",
            url: "/inv/dataratectrl/setrate",
            dataType:"json",
            cache:false,
            data:param,
            success: function (data) {
            	var result = data.msgCd;
                if (result !='INV00000') {
                    swal(data.msgInfo);
                } else {
                    swal($.i18n.t("inv.message.update.success"))
                }
                return;
            },
            error : function (data) {
            	swal(data.msgInfo);
                return;
            },
        });
    }
    $(document).ready(function () {
        var languageUrl;
        switch ($.cookie('lang')) {
            case 'zh':
                languageUrl = '/datatables/plugins/i18n/Chinese.lang';
                break;
            case 'en':
                languageUrl = '/datatables/plugins/i18n/English.lang';
                break;
            case 'km':
                languageUrl = '/datatables/plugins/i18n/Cambodia.lang';
                break;
        }

        i18nLoad.then(function () {
            editor = new $.fn.dataTable.Editor({
                ajax: {
                },
                table: "#feeInf",
                idSrc: 'proId',
                i18n: {
                }
            });


            table = $('#feeInf').DataTable({
                dom: 'Blfrtip',
                ajax: {
                    contentType: 'application/json',
                    url: '/inv/dataratectrl/findAll',
                    type: 'POST',
                    data: function (d) {
                    	d.extra_search = {
                    			"proId" : $("#proId").val(),
                                "beginDate" : $("#beginDate").val(),
                                "endDate" : $("#endDate").val()
                                };
                        return JSON.stringify(d);
                    }
                },
                serverSide: true,
                searchDelay: 1000,
                responsive: true,
                processing: true,
                language: {
                    url: languageUrl
                },
                select: true,
                buttons: [
                    'copy',
                    'csv',
                    'excel'
                ],
                columns: [{
                    data: 'rateDate'
                }, {
                    data: 'proId'
                }, {
                    data: 'proName'
                },{
                    data: 'rate'
                }, {
                    data: 'createUserId'
                }
                ]
            });
        });
    });
    /**初始化日期控件**/
    var beginTimePick = $('#beginDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        onChangeDate: function(dateText, inst) {
            endTimePick.datetimepicker('minDate',new Date(dateText.replace("-", "-")));
        }
    });

    var endTimePick = $('#endDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d'
        // maxDate:'+1970/01/01',
    });
    /**初始化日期控件**/
    var date=new Date();
    var nowDt="'"+date.getFullYear()+"/"+date.getMonth()+1+"/"+date.getDate()+"'";
    var ubeginTimePick = $('#ubeginDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d',
        onChangeDate: function(dateText, inst) {
            uendTimePick.datetimepicker('minDate',new Date(dateText.replace("-", "-")));
        },
      	minDate:nowDt
    });

    var uendTimePick = $('#uendDate').datetimepicker({
        lang:'en',
        timepicker:false,
        validateOnBlur: false,
        format:'Y-m-d ',
        formatDate:'Y-m-d'
        // maxDate:'+1970/01/01',
    });
</script>
</body>

</html>