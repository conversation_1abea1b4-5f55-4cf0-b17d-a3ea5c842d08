/* Web3.0 Login Page Styles */
:root {
    --color-primary: rgb(145, 85, 253);
    --color-primary-dark: rgb(130, 71, 229);
    --color-background: rgb(244, 245, 250);
    --color-white: rgb(255, 255, 255);
    --color-text-primary: rgb(58, 53, 65);
    --color-text-secondary: rgb(145, 85, 253);
    --shadow-card: 0px 2px 10px 0px rgba(58, 53, 65, 0.1);
    --shadow-button: 0px 4px 8px -4px rgba(58, 53, 65, 0.42);
    --radius-card: 6px;
    --radius-button: 5px;
    --radius-input: 4px;
    --space-xs: 8px;
    --space-sm: 16px;
    --space-md: 24px;
    --space-lg: 48px;
    --font-size-sm: 14px;
    --font-size-md: 16px;
    --font-size-lg: 24px;
    --line-height-sm: 20px;
    --line-height-md: 24px;
    --line-height-lg: 32px;
}

/* Reset and base styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

.login-page {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--color-background);
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

/* Background */
.login-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
}

/* Decorative elements using CSS */
.login-background::before {
    content: '';
    position: absolute;
    top: 10%;
    left: 5%;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
    z-index: 1;
}

.login-background::after {
    content: '';
    position: absolute;
    bottom: 10%;
    right: 5%;
    width: 150px;
    height: 150px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    z-index: 1;
}

/* Main container */
.login-container {
    position: relative;
    z-index: 2;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-md);
}

/* Login card */
.login-card {
    background: var(--color-white);
    border-radius: var(--radius-card);
    box-shadow: var(--shadow-card);
    padding: var(--space-lg) var(--space-md) 36px var(--space-md);
    width: 100%;
    max-width: 450px;
    animation: fadeInUp 0.6s ease-out;
    position: relative;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Card language switcher */
.card-language-switcher {
    position: absolute;
    top: var(--space-md);
    right: var(--space-md);
    z-index: 10;
}

.card-language-switcher .language-selector {
    display: flex;
    align-items: center;
    gap: 6px;
    background: rgba(145, 85, 253, 0.1);
    padding: 6px 12px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    border: 1px solid rgba(145, 85, 253, 0.2);
}

.card-language-switcher .language-selector:hover {
    background: rgba(145, 85, 253, 0.15);
    border-color: rgba(145, 85, 253, 0.3);
}

.card-language-switcher .language-icon {
    color: var(--color-primary);
    font-size: 14px;
}

.card-language-switcher .language-text {
    font-size: 12px;
    color: var(--color-primary);
    font-weight: 500;
}

.card-language-switcher .language-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    background: var(--color-white);
    border-radius: var(--radius-input);
    box-shadow: var(--shadow-card);
    padding: 8px;
    min-width: 120px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease-in-out;
}

.card-language-switcher .language-selector:hover .language-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.card-language-switcher .language-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 10px;
    border-radius: var(--radius-input);
    text-decoration: none;
    color: var(--color-text-primary);
    font-size: 12px;
    transition: background-color 0.2s ease-in-out;
}

.card-language-switcher .language-option:hover {
    background: #f5f5f5;
    color: var(--color-text-primary);
    text-decoration: none;
}

.card-language-switcher .language-option.active {
    background: var(--color-primary);
    color: var(--color-white);
}

.card-language-switcher .language-option img {
    width: 16px;
    height: 12px;
    object-fit: cover;
    border-radius: 2px;
}

/* Logo section */
.logo-section {
    text-align: center;
    margin-bottom: var(--space-lg);
    margin-top: var(--space-sm);
}

.logo-name {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-lg);
    font-weight: 700;
    color: var(--color-text-primary);
    margin: 0;
}

/* Form styles */
.login-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.form-field {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
}

.field-label {
    font-size: var(--font-size-sm);
    line-height: var(--line-height-sm);
    font-weight: 500;
    color: var(--color-text-primary);
}

.input-wrapper {
    position: relative;
}

.form-input {
    width: 100%;
    padding: 12px 16px 12px 44px;
    border: 1px solid #e0e0e0;
    border-radius: var(--radius-input);
    font-size: var(--font-size-md);
    line-height: var(--line-height-md);
    background: var(--color-white);
    transition: all 0.2s ease-in-out;
}

.form-input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(145, 85, 253, 0.1);
}

.input-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #9e9e9e;
    font-size: 16px;
}

/* Form options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: var(--space-xs) 0;
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    cursor: pointer;
}

.checkbox-input {
    width: 16px;
    height: 16px;
    accent-color: var(--color-primary);
}

.checkbox-label {
    font-size: var(--font-size-sm);
    color: var(--color-text-primary);
}

.forgot-link {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    text-decoration: none;
    transition: opacity 0.2s ease-in-out;
}

.forgot-link:hover {
    opacity: 0.8;
    text-decoration: underline;
}

/* Login button */
.login-button {
    width: 100%;
    padding: 12px 24px;
    background: var(--color-primary);
    color: var(--color-white);
    border: none;
    border-radius: var(--radius-button);
    font-size: var(--font-size-md);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    box-shadow: var(--shadow-button);
    margin-top: var(--space-sm);
}

.login-button:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

.login-button:active {
    transform: translateY(0);
}



/* Alert styles */
.alert {
    padding: 12px 16px;
    border-radius: var(--radius-input);
    font-size: var(--font-size-sm);
    margin: 0;
}

.alert-danger {
    background: #fee;
    color: #c33;
    border: 1px solid #fcc;
}

/* Responsive design */
@media (max-width: 768px) {
    .login-container {
        padding: var(--space-sm);
    }

    .login-card {
        padding: var(--space-md) var(--space-sm) var(--space-md) var(--space-sm);
    }

    .card-language-switcher {
        top: var(--space-sm);
        right: var(--space-sm);
    }

    .card-language-switcher .language-text {
        display: none;
    }

    .card-language-switcher .language-selector {
        padding: 6px 8px;
    }
}
