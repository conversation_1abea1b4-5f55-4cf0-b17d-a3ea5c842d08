@charset "UTF-8";
/*
 * blueimp Gallery Indicator CSS 1.1.0
 * https://github.com/blueimp/Gallery
 *
 * Copyright 2013, <PERSON>
 * https://blueimp.net
 *
 * Licensed under the MIT license:
 * http://www.opensource.org/licenses/MIT
 */

.blueimp-gallery > .indicator {
  position: absolute;
  top: auto;
  right: 15px;
  bottom: 15px;
  left: 15px;
  margin: 0 40px;
  padding: 0;
  list-style: none;
  text-align: center;
  line-height: 10px;
  display: none;
}
.blueimp-gallery > .indicator > li {
  display: inline-block;
  width: 9px;
  height: 9px;
  margin: 6px 3px 0 3px;
  -webkit-box-sizing: content-box;
     -moz-box-sizing: content-box;
          box-sizing: content-box;
  border: 1px solid transparent;
  background: #ccc;
  background: rgba(255, 255, 255, 0.25) center no-repeat;
  border-radius: 5px;
  box-shadow: 0 0 2px #000;
  opacity: 0.5;
  cursor: pointer;
}
.blueimp-gallery > .indicator > li:hover,
.blueimp-gallery > .indicator > .active {
  background-color: #fff;
  border-color: #fff;
  opacity: 1;
}
.blueimp-gallery-controls > .indicator {
  display: block;
  /* Fix z-index issues (controls behind slide element) on Android: */
  -webkit-transform: translateZ(0);
     -moz-transform: translateZ(0);
      -ms-transform: translateZ(0);
       -o-transform: translateZ(0);
          transform: translateZ(0);
}
.blueimp-gallery-single > .indicator {
  display: none;
}
.blueimp-gallery > .indicator {
  -webkit-user-select: none;
   -khtml-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

/* IE7 fixes */
*+html .blueimp-gallery > .indicator > li {
  display: inline;
}
