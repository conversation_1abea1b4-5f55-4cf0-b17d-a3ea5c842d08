<div align="center">
  <a href="http://miromannino.com/projects/justified-gallery/" target="_blank">
    <img alt="image" src="https://raw.github.com/miromannino/Justified-Gallery/gh-imgs/jgcover.png" />
  </a>
</div>

This is a JQuery plugin that allows you to create an high quality justified gallery of images. 

A common problem, for people who create sites, is to create an elegant image gallery that manages 
the various sizes of images. Flickr and Google+ manage this situation in an excellent way, 
the purpose of this plugin is to give you the power of this solutions, with a new fast algorithm.

You can read the entire description of this project 
in the <a href="http://miromannino.com/projects/justified-gallery/">official project page</a>.
