<!doctype html>

<title>CodeMirror: Language Modes</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../doc/docs.css">

<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../doc/logo.png"></a>

  <ul>
    <li><a href="../index.html">Home</a>
    <li><a href="../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a class=active href="#">Language modes</a>
  </ul>
</div>

<article>

<h2>Language modes</h2>

<p>This is a list of every mode in the distribution. Each mode lives
in a subdirectory of the <code>mode/</code> directory, and typically
defines a single JavaScript file that implements the mode. Loading
such file will make the language available to CodeMirror, through
the <a href="manual.html#option_mode"><code>mode</code></a>
option.</p>

<div style="-webkit-columns: 100px 2; -moz-columns: 100px 2; columns: 100px 2;">
    <ul style="margin-top: 0">
      <li><a href="apl/index.html">APL</a></li>
      <li><a href="asterisk/index.html">Asterisk dialplan</a></li>
      <li><a href="clike/index.html">C, C++, C#</a></li>
      <li><a href="clojure/index.html">Clojure</a></li>
      <li><a href="cobol/index.html">COBOL</a></li>
      <li><a href="coffeescript/index.html">CoffeeScript</a></li>
      <li><a href="commonlisp/index.html">Common Lisp</a></li>
      <li><a href="css/index.html">CSS</a></li>
      <li><a href="cypher/index.html">Cypher</a></li>
      <li><a href="python/index.html">Cython</a></li>
      <li><a href="d/index.html">D</a></li>
      <li><a href="django/index.html">Django</a> (templating language)</li>
      <li><a href="diff/index.html">diff</a></li>
      <li><a href="dtd/index.html">DTD</a></li>
      <li><a href="dylan/index.html">Dylan</a></li>
      <li><a href="ecl/index.html">ECL</a></li>
      <li><a href="eiffel/index.html">Eiffel</a></li>
      <li><a href="erlang/index.html">Erlang</a></li>
      <li><a href="fortran/index.html">Fortran</a></li>
      <li><a href="mllike/index.html">F#</a></li>
      <li><a href="gas/index.html">Gas</a> (AT&amp;T-style assembly)</li>
      <li><a href="gherkin/index.html">Gherkin</a></li>
      <li><a href="go/index.html">Go</a></li>
      <li><a href="groovy/index.html">Groovy</a></li>
      <li><a href="haml/index.html">HAML</a></li>
      <li><a href="haskell/index.html">Haskell</a></li>
      <li><a href="haxe/index.html">Haxe</a></li>
      <li><a href="htmlembedded/index.html">HTML embedded scripts</a></li>
      <li><a href="htmlmixed/index.html">HTML mixed-mode</a></li>
      <li><a href="http/index.html">HTTP</a></li>
      <li><a href="clike/index.html">Java</a></li>
      <li><a href="jade/index.html">Jade</a></li>
      <li><a href="javascript/index.html">JavaScript</a></li>
      <li><a href="jinja2/index.html">Jinja2</a></li>
      <li><a href="julia/index.html">Julia</a></li>
      <li><a href="kotlin/index.html">Kotlin</a></li>
      <li><a href="css/less.html">LESS</a></li>
      <li><a href="livescript/index.html">LiveScript</a></li>
      <li><a href="lua/index.html">Lua</a></li>
      <li><a href="markdown/index.html">Markdown</a> (<a href="gfm/index.html">GitHub-flavour</a>)</li>
      <li><a href="mirc/index.html">mIRC</a></li>
      <li><a href="modelica/index.html">Modelica</a></li>
      <li><a href="nginx/index.html">Nginx</a></li>
      <li><a href="ntriples/index.html">NTriples</a></li>
      <li><a href="mllike/index.html">OCaml</a></li>
      <li><a href="octave/index.html">Octave</a> (MATLAB)</li>
      <li><a href="pascal/index.html">Pascal</a></li>
      <li><a href="pegjs/index.html">PEG.js</a></li>
      <li><a href="perl/index.html">Perl</a></li>
      <li><a href="php/index.html">PHP</a></li>
      <li><a href="pig/index.html">Pig Latin</a></li>
      <li><a href="properties/index.html">Properties files</a></li>
      <li><a href="puppet/index.html">Puppet</a></li>
      <li><a href="python/index.html">Python</a></li>
      <li><a href="q/index.html">Q</a></li>
      <li><a href="r/index.html">R</a></li>
      <li><a href="rpm/index.html">RPM</a></li>
      <li><a href="rst/index.html">reStructuredText</a></li>
      <li><a href="ruby/index.html">Ruby</a></li>
      <li><a href="rust/index.html">Rust</a></li>
      <li><a href="sass/index.html">Sass</a></li>
      <li><a href="clike/scala.html">Scala</a></li>
      <li><a href="scheme/index.html">Scheme</a></li>
      <li><a href="css/scss.html">SCSS</a></li>
      <li><a href="shell/index.html">Shell</a></li>
      <li><a href="sieve/index.html">Sieve</a></li>
      <li><a href="slim/index.html">Slim</a></li>
      <li><a href="smalltalk/index.html">Smalltalk</a></li>
      <li><a href="smarty/index.html">Smarty</a></li>
      <li><a href="smartymixed/index.html">Smarty/HTML mixed</a></li>
      <li><a href="solr/index.html">Solr</a></li>
      <li><a href="sql/index.html">SQL</a> (several dialects)</li>
      <li><a href="sparql/index.html">SPARQL</a></li>
      <li><a href="stex/index.html">sTeX, LaTeX</a></li>
      <li><a href="tcl/index.html">Tcl</a></li>
      <li><a href="textile/index.html">Textile</a></li>
      <li><a href="tiddlywiki/index.html">Tiddlywiki</a></li>
      <li><a href="tiki/index.html">Tiki wiki</a></li>
      <li><a href="toml/index.html">TOML</a></li>
      <li><a href="tornado/index.html">Tornado</a> (templating language)</li>
      <li><a href="turtle/index.html">Turtle</a></li>
      <li><a href="vb/index.html">VB.NET</a></li>
      <li><a href="vbscript/index.html">VBScript</a></li>
      <li><a href="velocity/index.html">Velocity</a></li>
      <li><a href="verilog/index.html">Verilog/SystemVerilog</a></li>
      <li><a href="xml/index.html">XML/HTML</a></li>
      <li><a href="xquery/index.html">XQuery</a></li>
      <li><a href="yaml/index.html">YAML</a></li>
      <li><a href="z80/index.html">Z80</a></li>
    </ul>
  </div>

</article>
