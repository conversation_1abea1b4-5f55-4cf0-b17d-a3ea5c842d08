<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>TMS Login - Web3.0 Style</title>
    
    <link href="css/bootstrap.min.css" rel="stylesheet" />
    <link href="font-awesome/css/font-awesome.css" rel="stylesheet" />
    <link href="css/animate.css" rel="stylesheet" />
    <link href="css/style.css" rel="stylesheet" />
    <link href="css/login-web3.css" rel="stylesheet" />
</head>

<body class="login-page">
    <!-- Background with decorative elements -->
    <div class="login-background">
        <img src="figma-assets/Bg-x1.png" alt="Background" class="bg-image" />
        <div class="decorative-elements">
            <img src="figma-assets/Tree-x1.png" alt="Tree" class="tree-decoration" />
            <img src="figma-assets/Mask-x1.png" alt="Mask" class="mask-decoration" />
        </div>
    </div>

    <!-- Main login container -->
    <div class="login-container">
        <!-- Login Card -->
        <div class="login-card" id="login-body">
            <!-- Logo Section -->
            <div class="logo-section">
                <h2 class="logo-name">TMS</h2>
            </div>

            <!-- Login Form -->
            <form class="login-form" role="form" action="/login" method="post">
                <!-- Username Field -->
                <div class="form-field">
                    <label class="field-label">Username</label>
                    <div class="input-wrapper">
                        <input 
                            type="text" 
                            name="username" 
                            class="form-input" 
                            placeholder="Enter your username"
                            required 
                        />
                        <i class="fa fa-user input-icon"></i>
                    </div>
                </div>

                <!-- Password Field -->
                <div class="form-field">
                    <label class="field-label">Password</label>
                    <div class="input-wrapper">
                        <input 
                            type="password" 
                            name="password" 
                            class="form-input" 
                            placeholder="Enter your password"
                            required 
                        />
                        <i class="fa fa-lock input-icon"></i>
                    </div>
                </div>

                <!-- Remember & Forgot Password -->
                <div class="form-options">
                    <label class="checkbox-wrapper">
                        <input type="checkbox" class="checkbox-input" />
                        <span class="checkbox-label">Remember me</span>
                    </label>
                    <a href="#" class="forgot-link">Forgot Password?</a>
                </div>

                <!-- Login Button -->
                <button type="submit" class="login-button">Sign In</button>
            </form>
        </div>

        <!-- Language Switcher -->
        <div class="language-switcher">
            <div class="language-selector">
                <i class="fa fa-globe language-icon"></i>
                <span class="language-text">English</span>
                <div class="language-dropdown">
                    <a class="language-option active" data-lang="zh">
                        <img src="img/flags/32/China.png" alt="中文" />
                        <span>中文</span>
                    </a>
                    <a class="language-option" data-lang="en">
                        <img src="img/flags/32/United-States.png" alt="English" />
                        <span>English</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/jquery-2.1.1.js"></script>
    <script src="js/bootstrap.min.js"></script>
    
    <script>
        $(document).ready(function () {
            // Language switching
            $('.language-option').on('click', function (e) {
                e.preventDefault();
                var lang = $(this).data('lang');
                
                $('.language-option').removeClass('active');
                $(this).addClass('active');
                
                if (lang === 'en') {
                    $('.language-text').text('English');
                } else if (lang === 'zh') {
                    $('.language-text').text('中文');
                }
            });

            // Form enhancements
            $('.form-input').on('focus', function() {
                $(this).closest('.form-field').addClass('focused');
            });

            $('.form-input').on('blur', function() {
                $(this).closest('.form-field').removeClass('focused');
            });

            // Add loading state to login button
            $('.login-form').on('submit', function(e) {
                e.preventDefault(); // Prevent actual submission for demo
                $('.login-button').prop('disabled', true).text('Signing in...');
                
                // Simulate login process
                setTimeout(function() {
                    $('.login-button').prop('disabled', false).text('Sign In');
                    alert('Demo: Login functionality would be processed here');
                }, 2000);
            });
        });
    </script>
</body>
</html>
