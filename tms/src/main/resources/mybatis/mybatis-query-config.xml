<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <!-- 开启驼峰命名转换 -->
        <setting name="mapUnderscoreToCamelCase" value="true"/>
        <!-- 开启延迟加载 -->
        <setting name="lazyLoadingEnabled" value="true"/>
        <!-- 设置超时时间 -->
        <setting name="defaultStatementTimeout" value="30"/>
        <!-- 开启二级缓存 -->
        <setting name="cacheEnabled" value="true"/>
    </settings>
    
    <!-- 类型别名配置 -->
    <typeAliases>
        <!-- 手续费查询相关类型别名 -->
        <typeAlias alias="FeeOrderQueryDO" type="com.hisun.tms.cpt.model.FeeOrderQueryDO"/>
        
        <!-- 汇率查询相关类型别名 -->
        <typeAlias alias="ExchangeRateDO" type="com.hisun.tms.tfm.model.ExchangeRateDO"/>
        
        <!-- 手续费参数管理相关类型别名 -->
        <typeAlias alias="FeeRuleManagerDO" type="com.hisun.tms.tfm.model.FeeRuleManagerDO"/>
        <typeAlias alias="TfmFindInput" type="com.hisun.tms.tfm.model.TfmFindInput"/>
        
        <!-- 账户地址管理相关类型别名 -->
        <typeAlias alias="AccountAddressDO" type="com.hisun.tms.acm.model.AccountAddressDO"/>
        
        <!-- Cregis回调记录相关类型别名 -->
        <typeAlias alias="DmTransferCallbackDO" type="com.hisun.tms.cpt.entity.DmTransferCallbackDO"/>
        
        <!-- 通用查询输入参数类型别名 -->
        <typeAlias alias="QueryFindInput" type="com.hisun.tms.common.datatables.QueryFindInput"/>
    </typeAliases>
    
</configuration>