# MyBatis 查询功能配置说明

## 概述

此目录包含专门为手续费查询和汇率查询功能创建的 MyBatis 配置文件，用于避免与现有的 TfmConfig 产生冲突。

## 配置文件

### mybatis-query-config.xml

-   **用途**: MyBatis 核心配置文件，用于查询功能
-   **功能**:
    -   配置 MyBatis 基本设置（驼峰命名转换、延迟加载等）
    -   定义类型别名（FeeOrderQueryDO、ExchangeRateDO）
    -   预留插件配置位置

## Java 配置类

### QueryFunctionMyBatisConfig.java

-   **位置**: `com.hisun.tms.common.mybatiesConfig.QueryFunctionMyBatisConfig`
-   **用途**: Spring Boot 配置类，用于初始化查询功能的 MyBatis 组件
-   **功能**:
    -   扫描 DAO 接口：`com.hisun.tms.cpt.dao` 和 `com.hisun.tms.tfm.dao`
    -   扫描 Mapper XML 文件：`classpath*:com/hisun/**/mapper/{cpt,tfm}/*.xml`
    -   使用 `tfmDataSource` 数据源
    -   创建独立的 SqlSessionFactory、SqlSessionTemplate 和 TransactionManager

## 支持的功能

### 手续费查询

-   **DAO 接口**: `com.hisun.tms.cpt.dao.ICptFeeOrderQueryDao`
-   **Mapper XML**: `com/hisun/tms/mapper/cpt/CptFeeOrderQueryMapper.xml`
-   **数据模型**: `com.hisun.tms.cpt.model.FeeOrderQueryDO`
-   **数据表**: `tfm_fee_order`

### 汇率查询

-   **DAO 接口**: `com.hisun.tms.tfm.dao.ITfmExchangeRateDao`
-   **Mapper XML**: `com/hisun/tms/mapper/tfm/TfmExchangeRateMapper.xml`
-   **数据模型**: `com.hisun.tms.tfm.model.ExchangeRateDO`
-   **数据表**: `tfm_exchange_rate`

## 冲突避免策略

1. **独立的 SqlSessionFactory**: 使用 `queryFunctionSqlSessionFactory` 而不是默认的
2. **独立的 SqlSessionTemplate**: 使用 `queryFunctionSqlSessionTemplate`
3. **独立的 TransactionManager**: 使用 `queryFunctionTransactionManager`
4. **特定的包扫描**: 只扫描查询相关的 DAO 包，避免与现有配置重叠
5. **注解过滤**: 使用 `@Mapper` 注解过滤，确保只扫描标记的接口

## 注意事项

1. 此配置与现有的 `TfmMyBatisConfig` 完全独立，不会产生冲突
2. 使用相同的 `tfmDataSource`，因为查询的表都在 tfm 数据库中
3. 配置文件路径和命名都经过精心设计，避免与现有配置冲突
4. 所有 Bean 名称都使用 `queryFunction` 前缀，确保唯一性

## 使用方法

配置会自动被 Spring Boot 加载，无需手动配置。DAO 接口会自动注入到 Service 层中使用。

```java
@Service
public class CptFeeOrderQueryServiceImpl {

    @Autowired
    private ICptFeeOrderQueryDao cptFeeOrderQueryDao;

    @Autowired
    private ITfmExchangeRateDao tfmExchangeRateDao;

    // 使用 DAO 进行数据操作
}
```
