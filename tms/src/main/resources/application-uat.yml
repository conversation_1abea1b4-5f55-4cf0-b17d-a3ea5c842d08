eureka:
  client:
    serviceUrl:
#      defaultZone: http://************:9002/eureka/,http://************:9002/eureka/
      defaultZone: http://**************:9002/eureka/
    register-with-eureka: false
  instance:
    prefer-ip-address: true

server:
  port: 9020
  compression:
    enabled: true
  connection-timeout: 3000

spring:
  application:
    name: TMS
  datasource:
    tomcat:
      max-active: 20
      max-idle: 20
      min-idle: 10
      initial-size: 10
      max-wait: 10000
      test-while-idle: true
      validation-query: SELECT 1
      validation-query-timeout: 10
      driver-class-name: com.mysql.jdbc.Driver
    tms:
      url: **************************************************************************************************
      username: tms
      password: tms
    demo:
      url: **************************************************************************************************
      username: tms
      password: tms
    inv:
      url: jdbc:mysql://*************:3306/seatelpay_inv?useSSL=false&useUnicode=true&characterEncoding=utf-8
      username: inv
      password: inv
    cpm:
      url: **************************************************************************************************
      username: cpm
      password: cpm
    csh:
      url: **************************************************************************************************
      username: csh
      password: csh
    mkm:
      url: **************************************************************************************************
      username: mkm
      password: mkm
    acm:
      url: **************************************************************************************************
      username: acm
      password: acm
    chk:
      url: **************************************************************************************************
      username: chk
      password: chk
    cpo:
      url: **************************************************************************************************
      username: cpo
      password: cpo
    cpi:
      url: **************************************************************************************************
      username: cpi
      password: cpi
    cmm:
      url: **************************************************************************************************
      username: cmm
      password: cmm
    csm:
      url: **************************************************************************************************
      username: csm
      password: csm
    tfm:
      url: **************************************************************************************************
      username: tfm
      password: tfm
    rsm:
      url: **************************************************************************************************
      username: rsm
      password: rsm
    bil:
      url: **************************************************************************************************
      username: bil
      password: bil
    urm:
      url: **************************************************************************************************
      username: urm
      password: urm
    onr:
      url: **************************************************************************************************
      username: onr
      password: onr
    rpt:
      url: **************************************************************************************************
      username: tms
      password: tms
  jpa:
    show-sql: true
    database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
    hibernate:
      ddl-auto: update
      naming-strategy: org.hibernate.cfg.ImprovedNamingStrategy
    properties:
      hibernate:
        enable_lazy_load_no_trans: true
  thymeleaf:
    cache: false
    mode: HTML
  jackson:
    serialization:
      write_dates_as_timestamps: false
  mail:
    host: smtp.seatelgroup.com
    username: <EMAIL>
    password: seatel001
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true

#图片服务器参数
upload:
  native:
    paths: upload
  remote:
    url: http://*************:8888
    banner:
      key: 9HScyIXgLE1x5MZg4nILXMzVK3ACMzxuv47DPNTvMGhK0qjG0BDZjwonGfXu2SNZ
      bucket: banner
merc:
  upload:
     depath: /data/tms/merc

mkm:
  local:
    upload: /home/<USER>/data/mkm/local
  remote:
    path: /home/<USER>/data/mkm/remote
    ip: *************
    port: 9898
    user: payment
    password: Hisunpay2017
    timeout: 3000