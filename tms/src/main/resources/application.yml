eureka:
    client:
        serviceUrl:
            defaultZone: http://************:9002/eureka/,http://************:9002/eureka/
        register-with-eureka: false
    instance:
        prefer-ip-address: true

logging:
    config: classpath:logback-spring.xml

server:
    port: 9020
    compression:
        enabled: true
    connection-timeout: 3000

spring:
    http:
        multipart:
            maxFileSize: 10Mb
    application:
        name: TMS
    redis:
        database: 0
        host: ************
        port: 6379
        password: Hisunpay2017
        pool:
            #连接池最大连接数（使用负值表示没有限制）
            max-active: 8
            #连接池最大阻塞等待时间（使用负值表示没有限制）
            max-wait: 10000
            # 连接池中的最大空闲连接
            max-idle: 8
            # 连接池中的最小空闲连接
            min-idle: 1
        #连接超时时间（毫秒）
        timeout: 10000
        #默认缓存过期时间(秒)
        default-expiration: 600
    datasource:
        tomcat:
            max-wait: 10000
            max-active: 10
            test-on-borrow: true
        tms:
            driver-class-name: com.mysql.jdbc.Driver
            url: ***************************************************************************************************
            username: tms
            password: tms
            testWhileIdle: true
            validationQuery: SELECT 1
        demo:
            driver-class-name: com.mysql.jdbc.Driver
            url: ***************************************************************************************************
            username: tms
            password: tms
            testWhileIdle: true
            validationQuery: SELECT 1
        inv:
            driver-class-name: com.mysql.jdbc.Driver
            url: ***************************************************************************************************
            username: inv
            password: inv
            testWhileIdle: true
            validationQuery: SELECT 1
        cpm:
            driver-class-name: com.mysql.jdbc.Driver
            url: ***************************************************************************************************
            username: cpm
            password: cpm
            testWhileIdle: true
            validationQuery: SELECT 1
        csh:
            driver-class-name: com.mysql.jdbc.Driver
            url: ***************************************************************************************************
            username: csh
            password: csh
            testWhileIdle: true
            validationQuery: SELECT 1
        mkm:
            driver-class-name: com.mysql.jdbc.Driver
            url: ***************************************************************************************************
            username: mkm
            password: mkm
            testWhileIdle: true
            validationQuery: SELECT 1
        acm:
            driver-class-name: com.mysql.jdbc.Driver
            url: ***************************************************************************************************
            username: acm
            password: acm
            testWhileIdle: true
            validationQuery: SELECT 1
        chk:
            driver-class-name: com.mysql.jdbc.Driver
            url: jdbc:mysql://**************:3399/seatelpay_chk?useSSL=false&useUnicode=true&characterEncoding=utf-8
            username: chk
            password: chk
            testWhileIdle: true
            validationQuery: SELECT 1
        cpo:
            driver-class-name: com.mysql.jdbc.Driver
            url: jdbc:mysql://**************:3399/seatelpay_cpo?useSSL=false&useUnicode=true&characterEncoding=utf-8
            username: cpo
            password: cpo
            testWhileIdle: true
            validationQuery: SELECT 1
        cpi:
            driver-class-name: com.mysql.jdbc.Driver
            url: jdbc:mysql://**************:3399/seatelpay_cpi?useSSL=false&useUnicode=true&characterEncoding=utf-8
            username: cpi
            password: cpi
            testWhileIdle: true
            validationQuery: SELECT 1
        cmm:
            driver-class-name: com.mysql.jdbc.Driver
            url: jdbc:mysql://**************:3399/seatelpay_cmm?useSSL=false&useUnicode=true&characterEncoding=utf-8
            username: cmm
            password: cmm
            testWhileIdle: true
            validationQuery: SELECT 1
        csm:
            driver-class-name: com.mysql.jdbc.Driver
            url: jdbc:mysql://**************:3399/seatelpay_csm?useSSL=false&useUnicode=true&characterEncoding=utf-8
            username: csm
            password: csm
            testWhileIdle: true
            validationQuery: SELECT 1
        tfm:
            driver-class-name: com.mysql.jdbc.Driver
            url: jdbc:mysql://**************:3399/seatelpay_tfm?useSSL=false&useUnicode=true&characterEncoding=utf-8
            username: tfm
            password: tfm
            testWhileIdle: true
            validationQuery: SELECT 1
        rsm:
            driver-class-name: com.mysql.jdbc.Driver
            url: jdbc:mysql://**************:3399/seatelpay_rsm?useSSL=false&useUnicode=true&characterEncoding=utf-8
            username: rsm
            password: rsm
            testWhileIdle: true
            validationQuery: SELECT 1
        bil:
            driver-class-name: com.mysql.jdbc.Driver
            url: jdbc:mysql://**************:3399/seatelpay_bil?useSSL=false&useUnicode=true&characterEncoding=utf-8
            username: bil
            password: bil
            testWhileIdle: true
            validationQuery: SELECT 1
        urm:
            driver-class-name: com.mysql.jdbc.Driver
            url: jdbc:mysql://**************:3399/seatelpay_urm?useSSL=false&useUnicode=true&characterEncoding=utf-8
            username: urm
            password: urm
            testWhileIdle: true
            validationQuery: SELECT 1
        onr:
            driver-class-name: com.mysql.jdbc.Driver
            url: jdbc:mysql://**************:3399/seatelpay_onr?useSSL=false&useUnicode=true&characterEncoding=utf-8
            username: onr
            password: onr
            testWhileIdle: true
            validationQuery: SELECT 1
        rpt:
            driver-class-name: com.mysql.jdbc.Driver
            url: ***************************************************************************************************
            username: tms
            password: tms
            testWhileIdle: true
            validationQuery: SELECT 1
    jpa:
        show-sql: true
        database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
        hibernate:
            ddl-auto: update
            naming-strategy: org.hibernate.cfg.ImprovedNamingStrategy
        properties:
            hibernate:
                enable_lazy_load_no_trans: true
    thymeleaf:
        cache: false
        mode: HTML
    jackson:
        serialization:
            write_dates_as_timestamps: false
    mail:
        host: smtp.seatelgroup.com
        username: <EMAIL>
        password: seatel001
        properties:
            mail:
                smtp:
                    auth: true
                    starttls:
                        enable: true
                        required: true

ribbon:
    #retry next Server times
    MaxAutoRetriesNextServer: 0
    #retry same Server times
    MaxAutoRetries: 0
    ReadTimeout: 20000
    ConnectTimeout: 5000

feign:
    httpclient:
        enabled: true
        #max connection for http client
        maxTotal: 30
        #max per route connection for http client
        defaultMaxPerRoute: 5
        #Returns the duration of time which this connection can be safely kept idle
        alive: 60000
        #idle timeout for connection /ms
        idleTimeoutMillis: 30000
        #clear expire and idle timeout http connections schedule rate
        clearConnectionsRate: 30000
    # feign client validation
    validation:
        enabled: false
    # feign compression support
    compression:
        request:
            enabled: true
            mime-types: application/json
            min-request-size: 2048
        response:
            enabled: true

#图片服务器参数
upload:
    native:
        paths: upload
    remote:
        url: http://************:8888
        banner:
            key: 9HScyIXgLE1x5MZg4nILXMzVK3ACMzxuv47DPNTvMGhK0qjG0BDZjwonGfXu2SNZ
            bucket: banner
merc:
    upload:
        depath: /data/tms/merc

mkm:
    local:
        upload: /home/<USER>/data/mkm/local
    remote:
        path: /home/<USER>/data/mkm/remote
        ip: ************
        port: 22
        user: payment
        password: Hisunpay2017
        timeout: 3000
