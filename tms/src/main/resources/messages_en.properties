email.activiti.subject=Test mail subject
email.activiti.greeting=Hello, {0} !
email.activiti.name=lvhailong
email.activiti.content=This is a test email
email.activiti.end=Regards# Use
r Registration Management
urm.register.title=User Registration Management
urm.register.content=User Registration Management
urm.register.add=Add
urm.register.search=Search
urm.register.reset=Reset
urm.register.save=Save
urm.register.cancel=Cancel
urm.register.close=Close
urm.register.operations=Operations
urm.register.addTitle=Add User Registration
urm.register.editTitle=Edit User Registration
urm.register.detailTitle=User Registration Details

# User Registration Fields
urm.register.userId=User ID
urm.register.mblNo=Mobile Number
urm.register.usrNm=User Name
urm.register.usrSts=User Status
urm.register.usrLvl=User Level
urm.register.idChkFlg=ID Check Flag
urm.register.idType=ID Type
urm.register.idNo=ID Number
urm.register.usrGender=User Gender
urm.register.usrNation=User Nation
urm.register.usrBirthDt=Birth Date
urm.register.usrRegCnl=Registration Channel
urm.register.usrRegIp=Registration IP
urm.register.createTime=Create Time
urm.register.modifyTime=Modify Time