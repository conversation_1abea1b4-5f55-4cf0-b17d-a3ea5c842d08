eureka:
  client:
    serviceUrl:
      defaultZone: http://*************:9002/eureka/,http://*************:9002/eureka/
    register-with-eureka: false
  instance:
    prefer-ip-address: true

server:
  port: 9020
  compression:
    enabled: true
  connection-timeout: 3000

spring:
  application:
    name: TMS
  datasource:
    tomcat:
      max-wait: 10000
      max-active: 20
      test-on-borrow: true
    tms:
      driver-class-name: com.mysql.jdbc.Driver
      url: **********************************************************************************************
      username: tms
      password: tms
      testWhileIdle: true
      validationQuery: SELECT 1
    demo:
      driver-class-name: com.mysql.jdbc.Driver
      url: **********************************************************************************************
      username: tms
      password: tms
      testWhileIdle: true
      validationQuery: SELECT 1
    inv:
      driver-class-name: com.mysql.jdbc.Driver
      url: **********************************************************************************************
      username: inv
      password: inv
      testWhileIdle: true
      validationQuery: SELECT 1
    cpm:
      driver-class-name: com.mysql.jdbc.Driver
      url: **********************************************************************************************
      username: cpm
      password: cpm
      testWhileIdle: true
      validationQuery: SELECT 1
    csh:
      driver-class-name: com.mysql.jdbc.Driver
      url: **********************************************************************************************
      username: csh
      password: csh
      testWhileIdle: true
      validationQuery: SELECT 1
    mkm:
      driver-class-name: com.mysql.jdbc.Driver
      url: **********************************************************************************************
      username: mkm
      password: mkm
      testWhileIdle: true
      validationQuery: SELECT 1
    acm:
      driver-class-name: com.mysql.jdbc.Driver
      url: **********************************************************************************************
      username: acm
      password: acm
      testWhileIdle: true
      validationQuery: SELECT 1
    chk:
      driver-class-name: com.mysql.jdbc.Driver
      url: **********************************************************************************************
      username: chk
      password: chk
      testWhileIdle: true
      validationQuery: SELECT 1
    cpo:
      driver-class-name: com.mysql.jdbc.Driver
      url: **********************************************************************************************
      username: cpo
      password: cpo
      testWhileIdle: true
      validationQuery: SELECT 1
    cpi:
      driver-class-name: com.mysql.jdbc.Driver
      url: **********************************************************************************************
      username: cpi
      password: cpi
      testWhileIdle: true
      validationQuery: SELECT 1
    cmm:
      driver-class-name: com.mysql.jdbc.Driver
      url: **********************************************************************************************
      username: cmm
      password: cmm
      testWhileIdle: true
      validationQuery: SELECT 1
    csm:
      driver-class-name: com.mysql.jdbc.Driver
      url: **********************************************************************************************
      username: csm
      password: csm
      testWhileIdle: true
      validationQuery: SELECT 1
    tfm:
      driver-class-name: com.mysql.jdbc.Driver
      url: **********************************************************************************************
      username: tfm
      password: tfm
      testWhileIdle: true
      validationQuery: SELECT 1
    rsm:
      driver-class-name: com.mysql.jdbc.Driver
      url: **********************************************************************************************
      username: rsm
      password: rsm
      testWhileIdle: true
      validationQuery: SELECT 1
    bil:
      driver-class-name: com.mysql.jdbc.Driver
      url: **********************************************************************************************
      username: bil
      password: bil
      testWhileIdle: true
      validationQuery: SELECT 1
    urm:
      driver-class-name: com.mysql.jdbc.Driver
      url: **********************************************************************************************
      username: urm
      password: urm
      testWhileIdle: true
      validationQuery: SELECT 1
    onr:
      driver-class-name: com.mysql.jdbc.Driver
      url: **********************************************************************************************
      username: onr
      password: onr
      testWhileIdle: true
      validationQuery: SELECT 1
    rpt:
      driver-class-name: com.mysql.jdbc.Driver
      url: **********************************************************************************************
      username: tms
      password: tms
      testWhileIdle: true
      validationQuery: SELECT 1
  jpa:
    show-sql: true
    database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
    hibernate:
      ddl-auto: update
      naming-strategy: org.hibernate.cfg.ImprovedNamingStrategy
    properties:
      hibernate:
        enable_lazy_load_no_trans: true
  thymeleaf:
    cache: false
    mode: HTML
  jackson:
    serialization:
      write_dates_as_timestamps: false
  mail:
    host: smtp.seatelgroup.com
    username: <EMAIL>
    password: seatel001
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true

ribbon :
  #retry next Server times
  MaxAutoRetriesNextServer : 0
  #retry same Server times
  MaxAutoRetries : 0
  ReadTimeout : 20000
  ConnectTimeout : 5000

feign :
  httpclient :
    enabled : true
    #max connection for http client
    maxTotal : 30
    #max per route connection for http client
    defaultMaxPerRoute : 5
    #Returns the duration of time which this connection can be safely kept idle
    alive : 60000
    #idle timeout for connection /ms
    idleTimeoutMillis : 30000
    #clear expire and idle timeout http connections schedule rate
    clearConnectionsRate : 30000
  # feign client validation
  validation :
    enabled : false
  # feign compression support
  compression :
    request :
      enabled : true
      mime-types : application/json
      min-request-size : 2048
    response:
      enabled : true

#图片服务器参数
upload:
  native:
    paths: upload
  remote:
    url: https://image.bestmpay.com
    banner:
      key: oux6bu22rIzLy5xReCejrqPwscJFqSenjVSLsSnvPHOnDWCuytrzA1cXuHU9jprd
      bucket: banner
merc:
  upload:
     depath: /data/tms/merc

mkm:
  loacl:
    upload: /data/mkm/upload
  remote:
    path: /data/mkm/upload
    ip: *************
    port: 9898
    user: seatel
    password: seatel
    timeout: 300