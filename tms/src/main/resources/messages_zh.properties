email.activiti.subject=测试邮件主题
email.activiti.greeting=你好， {0}！
email.activiti.name=吕海龙
email.activiti.content=这是一封测试邮件
email.activiti.end=致敬

settle.busType.pay=条码消费
settle.busType.qrConsume=扫码付消费
settle.busType.flow=充流量
settle.busType.telFee=充话费
settle.busType.self=自主结算
settle.busType.auto=自动结算
settle.busType.draw=营业厅提现

urm.manager.addsettle.failed=开户成功,结算信息录入失败请到商户修改页面修改商户结算信息
urm.manager.addrate.failed=开户成功,商户利率信息录入失败请到商户修改页面修改商户利率信息
urm.manager.addres.failed=开户成功,商户图片信息录入失败请到商户修改页面修改商户图片信息
urm.manager.noUser=查询不到该商户记录
urm.manager.update.failed=修改商户表信息失败
urm.manager.card.enscrypt=银行卡卡号加密失败
urm.manager.update.settle.failed=修改结算信息失败
urm.manager.update.res.failed=修改商户图片信息失败
urm.manager.update.rate.failed=修改失败
urm.manager.update.seq.failed=修改商户流水表失败

email.user.greeting=您好， {0}！
email.user.subject=[TMS]新增柜员成功
email.user.content=恭喜您，新增柜员成功！
email.user.id=编号为：{0}
email.user.pwd=密码为：{0}
email.user.end=致敬！# 商
户注册管理
urm.register.title=商户注册管理
urm.register.content=商户注册管理
urm.register.add=新增
urm.register.search=查询
urm.register.reset=重置
urm.register.save=保存
urm.register.cancel=取消
urm.register.close=关闭
urm.register.operations=操作
urm.register.addTitle=新增用户注册
urm.register.editTitle=编辑用户注册
urm.register.detailTitle=用户注册详情

# 用户注册字段
urm.register.userId=用户ID
urm.register.mblNo=手机号码
urm.register.usrNm=用户姓名
urm.register.usrSts=用户状态
urm.register.usrLvl=用户级别
urm.register.idChkFlg=实名标志
urm.register.idType=证件类型
urm.register.idNo=证件号码
urm.register.usrGender=用户性别
urm.register.usrNation=用户归属国家
urm.register.usrBirthDt=出生日期
urm.register.usrRegCnl=注册渠道
urm.register.usrRegIp=注册IP
urm.register.createTime=创建时间
urm.register.modifyTime=更新时间

