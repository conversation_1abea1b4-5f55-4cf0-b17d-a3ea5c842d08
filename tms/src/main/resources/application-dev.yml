eureka:
  client:
    serviceUrl:
      #      defaultZone: http://*************:9002/eureka/,http://*************:9002/eureka/
      defaultZone: http://127.0.0.1:9002/eureka/
    register-with-eureka: true
  instance:
    prefer-ip-address: true

server:
  port: 9020
  compression:
    enabled: true
  connection-timeout: 3000

spring:
  application:
    name: TMS
  datasource:
    tomcat:
      max-active: 20
      max-idle: 20
      min-idle: 10
      initial-size: 10
      max-wait: 10000
      test-while-idle: true
      validation-query: SELECT 1
      validation-query-timeout: 10
      driver-class-name: com.mysql.jdbc.Driver
    tms:
      url: jdbc:mysql://*************:3306/wallet3?useSSL=false&useUnicode=true&characterEncoding=utf-8
      username: payment
      password: payment
    demo:
      url: jdbc:mysql://*************:3306/wallet3?useSSL=false&useUnicode=true&characterEncoding=utf-8
      username: payment
      password: payment
    inv:
      url: jdbc:mysql://*************:3306/wallet3?useSSL=false&useUnicode=true&characterEncoding=utf-8
      username: payment
      password: payment
    cpm:
      url: jdbc:mysql://*************:3306/wallet3?useSSL=false&useUnicode=true&characterEncoding=utf-8
      username: payment
      password: payment
    csh:
      url: jdbc:mysql://*************:3306/wallet3?useSSL=false&useUnicode=true&characterEncoding=utf-8
      username: payment
      password: payment
    mkm:
      url: jdbc:mysql://*************:3306/wallet3?useSSL=false&useUnicode=true&characterEncoding=utf-8
      username: payment
      password: payment
    acm:
      url: jdbc:mysql://*************:3306/wallet3?useSSL=false&useUnicode=true&characterEncoding=utf-8
      username: payment
      password: payment
    chk:
      url: jdbc:mysql://*************:3306/wallet3?useSSL=false&useUnicode=true&characterEncoding=utf-8
      username: payment
      password: payment
    cpo:
      url: jdbc:mysql://*************:3306/wallet3?useSSL=false&useUnicode=true&characterEncoding=utf-8
      username: payment
      password: payment
    cpi:
      url: jdbc:mysql://*************:3306/wallet3?useSSL=false&useUnicode=true&characterEncoding=utf-8
      username: payment
      password: payment
    cmm:
      url: jdbc:mysql://*************:3306/wallet3?useSSL=false&useUnicode=true&characterEncoding=utf-8
      username: payment
      password: payment
    csm:
      url: jdbc:mysql://*************:3306/wallet3?useSSL=false&useUnicode=true&characterEncoding=utf-8
      username: payment
      password: payment
    tfm:
      url: jdbc:mysql://*************:3306/wallet3?useSSL=false&useUnicode=true&characterEncoding=utf-8
      username: payment
      password: payment
    rsm:
      url: jdbc:mysql://*************:3306/wallet3?useSSL=false&useUnicode=true&characterEncoding=utf-8
      username: payment
      password: payment
    bil:
      url: jdbc:mysql://*************:3306/wallet3?useSSL=false&useUnicode=true&characterEncoding=utf-8
      username: payment
      password: payment
    urm:
      url: jdbc:mysql://*************:3306/wallet3?useSSL=false&useUnicode=true&characterEncoding=utf-8
      username: payment
      password: payment
    onr:
      url: jdbc:mysql://*************:3306/wallet3?useSSL=false&useUnicode=true&characterEncoding=utf-8
      username: payment
      password: payment
    rpt:
      url: jdbc:mysql://*************:3306/wallet3?useSSL=false&useUnicode=true&characterEncoding=utf-8
      username: payment
      password: payment
    tam:
      url: jdbc:mysql://*************:3306/wallet3?useSSL=false&useUnicode=true&characterEncoding=utf-8
      username: payment
      password: payment
  jpa:
    show-sql: true
    database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
    hibernate:
      ddl-auto: update
      naming-strategy: org.hibernate.cfg.ImprovedNamingStrategy
    properties:
      hibernate:
        enable_lazy_load_no_trans: true
  thymeleaf:
    cache: false
    mode: HTML
  jackson:
    serialization:
      write_dates_as_timestamps: false
  mail:
    host: smtp.seatelgroup.com
    username: <EMAIL>
    password: seatel001
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true

#图片服务器参数
upload:
  native:
    paths: upload
  remote:
    url: http://**************:8888
    banner:
      key: 9HScyIXgLE1x5MZg4nILXMzVK3ACMzxuv47DPNTvMGhK0qjG0BDZjwonGfXu2SNZ
      bucket: banner
merc:
  upload:
    depath: /data/tms/merc

mkm:
  local:
    upload: /home/<USER>/data/mkm/local
  remote:
    path: /home/<USER>/data/mkm/remote
    ip: *************
    port: 22
    user: payment
    password: Hisunpay2017
    timeout: 3000